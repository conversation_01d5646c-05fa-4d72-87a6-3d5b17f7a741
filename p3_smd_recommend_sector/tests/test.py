least_trade_num = 500


def fnc(dropout, months, holding_num, p1):
    mask = dropout_stocks1(big_mask, close_df1, dropout, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]
    res = {}

    f3 = (
        result_mask[mask_quantile(result_mask, p1, p1 + 0.05)]
        .select_dtypes(np.number)
        .apply(lambda row: row.nlargest(holding_num), axis=1)
    )
    f4 = ~f3.isna()
    f4.index = pd.to_datetime(f4.index) + pd.Timedelta(hours=8)
    f4 = f4.stack()

    rrr = rr.loc[f4.loc[f4].index.intersection(rr.index)].reset_index(drop=1)
    # print(rrr)
    if not (rrr.empty or len(rrr) < least_trade_num):
        res[(dropout, months, holding_num, p1)] = rrr
    return res


if __name__ == "__main__":
    from multiprocessing import Pool

    with Pool(14) as p:
        res = p.starmap(
            fnc,
            product(
                [0.3, 0.4, 0.5],
                [1, 2, 3, 6, 12],
                [5, 6, 7, 8, 10],
                np.arange(0.0, 1.0, 0.05),
            ),
        )

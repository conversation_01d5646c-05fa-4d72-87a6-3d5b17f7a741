import sys

sys.path.insert(0, "/home/<USER>/work/lib")

import argparse
import pathlib
import time
from itertools import product

import aichemy.project as yf_prj
import numpy as np
import pandas as pd
import smd_module.factor as smdf
import torch
from aichemy.data_ops import run_construct_dataset
from aichemy.factor_analyse import *
from aichemy.factor_analyse.method import calc_ic, calc_rank_ic
from aichemy.ml.experiments_idx import *
from loguru import logger
from tqdm import tqdm
from mindgo_api import *

from project import *

torch.set_num_threads(6)

parser = argparse.ArgumentParser()
parser.add_argument("--start_date", type=str, default="20200101")
parser.add_argument("--mid_date", type=str, default="20221231")
parser.add_argument("--end_date", type=str, default="20241231")
parser.add_argument("--interval", type=int, default=5)
parser.add_argument("--pool", type=str, default="000905")
parser.add_argument("--easy", action="store_true", default=False)
parser.add_argument("--log_dir", type=str, default="log_20241118")
parser.add_argument("--rolling", type=str, required=True)
args = parser.parse_args()

start_date = args.start_date
mid_date = args.mid_date
end_date = args.end_date
interval = args.interval

gl_logger_id = logger.add(
    f"{args.log_dir}/{start_date[:6]}-{mid_date[:6]}_{end_date[:6]}_{interval}_{args.pool}{'_easy' if args.easy else ''}.log",
    filter=lambda record: "begin" in record["message"]
    or "Finally" in record["message"]
    or "top 5" in record["message"],
)


try:
    tmp = get_price(
        get_all_securities_(start_date, end_date),
        start_date,
        end_date,
        "1d",
        ["open", "close", "high_limit", "low"],
        fq="post",
        skip_paused=True,
        is_panel=True,
    )

    close_df = tmp["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)

    open_df = tmp["open"]
    open_df.index = (pd.to_datetime(open_df.index) + pd.Timedelta(hours=7)).astype(int)

    hl = tmp["low"] < tmp["high_limit"]
    hl.index = (pd.to_datetime(hl.index) + pd.Timedelta(hours=7)).astype(int)

    return_df = close_df.shift(-interval) / open_df.shift(-1) - 1
    alpha_return_df = return_df.sub(return_df.mean(axis=1), axis=0)

    cc_return_df = close_df.shift(-interval) / close_df.shift(0) - 1
    alpha_cc_return_df = cc_return_df.sub(cc_return_df.mean(axis=1), axis=0)
except Exception as e:
    logger.exception(e)
    raise e


def func_train(x):
    return x[0]


def func_non_train(x):
    return x[1]


# change 以分组return为指标
def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
    y_hat = y_hat[:, :, -1].flatten()
    y = y[:, :, -1].flatten()
    ic = calc_ic(y_hat, y)
    rank_ic = calc_rank_ic(y_hat, y)
    tmp = np.argsort(y)
    tmp_y = y[tmp]
    tmp_y_hat = y_hat[tmp]

    n_group = len(y) // (500 if args.pool == "000905" else 1000)

    ic_lst = []
    rank_ic_lst = []
    return_lst = []
    for i in range(n_group):
        t1 = tmp_y_hat[i::n_group]
        t2 = tmp_y[i::n_group]
        ic_lst.append(calc_ic(t1, t2))
        rank_ic_lst.append(calc_rank_ic(t1, t2))
        return_lst.append(t2[t1 > np.percentile(t1, 95)].mean())

    ret = {
        flag: {
            "Return": np.mean(return_lst),
            # "Return": y[y_hat > np.percentile(y_hat, 95)].mean(),
            "IC": ic,
            "IR": np.mean(ic_lst) / np.std(ic_lst),
            "RankIC": rank_ic,
            "RankIR": np.mean(rank_ic_lst) / np.std(rank_ic_lst),
        }
    }
    logger.info(ret)
    return ret


ExpTSPrd.evaluate = evaluate


def decide_metrics(loss, evl):
    return evl["Return"]


def main(ic_threshold, ir_threshold, sd, md, ed):
    try:
        yf_prj.dump(
            version=f"rolling中证500{args.rolling}",
            explicit_dir=f"{sd}_{md}_{ed}",
            g=globals(),
            t1=sd,
            t2=md,
            t3=ed,
            kwargs={
                "num_steps": 1,
                "leading_days": 128,
                "idx": True,
                "shuffle": False,
                "drop_threshold_nan_ratio": 0.2,
                "non_training_size": 0.3,
                "cs_x_scaling_method": "none",
                "cs_y_scaling_method": "none",
                "x_scaling_method": "zscore",
                "y_scaling_method": "none",
            },
            pj_construct_dataset=construct_dataset,
        )

        log_id = logger.add(pathlib.Path(path, "log"))
        logger.info(
            f"Exp {project_id} begin: start: {sd}, mid: {md}, end: {ed}, pool: {args.pool}, interval: {interval}, ic_threshold: {ic_threshold}, ir_threshold: {ir_threshold}"
        )

        data = run_construct_dataset(
            pj_construct_dataset,
            t1=t1,
            t2=t2,
            t3=t3,
            **kwargs,
            ic_threshold=ic_threshold,
            ir_threshold=ir_threshold,
        )

        r_data = process_train_data(
            data,
            t1=t1,
            t2=t2,
            path=path,
            **kwargs,
            # func_train=func_non_train,
            # func_train=func_train,
            func_non_train=func_non_train,
        )

        exp = ExpLinear(path)
        d = exp.construct_index_dataset(*r_data, test_size=0.0, dump=False)
        exp.load_data(*d)
        _ = exp.train(
            enable_log=True,
            progress_log=100,
            tensorboard_comment=project_id,
            decide_metrics=decide_metrics,
        )

        vali_t1 = (
            pd.Timestamp(t2)
            - pd.Timedelta(days=int((pd.Timestamp(t2) - pd.Timestamp(t1)).days * kwargs["non_training_size"]))
        ).strftime("%Y%m%d")
        vali_t2 = t2

        result_ = predict_apply_data(
            exp, data, vali_t1, vali_t2, path=path, enable_log=False, **kwargs, func_pred=func_non_train
        )

        _ = full_test(result_, close_df, interval, 1, "RankIC", 20, is_alpha=1, save_path=path)
        logger.info(
            f"训练集{vali_t1}-{vali_t2}：top 5% ARR: {_['gr']['group_return']['19']['ARR']*np.sign(_['gr']['group_return']['19']['RET']):.4f} MDD: {_['gr']['group_return']['19']['MDD']:.4f}, IC: {_['ic']['ic_mean']:.4f} IC_std: {_['ic']['ic_std']:.4f} IR: {_['ic']['ir']:.4f}"
        )

        vali_t1 = t2
        vali_t2 = t3
        result_ = predict_apply_data(
            exp, data, vali_t1, vali_t2, path=path, enable_log=False, **kwargs, func_pred=func_non_train
        )

        _ = full_test(result_, close_df, interval, 1, "RankIC", 20, is_alpha=1, save_path=path)
        result_ = save_predict_result([result_], file=path + "/predict.csv")
        logger.info(
            f"测试集{vali_t1}-{vali_t2}：top 5% ARR: {_['gr']['group_return']['19']['ARR']*np.sign(_['gr']['group_return']['19']['RET']):.4f} MDD: {_['gr']['group_return']['19']['MDD']:.4f}, IC: {_['ic']['ic_mean']:.4f} IC_std: {_['ic']['ic_std']:.4f} IR: {_['ic']['ir']:.4f}"
        )
    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(log_id)


def iter_icir():
    try:
        for ic_threshold, ir_threshold in product([0.02, 0.03, 0.04, 0.05], [0.2, 0.3, 0.4]):
            main(ic_threshold, ir_threshold, start_date, mid_date, end_date)
    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(gl_logger_id)


def rolling():
    def get_next_month_end(date_str: str, months: int = 2) -> str:
        """获取n个月后的月末日期

        Args:
            date_str (str): 日期字符串，格式：'YYYY-MM-DD' 或 'YYYYMMDD'
            months (int): 往后推几个月

        Returns:
            str: YYYYMMDD格式的日期字符串
        """
        date = pd.Timestamp(date_str)
        next_month_end = date + pd.offsets.MonthEnd(months)
        return next_month_end.strftime("%Y%m%d")

    a, b = args.rolling.split("|")
    try:
        ed = end_date
        md = get_next_month_end(ed, -int(b))
        sd = get_next_month_end(md, -int(a))
        while True:
            main(0.015, 0.3, sd, md, ed)
            ed = get_next_month_end(ed, -int(b))
            md = get_next_month_end(md, -int(b))
            sd = get_next_month_end(sd, -int(b))
            if ed < mid_date:
                break
    except Exception as e:
        logger.exception(e)


if __name__ == "__main__":
    rolling()

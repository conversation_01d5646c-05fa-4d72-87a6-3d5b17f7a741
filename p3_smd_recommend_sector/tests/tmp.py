X = data[
    [
        "price_pct",
        "turnover",
        "num_k",
        "mv",
        "lmv",
        "cnt_count",
        "cnt_mv",
        "cnt_lmv",
        "fdje_10",
        "ztsl_10",
        "fdje_20",
        "ztsl_20",
        "jhjj_fdje_10",
        "jhjj_ztsl_10",
        "price_pct1",
        "cnt_turnover",
        "cnt_price_pct1",
        "cnt_price_pct2",
        "jump",
        "cnt_jump",
        "market_price_pct1_000001.SH",
        "market_price_pct2_000001.SH",
        "market_jump_000001.SH",
        "market_turnover_000001.SH",
        "market_price_pct1_399001.SZ",
        "market_price_pct2_399001.SZ",
        "market_jump_399001.SZ",
        "market_turnover_399001.SZ",
        "market_price_pct1_399006.SZ",
        "market_price_pct2_399006.SZ",
        "market_jump_399006.SZ",
        "market_turnover_399006.SZ",
    ]
]
Y = data["return"] > 0

X["lmv/clmv"] = X["lmv"] / X["cnt_lmv"]
X["mv/cmv"] = X["mv"] / X["cnt_mv"]
X["market_turnover"] = X["market_turnover_000001.SH"] + X["market_turnover_399001.SZ"]
X["ztsl_10/cnt_count"] = X["ztsl_10"] / X["cnt_count"]
X["ztsl/cnt_count"] = (X["ztsl_10"] + X["ztsl_20"]) / X["cnt_count"]
X["fdje10 / cnt_turnover"] = X["fdje_10"] / X["cnt_turnover"]
X["fdje / cnt_turnover"] = (X["fdje_10"] + X["fdje_20"]) / X["cnt_turnover"]
X["cnt_turnover/market_turnover"] = X["cnt_turnover"] / X["market_turnover"]
X["turnover/cnt_turnover"] = X["turnover"] / X["cnt_turnover"]
X["turnover/market_turnover"] = X["turnover"] / X["market_turnover"]
X["turnover/num_k"] = X["turnover"] / X["num_k"]
X["turnover/lmv"] = X["turnover"] / X["lmv"]


test_size = 0.2
vali_size = 0.3

train_x, test_x, train_y, test_y = split_data(X, Y, test_size=test_size, test_last=True)
train_x, vali_x, train_y, vali_y = split_data(train_x, train_y, test_size=test_size, test_last=True)
len(train_x), len(vali_x), len(test_x)

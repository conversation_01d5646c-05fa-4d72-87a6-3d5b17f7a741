def fnc(p1, close_df, dropout, months):
    mask = dropout_stocks1(big_mask, close_df1, dropout, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]
    res = {}

    tmp_bret_df = close_df / close_df.rolling(p1).min() - 1
    tmp_bret_df = tmp_bret_df[mask]
    if p1 > 1:
        d = close_df.rolling(p1).apply(lambda x: x.argmin())
        max_res = pd.DataFrame(np.nan, index=d.index, columns=d.columns)
        for i in range(p1):
            l = p1 - i
            max_res[d == i] = close_df.rolling(l).max()[d == i]
        tmp_df1 = (close_df - close_df.rolling(p1).min()) / (max_res - close_df.rolling(p1).min())
        tmp_df2 = close_df / max_res

    for p6, p7 in tqdm(list(product([0.7, 0.8, 0.9], [0.95, 0.97]))):
        if p1 > 1:
            f5 = tmp_df1 >= p6
            # print(f5.sum(axis=1),np.nansum(f5.values))

            f5 = f5 | (tmp_df2 > p7)
            # print(f5.sum(axis=1),np.nansum(f5.values))
        else:
            f5 = pd.DataFrame(True, index=close_df.index, columns=close_df.columns)

        if np.nansum(f5.values) < 1500:
            continue

        for p2 in [(0.9, None), (0.85, None)]:
            tmp_df3 = mask_quantile(tmp_bret_df, *p2).astype(int)

            for p4 in [1, 2, 3, 5]:
                tmp_df4 = tmp_df3.rolling(p4).sum()

                for p5 in [1, 2, 3]:
                    if p5 > p4:
                        continue

                    f1 = tmp_df4 >= p5
                    ff = f1 & f5
                    # print(ff.sum(axis=1),np.nansum(ff.values))
                    if np.nansum(ff.values) < 800:
                        continue
                    for p3 in [(0.85, None), (0.9, None)]:
                        tmp_df5 = mask_quantile(result, *p3) & mask

                        for p8 in [(0.85, None), (0.9, None)]:

                            f2 = tmp_df5 | mask_quantile(result_mask, *p8)

                            fff = ff & f2
                            if np.nansum(fff.values) < 1500:
                                continue

                            f3 = result[fff].select_dtypes(np.number).apply(lambda row: row.nlargest(8), axis=1)
                            f4 = ~f3.isna()
                            f4.index = pd.to_datetime(f4.index) + pd.Timedelta(hours=8)
                            f4 = f4.stack()

                            rrr = rr.loc[f4.loc[f4].index.intersection(rr.index)].reset_index(drop=1)
                            # print(rrr)
                            if not (rrr.empty or len(rrr) < 1500):
                                res[(dropout, months, p1, p2, p3, p4, p5, p6, p7, p8)] = rrr
    return res


# rrr = fnc(5, close_df.loc[:, mask.any(axis=0)])

if __name__ == "__main__":
    from multiprocessing import Pool

    with Pool(12) as p:
        res = p.starmap(
            fnc,
            product(
                [5, 10, 15, 20, 30, 3],
                [close_df.loc[:, big_mask.any(axis=0)]],
                [0.3, 0.4, 0.5],
                [1, 2, 3, 6],
            ),
        )
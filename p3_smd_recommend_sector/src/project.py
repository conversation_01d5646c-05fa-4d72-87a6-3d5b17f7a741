import copy
import json
from typing import Union, Iterable
from multiprocessing import Manager, RLock
import pathlib
import pickle
import random
from itertools import product
from typing import Dict, List, Optional

import numpy as np
import pandas as pd
from aichemy.data_ops import ApplyPipeline, ResearchPipeline
from aichemy.factor_analyse.method import stat_ic_series
from aichemy.factor_analyse.sql_handler import SQLHandlerOfAlphaIC
from aichemy.utils import slice_dataset
from loguru import logger
from tqdm import tqdm

try:
    import smd_module.factor as smdf
    from mindgo_api import get_index_stocks, get_price  # type: ignore
    from smd_module.utils import StockPoolMask, get_all_securities_
except ImportError as e:
    print(e)

EXCEPTED_FACTOR = {
    # "量价强化学习类": ["RL_DAY_MF_ALPHA6", "RL_DAY_MF_ALPHA11", "RL_DAY_MF_ALPHA7"],
    # "日频量价技术指标类": ["DAY_PV_VVR"],
    # "集合竞价量价类": ["OB_BT_NBOC", "SS_RD_CA1R"],
    # "新闻预测文本训练类": ["NEWS_BUZZ_IWF", "NEWS_BUZZ_ICQF"],
    # "Tick快照量价类": ["TICK_OB_MPB", "TICK_PV_HPVPC", "TICK_PV_LPVPC"],
}
ALPHA_IC_DB = "/home/<USER>/work/因子工厂/alpha_ic.db"


# def get_selected_factors(file, threshold) -> Dict[str, List[str]]:
#     result = json.load(open(pathlib.Path(file)))
#     ic_df = []
#     for hxf in result.keys():
#         for factor in result[hxf].keys():
#             tmp = {}
#             tmp["hxf"] = hxf
#             tmp["factor"] = factor
#             tmp.update(result[hxf][factor]["ic_analyse"])
#             ic_df.append(tmp)

#     ic_df = pd.DataFrame(ic_df)
#     ic_df["ic_mean_abs"] = ic_df["ic_mean"].abs()
#     tmp = {}
#     for i, j in ic_df.loc[ic_df["ic_mean_abs"] > threshold].iterrows():
#         if j["hxf"] in ["Transformer量价预测类"]:
#             continue
#         if j["hxf"] not in tmp:
#             tmp[j["hxf"]] = set()
#         tmp[j["hxf"]].add(j["factor"])
#     return tmp


def get_selected_factors(threshold, interval_length, start_time, end_time) -> Dict[str, List[str]]:
    sh = SQLHandlerOfAlphaIC(ALPHA_IC_DB)

    selected_factors = {}
    for x in threshold.keys():
        d = sh.get(None, x, interval_length, 1, start_time, end_time)

        result = {}
        for i, j in d.groupby("alpha"):
            tmp = stat_ic_series(j["value"])
            tmp.pop("ic_series")
            result[i] = tmp

        result = pd.DataFrame(result).T
        result["abs(ic_mean)"] = result["ic_mean"].abs()
        for i in result.index[result["abs(ic_mean)"] >= threshold[x]]:
            f1, f2 = i.split("-")
            if f1 in ["Transformer量价预测类"]:
                continue
            if f1 not in selected_factors:
                selected_factors[f1] = set()
            selected_factors[f1].add(f2)

    return selected_factors


def get_selected_factors_by_abc_ic(threshold, interval_length, start_time, end_time):
    sh = SQLHandlerOfAlphaIC(ALPHA_IC_DB)

    selected_factors = {}
    for x in threshold.keys():
        d = sh.get(None, x, interval_length, 1, start_time, end_time)

        result = {}
        for i, j in d.groupby("alpha"):
            tmp = stat_ic_series(j["value"])
            tmp.pop("ic_series")
            result[i] = tmp

        result = pd.DataFrame(result).T
        for i in result.index[result["abs_ic_mean"] >= threshold[x]]:
            f1, f2 = i.split("-")
            if f1 in ["Transformer量价预测类"]:
                continue
            if f1 not in selected_factors:
                selected_factors[f1] = set()
            selected_factors[f1].add(f2)

    return selected_factors


def construct_dataset(
    start_dt,
    end_dt,
    factor_cache_file=smdf.DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file=smdf.DEFAULT_INDUSTRY_DICT_CACHE_FILE,
    **kwargs,
):
    data = {}

    fnc = {"abs(ic)": get_selected_factors, "abs_ic": get_selected_factors_by_abc_ic}[kwargs["ic_type"]]
    # 构建X，hxfactor
    selected_factor = fnc(kwargs["ic_threshold"], "5D", f"{kwargs['t1']} 0000", f"{kwargs['t2']} 2359")

    # 构建X, 对应行业的走势
    iclose_df = smdf.get_factor("行业量价因子", "close", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    iopen_df = smdf.get_factor("行业量价因子", "open", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    ilow_df = smdf.get_factor("行业量价因子", "low", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    ihigh_df = smdf.get_factor("行业量价因子", "high", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    data["[x]i_o_c"] = iopen_df / iclose_df - 1
    data["[x]i_h_c"] = ihigh_df / iclose_df - 1
    data["[x]i_l_c"] = ilow_df / iclose_df - 1
    data["[x]i_pc_c"] = iclose_df / iclose_df.shift(1) - 1
    data["[x]i_pc_c5"] = iclose_df / iclose_df.shift(5) - 1

    # 构建X，行业0-1因子
    # for type_ in ["行业因子"]:
    #     for industry_index_thscode in tqdm([j for j in factor_dict[type_] if ".TI" in j], desc=type_):
    #         data[f"[x]{industry_index_thscode}"] = get_factor(type_, industry_index_thscode).fillna(0)

    # 构建X，市值因子
    data["[x]市值因子"] = np.log(
        smdf.get_factor("市值因子", "市值因子", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    )

    # 构建X，个股近期价格
    tmp = get_price(
        get_all_securities_(start_dt, end_dt),
        start_dt,
        end_dt,
        "1d",
        ["open", "close", "is_st", "high", "low", "prev_close", "turnover", "turnover_rate"],
        fq="post",
        is_panel=True,
        skip_paused=True,
    )
    close_df = tmp["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)
    open_df = tmp["open"]
    open_df.index = (pd.to_datetime(open_df.index) + pd.Timedelta(hours=7)).astype(int)
    low_df = tmp["low"]
    low_df.index = (pd.to_datetime(low_df.index) + pd.Timedelta(hours=7)).astype(int)
    high_df = tmp["high"]
    high_df.index = (pd.to_datetime(high_df.index) + pd.Timedelta(hours=7)).astype(int)
    prev_close_df = tmp["prev_close"]
    prev_close_df.index = (pd.to_datetime(prev_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    turnover_df = tmp["turnover"]
    turnover_df.index = (pd.to_datetime(turnover_df.index) + pd.Timedelta(hours=7)).astype(int)
    turnover_rate_df = tmp["turnover_rate"]
    turnover_rate_df.index = (pd.to_datetime(turnover_rate_df.index) + pd.Timedelta(hours=7)).astype(int)

    data["[x]h_c"] = high_df / close_df - 1
    data["[x]l_c"] = low_df / close_df - 1
    data["[x]o_c"] = open_df / close_df - 1
    data["[x]turnover"] = turnover_df.div(turnover_df.sum(axis=1), axis=0)
    data["[x]turnover_rate"] = turnover_rate_df
    data["[x]1"] = close_df / close_df.shift(1)
    data["[x]0"] = close_df / close_df.shift(5)
    for hxf in selected_factor.keys():
        for factor_name in tqdm(selected_factor[hxf], desc=hxf):
            if factor_name not in EXCEPTED_FACTOR.get(hxf, []):
                dd = smdf.get_factor(hxf, factor_name, factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
                # ddd = 1 - (dd.shift(5) - cc5).abs().div(cc5.max(axis=1), axis=0)
                data[f"[x]{factor_name}"] = dd

    logger.info("X构建完成")

    # 构建Y
    yy: pd.DataFrame = close_df.shift(-5) / open_df.shift(-1) - 1
    # edt = to_nstimestamp(f"{kwargs['t2']} 235959")
    # for i in yy.index:
    #     if i >= edt:
    #         continue
    #     y1 = yy.loc[i].quantile(0.05)
    #     yy.loc[i, yy.loc[i] <= y1] = y1
    #     y2 = yy.loc[i].quantile(0.95)
    #     yy.loc[i, yy.loc[i] >= y2] = y2

    #     if (~yy.loc[i].isna()).sum() == 0:
    #         continue
    #     yy.loc[i] = stats.boxcox(yy.loc[i] + 1 - yy.loc[i].min())[0]
    data["[y]"] = yy
    logger.info("Y构建完成")

    spm = StockPoolMask()
    for k in data.keys():
        data[k] = spm(data[k])
    logger.info("股票池筛选完成")

    return data


def construct_dataset_rank(
    start_dt,
    end_dt,
    factor_cache_file=smdf.DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file=smdf.DEFAULT_INDUSTRY_DICT_CACHE_FILE,
    **kwargs,
):
    data = {}

    fnc = {"abs(ic)": get_selected_factors, "abs_ic": get_selected_factors_by_abc_ic}[kwargs["ic_type"]]
    # 构建X，hxfactor
    selected_factor = fnc(kwargs["ic_threshold"], "5D", f"{kwargs['t1']} 0000", f"{kwargs['t2']} 2359")

    # 构建X, 对应行业的走势
    iclose_df = smdf.get_factor("行业量价因子", "close", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    iopen_df = smdf.get_factor("行业量价因子", "open", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    ilow_df = smdf.get_factor("行业量价因子", "low", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    ihigh_df = smdf.get_factor("行业量价因子", "high", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    data["[x]i_o_c"] = iopen_df / iclose_df - 1
    data["[x]i_h_c"] = ihigh_df / iclose_df - 1
    data["[x]i_l_c"] = ilow_df / iclose_df - 1
    data["[x]i_pc_c"] = iclose_df / iclose_df.shift(1) - 1
    data["[x]i_pc_c5"] = iclose_df / iclose_df.shift(5) - 1

    # 构建X，行业0-1因子
    # for type_ in ["行业因子"]:
    #     for industry_index_thscode in tqdm([j for j in factor_dict[type_] if ".TI" in j], desc=type_):
    #         data[f"[x]{industry_index_thscode}"] = get_factor(type_, industry_index_thscode).fillna(0)

    # 构建X，市值因子
    data["[x]市值因子"] = np.log(
        smdf.get_factor("市值因子", "市值因子", factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    )

    # 构建X，个股近期价格
    tmp = get_price(
        get_all_securities_(start_dt, end_dt),
        start_dt,
        end_dt,
        "1d",
        ["open", "close", "is_st", "high", "low", "prev_close", "turnover", "turnover_rate"],
        fq="post",
        is_panel=True,
        skip_paused=True,
    )
    close_df = tmp["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)
    open_df = tmp["open"]
    open_df.index = (pd.to_datetime(open_df.index) + pd.Timedelta(hours=7)).astype(int)
    low_df = tmp["low"]
    low_df.index = (pd.to_datetime(low_df.index) + pd.Timedelta(hours=7)).astype(int)
    high_df = tmp["high"]
    high_df.index = (pd.to_datetime(high_df.index) + pd.Timedelta(hours=7)).astype(int)
    prev_close_df = tmp["prev_close"]
    prev_close_df.index = (pd.to_datetime(prev_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    turnover_df = tmp["turnover"]
    turnover_df.index = (pd.to_datetime(turnover_df.index) + pd.Timedelta(hours=7)).astype(int)
    turnover_rate_df = tmp["turnover_rate"]
    turnover_rate_df.index = (pd.to_datetime(turnover_rate_df.index) + pd.Timedelta(hours=7)).astype(int)

    data["[x]h_c"] = high_df / close_df - 1
    data["[x]l_c"] = low_df / close_df - 1
    data["[x]o_c"] = open_df / close_df - 1
    data["[x]turnover"] = turnover_df.div(turnover_df.sum(axis=1), axis=0)
    data["[x]turnover_rate"] = turnover_rate_df
    data["[x]1"] = close_df / close_df.shift(1)
    data["[x]0"] = close_df / close_df.shift(5)
    for hxf in selected_factor.keys():
        for factor_name in tqdm(selected_factor[hxf], desc=hxf):
            if factor_name not in EXCEPTED_FACTOR.get(hxf, []):
                dd = smdf.get_factor(hxf, factor_name, factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
                # ddd = 1 - (dd.shift(5) - cc5).abs().div(cc5.max(axis=1), axis=0)
                data[f"[x]{factor_name}"] = dd

    logger.info("X构建完成")

    # 构建Y
    yy: pd.DataFrame = close_df.shift(-5) / open_df.shift(-1) - 1
    # edt = to_nstimestamp(f"{kwargs['t2']} 235959")
    # for i in yy.index:
    #     if i >= edt:
    #         continue
    #     y1 = yy.loc[i].quantile(0.05)
    #     yy.loc[i, yy.loc[i] <= y1] = y1
    #     y2 = yy.loc[i].quantile(0.95)
    #     yy.loc[i, yy.loc[i] >= y2] = y2

    #     if (~yy.loc[i].isna()).sum() == 0:
    #         continue
    #     yy.loc[i] = stats.boxcox(yy.loc[i] + 1 - yy.loc[i].min())[0]
    data["[y]"] = yy
    logger.info("Y构建完成")

    for key in data:
        data[key] = data[key].rank(axis=1)

    spm = StockPoolMask()
    for k in data.keys():
        data[k] = spm(data[k])
    logger.info("股票池筛选完成")

    return data


def get_close_df(factor_df):
    symbols = factor_df.columns.tolist()
    start_ts = pd.to_datetime(factor_df.index[0])
    end_ts = pd.to_datetime(factor_df.index[-1]) + pd.Timedelta(hours=30)
    start_dt = start_ts.strftime("%Y-%m-%d")
    end_dt = end_ts.strftime("%Y-%m-%d")

    cache_file = "/home/<USER>/work/database/cache/close_df.pkl"
    cache_data = {}

    if pathlib.Path(cache_file).exists():
        with open(cache_file, "rb") as f:
            cache_data = pickle.load(f)

    if "close_df" in cache_data:
        cached_df = cache_data["close_df"]
        cached_start = pd.to_datetime(cached_df.index[0])
        cached_end = pd.to_datetime(cached_df.index[-1])
        cached_symbols = set(cached_df.columns)
        needed_symbols = set(symbols)

        if cached_start <= start_ts and cached_end >= end_ts and needed_symbols.issubset(cached_symbols):
            # Can use cache - slice out what we need
            close_df = cached_df.loc[
                (cached_df.index >= factor_df.index[0]) & (cached_df.index <= factor_df.index[-1]), symbols
            ]
            return close_df, "D"

    # Need to fetch new data
    tmp = get_price(symbols, start_dt, end_dt, "1d", ["close"], fq="post", is_panel=True, skip_paused=True)
    close_df = tmp["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)

    # Update cache
    cache_data["close_df"] = close_df
    with open(cache_file, "wb") as f:
        pickle.dump(cache_data, f)

    return close_df, "D"


manager = Manager()
shared_dict = manager.dict()
mp_lock = RLock()


def factor_df_gnt(
    start_dt: str,
    end_dt: str,
    factor_list=None,
    shift: Union[int, Iterable] = 0,
    mask: bool = True,
    cached=False,
    **kwargs,
):
    """hxf因子生成器，遍历所有hxf因子，除了集合竞价量价类和Transformer量价预测类"""
    hxfactor_dict = smdf.get_factor_dict(smdf.DEFAULT_FACTOR_CACHE_FILE)
    if mask:
        stock_pool = StockPoolMask()

    def get_cached_factor(hxf, factor):
        if f"{hxf}-{factor}" in shared_dict:
            df = shared_dict[f"{hxf}-{factor}"].copy()
            return df

        df = smdf.get_factor(
            name1=hxf,
            name2=factor,
            shift=0,
            factor_cache_file=smdf.DEFAULT_FACTOR_CACHE_FILE,
            industry_dict_cache_file=smdf.DEFAULT_INDUSTRY_DICT_CACHE_FILE,
            cached=False,
        )
        if mask:
            df = stock_pool(df, **kwargs)
        if cached:
            with mp_lock:
                if f"{hxf}-{factor}" not in shared_dict:
                    shared_dict[f"{hxf}-{factor}"] = df.copy()
        return df

    for hxf in smdf.HXFACTOR + ["资金流", "财务因子"]:
        if hxf in ["集合竞价量价类", "Transformer量价预测类"]:
            continue
        factor_lst = copy.deepcopy(hxfactor_dict.get(hxf, []))
        random.shuffle(factor_lst)
        for factor in factor_lst:
            if factor_list is not None and f"{hxf}-{factor}({shift})" not in factor_list:
                continue
            tmp = get_cached_factor(hxf, factor)
            if isinstance(shift, Iterable):
                for s in shift:
                    yield (
                        f"{hxf}-{factor}({s})",
                        slice_dataset(tmp.shift(s), f"{start_dt} 090000", f"{end_dt} 150000", "both"),
                    )
            else:
                yield (
                    f"{hxf}-{factor}({shift})",
                    slice_dataset(tmp.shift(shift), f"{start_dt} 090000", f"{end_dt} 150000", "both"),
                )


def generate_select_stocks(df: pd.DataFrame, dump=True, filename="select_stocks.json") -> Dict[str, List[Dict]]:
    """根据个股预测值的dataframe, 转换成dict，格式如{"2023-01-01 15:00": [{"symbol": "000001.SH", "value": 0.1}]}

    Args:
        df (pd.DataFrame): _description_
        dump (bool, optional): _description_. Defaults to True.

    Returns:
        Dict[str, List[Dict]]: 格式如{"2023-01-01 15:00": [{"symbol": "000001.SH", "value": 0.1}]}
    """
    ret = {}
    for i, j in df.iterrows():
        ret[pd.Timestamp(i).strftime("%Y-%m-%d %H:%M")] = [{"symbol": k, "value": j[k]} for k in j.dropna().index]
    if dump:
        with open(filename, "w") as f:
            json.dump(ret, f)
    return ret


def process_train_data(data, t1, t2, path="./test", **kwargs):
    train_data = {
        k: slice_dataset(
            v, (pd.Timestamp(t1) - pd.Timedelta(f"{kwargs['leading_days']}D")).strftime("%Y-%m-%d"), t2, "both"
        )
        for k, v in data.items()
    }
    spm = StockPoolMask(5)
    for k in train_data.keys():
        train_data[k] = spm(train_data[k], drop_paused=True)

    research_pipeline = ResearchPipeline(
        path,
        cs_x_scaling_method=kwargs.get("cs_x_scaling_method", "none"),
        cs_y_scaling_method=kwargs.get("cs_y_scaling_method", "cs_zscore"),
        x_scaling_method=kwargs.get("x_scaling_method", "zscore"),
        y_scaling_method=kwargs.get("y_scaling_method", "zscore"),
        num_steps=kwargs.get("num_steps", None),
        dump=True,
        non_training_size=kwargs.get("non_training_size", 0.25),
        drop_threshold_nan_ratio=kwargs.get("drop_threshold_nan_ratio", 1.0),
    )
    if not kwargs.get("idx", False):
        r_data = research_pipeline.process_data(
            train_data,
            shuffle=kwargs.get("shuffle", True),
            enable_log=kwargs.get("enable_log", True),
            chunk_num=kwargs.get("chunk_num", 1),
            func_train=kwargs.get("func_train", None),
            func_non_train=kwargs.get("func_non_train", None),
        )
    else:
        r_data = research_pipeline.process_idx_data(
            train_data,
            shuffle=kwargs.get("shuffle", True),
            enable_log=kwargs.get("enable_log", True),
            func_train=kwargs.get("func_train", None),
            func_non_train=kwargs.get("func_non_train", None),
        )
    if len(r_data[0]) == 0:
        return None

    if "ref" in kwargs:
        kwargs["ref"]["research_pipeline"] = research_pipeline

    return r_data


def predict_apply_data(model, data, t2, t3, path="./test", **kwargs) -> pd.DataFrame:
    """返回的DataFrame, index为nstimestamp, 不是pd.Timestamp"""
    apply_data = {
        k: slice_dataset(
            v,
            (pd.Timestamp(t2) - pd.Timedelta(f"{kwargs['leading_days']}D")).strftime("%Y-%m-%d 00:00:00"),
            pd.Timestamp(t3).strftime("%Y-%m-%d 23:59:59"),
            "right",
        )
        for k, v in data.items()
    }
    spm = StockPoolMask(5)
    for k in apply_data.keys():
        apply_data[k] = spm(apply_data[k], drop_paused=False)
    apply_pipeline = ApplyPipeline(path)
    if not kwargs.get("idx", False):
        apply_x = apply_pipeline.process_data(
            apply_data, enable_log=kwargs.get("enable_log", True), func_pred=kwargs.get("func_pred", None)
        )
        apply_y_hat = model.predict(apply_x)
    else:
        apply_idx, apply_x = apply_pipeline.process_idx_data(
            apply_data, enable_log=kwargs.get("enable_log", True), func_pred=kwargs.get("func_pred", None)
        )
        apply_y_hat = model.predict(apply_x, index=apply_idx)
    apply_y_hat = apply_pipeline.reverse_engineering(apply_y_hat)
    apply_y_hat = slice_dataset(
        apply_y_hat,
        pd.Timestamp(t2).strftime("%Y-%m-%d 00:00:00"),
        pd.Timestamp(t3).strftime("%Y-%m-%d 23:59:59"),
        "right",
    )

    # tmp = apply_data["[y]"].copy().reindex(index=apply_y_hat.index, columns=apply_y_hat.columns)
    # tmp.index = tmp.index.map(from_nstimestamp)
    return apply_y_hat


# def train_one_round(data, t1, t2, t3, path="./test", **kwargs):
#     print("模型训练数据区间：{}——{}，预测区间：{}——{}".format(t1, t2, t2, t3))

#     r_data = process_train_data(data, t1, t2, path, **kwargs)

#     exp = ExpLinear(path)
#     exp = ExpAngosformer(path)
#     exp.load_data(*exp.construct_dataset(r_data), test_size=0.5)
#     exp.train()

#     # from sklearn import linear_model

#     # reg = linear_model.Ridge(alpha=1)
#     # reg.fit(training_x, training_y)

#     # apply_y_hat = reg.predict(apply_x)

#     return predict_apply_data(exp, data, t2, t3, path, **kwargs)


def save_predict_result(result: List[pd.DataFrame], dump=True, file="./predict.csv") -> pd.DataFrame:
    """把rolling预测的结果保存到csv

    Args:
        result (List[pd.DataFrame]): 列表中每个元素为每一次rolling过程的预测结果
        file (str, optional): 保存文件. Defaults to "./predict.csv".

    Returns:
        pd.DataFrame: 全部的预测结果, index为datetime，columns为股票代码
    """
    result_ = pd.concat(result, axis=0).sort_index()
    result_.index = result_.index.map(lambda x: pd.to_datetime(x, utc=True).tz_convert("Asia/Shanghai"))
    if dump:
        result_.to_csv(file)
    return result_


def drop_features(df, threshold):
    tmp = (df > threshold).sum(axis=1)[::-1]
    if (tmp > 0).any():
        tmp = tmp.index[tmp.argmax()]
        return drop_features(df.drop(columns=tmp, index=tmp), threshold)
    else:
        return df


def qbk(forward_return_df: pd.DataFrame, plot: bool = False, **kwargs) -> dict:
    """快速回测

    Args:
        fret (pd.DataFrame): 收益矩阵，index为日期，columns为股票代码
        plot (bool, optional): 是否绘图. Defaults to False.

    Returns:
        dict: 回测结果
    """
    fv = forward_return_df.values
    pcr = np.mean(fv[fv > 0]) / np.abs(np.mean(fv[fv <= 0]))
    wr = (tmp := np.sum(fv > 0)) / (tmp + np.sum(fv <= 0))
    count = np.sum(fv[~np.isnan(fv)] != 0)
    forward_return_df = forward_return_df.mean(axis=1).fillna(0)
    dwr = np.mean(forward_return_df > 0)
    forward_return_df = forward_return_df.cumsum()
    arr = forward_return_df.iloc[-1] / len(forward_return_df) * 250
    mdd = (forward_return_df.cummax() - forward_return_df).max()
    if plot:
        forward_return_df.plot(figsize=(22, 4), **kwargs.get("plot_kwargs", {}))
    return {"WR": wr, "COUNT": count, "ARR": arr, "MDD": mdd, "DWR": dwr, "PCR": pcr}


def mask_quantile(df: pd.DataFrame, q1: Optional[float], q2: Optional[float]) -> pd.DataFrame:
    """根据分位数进行mask，按行，仅保留q1和q2之间的数据为True，左闭右开，q1和q2之外的数据为False，包括NaN

    Args:
        df (pd.DataFrame): 数据矩阵
        q1 (Optional[float]): 分位数1，左闭，如果为None，则表示从最小值开始，包括最小值
        q2 (Optional[float]): 分位数2，右开，如果为None，则表示到最大值结束，包括最大值

    Raises:
        ValueError: q1和q2不能同时为None

    Returns:
        pd.DataFrame: 掩码矩阵
    """
    if q1 is None and q2 is None:
        raise ValueError("q1和q2不能同时为None")

    if q1 is None and q2 is not None:
        row_medians2 = df.apply(lambda x: x.quantile(q2), axis=1)
        return df.apply(lambda row: row < row_medians2[row.name], axis=1)

    if q1 is not None and q2 is None:
        row_medians1 = df.apply(lambda x: x.quantile(q1), axis=1)
        return df.apply(lambda row: row >= row_medians1[row.name], axis=1)

    if q1 >= q2:
        raise ValueError("q1必须小于q2")

    row_medians1 = df.apply(lambda x: x.quantile(q1), axis=1)
    row_medians2 = df.apply(lambda x: x.quantile(q2), axis=1)
    return df.apply(lambda row: (row >= row_medians1[row.name]) & (row < row_medians2[row.name]), axis=1)


def select_stock(result, mask1: Optional[pd.DataFrame] = None, map_dict={(0.9, None): 5, (0.8, 0.9): 3, (0.7, 0.8): 1}):
    res = {i: [] for i in result.index}
    for i, j in map_dict.items():
        mask2 = mask_quantile(result, *i)
        if mask1 is None:
            tmp = result[mask2]
        else:
            tmp = result[mask1 & mask2]
        for tsp, row in tmp.iterrows():
            n = j - len(res[tsp])
            if n > 0:
                slc = row.dropna().nlargest(n).index.tolist()
                res[tsp].extend(slc)
    res = {
        pd.to_datetime(k, utc=True).tz_convert("Asia/Shanghai").strftime("%Y-%m-%d %H:%M"): [{"symbol": vv} for vv in v]
        for k, v in res.items()
    }
    return res


def get_index_stocks_periodically(index_code, tmpl_df):
    ret = pd.DataFrame(0, index=tmpl_df.index, columns=tmpl_df.columns)
    a = [f"{i}{j}" for i, j in product(range(2015, 2099), ["{:>02}01".format(k + 1) for k in range(0, 12, 3)])]
    for i_, j_ in zip(a[:-1], a[1:]):
        i = pd.Timestamp(i_).timestamp() * 1e9
        j = pd.Timestamp(j_).timestamp() * 1e9
        if i > ret.index[-1]:
            break
        aa = get_index_stocks(index_code, date=i_)
        ret.loc[(ret.index >= i) & (ret.index < j), ret.columns.intersection(aa)] = 1
    ret = ret.astype(bool)
    return ret


def dropout_000300_v5(index_stocks_df, close_df, dropout=0.3, months=6):
    ret = index_stocks_df.copy()
    fret_df = close_df / close_df.shift(1) - 1

    month_timestamp_map = {}
    tmp = pd.Series(fret_df.index)
    tmp = pd.concat([tmp, pd.to_datetime(tmp).apply(lambda x: x.year * 12 + x.month)], axis=1)
    for i, j in tmp.groupby(1):
        month_timestamp_map[i] = (j[0].min(), j[0].max())

    month_drop_stocks_map = {}
    for i, j in month_timestamp_map.items():
        if i - months not in month_timestamp_map:
            continue
        if i - 1 not in month_timestamp_map:
            continue
        st = month_timestamp_map[i - months][0]
        ed = month_timestamp_map[i - 1][1]
        tmp = fret_df.loc[(fret_df.index >= st) & (fret_df.index <= ed)]
        tmp = tmp.dropna(axis=1, how="all").cumsum(axis=0)
        tmp = (tmp.cummax(axis=0).iloc[-1] - tmp.iloc[-1]).sort_values(ascending=False)
        # tmp = tmp[index_stocks_df.loc[tmp.name]]
        month_drop_stocks_map[i] = tmp.index[: int(len(tmp) * dropout)].tolist()

    for i in month_drop_stocks_map:
        ret.loc[
            (ret.index >= month_timestamp_map[i][0]) & (ret.index <= month_timestamp_map[i][1]),
            month_drop_stocks_map[i],
        ] = False
    return ret


def dropout_000905_v5(index_stocks_df, close_df, dropout=0.3, keepin=0.1, months=6):
    ret = index_stocks_df.copy()
    fret_df = close_df / close_df.shift(1) - 1

    month_timestamp_map = {}
    tmp = pd.Series(fret_df.index)
    tmp = pd.concat([tmp, pd.to_datetime(tmp).apply(lambda x: x.year * 12 + x.month)], axis=1)
    for i, j in tmp.groupby(1):
        month_timestamp_map[i] = (j[0].min(), j[0].max())

    month_drop_stocks_map = {}
    for i, j in month_timestamp_map.items():
        if i - months not in month_timestamp_map:
            continue
        if i - 1 not in month_timestamp_map:
            continue
        st = month_timestamp_map[i - months][0]
        ed = month_timestamp_map[i - 1][1]
        tmp = fret_df.loc[(fret_df.index >= st) & (fret_df.index <= ed)]
        tmp = tmp.dropna(axis=1, how="all").cumsum(axis=0)
        tmp1 = (tmp.cummax(axis=0).iloc[-1] - tmp.iloc[-1]).sort_values(ascending=False)
        idx = index_stocks_df.loc[tmp1.name]
        tmp1 = tmp1[idx.loc[idx].index.intersection(tmp1.index)]
        tmp1 = tmp1.index[: int(len(tmp1) * dropout)]
        tmp2 = (tmp.iloc[-1] - tmp.cummin(axis=0).iloc[-1]).sort_values(ascending=False)
        idx = index_stocks_df.loc[tmp2.name]
        tmp2 = tmp2[idx.loc[idx].index.intersection(tmp2.index)]
        tmp2 = tmp2.index[: int(len(tmp2) * keepin)]
        month_drop_stocks_map[i] = list(set(tmp1) - set(tmp2))
        # month_drop_stocks_map[i] = tmp1.tolist()

    for i in month_drop_stocks_map:
        ret.loc[
            (ret.index >= month_timestamp_map[i][0]) & (ret.index <= month_timestamp_map[i][1]),
            month_drop_stocks_map[i],
        ] = False
    return ret


def select000300(result, close_df, dropout, months, holding_num, p1):
    close_df = close_df[result.columns]
    index_mask = get_index_stocks_periodically("000300.SH", close_df)
    mask = dropout_000300_v5(index_mask, close_df, dropout, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]
    f3 = (
        result_mask[mask_quantile(result_mask, p1, None)]
        .select_dtypes(np.number)
        .apply(lambda row: row.nlargest(holding_num), axis=1)
    )
    f4 = ~f3.isna()
    return f4


def select000905(result, close_df, dropout, keepin, months, holding_num, p1, p2):
    result = result.rolling(p2).mean()
    index_mask = get_index_stocks_periodically("000905.SH", close_df)
    mask = dropout_000905_v5(index_mask, close_df, dropout, keepin, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]  # [mask_quantile(turnover_df, p1, p1 + 0.1)]
    f3 = result_mask[result_mask > 0].select_dtypes(np.number).apply(lambda row: row.nlargest(holding_num), axis=1)
    f4 = ~f3.isna()
    return f4


def select000852(result, close_df, dropout, keepin, months, holding_num):
    index_mask = get_index_stocks_periodically("000852.SH", close_df)
    mask = dropout_000905_v5(index_mask, close_df, dropout, keepin, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]
    f3 = result_mask.select_dtypes(np.number).apply(lambda row: row.nlargest(holding_num), axis=1)
    f4 = ~f3.isna()
    return f4


def dropout_000300_v6(index_stocks_df, close_df, dropout=0.3, months=6):
    ret = index_stocks_df.copy()
    fret_df = close_df / close_df.shift(1) - 1

    month_timestamp_map = {}
    tmp = pd.Series(fret_df.index)
    tmp = pd.concat([tmp, pd.to_datetime(tmp).apply(lambda x: x.year * 12 + x.month)], axis=1)
    for i, j in tmp.groupby(1):
        month_timestamp_map[i] = (j[0].min(), j[0].max())

    month_drop_stocks_map = {}
    for i, j in month_timestamp_map.items():
        if i - months not in month_timestamp_map:
            continue
        if i - 1 not in month_timestamp_map:
            continue
        st = month_timestamp_map[i - months][0]
        ed = month_timestamp_map[i - 1][1]
        tmp = fret_df.loc[(fret_df.index >= st) & (fret_df.index <= ed)]
        tmp = tmp.dropna(axis=1, how="all").cumsum(axis=0)
        tmp = tmp.cummax(axis=0).iloc[-1] - tmp.iloc[-1]
        finite_tmp = tmp.loc[np.isfinite(tmp)]
        if finite_tmp.empty:
            month_drop_stocks_map[i] = tmp.index.tolist()
        else:
            month_drop_stocks_map[i] = tmp.index.difference(
                finite_tmp.loc[finite_tmp <= np.percentile(finite_tmp.values, 100 - 100 * dropout)].index
            ).tolist()

    for i in month_drop_stocks_map:
        ret.loc[
            (ret.index >= month_timestamp_map[i][0]) & (ret.index <= month_timestamp_map[i][1]),
            ret.columns.isin(month_drop_stocks_map[i]),
        ] = False
    return ret


def dropout_000905_v6(index_stocks_df, close_df, dropout=0.3, keepin=0.1, months=6):
    ret = index_stocks_df.copy()
    fret_df = close_df / close_df.shift(1) - 1

    month_timestamp_map = {}
    tmp = pd.Series(fret_df.index)
    tmp = pd.concat([tmp, pd.to_datetime(tmp).apply(lambda x: x.year * 12 + x.month)], axis=1)
    for i, j in tmp.groupby(1):
        month_timestamp_map[i] = (j[0].min(), j[0].max())

    month_drop_stocks_map = {}
    for i, j in month_timestamp_map.items():
        if i - months not in month_timestamp_map:
            continue
        if i - 1 not in month_timestamp_map:
            continue
        st = month_timestamp_map[i - months][0]
        ed = month_timestamp_map[i - 1][1]
        tmp = fret_df.loc[(fret_df.index >= st) & (fret_df.index <= ed)]
        tmp = tmp.dropna(axis=1, how="all").cumsum(axis=0)

        idx = index_stocks_df.loc[ed]
        idx = idx.loc[idx].index.intersection(tmp.columns)

        tmp1 = (tmp.max(axis=0) - tmp.iloc[-1]).loc[idx]
        finite_tmp1 = tmp1.loc[np.isfinite(tmp1)]
        if finite_tmp1.empty:
            tmp1 = tmp1.index.tolist()
        else:
            tmp1 = tmp1.index.difference(
                # finite_tmp1.loc[finite_tmp1 < np.percentile(finite_tmp1.values, 100 - 100 * dropout)].index
                finite_tmp1.loc[finite_tmp1 <= np.percentile(finite_tmp1.values, 100 - 100 * dropout)].index
            ).tolist()

        tmp2 = (tmp.iloc[-1] - tmp.min(axis=0)).loc[idx]
        finite_tmp2 = tmp2.loc[np.isfinite(tmp2)]
        if finite_tmp2.empty:
            tmp2 = []
        else:
            tmp2 = finite_tmp2.loc[finite_tmp2 >= np.percentile(finite_tmp2.values, 100 - 100 * keepin)].index.tolist()

        month_drop_stocks_map[i] = list(set(tmp1) - set(tmp2))
        # print(i, len(month_drop_stocks_map[i]), len(tmp1), len(tmp2))

    for i in month_drop_stocks_map:
        ret.loc[
            (ret.index >= month_timestamp_map[i][0]) & (ret.index <= month_timestamp_map[i][1]),
            ret.columns.isin(month_drop_stocks_map[i]),
        ] = False
    return ret


def select000300_v6(result, close_df, dropout=0.8, months=1, holding_num=5, p1=0.95):
    # 0.8, 1, 5, 0.95
    index_mask = get_index_stocks_periodically("000300.SH", close_df)

    mask = dropout_000300_v6(index_mask, close_df, dropout, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]
    f3 = (
        result_mask[mask_quantile(result_mask, p1, None)]
        .select_dtypes(np.number)
        .apply(lambda row: row.nlargest(holding_num), axis=1)
    )
    f4 = ~f3.isna()
    return f4


def select000905_v6(result, close_df, dropout=0.55, keepin=0.1, months=12, holding_num=5, p1=0.0, p2=7):
    # 0.55, 0.10, 12, 5, 0.0, 7
    index_mask = get_index_stocks_periodically("000905.SH", close_df)

    result = result.rolling(p2).mean()
    mask = dropout_000905_v6(index_mask, close_df, dropout, keepin, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]  # [mask_quantile(turnover_df, p1, p1 + 0.1)]
    f3 = result_mask[result_mask > 0].select_dtypes(np.number).apply(lambda row: row.nlargest(holding_num), axis=1)
    f4 = ~f3.isna()
    return f4


def select000852_v6(result, close_df, dropout=0.4, months=1, holding_num=6, p1=10, keepin=0.2):
    # 0.4, 1, 6, 10, 0.2
    index_mask = get_index_stocks_periodically("000852.SH", close_df)

    result = result.rank(axis=1, ascending=False).rolling(p1).mean()
    mask = dropout_000905_v6(index_mask, close_df, dropout, keepin, months)
    mask = mask.reindex(index=result.index, columns=result.columns, fill_value=False)
    result_mask = result[mask]
    f4 = result_mask.rank(axis=1, ascending=True, method="min") <= holding_num
    return f4

class Position:
    def __init__(self, symbol, amount, cost_basis, last_price):
        self.symbol = symbol
        self.amount = amount
        self.cost_basis = cost_basis
        self.last_price = last_price


class Order:
    def __init__(self, order_id, symbol,ctime, amount, price, side):
        self.order_id = order_id
        self.symbol = symbol
        self.ctime = ctime
        self.amount = amount
        self.price = price
        self.side = side

        self.filled_amount = 0

    def update(self, filled_amount):
        self.filled_amount = filled_amount

    def bind(self, )
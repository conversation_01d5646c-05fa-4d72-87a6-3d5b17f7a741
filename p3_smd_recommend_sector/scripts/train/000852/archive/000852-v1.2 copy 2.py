# @description: 使用新的analyse方法加速
import sys

sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/dev")

import argparse
import pathlib
import pickle
import random
import re
import shutil
import sys
import time
from functools import lru_cache, reduce
from itertools import product
from pathlib import Path
from typing import Callable, ClassVar, List, Optional

import aichemy.project as yf_prj
import numpy as np
import optuna
import pandas as pd
import smd_module.factor as smdf
import torch
from aichemy.data_ops import run_construct_dataset
from aichemy.factor_analyse import *
from aichemy.factor_analyse.analyse import FactorAnalyse
from aichemy.factor_analyse.method import calc_ic, calc_rank_ic
from aichemy.ml.experiments_idx import *
from aichemy.project import predict_apply_data, process_train_data, rolling, save_predict_result
from aichemy.utils import is_notebook, slice_dataset
from loguru import logger
from mindgo_api import get_price
from optuna import Trial, TrialPruned, create_study, trial
from smd_module.utils import StockPoolMask, calc_trade_day, get_all_securities_, get_index_stocks_periodically

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm

torch.set_num_threads(6)


# if not is_notebook():
#     parser = argparse.ArgumentParser()
#     parser.add_argument("--start_date", type=str, default="20200101")
#     parser.add_argument("--mid_date", type=str, default="20221231")
#     parser.add_argument("--end_date", type=str, default="20241231")
#     parser.add_argument("--interval", type=int, default=5)
#     parser.add_argument("--pool", type=str, default="000905.SH")
#     parser.add_argument("--easy", action="store_true", default=False)
#     parser.add_argument("--log_dir", type=str, default="log_20241118")
#     parser.add_argument("--rolling", type=str)
#     parser.add_argument("--num_groups", type=int, default=10)
#     args = parser.parse_args()

#     start_date = args.start_date
#     mid_date = args.mid_date
#     end_date = args.end_date
#     interval = args.interval
#     gl_log_dir = args.log_dir
#     gl_pool = args.pool
#     gl_easy = args.easy
#     gl_rolling = args.rolling
#     gl_num_groups = args.num_groups
# else:
start_date = "20200101"
mid_date = "20230630"
end_date = "20241231"
interval = 5
gl_log_dir = "rolling9/clipic"
gl_pool = "000852.SH"
gl_easy = False
gl_rolling = ""
gl_num_groups = 10

logger.info(
    f"start_date: {start_date}, mid_date: {mid_date}, end_date: {end_date}, interval: {interval}, pool: {gl_pool}, easy: {gl_easy}, rolling: {gl_rolling}"
)

gl_ref = {}

gl_logger_id = logger.add(
    f"{gl_log_dir}/{start_date[:6]}-{mid_date[:6]}_{end_date[:6]}_{interval}_{gl_pool}{'_easy' if gl_easy else ''}.log",
    filter=lambda record: "begin" in record["message"]
    or "Finally" in record["message"]
    or "top N" in record["message"],
)


try:
    tmp = "2025-04-25"
    if not pathlib.Path(tmp).exists():
        pickle.dump(
            get_price(
                get_all_securities_("2018-01-01", tmp),
                "2018-01-01",
                tmp,
                "1d",
                ["open", "close", "high_limit", "low", "high"],
                fq="post",
                skip_paused=True,
                is_panel=True,
            ),
            open(tmp, "wb"),
        )
    tmp = pickle.load(open(tmp, "rb"))

    gl_high_df = tmp["high"]
    gl_high_df.index = (pd.to_datetime(gl_high_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_low_df = tmp["low"]
    gl_low_df.index = (pd.to_datetime(gl_low_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_close_df = tmp["close"]
    gl_close_df.index = (pd.to_datetime(gl_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_open_df = tmp["open"]
    gl_open_df.index = (pd.to_datetime(gl_open_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_hl = tmp["low"] < tmp["high_limit"]
    gl_hl.index = (pd.to_datetime(gl_hl.index) + pd.Timedelta(hours=7)).astype(int)

    # gl_return_df = gl_close_df.shift(-interval) / gl_close_df - 1
    gl_return_df = gl_close_df.shift(-interval) / gl_open_df.shift(-1) - 1

    tmp = get_price(
        gl_pool,
        "2018-01-01",
        "2025-04-25",
        "1d",
        ["open", "close", "high_limit", "low"],
        fq="post",
        skip_paused=True,
    )
    # tmp = tmp["close"].shift(-interval) / tmp["close"] - 1
    tmp = tmp["close"].shift(-interval) / tmp["open"].shift(-1) - 1
    tmp.index = (pd.to_datetime(tmp.index) + pd.Timedelta(hours=7)).astype(int)

    gl_alpha_return_df = gl_return_df.sub(tmp, axis=0)

except Exception as e:
    logger.exception(e)
    raise e


def func_train(x):
    return x[0]


def func_non_train(x):
    return x[1]


def func_pred(x):
    return x[2]


def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
    global gl_ref
    rp = gl_ref["research_pipeline"]
    y_hat = rp.reverse_engineering(y_hat[:, :, -1], "non_train").values
    y = rp.reverse_engineering(y[:, :, -1], "non_train").values

    y_hat = y_hat[np.isfinite(y_hat).any(axis=1), :]
    y = y[np.isfinite(y).any(axis=1), :]

    ic_lst = []
    rank_ic_lst = []
    return_lst = []
    for i in range(len(y_hat)):
        group_y_hat = y_hat[i]
        group_y = y[i]
        ic_lst.append(calc_ic(group_y_hat, group_y))
        rank_ic_lst.append(calc_rank_ic(group_y_hat, group_y))
        # t3 = group_y[group_y_hat > np.percentile(group_y_hat[np.isfinite(group_y_hat)], 100 - 100 / gl_num_groups)]
        t3 = group_y[pd.Series(group_y_hat).rank(ascending=False) <= 8]
        return_lst.append(np.nanmean(t3))

    ret = {
        flag: {
            "Return": np.mean(return_lst),
            "IC": np.mean(ic_lst),
            "IR": np.mean(ic_lst) / np.std(ic_lst),
            "RankIC": np.mean(rank_ic_lst),
            "RankIR": np.mean(rank_ic_lst) / np.std(rank_ic_lst),
        }
    }
    logger.info(ret)
    return ret


ExpTSPrd.evaluate = evaluate


def decide_metrics(loss, evl):
    return evl["Return"]


class FactorSelect:
    factor_result: ClassVar[pd.DataFrame] = pd.DataFrame()

    def __init__(self, pool):
        if FactorSelect.factor_result.empty:
            directory = Path("/home/<USER>/work/因子工厂/result_2025-04-30/")
            res = []

            # 获取目录下的所有文件和子目录
            for path in directory.iterdir():
                if path.is_file() and {"000852.SH": "_5_000852.csv", "000905.SH": "_5_000905.csv", "all": "_5.csv"}[
                    pool
                ] in str(path):  # 只筛选文件
                    tmp = re.search(r"(\d{8})-(\d{6})", str(path.name))
                    if tmp:
                        dt = tmp.group(1)
                        month = tmp.group(2)
                        d = pd.read_csv(path, index_col=0)
                        d["month"] = month
                        d["dt"] = dt
                        res.append(d)
                    else:
                        raise
            d = pd.concat(res, axis=0)
            d = d.sort_values("month")
            d = d.reset_index()
            d["shift"] = d["f_name"].apply(lambda x: re.search(r"\((\d+)\)", x).group(1))
            d = d.astype({"dt": int})
            FactorSelect.factor_result = d

    def slice(self, end_dt, n):
        return (
            self.factor_result.loc[self.factor_result["dt"] <= int(pd.Timestamp(end_dt).strftime("%Y%m%d"))]
            .groupby("f_name")
            .apply(lambda x: x.nlargest(n, "dt"))
            .reset_index(drop=True)
        )

    @lru_cache(500)
    def _get_factor_by_ic(self, end_dt, n, ic_threshold, ir_threshold) -> pd.Series:
        tmp = self.slice(end_dt, n)
        cols = list(filter(lambda x: "1_0_ic_mean" in x and "top" not in x, tmp.columns))
        count_cols = list(map(lambda s: s.replace("ic_mean", "valid_count"), cols))
        sum_cols = list(map(lambda s: f"sum_{s}", cols))

        for col in cols:
            tmp[f"sum_{col}"] = tmp[col.replace("ic_mean", "valid_count")] * tmp[col]
        tmp = tmp.groupby("f_name").apply(lambda x: x[count_cols + sum_cols].sum(axis=0))

        a = tmp[sum_cols]
        a.columns = a.columns.map(lambda x: x[4:])
        b = tmp[count_cols]
        b.columns = b.columns.map(lambda x: x.replace("valid_count", "ic_mean"))

        c = tmp[count_cols] > (np.max(tmp[count_cols]) * 0.8)
        c.columns = c.columns.map(lambda x: x.replace("valid_count", "ic_mean"))

        d = (a / b)[c].abs().max(axis=1)
        d = d.loc[d > ic_threshold]
        e = (a / b)[c].loc[d.index].abs().idxmax(axis=1)
        return e

    @lru_cache(500)
    def _get_factor_by_abs_ic(self, end_dt, n, ic_threshold, ir_threshold) -> pd.Series:
        tmp = self.slice(end_dt, n)
        cols = list(filter(lambda x: "1_0_ic_mean" in x and "top" not in x, tmp.columns))
        count_cols = list(map(lambda s: s.replace("ic_mean", "valid_count"), cols))
        sum_cols = list(map(lambda s: f"sum_{s}", cols))

        for col in cols:
            tmp[f"sum_{col}"] = tmp[col.replace("ic_mean", "valid_count")] * tmp[col]
        tmp = tmp.groupby("f_name").apply(lambda x: x[count_cols + sum_cols].abs().sum(axis=0))

        a = tmp[sum_cols]
        a.columns = a.columns.map(lambda x: x[4:])
        b = tmp[count_cols]
        b.columns = b.columns.map(lambda x: x.replace("valid_count", "ic_mean"))

        c = tmp[count_cols] > (np.max(tmp[count_cols]) * 0.8)
        c.columns = c.columns.map(lambda x: x.replace("valid_count", "ic_mean"))

        d = (a / b)[c].abs().max(axis=1)
        d = d.loc[d > ic_threshold]
        e = (a / b)[c].loc[d.index].abs().idxmax(axis=1)
        return e

    def get_factor(self, end_dt, n, ic_threshold, ir_threshold, shift):
        ret = {}
        ee = [
            self._get_factor_by_ic(end_dt, n, ic_threshold[0], ir_threshold[0]),
            # self._get_factor_by_abs_ic(end_dt, n, ic_threshold[1], ir_threshold[1]),
        ]
        for e in ee:
            for method, v in e.groupby(e):
                for vv in v.index:
                    fct_type_name, v2 = vv.split("-")
                    tmp = re.search(r"(.+)\((\d+)\)", v2)
                    if not tmp:
                        raise
                    fct_name = tmp.group(1)
                    shift_ = int(tmp.group(2))
                    if shift_ <= shift:
                        ret.setdefault(method, {}).setdefault(fct_type_name, set()).add((fct_name, shift_))
        return ret


def construct_dataset(
    t1,
    t2,
    t3,
    factor_mapping,
    factor_cache_file=smdf.DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file=smdf.DEFAULT_INDUSTRY_DICT_CACHE_FILE,
    **kwargs,
):
    start_dt = (pd.Timestamp(t1) - pd.Timedelta(days=250)).strftime("%Y%m%d")
    end_dt = pd.Timestamp(t3).strftime("%Y%m%d")

    data = {}

    # 构建X，个股近期价格
    # tmp = get_price(
    #     get_all_securities_(start_dt, end_dt),
    #     start_dt,
    #     end_dt,
    #     "1d",
    #     ["open", "close", "is_st", "high", "low", "prev_close", "turnover", "turnover_rate"],
    #     fq="post",
    #     is_panel=True,
    #     skip_paused=True,
    # )
    # close_df = tmp["close"]
    # close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)
    # open_df = tmp["open"]
    # open_df.index = (pd.to_datetime(open_df.index) + pd.Timedelta(hours=7)).astype(int)
    # low_df = tmp["low"]
    # low_df.index = (pd.to_datetime(low_df.index) + pd.Timedelta(hours=7)).astype(int)
    # high_df = tmp["high"]
    # high_df.index = (pd.to_datetime(high_df.index) + pd.Timedelta(hours=7)).astype(int)
    # prev_close_df = tmp["prev_close"]
    # prev_close_df.index = (pd.to_datetime(prev_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    # turnover_df = tmp["turnover"]
    # turnover_df.index = (pd.to_datetime(turnover_df.index) + pd.Timedelta(hours=7)).astype(int)
    # turnover_rate_df = tmp["turnover_rate"]
    # turnover_rate_df.index = (pd.to_datetime(turnover_rate_df.index) + pd.Timedelta(hours=7)).astype(int)

    # 构建Y
    spm = StockPoolMask(interval)

    yy = slice_dataset(
        (gl_high_df / 2 + gl_low_df / 2).shift(-interval) / gl_open_df.shift(-1) - 1,
        f"{start_dt} 0000",
        f"{end_dt} 2359",
        "both",
    )
    # yy = slice_dataset(gl_close_df.shift(-interval) / gl_close_df - 1, f"{start_dt} 0000", f"{end_dt} 2359", "both")
    # yy = slice_dataset(gl_return_df, f"{start_dt} 0000", f"{end_dt} 2359", "both")
    data["[y]"] = spm(yy)
    logger.info("Y构建完成")

    stock_mask = get_index_stocks_periodically(f"{gl_pool}", tmpl_df=data["[y]"])

    # turnover_mask = mask_quantile(turnover_df[stock_mask], 0.8, None)
    # stock_mask = turnover_mask

    with tqdm(
        total=sum([len(vv) for v in factor_mapping.values() for vv in v.values()]), desc=f"因子构建 {start_dt}-{end_dt}"
    ) as pbar:
        for method, fm in factor_mapping.items():
            for hxf, j in fm.items():
                for factor_name, shift in j:
                    pbar.update(1)
                    dd = smdf.get_factor(
                        hxf,
                        factor_name,
                        shift,
                        factor_cache_file,
                        industry_dict_cache_file,
                        start_dt,
                        end_dt,
                        cached=False,
                    )
                    dd = spm(dd, True, True, True, True, True)[stock_mask]
                    dd.replace([np.inf, -np.inf], np.nan, inplace=True)
                    factor_name = f"{factor_name}({shift})"
                    if "clip" not in method:
                        if "(raw)" in method:
                            data[f"[x]{factor_name}"] = dd
                        elif "(log_0)" in method:
                            data[f"[x]{factor_name}"] = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
                        elif "(log_1)" in method:
                            data[f"[x]{factor_name}"] = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
                        elif "(-log_0)" in method:
                            dd *= -1
                            data[f"[x]{factor_name}"] = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
                        elif "(-log_1)" in method:
                            dd *= -1
                            data[f"[x]{factor_name}"] = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
                        else:
                            raise
                    else:
                        dd = np.clip(
                            dd,
                            dd.quantile(0.05, axis=1).to_frame().to_numpy(),
                            dd.quantile(0.95, axis=1).to_frame().to_numpy(),
                        )
                        if "(clip_raw)" in method:
                            data[f"[x]{factor_name}"] = dd
                        elif "(clip_log_0)" in method:
                            data[f"[x]{factor_name}"] = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
                        elif "(clip_log_1)" in method:
                            data[f"[x]{factor_name}"] = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
                        elif "(clip_-log_0)" in method:
                            dd *= -1
                            data[f"[x]{factor_name}"] = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
                        elif "(clip_-log_1)" in method:
                            dd *= -1
                            data[f"[x]{factor_name}"] = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
                        else:
                            raise

    logger.info("X构建完成")

    # tt1 = pd.Timestamp(f"{kwargs['t1']} 000000", tz="Asia/Shanghai").value
    # tt2 = pd.Timestamp(f"{kwargs['t2']} 235959", tz="Asia/Shanghai").value
    # yy1 = yy.loc[(yy.index >= tt1) & (yy.index <= tt2)].values
    # mask1 = None
    # for i, j in kwargs.get("include", []):
    #     tmp_mask = yy.ge(yy.quantile(i / 100, axis=1), axis=0) & yy.le(yy.quantile(j / 100, axis=1), axis=0)
    #     # tmp_mask=(yy > np.percentile(yy1, i)).fillna(False) & (yy < np.percentile(yy1, j)).fillna(False)
    #     mask1 = tmp_mask if mask1 is None else (mask1 | tmp_mask)

    yyy = yy[stock_mask]
    if kwargs.get("include", []):
        mask1 = reduce(
            lambda x, y: x | y,
            (
                yyy.ge(yyy.quantile(i / 100, axis=1), axis=0) & yyy.le(yyy.quantile(j / 100, axis=1), axis=0)
                for i, j in kwargs.get("include", [])
            ),
        )
    else:
        #     mask1 = None

        # if mask1 is None:
        mask1 = pd.DataFrame(True, index=yy.index, columns=yy.columns)

    # data["[mask]0"] = stock_mask & mask1
    data["[mask]0"] = stock_mask & mask1
    data["[mask]1"] = stock_mask
    data["[mask]2"] = stock_mask
    # data["[split]"] = pd.DataFrame(1, index=yy.index, columns=yy.columns)
    for k in data.keys():
        data[k] = data[k].dropna(axis=1, how="all")

    return data


def init_exp(sd, md, ed, ic_threshold, ir_threshold, include, **main_kwargs):
    try:
        factor_select = FactorSelect(gl_pool)
        factors = factor_select.get_factor(
            md, main_kwargs["lookback"], ic_threshold, ir_threshold, main_kwargs["shift"]
        )
        if not factors:
            raise pd.errors.EmptyDataError("没有符合条件的因子")

        yf_prj.dump(
            version=gl_log_dir,
            explicit_dir=f"{sd}_{md}_{ed}",
            g=globals(),
            t1=sd,
            t2=md,
            t3=ed,
            factors=factors,
            kwargs={
                "num_steps": None,
                "leading_days": 0,
                "idx": False,
                "shuffle": False,
                "drop_threshold_nan_ratio": 0.2,
                "non_training_size": 0.0,
                "cs_x_scaling_method": "cs_zscore",
                "cs_y_scaling_method": "cs_zscore",
                "x_scaling_method": "none",
                "y_scaling_method": "none",
            },
            pj_construct_dataset=construct_dataset,
        )

        log_id = logger.add(pathlib.Path(path, "log"))
        logger.info(
            f"Exp {project_id} begin: start: {sd}, mid: {md}, end: {ed}, pool: {gl_pool}, interval: {interval}, ic_threshold: {ic_threshold}, ir_threshold: {ir_threshold}, include: {include}, main_kwargs: {main_kwargs}"
        )

        data = run_construct_dataset(
            pj_construct_dataset, t1=t1, t2=t2, t3=t3, factor_mapping=factors, **kwargs, include=include, **main_kwargs
        )

        global gl_ref
        r_data = process_train_data(
            data,
            start_dt=t1,
            end_dt=t2,
            path=path,
            **kwargs,
            func_train=func_train,
            func_non_train=func_non_train,
            ref=gl_ref,
            enable_log=False,
        )
        return {"log_id": log_id, "r_data": r_data, "data": data}
    except pd.errors.EmptyDataError:
        logger.error("没有符合条件的因子")
        raise
    except Exception as e:
        logger.error("Experiment初始化失败")
        logger.exception(e)
        raise
    finally:
        logger.remove(log_id)


def test_model(exp, data, vali_t1, vali_t2, epoch, flag, path1, path2, analyse=True):
    # exp.load_model(model=pathlib.Path(path2, "checkpoints", f"model_{epoch}.pt"))
    result = predict_apply_data(
        exp, data, vali_t1, vali_t2, path=path1, enable_log=False, **kwargs, func_pred=func_pred
    )
    if not analyse:
        save_predict_result([result], file=path2 + f"/{flag}_predict.csv")
        return result, {}
    return analyse(result, vali_t1, vali_t2, flag, path2)


def analyse(result, vali_t1, vali_t2, flag, path2):
    save_predict_result([result], file=path2 + f"/{flag}_predict.csv")

    fa = FactorAnalyse(result, gl_alpha_return_df)
    ic = fa.ic(1, 0, False, 1, 0)
    ic.plot(savefig=path2)
    ics = ic.stats

    fb = fa.backtest(1, 0, False, False, gl_num_groups, 5, 0.0)
    fb.plot(savefig=path2)
    fbs = fb.stats

    logger.info(
        f"{gl_log_dir}_{flag} {vali_t1}-{vali_t2}：\
    top N% ARR: {fbs['arr'].iloc[-1]:.4f} \
    MDD: {fbs['mdd'].iloc[-1]:.4f}, \
    IC: {ics['ic_mean'].iloc[0]:.4f} \
    IR: {ics['ir'].iloc[0]:.4f}"
    )
    return result, {
        "arr": fbs["arr"].iloc[-1],
        "mdd": fbs["mdd"].iloc[-1],
        "ic": ics["ic_mean"].iloc[0],
        "ir": ics["ir"].iloc[0],
    }


def main(sd, md, ed, ic_threshold, ir_threshold, include, **main_kwargs):
    try:
        exp_dict = init_exp(sd, md, ed, ic_threshold, ir_threshold, include, **main_kwargs)
    except Exception:
        return pd.DataFrame(), {}

    try:
        from sklearn.linear_model import LinearRegression, Ridge, RidgeCV
        from sklearn.pipeline import Pipeline

        x_array, y_array = exp_dict["r_data"][0], exp_dict["r_data"][2]

        class FS:
            def fit(self, x_array, y_array):
                x = pd.DataFrame(x_array)
                y = pd.Series(y_array.flatten())

                # 1. 计算每个特征与 y 的相关性
                corr_with_y = x.corrwith(y).abs().sort_values(ascending=False)

                # 2. 计算特征之间的相关性矩阵
                corr_matrix = x.corr().abs().reindex(index=corr_with_y.index, columns=corr_with_y.index)

                # 3. 设置阈值
                threshold = 0.8

                # 4. 创建一个要保留的特征集合
                to_drop = set()
                kept_features = []

                for i in range(len(corr_matrix.columns)):
                    col_i = corr_with_y.index[i]
                    if col_i in to_drop:
                        continue
                    for j in range(i + 1, len(corr_matrix.columns)):
                        col_j = corr_matrix.columns[j]
                        if corr_matrix.at[col_i, col_j] > threshold:
                            # 如果两个特征高度相关，保留与 y 相关性高的那个
                            if corr_with_y[col_i] >= corr_with_y[col_j]:
                                to_drop.add(col_j)
                            else:
                                to_drop.add(col_i)

                # 5. 最终保留的特征列表
                self.selected_features = [col for col in x.columns if col not in to_drop]
                print("被剔除的特征:", to_drop)
                print("最终保留的特征:", self.selected_features)
                return self

            def transform(self, x):
                return x[:, self.selected_features]

        pipeline = Pipeline([("fs", FS()), ("model", LinearRegression())])
        # model = LinearRegression()
        # # model = RidgeCV(alphas=np.array([1e-5, 1e-4, 1e-3, 0.01, 0.05, 0.1, 0.5, 1, 5, 10]), store_cv_values=True)
        # model.fit(x, y)
        pipeline.fit(x_array, y_array)
        # pickle.dump(pipeline, open(pathlib.Path(path, "model.pkl"), "wb"))

        tt1, tt2 = (pd.Timestamp(md) + pd.Timedelta(days=1)).strftime("%Y%m%d"), ed
        return test_model(
            pipeline,
            exp_dict["data"],
            tt1,
            tt2,
            0,
            "test_at_best_metrics",
            path,
            path,
            analyse=main_kwargs.get("analyse", True),
        )

        # exp = ExpLinear(path)
        # exp = ExpLinear(path)
        # d = exp.construct_index_dataset(*exp_dict["r_data"], test_size=0.0, dump=False)
        # exp.load_data(*d)
        # _ = exp.train(enable_log=True, progress_log=100, tb_comment=project_id, decide_metrics=decide_metrics)

        # vt1, vt2 = "20220701", "20230630"
        # tt1, tt2 = "20230701", "20241231"
        # best_vali_metrics_epoch = exp.matrics_tracker.best_vali_metrics_epoch
        # lowest_vali_loss_epoch = exp.matrics_tracker.lowest_vali_loss_epoch
        # test_model(exp, exp_dict["data"], vt1, vt2, best_vali_metrics_epoch, "vali_at_best_metrics", path, path)
        # test_model(exp, exp_dict["data"], tt1, tt2, best_vali_metrics_epoch, "test_at_best_metrics", path, path)
        # test_model(exp, exp_dict["data"], vt1, vt2, lowest_vali_loss_epoch, "vali_at_lowest_loss", path, path)
        # test_model(exp, exp_dict["data"], tt1, tt2, lowest_vali_loss_epoch, "test_at_lowest_loss", path, path)
    except Exception as e:
        logger.error("Experiment运行失败")
        logger.exception(e)
        logger.remove(exp_dict["log_id"])
        return pd.DataFrame(), {}


def wrap_main(sd, md, ed, *args, **kwargs):
    sd = calc_trade_day(sd, 5).strftime("%Y-%m-%d")
    md = calc_trade_day(md, 5).strftime("%Y-%m-%d")
    ed = calc_trade_day(ed, 5).strftime("%Y-%m-%d")
    return main(sd, md, ed, *args, **kwargs)


def iter_icir():
    try:
        for ic_threshold, ir_threshold in product([0.015, 0.02, 0.025], [0.2, 0.3]):
            main(ic_threshold, ir_threshold, start_date, mid_date, end_date, [])
    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(gl_logger_id)


def iter_include():
    try:
        for include in [
            [(2.5, 45), (55, 97.5)],
            [(2.5, 40), (60, 97.5)],
            [(2.5, 35), (65, 97.5)],
            [(2.5, 30), (70, 97.5)],
            [(2.5, 25), (75, 97.5)],
            [(5, 45), (55, 95)],
            [(5, 40), (60, 95)],
            [(5, 35), (65, 95)],
            [(5, 30), (70, 95)],
            [(5, 25), (75, 95)],
        ]:
            main(0.02, 0.3, start_date, mid_date, end_date, include)
    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(gl_logger_id)


def tuning(ic_threshold, ir_threshold, sd, md, ed, include, **main_kwargs):
    try:
        exp_dict = init_exp(sd, md, ed, ic_threshold, ir_threshold, include, **main_kwargs)

        exp = ExpLinear(path)
        d = exp.construct_index_dataset(*exp_dict["r_data"], test_size=0.0, dump=True)

        def objective(trial):
            # 设定超参数搜索空间
            args = {
                "lr": trial.suggest_float("lr", 5e-5, 1e-2, log=True),
                # "criterion": trial.suggest_categorical("criterion", ["mae", "mse"]),
                # "beta1": trial.suggest_float("beta1", 0.8, 0.99),
                # "beta2": trial.suggest_float("beta2", 0.9, 0.999),
                "weight_decay": trial.suggest_float("weight_decay", 5e-6, 5e-2, log=True),
                "symbol_batch_norm": trial.suggest_categorical("symbol_batch_norm", [True]),
                # "factor": trial.suggest_float("factor", 0.2, 0.8, step=0.1),
                # "patience": trial.suggest_int("patience", 1, 4, step=1),
            }

            # exp = ExpLinear(path)
            exp_id = str(uuid.uuid4())
            logger.info(f"Exp {exp_id} begin")
            tmp_path = str(pathlib.Path(path, exp_id))

            exp = ExpTimeMixer1(tmp_path)
            exp.update_args(args)
            # exp.update_args({"train_epochs": 10, "batch_size": 512, "dropout": 0.0})
            exp.load_data(data_load_mode=2, test_size=0.0, dataset_path=path)
            exp.train(
                enable_log=True,
                progress_log=100,
                trial=trial,
                tb_comment=project_id,
                decide_metrics=decide_metrics,
            )

            vt1, vt2 = "20220701", "20230630"
            tt1, tt2 = "20230701", "20241231"
            best_vali_metrics_epoch = exp.matrics_tracker.best_vali_metrics_epoch
            lowest_vali_loss_epoch = exp.matrics_tracker.lowest_vali_loss_epoch
            test_model(exp, exp_dict["data"], vt1, vt2, best_vali_metrics_epoch, "vali_at_best_metrics", path, tmp_path)
            test_model(exp, exp_dict["data"], tt1, tt2, best_vali_metrics_epoch, "test_at_best_metrics", path, tmp_path)
            test_model(exp, exp_dict["data"], vt1, vt2, lowest_vali_loss_epoch, "vali_at_lowest_loss", path, tmp_path)
            test_model(exp, exp_dict["data"], tt1, tt2, lowest_vali_loss_epoch, "test_at_lowest_loss", path, tmp_path)

            res = exp.matrics_tracker.best_vali_metrics
            torch.cuda.empty_cache()
            return res

        # 创建 Study
        study = create_study(
            direction="maximize",
            pruner=optuna.pruners.MedianPruner(n_startup_trials=10),
            storage="sqlite:///db.sqlite3",
            # storage="postgresql+psycopg2://postgres:123456@192.168.1.202:5432/optuna_dashboard",
            study_name=f"测试合适区间_{main_kwargs['turnover1']}_{main_kwargs['turnover2']}",
            load_if_exists=True,
        )

        # 开始优化
        study.optimize(objective, n_trials=15, show_progress_bar=True)
    except Exception as e:
        logger.error("Tuning运行失败")
        logger.exception(e)
        logger.remove(exp_dict["log_id"])


def sub_process(ic, ir, train_length, lookback, test_length, include, shift, n_cores=1):
    global gl_log_dir
    gl_log_dir = f"{gl_log_dir.split('_')[0]}_{train_length}_{lookback}_{ic}_{ir}_{test_length}_{include}_{shift}"
    if (tmp := pathlib.Path(gl_log_dir, "_predict.csv")).exists():
        return
    else:
        if tmp.parent.exists():
            for i in tmp.parent.iterdir():
                if i.is_file():
                    i.unlink()
                else:
                    shutil.rmtree(i)

    rolling(
        end_date="2025-03-31",
        num=int(30 / test_length),
        train_month=train_length,
        test_month=test_length,
        n_cores=n_cores,
        summary=lambda x: analyse(pd.concat([i[0] for i in x], axis=0), "", "", "", gl_log_dir),
        func=wrap_main,
        ic_threshold=ic,
        ir_threshold=ir,
        include=include,
        lookback=lookback,
        analyse=False,
        shift=shift,
    )


if __name__ == "__main__":
    # main(start_date, mid_date, end_date, 0.2, 0.4, [])

    from multiprocessing import Pool

    pool = Pool(processes=6)
    res = []
    for (
        ic,
        ir,
        test_length,
        include,
        shift,
        train_length,
        lookback,
    ) in product(
        [0.02],
        reversed([0.0]),
        reversed([1, 2, 3, 6]),
        [
            [(0.0, 40), (60, 100)],
            [(0.0, 30), (70, 100)],
            [(0.0, 20), (80, 100)],
            # [(2.5, 30), (70, 97.5)],
            # [(2.5, 25), (75, 97.5)],
            # [(2.5, 20), (80, 97.5)],
        ],
        [0, 5, 10, 15],
        [12, 18, 24, 36, 48, 60],
        [24, 36, 48, 60, 72],
    ):
        if lookback < train_length:
            continue
        if train_length <= 4 * test_length:
            continue
        res.append(pool.apply_async(sub_process, (ic, ir, train_length, lookback, test_length, include, shift)))
    print(f"任务总数: {len(res)}")
    pool.close()
    pool.join()

    # iter_include()
    # iter_icir()

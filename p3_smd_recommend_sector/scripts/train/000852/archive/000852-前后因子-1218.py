import sys

sys.path.insert(0, "/home/<USER>/work/lib")

import argparse
import pickle
import pathlib
import time
from itertools import product

import aichemy.project as yf_prj
import numpy as np
import optuna
import pandas as pd
import smd_module.factor as smdf
import torch
from aichemy.data_ops import run_construct_dataset
from aichemy.factor_analyse import *
from aichemy.factor_analyse.method import calc_ic, calc_rank_ic
from aichemy.ml.experiments_idx import *
from loguru import logger
from mindgo_api import *
from optuna import Trial, TrialPruned, create_study, trial
from tqdm import tqdm
from smd_module.backtest import stats

from project import *

torch.set_num_threads(6)


def is_notebook():
    if "ipykernel_launcher.py" in sys.argv[0]:
        return True
    return False


if not is_notebook():
    parser = argparse.ArgumentParser()
    parser.add_argument("--start_date", type=str, default="20200101")
    parser.add_argument("--mid_date", type=str, default="20221231")
    parser.add_argument("--end_date", type=str, default="20241231")
    parser.add_argument("--interval", type=int, default=5)
    parser.add_argument("--pool", type=str, default="000905")
    parser.add_argument("--easy", action="store_true", default=False)
    parser.add_argument("--log_dir", type=str, default="log_20241118")
    parser.add_argument("--rolling", type=str)
    parser.add_argument("--num_groups", type=int, default=10)
    args = parser.parse_args()

    start_date = args.start_date
    mid_date = args.mid_date
    end_date = args.end_date
    interval = args.interval
    gl_log_dir = args.log_dir
    gl_pool = args.pool
    gl_easy = args.easy
    gl_rolling = args.rolling
    gl_num_groups = args.num_groups
else:
    start_date = "20200101"
    mid_date = "20230630"
    end_date = "20241231"
    interval = 5
    gl_log_dir = "log_000852"
    gl_pool = "000852"
    gl_easy = False
    gl_rolling = ""
    gl_num_groups = 10

logger.info(
    f"start_date: {start_date}, mid_date: {mid_date}, end_date: {end_date}, interval: {interval}, pool: {gl_pool}, easy: {gl_easy}, rolling: {gl_rolling}"
)

gl_ref = {}

gl_logger_id = logger.add(
    f"{gl_log_dir}/{start_date[:6]}-{mid_date[:6]}_{end_date[:6]}_{interval}_{gl_pool}{'_easy' if gl_easy else ''}.log",
    filter=lambda record: "begin" in record["message"]
    or "Finally" in record["message"]
    or "top N" in record["message"],
)


try:
    tmp = get_price(
        get_all_securities_(start_date, end_date),
        start_date,
        end_date,
        "1d",
        ["open", "close", "high_limit", "low"],
        fq="post",
        skip_paused=True,
        is_panel=True,
    )

    close_df = tmp["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)

    open_df = tmp["open"]
    open_df.index = (pd.to_datetime(open_df.index) + pd.Timedelta(hours=7)).astype(int)

    hl = tmp["low"] < tmp["high_limit"]
    hl.index = (pd.to_datetime(hl.index) + pd.Timedelta(hours=7)).astype(int)

    return_df = close_df.shift(-interval) / open_df.shift(-1) - 1
    alpha_return_df = return_df.sub(return_df.mean(axis=1), axis=0)

    cc_return_df = close_df.shift(-interval) / close_df.shift(0) - 1
    alpha_cc_return_df = cc_return_df.sub(cc_return_df.mean(axis=1), axis=0)

    big_mask, rr = pickle.load(open("/home/<USER>/work/通用回测框架/C端股票推荐/1000_1m_1.pkl", "rb"))

except Exception as e:
    logger.exception(e)
    raise e


def func_train(x):
    return x[0]


def func_non_train(x):
    return x[1]


def func_pred(x):
    return x[2]


def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
    global gl_ref
    rp = gl_ref["research_pipeline"]
    y_hat = rp.reverse_engineering(y_hat[:, :, -1], "non_train").values
    y = rp.reverse_engineering(y[:, :, -1], "non_train").values

    y_hat = y_hat[np.isfinite(y_hat).any(axis=1), :]
    y = y[np.isfinite(y).any(axis=1), :]

    ic_lst = []
    rank_ic_lst = []
    return_lst = []
    for i in range(len(y_hat)):
        group_y_hat = y_hat[i]
        group_y = y[i]
        ic_lst.append(calc_ic(group_y_hat, group_y))
        rank_ic_lst.append(calc_rank_ic(group_y_hat, group_y))
        t3 = group_y[group_y_hat > np.percentile(group_y_hat[np.isfinite(group_y_hat)], 100 - 100 / gl_num_groups)]
        return_lst.append(np.nanmean(t3))

    ret = {
        flag: {
            "Return": np.mean(return_lst),
            "IC": np.mean(ic_lst),
            "IR": np.mean(ic_lst) / np.std(ic_lst),
            "RankIC": np.mean(rank_ic_lst),
            "RankIR": np.mean(rank_ic_lst) / np.std(rank_ic_lst),
        }
    }
    logger.info(ret)
    return ret


ExpTSPrd.evaluate = evaluate


def decide_metrics(loss, evl):
    return evl["RankIC"]


def construct_dataset(
    start_dt,
    end_dt,
    factor_cache_file=smdf.DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file=smdf.DEFAULT_INDUSTRY_DICT_CACHE_FILE,
    **kwargs,
):
    if kwargs.get("t1"):
        start_dt = (pd.Timestamp(kwargs.get("t1")) - pd.Timedelta(days=250)).strftime("%Y%m%d")
    if kwargs.get("t3"):
        end_dt = pd.Timestamp(kwargs.get("t3")).strftime("%Y%m%d")

    data = {}

    # 构建X，hxfactor
    tmp_factor_list = []
    for tmp in [
        f"/home/<USER>/work/因子工厂/result/{202307}-{202412}_{5}.csv",
        f"/home/<USER>/work/因子工厂/result/{start_date[:6]}-{mid_date[:6]}_{interval}.csv",
    ]:
        while not pathlib.Path(tmp).exists():
            logger.info(f"{tmp} 不存在")
            time.sleep(60)
        res1 = pd.read_csv(tmp, index_col=0)
        res2 = res1.loc[
            (res1["0_0_ic_mean_cs"] > kwargs["ic_threshold"]) | (res1["0_0_ic_mean"] > kwargs["ic_threshold"])
        ]
        res2 = res2.loc[res2["1_0_ir"] > kwargs["ir_threshold"]]

        factor_name = {"original": {}, "cs": {}}

        for i, j in res2.iterrows():
            name1, name2 = i.split("-")
            if name1 not in smdf.HXFACTOR:
                continue
            if j["0_0_ic_mean_cs"] > j["0_0_ic_mean"]:
                if name1 not in factor_name["cs"]:
                    factor_name["cs"][name1] = []
                factor_name["cs"][name1].append(name2)
            else:
                if name1 not in factor_name["original"]:
                    factor_name["original"][name1] = []
                factor_name["original"][name1].append(name2)
        tmp_factor_list.append(factor_name)

    selected_factor = {"original": {}, "cs": {}}
    for i in set(tmp_factor_list[0]["original"].keys()).intersection(tmp_factor_list[1]["original"].keys()):
        selected_factor["original"][i] = list(
            set(tmp_factor_list[0]["original"][i]).intersection(tmp_factor_list[1]["original"][i])
        )
    for i in set(tmp_factor_list[0]["cs"].keys()).intersection(tmp_factor_list[1]["cs"].keys()):
        selected_factor["cs"][i] = list(set(tmp_factor_list[0]["cs"][i]).intersection(tmp_factor_list[1]["cs"][i]))

    # 构建X，个股近期价格
    tmp = get_price(
        get_all_securities_(start_dt, end_dt),
        start_dt,
        end_dt,
        "1d",
        ["open", "close", "is_st", "high", "low", "prev_close", "turnover", "turnover_rate"],
        fq="post",
        is_panel=True,
        skip_paused=True,
    )
    close_df = tmp["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)
    open_df = tmp["open"]
    open_df.index = (pd.to_datetime(open_df.index) + pd.Timedelta(hours=7)).astype(int)
    low_df = tmp["low"]
    low_df.index = (pd.to_datetime(low_df.index) + pd.Timedelta(hours=7)).astype(int)
    high_df = tmp["high"]
    high_df.index = (pd.to_datetime(high_df.index) + pd.Timedelta(hours=7)).astype(int)
    prev_close_df = tmp["prev_close"]
    prev_close_df.index = (pd.to_datetime(prev_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    turnover_df = tmp["turnover"]
    turnover_df.index = (pd.to_datetime(turnover_df.index) + pd.Timedelta(hours=7)).astype(int)
    turnover_rate_df = tmp["turnover_rate"]
    turnover_rate_df.index = (pd.to_datetime(turnover_rate_df.index) + pd.Timedelta(hours=7)).astype(int)

    for i, j in selected_factor.items():
        for hxf in j.keys():
            for factor_name in tqdm(j[hxf], desc=hxf):
                if factor_name not in EXCEPTED_FACTOR.get(hxf, []):
                    dd = smdf.get_factor(
                        hxf, factor_name, factor_cache_file, industry_dict_cache_file, start_dt, end_dt
                    )
                    if i == "cs":
                        data[f"[x]{factor_name}"] = dd.sub(dd.mean(axis=1), axis=0).div(dd.std(axis=1), axis=0)
                    else:
                        data[f"[x]{factor_name}"] = dd

    logger.info("X构建完成")

    # 构建Y
    yy: pd.DataFrame = close_df.shift(-interval) / open_df.shift(-1) - 1
    data["[y]"] = yy
    logger.info("Y构建完成")

    stock_mask = get_index_stocks_periodically(f"{gl_pool}.SH", tmpl_df=data["[y]"])
    spm = StockPoolMask(interval)

    for k in data.keys():
        data[k] = spm(data[k])

    data["[y]"] = data["[y]"].sub(data["[y]"].mean(axis=1), axis=0).div(data["[y]"].std(axis=1), axis=0)
    logger.info("股票池筛选完成")

    yy = spm(yy)
    tt1 = pd.Timestamp(f"{kwargs['t1']} 000000", tz="Asia/Shanghai").value
    tt2 = pd.Timestamp(f"{kwargs['t2']} 235959", tz="Asia/Shanghai").value
    yy1 = yy.loc[(yy.index >= tt1) & (yy.index <= tt2)].values
    yy1 = yy1[np.isfinite(yy1)]
    mask1 = None
    for i, j in kwargs.get("include", []):
        if mask1 is None:
            mask1 = (yy > np.percentile(yy1, i)).fillna(False) & (yy < np.percentile(yy1, j)).fillna(False)
        else:
            mask1 = mask1 | ((yy > np.percentile(yy1, i)).fillna(False) & (yy < np.percentile(yy1, j)).fillna(False))

    f1 = smdf.get_factor("量价强化学习类", "RL_DAY_MF_ALPHA7", start_dt=start_dt, end_dt=end_dt)
    f2 = smdf.get_factor("分钟频量价技术指标类", "MIN_TRADE_INST", start_dt=start_dt, end_dt=end_dt)
    f3 = smdf.get_factor("分钟频量价技术指标类", "MIN_TRADE_OUST", start_dt=start_dt, end_dt=end_dt)
    f4 = smdf.get_factor("分钟频量价技术指标类", "MIN_PD_TSC", start_dt=start_dt, end_dt=end_dt)
    f5 = smdf.get_factor("分钟频量价技术指标类", "MIN_TRADE_STREN", start_dt=start_dt, end_dt=end_dt)
    ff = (
        mask_quantile(f1[stock_mask], 0.2, None)
        & mask_quantile(f2[stock_mask], None, 0.8)
        & mask_quantile(f3[stock_mask], None, 0.8)
        & mask_quantile(f4[stock_mask], None, 0.8)
        & mask_quantile(f5[stock_mask], None, 0.8)
    )

    if mask1 is None:
        mask1 = pd.DataFrame(True, index=yy.index, columns=yy.columns)
    # data["[mask]0"] = stock_mask & mask1
    data["[mask]0"] = mask1 & ff
    data["[mask]1"] = stock_mask & ff
    data["[mask]2"] = stock_mask & ff
    # 20220701之后为验证集
    data["[split]"] = pd.DataFrame(1, index=yy.index, columns=yy.columns)
    data["[split]"].loc[yy.index >= pd.Timestamp("20220701 000000", tz="Asia/Shanghai").value] = 0

    return data


def main(ic_threshold, ir_threshold, sd, md, ed, include):
    try:
        yf_prj.dump(
            version=gl_log_dir,
            # explicit_dir=f"{sd}_{md}_{ed}",
            g=globals(),
            t1=sd,
            t2=md,
            t3=ed,
            kwargs={
                "num_steps": 1,
                "leading_days": 128,
                "idx": True,
                "shuffle": False,
                "drop_threshold_nan_ratio": 0.2,
                "non_training_size": 0.3,
                "cs_x_scaling_method": "none",
                "cs_y_scaling_method": "none",
                "x_scaling_method": "zscore",
                "y_scaling_method": "none",
            },
            pj_construct_dataset=construct_dataset,
        )

        log_id = logger.add(pathlib.Path(path, "log"))
        logger.info(
            f"Exp {project_id} begin: start: {sd}, mid: {md}, end: {ed}, pool: {gl_pool}, interval: {interval}, ic_threshold: {ic_threshold}, ir_threshold: {ir_threshold}, include: {include}"
        )

        data = run_construct_dataset(
            pj_construct_dataset,
            t1=t1,
            t2=t2,
            t3=t3,
            **kwargs,
            ic_threshold=ic_threshold,
            ir_threshold=ir_threshold,
            include=include,
            start_dt=sd,
            end_dt=ed,
        )

        global gl_ref
        r_data = process_train_data(
            data,
            t1=t1,
            t2=t2,
            path=path,
            **kwargs,
            # func_train=func_non_train,
            func_train=func_train,
            func_non_train=func_non_train,
            ref=gl_ref,
        )

        exp = ExpLinear(path)
        # exp = ExpTimeMixer1(path)
        d = exp.construct_index_dataset(*r_data, test_size=0.0, dump=False)
        exp.load_data(*d)
        _ = exp.train(
            enable_log=False,
            progress_log=100,
            tensorboard_comment=project_id,
            decide_metrics=decide_metrics,
        )

        # vali_t1 = (
        #     pd.Timestamp(t2)
        #     - pd.Timedelta(days=int((pd.Timestamp(t2) - pd.Timestamp(t1)).days * kwargs["non_training_size"]))
        # ).strftime("%Y%m%d")
        # vali_t2 = t2
        vali_t1 = "20220701"
        vali_t2 = "20241213"
        result = predict_apply_data(
            exp, data, vali_t1, vali_t2, path=path, enable_log=False, **kwargs, func_pred=func_pred
        )
        save_predict_result([result], file=path + f"/2021_2024_predict_{project_id}.csv")
        result_mask = result[big_mask]

        f3 = result_mask.select_dtypes(np.number).apply(lambda row: row.nlargest(8), axis=1)
        f4 = ~f3.isna()
        f4.index = pd.to_datetime(f4.index) + pd.Timedelta(hours=8)
        f4 = f4.stack()

        rrr = rr.loc[f4.loc[f4].index.intersection(rr.index)].reset_index(drop=1)
        if not rrr.empty:
            rrr = stats(rrr)
            logger.info(f"top N 累计日收益: {rrr['累计日收益']}")

        exp.load_model(
            model=pathlib.Path(path, "checkpoints", f"model_{exp.matrics_tracker.lowest_vali_loss_epoch}.pt")
        )
        result = predict_apply_data(
            exp, data, vali_t1, vali_t2, path=path, enable_log=False, **kwargs, func_pred=func_pred
        )
        save_predict_result([result], file=path + f"/2021_2024_predict_{project_id}1.csv")
        result_mask = result[big_mask]

        f3 = result_mask.select_dtypes(np.number).apply(lambda row: row.nlargest(8), axis=1)
        f4 = ~f3.isna()
        f4.index = pd.to_datetime(f4.index) + pd.Timedelta(hours=8)
        f4 = f4.stack()

        rrr = rr.loc[f4.loc[f4].index.intersection(rr.index)].reset_index(drop=1)
        if not rrr.empty:
            rrr = stats(rrr)
            logger.info(f"top N 累计日收益1: {rrr['累计日收益']}")

    #         _ = full_test(
    #             result_, close_df, interval, 1, "RankIC", gl_num_groups, is_alpha=0, save_path=pathlib.Path(path, "vali")
    #         )
    #         logger.info(
    #             f"训练集{vali_t1}-{vali_t2}：\
    # top N% ARR: {_['gr']['group_return'][str(gl_num_groups - 1)]['ARR']*np.sign(_['gr']['group_return'][str(gl_num_groups - 1)]['RET']):.4f} \
    # MDD: {_['gr']['group_return'][str(gl_num_groups - 1)]['MDD']:.4f}, \
    # IC: {_['ic']['ic_mean']:.4f} \
    # IC_std: {_['ic']['ic_std']:.4f} \
    # IR: {_['ic']['ir']:.4f}"
    #         )

    #         vali_t1 = t2
    #         vali_t2 = t3
    #         result_ = predict_apply_data(
    #             exp, data, vali_t1, vali_t2, path=path, enable_log=False, **kwargs, func_pred=func_pred
    #         )

    #         _ = full_test(
    #             result_, close_df, interval, 1, "RankIC", gl_num_groups, is_alpha=0, save_path=pathlib.Path(path, "test")
    #         )
    #         result_ = save_predict_result([result_], file=path + "/predict.csv")
    #         logger.info(
    #             f"测试集{vali_t1}-{vali_t2}：\
    # top N% ARR: {_['gr']['group_return'][str(gl_num_groups - 1)]['ARR']*np.sign(_['gr']['group_return'][str(gl_num_groups - 1)]['RET']):.4f} \
    # MDD: {_['gr']['group_return'][str(gl_num_groups - 1)]['MDD']:.4f}, \
    # IC: {_['ic']['ic_mean']:.4f} \
    # IC_std: {_['ic']['ic_std']:.4f} \
    # IR: {_['ic']['ir']:.4f}"
    #         )
    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(log_id)


def iter_icir():
    try:
        for ic_threshold, ir_threshold in product([0.015, 0.02, 0.025], [0.2, 0.25, 0.3]):
            main(ic_threshold, ir_threshold, start_date, mid_date, end_date, [])
    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(gl_logger_id)


def iter_include():
    try:
        for include in [
            [(2.5, 45), (55, 97.5)],
            [(2.5, 40), (60, 97.5)],
            [(2.5, 35), (65, 97.5)],
            [(2.5, 30), (70, 97.5)],
            [(2.5, 25), (75, 97.5)],
            [(5, 45), (55, 95)],
            [(5, 40), (60, 95)],
            [(5, 35), (65, 95)],
            [(5, 30), (70, 95)],
            [(5, 25), (75, 95)],
        ]:
            main(0.02, 0.3, start_date, mid_date, end_date, include)
    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(gl_logger_id)


def rolling():
    def get_next_month_end(date_str: str, months: int = 2) -> str:
        """获取n个月后的月末日期

        Args:
            date_str (str): 日期字符串，格式：'YYYY-MM-DD' 或 'YYYYMMDD'
            months (int): 往后推几个月

        Returns:
            str: YYYYMMDD格式的日期字符串
        """
        date = pd.Timestamp(date_str)
        next_month_end = date + pd.offsets.MonthEnd(months)
        return next_month_end.strftime("%Y%m%d")

    a, b = gl_rolling.split("|")
    try:
        ed = end_date
        md = get_next_month_end(ed, -int(b))
        sd = get_next_month_end(md, -int(a))
        while True:
            main(0.015, 0.3, sd, md, ed)
            ed = get_next_month_end(ed, -int(b))
            md = get_next_month_end(md, -int(b))
            sd = get_next_month_end(sd, -int(b))
            if ed < mid_date:
                break
    except Exception as e:
        logger.exception(e)


def tuning(ic_threshold, ir_threshold, sd, md, ed, include):
    try:
        yf_prj.dump(
            version="tuning中证500",
            # explicit_dir=f"{sd}_{md}_{ed}",
            g=globals(),
            t1=sd,
            t2=md,
            t3=ed,
            kwargs={
                "num_steps": 1,
                "leading_days": 128,
                "idx": True,
                "shuffle": False,
                "drop_threshold_nan_ratio": 0.2,
                "non_training_size": 0.3,
                "cs_x_scaling_method": "none",
                "cs_y_scaling_method": "none",
                "x_scaling_method": "zscore",
                "y_scaling_method": "none",
            },
            pj_construct_dataset=construct_dataset,
        )

        log_id = logger.add(pathlib.Path(path, "log"))
        logger.info(
            f"Exp {project_id} begin: start: {sd}, mid: {md}, end: {ed}, pool: {gl_pool}, interval: {interval}, ic_threshold: {ic_threshold}, ir_threshold: {ir_threshold}, include: {include}"
        )

        data = run_construct_dataset(
            pj_construct_dataset,
            t1=t1,
            t2=t2,
            t3=t3,
            **kwargs,
            ic_threshold=ic_threshold,
            ir_threshold=ir_threshold,
            include=include,
        )

        global gl_ref
        r_data = process_train_data(
            data,
            t1=t1,
            t2=t2,
            path=path,
            **kwargs,
            # func_train=func_non_train,
            func_train=func_train,
            func_non_train=func_non_train,
            ref=gl_ref,
        )

        exp = ExpLinear(path)
        d = exp.construct_index_dataset(*r_data, test_size=0.0, dump=True)

        def objective(trial):
            # 设定超参数搜索空间
            args = {
                "lr": trial.suggest_float("lr", 1e-6, 1e-3, log=True),
                "criterion": trial.suggest_categorical("criterion", ["mae", "mse"]),
                # "beta1": trial.suggest_float("beta1", 0.8, 0.99),
                # "beta2": trial.suggest_float("beta2", 0.9, 0.999),
                "weight_decay": trial.suggest_float("weight_decay", 1e-6, 1e-3, log=True),
                # "factor": trial.suggest_float("factor", 0.2, 0.8, step=0.1),
                # "patience": trial.suggest_int("patience", 1, 4, step=1),
            }

            exp = ExpLinear(path)
            exp.update_args(args)
            # exp.update_args({"train_epochs": 10, "batch_size": 512, "dropout": 0.0})
            exp.load_data(data_load_mode=2, test_size=0.0)
            exp.train(
                enable_log=1,
                progress_log=100,
                trial=trial,
                tensorboard_comment=project_id,
                decide_metrics=decide_metrics,
            )

            vali_t1 = (
                pd.Timestamp(t2)
                - pd.Timedelta(days=int((pd.Timestamp(t2) - pd.Timestamp(t1)).days * kwargs["non_training_size"]))
            ).strftime("%Y%m%d")
            vali_t2 = t2

            result_ = predict_apply_data(
                exp, data, vali_t1, vali_t2, path=path, enable_log=False, **kwargs, func_pred=func_non_train
            )

            _ = full_test(result_, close_df, interval, 1, "RankIC", 20, is_alpha=1, save_path=path)
            logger.info(
                f"训练集{vali_t1}-{vali_t2}：top 5% ARR: {_['gr']['group_return']['19']['ARR']*np.sign(_['gr']['group_return']['19']['RET']):.4f} MDD: {_['gr']['group_return']['19']['MDD']:.4f}, IC: {_['ic']['ic_mean']:.4f} IC_std: {_['ic']['ic_std']:.4f} IR: {_['ic']['ir']:.4f}"
            )

            vali_t1 = t2
            vali_t2 = t3
            result_ = predict_apply_data(
                exp, data, vali_t1, vali_t2, path=path, enable_log=False, **kwargs, func_pred=func_non_train
            )

            _ = full_test(result_, close_df, interval, 1, "RankIC", 20, is_alpha=1, save_path=path)
            result_ = save_predict_result([result_], file=path + "/predict.csv")
            logger.info(
                f"测试集{vali_t1}-{vali_t2}：top 5% ARR: {_['gr']['group_return']['19']['ARR']*np.sign(_['gr']['group_return']['19']['RET']):.4f} MDD: {_['gr']['group_return']['19']['MDD']:.4f}, IC: {_['ic']['ic_mean']:.4f} IC_std: {_['ic']['ic_std']:.4f} IR: {_['ic']['ir']:.4f}"
            )
            res = exp.train_record.best_vali_metrics
            torch.cuda.empty_cache()
            return res

        # 创建 Study
        study = create_study(
            direction="maximize",
            pruner=optuna.pruners.MedianPruner(n_startup_trials=10),
            storage="sqlite:///db.sqlite3",
            # storage="postgresql+psycopg2://postgres:123456@192.168.1.202:5432/optuna_dashboard",
            study_name="tuning_linear",
            load_if_exists=True,
        )

        # 开始优化
        study.optimize(objective, n_trials=30, show_progress_bar=True)

    except Exception as e:
        logger.exception(e)
    finally:
        logger.remove(log_id)


if __name__ == "__main__":
    # main(0.015, 0.3, start_date, mid_date, end_date, [])
    # iter_include()
    iter_icir()

"""
优化版本：使用multiprocessing.shared_memory实现更高效的共享内存方案
"""
import sys
sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/dev")

import multiprocessing as mp
import pickle
import tempfile
import pathlib
from typing import Dict, Tuple, Optional
from multiprocessing import shared_memory
import numpy as np
import pandas as pd
from loguru import logger


class SharedMemoryDataFrameManagerV2:
    """使用multiprocessing.shared_memory的高效共享内存管理器"""
    
    def __init__(self):
        self.shared_blocks = {}  # 存储共享内存块
        self.metadata = {}  # 存储DataFrame元数据
        
    def save_dataframe(self, key: str, df: pd.DataFrame) -> Dict:
        """将DataFrame保存到共享内存"""
        # 转换为numpy数组
        data_array = df.values.astype(np.float64)
        
        # 创建共享内存块
        shm = shared_memory.SharedMemory(create=True, size=data_array.nbytes)
        
        # 将数据复制到共享内存
        shared_array = np.ndarray(data_array.shape, dtype=np.float64, buffer=shm.buf)
        shared_array[:] = data_array[:]
        
        # 存储共享内存块引用
        self.shared_blocks[key] = shm
        
        # 存储元数据
        metadata = {
            'shm_name': shm.name,
            'shape': data_array.shape,
            'dtype': np.float64,
            'index': df.index,
            'columns': df.columns
        }
        self.metadata[key] = metadata
        
        logger.debug(f"已保存DataFrame '{key}' 到共享内存，大小: {data_array.nbytes / 1024 / 1024:.2f} MB")
        return metadata
    
    def load_dataframe(self, key: str, metadata: Dict = None) -> pd.DataFrame:
        """从共享内存加载DataFrame"""
        if metadata is None:
            metadata = self.metadata.get(key)
            if metadata is None:
                raise KeyError(f"DataFrame with key '{key}' not found")
        
        # 连接到现有的共享内存块
        try:
            shm = shared_memory.SharedMemory(name=metadata['shm_name'])
        except FileNotFoundError:
            raise KeyError(f"Shared memory block '{metadata['shm_name']}' not found")
        
        # 重建numpy数组
        shared_array = np.ndarray(metadata['shape'], dtype=metadata['dtype'], buffer=shm.buf)
        
        # 创建DataFrame副本（避免共享内存被意外修改）
        df = pd.DataFrame(shared_array.copy(), index=metadata['index'], columns=metadata['columns'])
        
        # 不要关闭共享内存，因为其他进程可能还在使用
        return df
    
    def get_metadata(self) -> Dict:
        """获取所有DataFrame的元数据"""
        return self.metadata.copy()
    
    def cleanup(self):
        """清理共享内存块"""
        for key, shm in self.shared_blocks.items():
            try:
                shm.close()
                shm.unlink()
                logger.debug(f"已清理共享内存块: {key}")
            except Exception as e:
                logger.warning(f"清理共享内存块 {key} 时出错: {e}")
        self.shared_blocks.clear()
        self.metadata.clear()


class OptimizedTaskContext:
    """优化的TaskContext，使用高效共享内存"""
    
    def __init__(self, pools, intervals, shared_metadata: Optional[Dict] = None):
        # 如果提供了共享元数据，说明是在子进程中，不需要重新计算数据
        self.pools = pools
        self.intervals = intervals
        self.shared_metadata = shared_metadata or {}
        self.shared_memory_manager = SharedMemoryDataFrameManagerV2()
        
        # 如果没有共享元数据，说明是在主进程中，需要初始化数据
        if not shared_metadata:
            self._initialize_data()
    
    def _initialize_data(self):
        """在主进程中初始化数据并保存到共享内存"""
        import smd_module.factor as smdf
        from smd_module.utils import StockPoolMask, get_index_stocks_periodically
        from aichemy.utils import slice_dataset
        
        logger.info("开始初始化数据...")
        
        # 加载基础数据
        gl_close_df = smdf.get_factor("行情", "close")
        gl_open_df = smdf.get_factor("行情", "open")
        gl_high_df = smdf.get_factor("行情", "high")
        gl_low_df = smdf.get_factor("行情", "low")
        gl_turnover_df = smdf.get_factor("行情", "turnover")
        gl_turnover_rate_df = smdf.get_factor("行情", "turnover_rate")
        
        # 计算股票池掩码
        self.stock_mask = {}
        RATIO = 0.9  # 从原代码中获取
        
        for pool in self.pools:
            if pool == "ALL":
                self.stock_mask[pool] = np.isfinite(gl_close_df)
            elif pool == "turnover":
                self.stock_mask[pool] = gl_turnover_df.gt(gl_turnover_df.quantile(RATIO, axis=1), axis=0) & np.isfinite(gl_close_df)
            elif pool == "turnover_rate":
                self.stock_mask[pool] = gl_turnover_rate_df.gt(gl_turnover_rate_df.quantile(RATIO, axis=1), axis=0) & np.isfinite(gl_close_df)
            else:
                self.stock_mask[pool] = get_index_stocks_periodically(f"{pool}.SH", gl_close_df)
        
        # 计算时间区间
        from p3_smd_recommend_sector.scripts.因子检测.20250902_v1 import get_all_dt_intervat
        self.all_dt_interval = get_all_dt_intervat()
        
        # 计算并保存return_df到共享内存
        logger.info("开始计算return_df并保存到共享内存...")
        for interval in self.intervals:
            sp = StockPoolMask(n=interval)
            for start_dt, end_dt in self.all_dt_interval:
                start_dt_ = (pd.Timestamp(start_dt) - pd.Timedelta(days=20)).strftime("%Y-%m-%d")
                end_dt_ = (pd.Timestamp(end_dt) + pd.Timedelta(days=20)).strftime("%Y-%m-%d")
                
                close_df = slice_dataset(gl_high_df / 2 + gl_low_df / 2, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
                open_df = slice_dataset(gl_open_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
                
                return_df = np.log(close_df.shift(-interval)) - np.log(open_df.shift(-1))
                processed_return_df = sp(
                    return_df,
                    drop_paused=2,
                    drop_close_hit_limit_up=True,
                    drop_close_hit_limit_dn=True,
                    drop_next_open_hit_limit_up=True,
                    drop_next_open_hit_limit_dn=True,
                )
                
                # 保存到共享内存
                key = f"return_df_{start_dt}_{end_dt}_{interval}"
                metadata = self.shared_memory_manager.save_dataframe(key, processed_return_df)
                self.shared_metadata[key] = metadata
        
        logger.info(f"已保存 {len(self.shared_metadata)} 个return_df到共享内存")
    
    def get_return_df(self, start_dt: str, end_dt: str, interval: int) -> pd.DataFrame:
        """从共享内存获取return_df"""
        key = f"return_df_{start_dt}_{end_dt}_{interval}"
        metadata = self.shared_metadata.get(key)
        if metadata is None:
            raise KeyError(f"Return df for ({start_dt}, {end_dt}, {interval}) not found")
        return self.shared_memory_manager.load_dataframe(key, metadata)
    
    def get_shared_metadata(self) -> Dict:
        """获取共享元数据，用于传递给子进程"""
        return self.shared_metadata.copy()
    
    def cleanup(self):
        """清理共享内存"""
        self.shared_memory_manager.cleanup()


def create_optimized_worker_process(shared_metadata: Dict, pools, intervals):
    """创建优化的工作进程函数"""
    def worker_func(input_queue, output_queue):
        # 在子进程中创建TaskContext，使用共享的元数据
        worker_context = OptimizedTaskContext(pools=pools, intervals=intervals, shared_metadata=shared_metadata)
        
        # 这里需要实现run方法，类似原来的逻辑
        # 由于篇幅限制，这里只是示例框架
        try:
            from queue import Empty
            while True:
                try:
                    factor = input_queue.get_nowait()
                    # 处理因子的逻辑...
                    # return_df = worker_context.get_return_df(start_dt, end_dt, interval)
                    # ... 其他处理逻辑
                    
                except Empty:
                    logger.info("因子队列为空，工作进程退出")
                    break
                except Exception as e:
                    logger.exception(e)
        finally:
            output_queue.put({})  # 发送空字典表示完成
    
    return worker_func


# 使用示例
if __name__ == "__main__":
    pools = ["turnover"]
    intervals = [2]
    n_cores = 4
    
    # 在主进程中初始化共享内存
    main_context = OptimizedTaskContext(pools=pools, intervals=intervals)
    shared_metadata = main_context.get_shared_metadata()
    
    try:
        # 创建队列
        factor_queue = mp.Manager().Queue()
        output_queue = mp.Manager().Queue()
        
        # 添加一些测试任务
        for i in range(10):
            factor_queue.put(f"test_factor_{i}")
        
        # 创建并启动工作进程
        worker_func = create_optimized_worker_process(shared_metadata, pools, intervals)
        processes = []
        
        for i in range(n_cores):
            p = mp.Process(target=worker_func, args=(factor_queue, output_queue))
            p.start()
            processes.append(p)
        
        # 等待所有进程完成
        for p in processes:
            p.join()
            
        logger.info("所有工作进程已完成")
        
    finally:
        # 清理共享内存
        main_context.cleanup()

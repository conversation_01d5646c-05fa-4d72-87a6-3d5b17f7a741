#!/usr/bin/env python3
"""
测试共享内存优化效果的脚本
"""

import multiprocessing as mp
import time
import psutil
import os
from typing import Dict
import pandas as pd
import numpy as np
from loguru import logger

# 导入优化后的类
from multiprocessing import shared_memory


class SharedMemoryDataFrameManager:
    """使用multiprocessing.shared_memory的高效共享内存管理器"""

    def __init__(self):
        self.shared_blocks = {}  # 存储共享内存块
        self.metadata = {}  # 存储DataFrame元数据

    def save_dataframe(self, key: str, df: pd.DataFrame) -> Dict:
        """将DataFrame保存到共享内存"""
        # 转换为numpy数组
        data_array = df.values.astype(np.float64)

        # 创建共享内存块
        shm = shared_memory.SharedMemory(create=True, size=data_array.nbytes)

        # 将数据复制到共享内存
        shared_array = np.ndarray(data_array.shape, dtype=np.float64, buffer=shm.buf)
        shared_array[:] = data_array[:]

        # 存储共享内存块引用
        self.shared_blocks[key] = shm

        # 存储元数据
        metadata = {
            "shm_name": shm.name,
            "shape": data_array.shape,
            "dtype": np.float64,
            "index": df.index,
            "columns": df.columns,
        }
        self.metadata[key] = metadata

        logger.debug(f"已保存DataFrame '{key}' 到共享内存，大小: {data_array.nbytes / 1024 / 1024:.2f} MB")
        return metadata

    def load_dataframe(self, key: str, metadata: Dict = None) -> pd.DataFrame:
        """从共享内存加载DataFrame"""
        if metadata is None:
            metadata = self.metadata.get(key)
            if metadata is None:
                raise KeyError(f"DataFrame with key '{key}' not found")

        # 连接到现有的共享内存块
        try:
            shm = shared_memory.SharedMemory(name=metadata["shm_name"])
        except FileNotFoundError:
            raise KeyError(f"Shared memory block '{metadata['shm_name']}' not found")

        # 重建numpy数组
        shared_array = np.ndarray(metadata["shape"], dtype=metadata["dtype"], buffer=shm.buf)

        # 创建DataFrame副本（避免共享内存被意外修改）
        df = pd.DataFrame(shared_array.copy(), index=metadata["index"], columns=metadata["columns"])

        # 不要关闭共享内存，因为其他进程可能还在使用
        return df

    def cleanup(self):
        """清理共享内存块"""
        for key, shm in self.shared_blocks.items():
            try:
                shm.close()
                shm.unlink()
                logger.debug(f"已清理共享内存块: {key}")
            except Exception as e:
                logger.warning(f"清理共享内存块 {key} 时出错: {e}")
        self.shared_blocks.clear()
        self.metadata.clear()


def get_memory_usage():
    """获取当前进程的内存使用情况"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB


def create_test_dataframes(n_dfs=10, rows=1000, cols=100):
    """创建测试用的DataFrame"""
    dfs = {}
    for i in range(n_dfs):
        # 创建随机数据
        data = np.random.randn(rows, cols)
        index = pd.date_range("2020-01-01", periods=rows, freq="D")
        columns = [f"col_{j}" for j in range(cols)]
        df = pd.DataFrame(data, index=index, columns=columns)
        dfs[f"test_df_{i}"] = df
    return dfs


def test_traditional_approach(test_dfs, n_processes=4):
    """测试传统方法（每个进程复制数据）"""
    logger.info("测试传统方法...")

    def worker_traditional(shared_dict, process_id):
        """传统工作进程"""
        logger.info(f"进程 {process_id} 开始，内存使用: {get_memory_usage():.2f} MB")

        # 模拟处理数据
        for key, df in shared_dict.items():
            # 简单的计算操作
            result = df.mean().sum()
            time.sleep(0.1)  # 模拟计算时间

        logger.info(f"进程 {process_id} 完成，内存使用: {get_memory_usage():.2f} MB")

    start_time = time.time()
    start_memory = get_memory_usage()

    # 使用Manager共享数据（实际上每个进程都会复制）
    manager = mp.Manager()
    shared_dict = manager.dict(test_dfs)

    processes = []
    for i in range(n_processes):
        p = mp.Process(target=worker_traditional, args=(shared_dict, i))
        p.start()
        processes.append(p)

    for p in processes:
        p.join()

    end_time = time.time()
    end_memory = get_memory_usage()

    logger.info(f"传统方法完成:")
    logger.info(f"  时间: {end_time - start_time:.2f} 秒")
    logger.info(f"  内存变化: {end_memory - start_memory:.2f} MB")

    return end_time - start_time, end_memory - start_memory


def test_shared_memory_approach(test_dfs, n_processes=4):
    """测试共享内存方法"""
    logger.info("测试共享内存方法...")

    def worker_shared_memory(shared_metadata, process_id):
        """共享内存工作进程"""
        logger.info(f"进程 {process_id} 开始，内存使用: {get_memory_usage():.2f} MB")

        # 创建共享内存管理器
        manager = SharedMemoryDataFrameManager()

        # 从共享内存加载数据并处理
        for key, metadata in shared_metadata.items():
            df = manager.load_dataframe(key, metadata)
            # 简单的计算操作
            result = df.mean().sum()
            time.sleep(0.1)  # 模拟计算时间

        logger.info(f"进程 {process_id} 完成，内存使用: {get_memory_usage():.2f} MB")

    start_time = time.time()
    start_memory = get_memory_usage()

    # 在主进程中创建共享内存
    shared_manager = SharedMemoryDataFrameManager()
    shared_metadata = {}

    for key, df in test_dfs.items():
        metadata = shared_manager.save_dataframe(key, df)
        shared_metadata[key] = metadata

    try:
        processes = []
        for i in range(n_processes):
            p = mp.Process(target=worker_shared_memory, args=(shared_metadata, i))
            p.start()
            processes.append(p)

        for p in processes:
            p.join()

        end_time = time.time()
        end_memory = get_memory_usage()

        logger.info(f"共享内存方法完成:")
        logger.info(f"  时间: {end_time - start_time:.2f} 秒")
        logger.info(f"  内存变化: {end_memory - start_memory:.2f} MB")

        return end_time - start_time, end_memory - start_memory

    finally:
        # 清理共享内存
        shared_manager.cleanup()


def main():
    """主测试函数"""
    logger.add("shared_memory_test.log", level="DEBUG")
    logger.info("开始共享内存优化测试")

    # 创建测试数据
    logger.info("创建测试数据...")
    test_dfs = create_test_dataframes(n_dfs=2, rows=500, cols=5000)

    total_size = sum(df.memory_usage(deep=True).sum() for df in test_dfs.values()) / 1024 / 1024
    logger.info(f"测试数据总大小: {total_size:.2f} MB")

    n_processes = 4
    logger.info(f"使用 {n_processes} 个进程进行测试")

    # 测试传统方法
    traditional_time, traditional_memory = test_traditional_approach(test_dfs, n_processes)

    # 等待一下让系统稳定
    time.sleep(2)

    # 测试共享内存方法
    shared_time, shared_memory = test_shared_memory_approach(test_dfs, n_processes)

    # 输出对比结果
    logger.info("=" * 50)
    logger.info("测试结果对比:")
    logger.info(f"传统方法 - 时间: {traditional_time:.2f}s, 内存变化: {traditional_memory:.2f}MB")
    logger.info(f"共享内存 - 时间: {shared_time:.2f}s, 内存变化: {shared_memory:.2f}MB")
    logger.info(f"时间优化: {((traditional_time - shared_time) / traditional_time * 100):.1f}%")
    logger.info(f"内存优化: {((traditional_memory - shared_memory) / traditional_memory * 100):.1f}%")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()

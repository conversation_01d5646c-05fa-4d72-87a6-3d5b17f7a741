import pickle
import sys
from typing import Tuple

sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/dev")
sys.path.insert(0, "/home/<USER>/work/C端项目-板块推荐/")

import pathlib

import numpy as np
import pandas as pd
import smd_module.factor as smdf
from aichemy.factor_analyse.analyse import FactorAnalyse
from aichemy.utils import is_notebook, slice_dataset
from loguru import logger
from mindgo_api import get_price  # type: ignore
from project import factor_df_gnt
from smd_module.utils import StockPoolMask, calc_trade_day, get_all_securities_, get_index_stocks_periodically

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm

logger.add(f"因子检测_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.log", level="TRACE")
RATIO = 0.8
PATH = f"/home/<USER>/work/因子工厂/turnover_{RATIO * 100:.0f}_930o_2025-07-27"
pathlib.Path(PATH).mkdir(parents=True, exist_ok=True)

gl_hxfactor_dict = smdf.get_factor_dict(smdf.DEFAULT_FACTOR_CACHE_FILE)

try:
    tmp = get_price(
        get_all_securities_("2018-01-01", pd.Timestamp.now().strftime("%Y-%m-%d")),
        "2018-01-01",
        pd.Timestamp.now().strftime("%Y-%m-%d"),
        "1d",
        ["open", "close", "high_limit", "low", "high", "turnover", "turnover_rate", "avg_price"],
        fq="post",
        skip_paused=True,
        is_panel=True,
    )
    gl_high_df = tmp["high"]
    gl_high_df.index = (pd.to_datetime(gl_high_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_low_df = tmp["low"]
    gl_low_df.index = (pd.to_datetime(gl_low_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_close_df = tmp["close"]
    gl_close_df.index = (pd.to_datetime(gl_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_open_df = tmp["open"]
    gl_open_df.index = (pd.to_datetime(gl_open_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_hl = tmp["low"] < tmp["high_limit"]
    gl_hl.index = (pd.to_datetime(gl_hl.index) + pd.Timedelta(hours=7)).astype(int)
    gl_turnover = tmp["turnover"]
    gl_turnover.index = (pd.to_datetime(gl_turnover.index) + pd.Timedelta(hours=7)).astype(int)
    gl_turnover_rate = tmp["turnover_rate"]
    gl_turnover_rate.index = (pd.to_datetime(gl_turnover_rate.index) + pd.Timedelta(hours=7)).astype(int)
    gl_avg_price_df = tmp["avg_price"]
    gl_avg_price_df.index = (pd.to_datetime(gl_avg_price_df.index) + pd.Timedelta(hours=7)).astype(int)

    gl_930_open_df = pickle.load(open("/home/<USER>/work/项目四：多因子选股/930open", "rb"))
    gl_930_open_df.index = (pd.to_datetime(gl_930_open_df.index) + pd.Timedelta(hours=7)).astype(int)

except Exception as e:
    logger.exception(e)


def process_df(df: pd.DataFrame, mode: int) -> Tuple[str, pd.DataFrame]:
    assert 0 <= mode < 10
    df = df.copy()
    if mode == 0:
        return "(raw)", df
    if mode == 1:
        return "(log_0)", np.log(1e-7 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore
    if mode == 2:
        return "(log_1)", np.log(1 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore
    if mode == 3:
        df = df * (-1)
        return "(-log_0)", np.log(1e-7 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore
    if mode == 4:
        df = df * (-1)
        return "(-log_1)", np.log(1 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore

    df = np.clip(
        df,
        df.quantile(0.05, axis=1).to_frame().to_numpy(),
        df.quantile(0.95, axis=1).to_frame().to_numpy(),
    )  # type: ignore
    tmp = process_df(df, mode - 5)
    return f"(clip_{tmp[0][1:-1]})", tmp[1]


def fnc(start_dt, end_dt, interval, pool):
    if pool == "ALL":
        res_path = pathlib.Path(
            PATH, f"{calc_trade_day(end_dt, interval).strftime('%Y%m%d')}-{end_dt.replace('-', '')[:6]}_{interval}.csv"
        )
    else:
        res_path = pathlib.Path(
            PATH,
            f"{calc_trade_day(end_dt, interval).strftime('%Y%m%d')}-{end_dt.replace('-', '')[:6]}_{interval}_{pool}.csv",
        )
    if res_path.exists():
        return
        existed_df = pd.read_csv(res_path, index_col=0)
    else:
        existed_df = pd.DataFrame()

    logger.info(f"{start_dt} - {end_dt} {interval} {pool} 因子检测开始")

    start_dt_ = (pd.Timestamp(start_dt) - pd.Timedelta(days=20)).strftime("%Y-%m-%d")
    end_dt_ = (pd.Timestamp(end_dt) + pd.Timedelta(days=20)).strftime("%Y-%m-%d")
    # close_df = slice_dataset(gl_close_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
    # open_df = slice_dataset(gl_open_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
    close_df = slice_dataset(gl_avg_price_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
    open_df = slice_dataset(gl_930_open_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
    if pool == "ALL":
        stock_mask = np.isfinite(close_df)
    elif pool == "turnover":
        stock_mask = gl_turnover.gt(gl_turnover.quantile(RATIO, axis=1), axis=0) & np.isfinite(close_df)
    elif pool == "turnover_rate":
        stock_mask = gl_turnover_rate.gt(gl_turnover_rate.quantile(RATIO, axis=1), axis=0) & np.isfinite(close_df)
    else:
        stock_mask = get_index_stocks_periodically(f"{pool}.SH", close_df)

    return_df = np.log(close_df.shift(-interval)) - np.log(open_df.shift(-1))
    # return_df = close_df.shift(-interval) / open_df.shift(-1) - 1
    return_df = StockPoolMask(n=interval)(
        return_df,
        drop_paused=2,
        drop_close_hit_limit_up=True,
        drop_close_hit_limit_dn=True,
        drop_next_open_hit_limit_up=True,
        drop_next_open_hit_limit_dn=True,
    )

    res = []
    for f_name, factor_df in tqdm(
        factor_df_gnt(
            start_dt,
            end_dt,
            shift=[i for i in range(0, 11)],
            cached=False,
            drop_close_hit_limit_up=True,
            drop_close_hit_limit_dn=True,
            drop_next_open_hit_limit_up=True,
            drop_next_open_hit_limit_dn=True,
        ),
        desc=f"{start_dt} - {end_dt} {interval} {pool} 因子检测",
    ):
        if f_name in existed_df.index:
            continue
        if factor_df.empty:
            logger.trace(f"{f_name} {start_dt} - {end_dt} {interval} {pool} 因子数据为空")
            continue
        try:
            tmp_res = {"f_name": f_name}
            factor_df.replace([np.inf, -np.inf], np.nan, inplace=True)
            factor_df = factor_df[stock_mask]

            for mode in range(10):
                processed_label, processed_df = process_df(factor_df, mode)
                fct_analyse = FactorAnalyse(processed_df, return_df)

                tmp = fct_analyse.ic(1, 0, False, 1, 0)
                tmp = tmp.stats.T.to_dict()[0]
                tmp_res[f"1_0_ir{processed_label}"] = tmp["ir"]
                tmp_res[f"1_0_ic_mean{processed_label}"] = tmp["ic_mean"]
                tmp_res[f"1_0_valid_count{processed_label}"] = tmp["valid_count"]

                tmp1 = fct_analyse.ic(1, 0, False, 10, 0)
                if tmp["ic_mean"] > 0:
                    tmp1 = tmp1.stats.T.to_dict()[9]
                    tmp_res[f"top_1_0_ic_mean{processed_label}"] = tmp1["ic_mean"]
                    tmp_res[f"top_1_0_ir{processed_label}"] = tmp1["ir"]
                    tmp_res[f"top_1_0_valid_count{processed_label}"] = tmp1["valid_count"]
                else:
                    tmp1 = tmp1.stats.T.to_dict()[0]
                    tmp_res[f"top_1_0_ic_mean{processed_label}"] = tmp1["ic_mean"]
                    tmp_res[f"top_1_0_ir{processed_label}"] = tmp1["ir"]
                    tmp_res[f"top_1_0_valid_count{processed_label}"] = tmp1["valid_count"]

                tmp = fct_analyse.rank_ic(1, 0, False, 1, 0)
                tmp = tmp.stats.T.to_dict()[0]
                tmp_res[f"1_0_rank_ir{processed_label}"] = tmp["ir"]
                tmp_res[f"1_0_rank_ic_mean{processed_label}"] = tmp["ic_mean"]
                tmp_res[f"1_0_rank_valid_count{processed_label}"] = tmp["valid_count"]

                tmp1 = fct_analyse.rank_ic(1, 0, False, 10, 0)
                if tmp["ic_mean"] > 0:
                    tmp1 = tmp1.stats.T.to_dict()[9]
                    tmp_res[f"top_1_0_rank_ic_mean{processed_label}"] = tmp1["ic_mean"]
                    tmp_res[f"top_1_0_rank_ir{processed_label}"] = tmp1["ir"]
                    tmp_res[f"top_1_0_rank_valid_count{processed_label}"] = tmp1["valid_count"]
                else:
                    tmp1 = tmp1.stats.T.to_dict()[0]
                    tmp_res[f"top_1_0_rank_ic_mean{processed_label}"] = tmp1["ic_mean"]
                    tmp_res[f"top_1_0_rank_ir{processed_label}"] = tmp1["ir"]
                    tmp_res[f"top_1_0_rank_valid_count{processed_label}"] = tmp1["valid_count"]

            res.append(tmp_res)
        except Exception:
            logger.error(f"{f_name} 因子检测失败")

    res1 = pd.DataFrame(res).set_index("f_name")
    res1 = pd.concat([existed_df, res1], axis=0)
    if res1.index.duplicated().any():
        raise pd.errors.DataError
    res1.to_csv(res_path)

    logger.info(f"{start_dt} - {end_dt} {interval} 因子检测完成")


def main(interval, n_cores=1):
    try:
        tasks = []
        # for pool in ["000852", "000905", "ALL"]:
        # for pool in ["000905", "000300", "ALL"]:
        for pool in ["turnover"]:
            end_date = pd.Timestamp("now") - pd.offsets.MonthEnd(1)
            while True:
                start_date = end_date - pd.offsets.MonthBegin(1)
                tasks.append((start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d"), interval, pool))
                end_date = end_date - pd.offsets.MonthEnd(1)

                if end_date < pd.Timestamp("2018-01-01"):
                    break

        print(f"总任务数:{len(tasks)}")
        if n_cores > 1:
            from multiprocessing import Pool

            with Pool(n_cores) as pool:
                pool.starmap(fnc, tasks)
        else:
            for task in tasks:
                fnc(*task)

    except Exception as e:
        logger.exception(e)


if __name__ == "__main__":
    # tmp = fnc("2020-01-01", "2023-12-31", 5)
    main(2, 30)
    # main(3, 29)

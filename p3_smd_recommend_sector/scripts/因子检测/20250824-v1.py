import sys

sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/dev")

import csv
import multiprocessing as mp
import pathlib
import pickle
from functools import partial
from queue import Empty
from typing import Iterable, <PERSON>ple

import numpy as np
import pandas as pd
import smd_module.factor as smdf
from aichemy.factor_analyse.analyse import FactorAnalyse
from aichemy.utils import is_notebook, slice_dataset
from loguru import logger
from mindgo_api import get_price  # type: ignore
from smd_module.utils import StockPoolMask, calc_trade_day, get_all_securities_, get_index_stocks_periodically

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm

logger.add(f"因子检测_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.log", level="TRACE")
RATIO = 0.9
PATH = f"/home/<USER>/work/因子工厂/turnover_{RATIO * 100:.0f}_orth1_open_to_hl2_2025-08-26.csv"
# PATH = f"/home/<USER>/work/因子工厂/turnover_{RATIO * 100:.0f}_mv39_2025-08-03"


def get_all_dt_intervat():
    result = []
    end_date = pd.Timestamp("now") - pd.offsets.MonthEnd(1)
    while True:
        start_date = end_date - pd.offsets.MonthBegin(1)
        result.append((start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")))
        end_date = end_date - pd.offsets.MonthEnd(1)
        if end_date < pd.Timestamp("2018-01-01"):
            break
    return result


def process_df(df: pd.DataFrame, mode: Iterable):
    def fnc(df, mode):
        if mode == 0:
            return "(raw)", df
        if mode == 1:
            return "(log_0)", np.log(1e-7 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore
        if mode == 2:
            return "(log_1)", np.log(1 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore
        if mode == 3:
            df = df * (-1)
            return "(-log_0)", np.log(1e-7 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore
        if mode == 4:
            df = df * (-1)
            return "(-log_1)", np.log(1 + df.add(df.min(axis=1).abs(), axis=0))  # type: ignore
        raise

    df_clip = np.clip(
        df,
        df.quantile(0.05, axis=1).to_frame().to_numpy(),
        df.quantile(0.95, axis=1).to_frame().to_numpy(),
    )  # type: ignore
    for i in mode:
        if i < 5:
            yield fnc(df, i)
        else:
            tmp = fnc(df_clip, i - 5)
            yield f"(clip_{tmp[0][1:-1]})", tmp[1]


class TaskContext:
    def __init__(self, pools, intervals) -> None:
        gl_close_df = smdf.get_factor("行情", "close")
        gl_open_df = smdf.get_factor("行情", "open")
        gl_high_df = smdf.get_factor("行情", "high")
        gl_low_df = smdf.get_factor("行情", "low")
        gl_turnover_df = smdf.get_factor("行情", "turnover")
        gl_turnover_rate_df = smdf.get_factor("行情", "turnover_rate")
        # mv = smdf.get_factor("市值因子", "市值因子")

        self.pools = pools
        self.intervals = intervals
        self.stock_mask = {}
        self.all_dt_interval = get_all_dt_intervat()
        self.return_df_mapping = {}

        for pool in pools:
            if pool == "ALL":
                self.stock_mask[pool] = np.isfinite(gl_close_df)
            elif pool == "turnover":
                self.stock_mask[pool] = gl_turnover_df.gt(gl_turnover_df.quantile(RATIO, axis=1), axis=0) & np.isfinite(
                    gl_close_df
                )
            elif pool == "turnover_rate":
                self.stock_mask[pool] = gl_turnover_rate_df.gt(
                    gl_turnover_rate_df.quantile(RATIO, axis=1), axis=0
                ) & np.isfinite(gl_close_df)
            else:
                self.stock_mask[pool] = get_index_stocks_periodically(f"{pool}.SH", gl_close_df)

        for interval in intervals:
            sp = StockPoolMask(n=interval)
            for start_dt, end_dt in self.all_dt_interval:
                start_dt_ = (pd.Timestamp(start_dt) - pd.Timedelta(days=20)).strftime("%Y-%m-%d")
                end_dt_ = (pd.Timestamp(end_dt) + pd.Timedelta(days=20)).strftime("%Y-%m-%d")
                close_df = slice_dataset(
                    gl_high_df / 2 + gl_low_df / 2, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both"
                )
                open_df = slice_dataset(gl_open_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
                # close_df = slice_dataset(gl_avg_price_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")
                # open_df = slice_dataset(gl_930_open_df, f"{start_dt_} 00:00:00", f"{end_dt_} 23:59:59", "both")

                return_df = np.log(close_df.shift(-interval)) - np.log(open_df.shift(-1))
                # return_df = close_df.shift(-interval) / open_df.shift(-1) - 1
                self.return_df_mapping[(start_dt, end_dt, interval)] = sp(
                    return_df,
                    drop_paused=2,
                    drop_close_hit_limit_up=True,
                    drop_close_hit_limit_dn=True,
                    drop_next_open_hit_limit_up=True,
                    drop_next_open_hit_limit_dn=True,
                )

    def run(self, input_queue, output_queue):
        try:
            masks = [
                partial(
                    StockPoolMask(n=interval),
                    drop_paused=2,
                    drop_close_hit_limit_up=True,
                    drop_close_hit_limit_dn=True,
                    drop_next_open_hit_limit_up=True,
                    drop_next_open_hit_limit_dn=True,
                )
                for interval in self.intervals
            ]
            masks = [
                lambda x: (i(x)[self.stock_mask[pool]], dict(**i.func.info, pool=pool))
                for i in masks
                for pool in self.pools
            ]
            while True:
                factor = input_queue.get_nowait()

                for (start_dt, end_dt), key, factor_df_, info in smdf.factor_df_gnt(
                    self.all_dt_interval,
                    factor_list=[factor],
                    masks=masks,
                ):
                    if factor_df_.empty:
                        logger.trace(f"{key} {start_dt} - {end_dt} 因子数据为空")
                        continue
                    factor_df = factor_df_.replace([np.inf, -np.inf], np.nan, inplace=False)
                    return_df = self.return_df_mapping[(start_dt, end_dt, info["interval"])]

                    tmp_res = {
                        "key": key,
                        "start_dt": start_dt,
                        "end_dt": end_dt,
                        "pool": info["pool"],
                        "interval": info["interval"],
                        "ctime": calc_trade_day(end_dt, info["interval"]).strftime("%Y-%m-%d"),
                    }

                    for processed_label, processed_df in process_df(factor_df, range(10)):
                        fct_analyse = FactorAnalyse(processed_df, return_df)

                        tmp = fct_analyse.ic(1, 0, False, 1, 0)
                        tmp = tmp.stats.T.to_dict()[0]
                        tmp_res[f"1_0_ir{processed_label}"] = tmp["ir"]
                        tmp_res[f"1_0_ic_mean{processed_label}"] = tmp["ic_mean"]
                        tmp_res[f"1_0_valid_count{processed_label}"] = tmp["valid_count"]

                        tmp1 = fct_analyse.ic(1, 0, False, 10, 0)
                        if tmp["ic_mean"] > 0:
                            tmp1 = tmp1.group([9]).stats.T.to_dict()[0]
                            # tmp1 = tmp1.stats.T.to_dict()[9]
                            tmp_res[f"top_1_0_ic_mean{processed_label}"] = tmp1["ic_mean"]
                            tmp_res[f"top_1_0_ir{processed_label}"] = tmp1["ir"]
                            tmp_res[f"top_1_0_valid_count{processed_label}"] = tmp1["valid_count"]
                        else:
                            tmp1 = tmp1.group([0]).stats.T.to_dict()[0]
                            # tmp1 = tmp1.stats.T.to_dict()[0]
                            tmp_res[f"top_1_0_ic_mean{processed_label}"] = tmp1["ic_mean"]
                            tmp_res[f"top_1_0_ir{processed_label}"] = tmp1["ir"]
                            tmp_res[f"top_1_0_valid_count{processed_label}"] = tmp1["valid_count"]

                        tmp = fct_analyse.rank_ic(1, 0, False, 1, 0)
                        tmp = tmp.stats.T.to_dict()[0]
                        tmp_res[f"1_0_rank_ir{processed_label}"] = tmp["ir"]
                        tmp_res[f"1_0_rank_ic_mean{processed_label}"] = tmp["ic_mean"]
                        tmp_res[f"1_0_rank_valid_count{processed_label}"] = tmp["valid_count"]

                        tmp1 = fct_analyse.rank_ic(1, 0, False, 10, 0)
                        if tmp["ic_mean"] > 0:
                            tmp1 = tmp1.group([9]).stats.T.to_dict()[0]
                            # tmp1 = tmp1.stats.T.to_dict()[9]
                            tmp_res[f"top_1_0_rank_ic_mean{processed_label}"] = tmp1["ic_mean"]
                            tmp_res[f"top_1_0_rank_ir{processed_label}"] = tmp1["ir"]
                            tmp_res[f"top_1_0_rank_valid_count{processed_label}"] = tmp1["valid_count"]
                        else:
                            tmp1 = tmp1.group([0]).stats.T.to_dict()[0]
                            # tmp1 = tmp1.stats.T.to_dict()[0]
                            tmp_res[f"top_1_0_rank_ic_mean{processed_label}"] = tmp1["ic_mean"]
                            tmp_res[f"top_1_0_rank_ir{processed_label}"] = tmp1["ir"]
                            tmp_res[f"top_1_0_rank_valid_count{processed_label}"] = tmp1["valid_count"]

                    output_queue.put(tmp_res)

        except Empty:
            logger.info("因子队列为空，工作进程退出")
        except Exception as e:
            logger.exception(e)
        finally:
            output_queue.put({})  # 发送空字典，表示处理完成


def write_dict_to_csv_dynamic(queue, filename: str, max_empty_count: int = 5, flush_on_write: bool = True):
    """
    从 multiprocessing.Queue 中接收 dict，动态确定表头并写入 CSV。
    空字典 {} 不写入，但计数，达到 max_empty_count 时退出。

    Args:
        queue: multiprocessing.Queue
        filename: 输出 CSV 文件名
        max_empty_count: 最大允许的连续空字典数量
        flush_on_write: 是否每次写入后刷新（实时落盘）
    """
    logger.info(f"开始写入 CSV 文件: {filename}")
    empty_count = 0
    fieldnames = None
    csvfile = None
    writer = None

    try:
        while True:
            data = queue.get()  # 阻塞等待

            if data == {}:
                empty_count += 1
                logger.info(f"收到空字典，累计 {empty_count}/{max_empty_count}")
                if empty_count >= max_empty_count:
                    logger.info("空字典数量达到上限，退出写入。")
                    break
            else:
                if not isinstance(data, dict):
                    logger.warning(f"跳过非字典数据: {data}")
                    continue

                # 第一次收到非空 dict，初始化 CSV 和表头
                if fieldnames is None:
                    fieldnames = list(data.keys())

                    # 创建文件并写入表头
                    csvfile = open(filename, "w", newline="", encoding="utf-8")
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                # 确保只写入已知字段（避免后续数据包含新 key）
                # 也可以选择扩展表头（见下方“可选改进”）
                filtered_data = {k: v for k, v in data.items() if k in fieldnames}

                # 对于缺失的字段，补 None（csv.DictWriter 会处理为 empty）
                for field in fieldnames:
                    if field not in filtered_data:
                        filtered_data[field] = None

                writer.writerow(filtered_data)

                if flush_on_write:
                    csvfile.flush()

    except Exception as e:
        logger.warning(f"写入过程中发生错误: {e}")
        raise
    finally:
        # 确保文件正确关闭
        if csvfile:
            csvfile.close()
        logger.info(f"CSV 写入完成，文件保存为: {filename}")


if __name__ == "__main__":
    pools = ["turnover"]
    intervals = [2]
    n_cores = 45
    tester = TaskContext(pools=pools, intervals=intervals)

    factor_dict = smdf.get_factor_dict(database="orth1")
    factor_queue = mp.Manager().Queue()
    output_queue = mp.Manager().Queue()

    for l1n in factor_dict.keys():
        for l2n in factor_dict[l1n]:
            factor_queue.put(("orth1", l1n, l2n, ",".join(str(i) for i in range(10))))

    n_cores = min(factor_queue.qsize(), n_cores)
    if n_cores == 1:
        tester.run(factor_queue, output_queue)
    else:
        for _ in range(n_cores):
            mp.Process(target=tester.run, args=(factor_queue, output_queue), daemon=True).start()
    del tester
    write_dict_to_csv_dynamic(output_queue, PATH, max_empty_count=n_cores, flush_on_write=True)

# -*- coding: utf-8 -*-
# @Description: 选股

from datetime import datetime

import numpy as np
import pandas as pd

from mindgo_api import (
    custom_sector,
    get_all_securities,
    get_price,
    notify_push,
    get_all_trade_days,
    get_industry_relate,
)  # type: ignore
from DataAPI.openapi.open_api import OpenAPI  # type: ignore
import argparse

parser = argparse.ArgumentParser()
parser.add_argument("--force", action="store_true", default=False, help="是否强制执行")
args = parser.parse_args()


def fnc1(symbol, end_date):
    """涨停回调"""
    data = get_price(
        symbol,
        None,
        end_date,
        "1d",
        ["open", "high", "low", "close", "volume", "high_limit", "is_paused", "is_st", "prev_close"],
        fq="pre",
        skip_paused=False,
        bar_count=50,
    )
    if data.empty:
        return False
    c1 = (data["close"] == data["high_limit"]).iloc[-2]  # 上一交易日涨停
    c2 = 0.98 <= (data["close"] / data["prev_close"]).iloc[-1] <= 1.05  # 当日涨幅在区间[-0.03, 0.05]之间
    c3 = data["is_st"].iloc[-1] == 0  # 非ST
    c4 = (data["close"] / data["open"]).iloc[-1] > 0.975  # 当日收盘>当日开盘*0.975
    c5 = data["high"].iloc[-5:].max() == data["high"].iloc[-30:].max()  # 近5天最高价为近30天最高价
    c6 = all(data["is_paused"].iloc[-5:] == 0)
    c7 = data["open"].iloc[-1] / data["prev_close"].iloc[-1] > 0.97  # 低开不超过3%
    return c1 and c2 and c3 and c4 and c5 and c6 and c7


def fnc2(symbol, end_date):
    """当日涨停"""
    data = get_price(
        symbol,
        None,
        end_date,
        "1d",
        ["open", "high", "low", "close", "volume", "high_limit", "is_paused", "is_st", "prev_close"],
        fq="pre",
        skip_paused=False,
        bar_count=50,
    )
    if data.empty:
        return False
    c1 = (data["close"] == data["high_limit"]).iloc[-1]  # 当日涨停
    c3 = data["is_st"].iloc[-1] == 0  # 非ST
    c5 = data["high"].iloc[-5:].max() == data["high"].iloc[-90:].max()  # 近5天最高价为近30天最高价
    c6 = all(data["is_paused"].iloc[-5:] == 0)
    c7 = data["high"].iloc[-5:].max() / data["low"].iloc[-5:].min() < 1.3
    return c1 and c3 and c5 and c6 and c7


def fnc3(symbol, end_date):
    """底部上升"""
    data = get_price(
        symbol,
        None,
        end_date,
        "1d",
        ["open", "high", "low", "close", "volume", "high_limit", "is_paused", "is_st", "prev_close"],
        fq="pre",
        skip_paused=False,
        bar_count=150,
    )
    if data.empty and data["close"].isna().iloc[0]:
        return False
    t = np.argmin(data["low"].iloc[-120:].values[::-1]) + 1  # 第1个低点
    d1 = np.log(data["low"].iloc[-t + 1 :]) - np.log(data["low"].iloc[-t])
    d1.iloc[-30:] = np.inf
    d1 = d1 * 100 / pd.Series(np.arange(len(d1)) + 1, index=d1.index)
    t1 = len(d1) - np.argmin(d1)  # 第2个低点

    c3 = data["is_st"].iloc[-1] == 0  # 非ST
    c4 = 1.2 < data["high"].iloc[-t:].max() / data["low"].iloc[-t:].min() < 1.65
    c5 = (data["close"].iloc[-1] - data["low"].iloc[-t:].min()) / (
        data["high"].iloc[-t:].max() - data["low"].iloc[-t:].min()
    ) > 0.85
    c6 = (data["high"].iloc[-t : -t1 + 1].max() - data["low"].iloc[-t:].min()) / (
        data["high"].iloc[-t:].max() - data["low"].iloc[-t:].min()
    ) > 0.85

    return c3 and c4 and c5 and c6


def fnc4(symbol, end_date):
    """底部上升"""
    data = get_price(
        symbol,
        None,
        end_date,
        "1d",
        ["open", "high", "low", "close", "volume", "high_limit", "is_paused", "is_st", "turnover", "prev_close"],
        fq="pre",
        skip_paused=False,
        bar_count=120,
    )
    if data.empty and data["close"].isna().iloc[0]:
        return False
    c1 = data["is_st"].iloc[-1] == 0  # 非ST
    c2 = data["turnover"].iloc[-1] > 1e9  # 成交金额>10亿
    c3 = data["close"].iloc[-1] < data["low"].min() * 3  # 120个交易日内涨幅小于3倍
    c4 = (
        data["close"].iloc[-1] > data["low"].min() / 2 + data["high"].max() / 2
    )  # 收盘价>近120个交易日内最低价的1/2+最高价的1/2
    c5 = data["close"].iloc[-1] > data["close"].iloc[-20:].mean()  # 收盘价>近20个交易日收盘价的均值
    c6 = data["close"].iloc[-1] > data["close"].iloc[-2] * 0.93
    return c1 and c2 and c3 and c4 and c5 and c6


def select_stocks(end_date, fncs):
    symbol_list = list(get_all_securities("stock", date=end_date).index)
    for sec_name, fnc_name in fncs.items():
        ret = set()
        for symbol in symbol_list:
            try:
                a = fnc_name(symbol, end_date)
            except:
                # print(i)
                # import traceback
                # traceback.print_exc()
                a = False
            if a:
                ret.add(symbol)
        custom_sector(sec_name, "append", list(ret))
        with open("/home/<USER>/work/crontabs/选股结果.txt", "a") as f:
            f.write(f"{datetime.now()} {sec_name}选股结果为：{list(ret)}\n")


def select_concept_sectors():
    """选出近期强势概念板块"""
    sql_api = OpenAPI()
    all_concepts = sql_api.get_all_concepts()
    all_concepts_thscode = all_concepts["行情代码"].apply(lambda x: x + ".TI").unique().tolist()
    all_concepts_thscode = [i for i in all_concepts_thscode if not i.startswith("883")]
    tmp = []
    for i in all_concepts_thscode:
        try:
            a = get_price(i, None, "now", "1d", ["close"], bar_count=250)["close"].rename(i)
            tmp.append(a)
        except:
            continue
    close_df = pd.concat(tmp, axis=1)
    s = custom_sector("强势概念板块").symbol
    custom_sector("强势概念板块", "pop", s)
    for i in [3, 5, 10, 20]:
        a = (close_df / close_df.rolling(i).min()).iloc[-1].sort_values(ascending=False)
        a = a.loc[a > 1]
        custom_sector("强势概念板块", "append", a.index[:5].tolist())


def select_industry_sectors():
    """选出近期强势行业板块"""
    for i1, i2 in [("s_industryid2", "申万板块"), ("industryid2", "同花顺板块")]:
        ths_industry = get_industry_relate(types=i1)["industry_thscode"].tolist()
        close_df = get_price(ths_industry, None, "now", "1d", ["close"], bar_count=250, is_panel=1)["close"]
        s = custom_sector(i2).symbol
        custom_sector(i2, "pop", s)
        for i in [3, 5, 10, 20, 60]:
            a = (close_df / close_df.rolling(i).min()).iloc[-1].sort_values(ascending=False)
            a = a.loc[a > 1]
            custom_sector(i2, "append", a.index[:5].tolist())


def main():
    s = custom_sector("当日涨停").symbol
    custom_sector("涨停回调", "append", s)
    custom_sector("当日涨停", "pop", s)
    custom_sector("近期活跃股", "pop", custom_sector("近期活跃股").symbol)
    custom_sector("涨停回调", "pop", custom_sector("涨停回调").symbol)
    custom_sector("底部上升", "pop", custom_sector("底部上升").symbol)

    select_stocks("now", {"当日涨停": fnc2, "涨停回调": fnc1, "底部上升": fnc3, "近期活跃股": fnc4})
    select_concept_sectors()
    select_industry_sectors()

    s1 = custom_sector("当日涨停").symbol
    s2 = custom_sector("涨停回调").symbol
    s3 = custom_sector("底部上升").symbol

    custom_sector("底部上升", "pop", [i for i in s3 if (i in s1 or i in s2)])
    custom_sector("涨停回调", "pop", [i for i in s2 if (i in s1)])

    notify_push(
        "量化平台选股已经完成", channel="wxpusher", subject="MindGo消息提醒", uids="UID_A97RxScBvVA9K2lJcUSpgTLzEmwi"
    )


if __name__ == "__main__":
    if pd.Timestamp.now().strftime("%Y-%m-%d") in get_all_trade_days().strftime("%Y-%m-%d") or args.force:
        main()

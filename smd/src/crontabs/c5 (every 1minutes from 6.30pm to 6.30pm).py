import sys

sys.path.insert(0, "/home/<USER>/work/复盘")
sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/dev")

import related_sectors
from mindgo_api import *
import argparse

parser = argparse.ArgumentParser()
parser.add_argument("--bar_count", type=int, default=5)
parser.add_argument("--end_dt", type=str, default="now")

args = parser.parse_args()

if __name__ == "__main__":
    date = get_trade_days(None, args.end_dt, 1)[-1].strftime("%Y-%m-%d")
    symbol_list = get_all_securities("stock", date=date).index.tolist()
    related_sectors.calc(symbol_list, args.bar_count, date, force=True)

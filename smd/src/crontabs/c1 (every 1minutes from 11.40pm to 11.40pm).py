# -*- coding: utf-8 -*-
# @Description: 基于期货排名数据进行因子计算

import pathlib
import re
from typing import List

import pandas as pd
from mindgo_api import *
from tqdm import tqdm


class DatabaseManager:
    def __init__(self, name: str, path: str = "", re_calc_length: int = 0, keep_length: int = 0):
        """因子数据库管理器

        Args:
            name (str): 因子名称
            path (str, optional): 数据库路径. Defaults to "".
            re_calc_length (int, optional): 重算长度，为了防止出现数据库修复的情况，近re_calc_length天的数据需要重算. Defaults to 0.
            keep_length (int, optional): 保留长度，为了控制下载文件的大小，所以只保留一段数据. Defaults to 0.
        """
        self.path = pathlib.Path(path, "factor.h5")
        self.name = name
        self.re_calc_length = re_calc_length
        self.keep_length = keep_length

        if self.path.exists():
            with pd.HDFStore(self.path, "r") as store:
                if self.name in store:
                    self.data = store[self.name].copy()
                else:
                    self.data = pd.DataFrame()
        else:
            self.data = pd.DataFrame()

        self.index: List[str] = []
        self.cache: List[pd.DataFrame] = []

        self._init()

    def contain(self, key):
        return key in self.index or pd.to_datetime(key) < pd.to_datetime(self.index[0] if self.index else "2020-01-01")

    def put(self, data):
        self.cache.append(data)

    def update(self):
        # 先把cache concat，再把data里和cache的数据重复的数据去掉
        cache = pd.concat([*self.cache], axis=0)
        self.data = pd.concat([self.data.loc[~self.data.index.isin(cache.index)], cache], axis=0)

        # index的排序和筛选
        self.data.index = pd.to_datetime(self.data.index)
        self.data.sort_index(inplace=True)
        if self.keep_length > 0:
            self.data = self.data.iloc[-self.keep_length :]
        self.data.index = self.data.index.strftime("%Y-%m-%d")

        # 落地
        with pd.HDFStore(self.path, "a") as store:
            store[self.name] = self.data
        self._init()

    def _init(self):
        # 每天要重算近self.re_calc_length天的数据, 如果为0则不重算
        if self.re_calc_length == 0:
            self.index = list(pd.to_datetime(self.data.index).sort_values().strftime("%Y-%m-%d"))
        else:
            self.index = list(
                pd.to_datetime(self.data.index).sort_values()[: -self.re_calc_length].strftime("%Y-%m-%d")
            )
        self.cache = []


def execute():
    factors = ["/ft001/factor001", "/ft001/factor002"]
    factors = {
        i: DatabaseManager(i, path="/home/<USER>/work/database/supermind_data/", re_calc_length=120, keep_length=252)
        for i in factors
    }

    for trade_day in tqdm(get_trade_days("20200101", pd.Timestamp.now())):
        tmp: str = trade_day.strftime("%Y-%m-%d")
        for k in factors:
            if not factors[k].contain(tmp):
                break
        else:
            continue

        q = query(futures_ranking).filter(futures_ranking.date == tmp)  # 数据已全部更新
        data = run_query(q)
        data["futures_ranking_date"] = data["futures_ranking_date"].apply(lambda x: x.strftime("%Y-%m-%d"))
        if data.empty:
            continue
        data["futures_code"] = data["futures_ranking_symbol"].apply(lambda x: re.findall("[a-zA-Z]*", x)[0])

        # factor001: 多空持仓变动净量/总量
        if "/ft001/factor001" in factors.keys() and not factors["/ft001/factor001"].contain(tmp):
            res = data.copy()
            res = res.loc[
                (res["futures_ranking_name"] == "多头持仓龙虎榜") | (res["futures_ranking_name"] == "空头持仓龙虎榜")
            ]
            res.loc[res["futures_ranking_name"] == "空头持仓龙虎榜", "futures_ranking_amount_delta"] *= -1
            res = (
                res.groupby(["futures_code", "futures_ranking_date"])
                .apply(
                    lambda x: x["futures_ranking_amount_delta"].sum() / x["futures_ranking_amount_delta"].abs().sum()
                )
                .unstack()
                .T
            )
            res.index.name = None
            res.columns.name = None
            factors["/ft001/factor001"].put(res)

        # factor002: 多空持仓对比
        if "/ft001/factor002" in factors.keys() and not factors["/ft001/factor002"].contain(tmp):
            res = data.copy()
            res = res.groupby(["futures_ranking_date", "futures_code", "futures_ranking_name"])[
                "futures_ranking_amount"
            ].sum()
            res = res.unstack()
            res = res["多头持仓龙虎榜"] / res["空头持仓龙虎榜"]
            res = res.unstack()
            res.index.name = None
            res.columns.name = None
            factors["/ft001/factor002"].put(res)

    for k in factors:
        factors[k].update()


if __name__ == "__main__":
    execute()

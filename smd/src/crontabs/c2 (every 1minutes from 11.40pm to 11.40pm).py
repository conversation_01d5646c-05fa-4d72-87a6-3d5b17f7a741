from DataAPI.openapi.open_api import OpenAPI  # type: ignore


def exec_sql():
    sql_api = OpenAPI()
    sql_api.get_all_contracts().to_csv("/home/<USER>/work/database/supermind_data/all_contracts.csv")
    sql_api.get_all_futures().to_csv("/home/<USER>/work/database/supermind_data/all_futures.csv")
    sql_api.get_domestic_calendar().to_csv("/home/<USER>/work/database/supermind_data/calendar.csv")
    sql_api.get_workday().to_csv("/home/<USER>/work/database/supermind_data/workday.csv")


if __name__ == "__main__":
    exec_sql()

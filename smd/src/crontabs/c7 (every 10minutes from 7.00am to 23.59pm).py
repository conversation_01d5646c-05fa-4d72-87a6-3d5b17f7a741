# -*- coding: utf-8 -*-
# @Description: 概念扫描
import sys

sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/dev")

from smd_module.utils import gl_concept_data
import pickle
import pathlib
from loguru import logger
from mindgo_api import *

logger.add("/home/<USER>/work/crontabs/概念扫描.log")

if __name__ == "__main__":
    save_path = "/home/<USER>/work/crontabs/concept_data.pkl"
    if not pathlib.Path(save_path).exists():
        with open(save_path, "wb") as f:
            pickle.dump(gl_concept_data, f)

    with open(save_path, "rb") as f:
        tmp = pickle.load(f)

    diff = gl_concept_data.loc[~gl_concept_data["行情代码"].isin(tmp["行情代码"])]
    if not diff.empty:
        for i, j in diff.groupby("板块名称"):
            text = f"新增{i}({j['行情代码'].iloc[0]})概念：{[ii for ii in j['股票代码'].tolist()]}"
            notify_push(text, channel="wxpusher", subject="MindGo消息提醒", uids="UID_A97RxScBvVA9K2lJcUSpgTLzEmwi")
            logger.info(text)

        with open(save_path, "wb") as f:
            pickle.dump(gl_concept_data, f)

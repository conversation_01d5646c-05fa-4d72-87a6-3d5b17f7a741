# -*- coding: utf-8 -*-
# @Description: 选股
import sys

sys.path = [i for i in sys.path if i != "/home/<USER>/work/dev"]
sys.path.insert(0, "/home/<USER>/work/pro1/")

import datetime
import fcntl
import pathlib
import pickle
import re
import time
from functools import reduce

import aichemy.project as yf_prj
import numpy as np
import optuna
import pandas as pd

# import project
import smd_module.factor as smdf
import tomli
import torch
import multiprocessing as mp
from aichemy.data_ops import run_construct_dataset
from aichemy.data_ops.base import check
from aichemy.ml.experiments_idx import *
from aichemy.ml.experiments_idx import ExpTSPrd
from aichemy.utils import from_nstimestamp, slice_dataset, to_nstimestamp
from autogluon.tabular import TabularDataset, TabularPredictor
from loguru import logger
from mindgo_api import (  # type: ignore
    custom_sector,
    get_all_trade_days,
    get_last_trade_day,
    get_price,
    get_security_info,
    notify_push,
)
from project import save_predict_result

# from project import *
from smd_module.utils import (
    StockPoolMask,
    calc_trade_day,
    get_index_stocks_periodically,
    get_latest_trade_day,
    get_price_df,
    get_thsindex,
)
from sqlalchemy import create_engine
from tqdm import tqdm

torch.set_num_threads(6)


class DDD:
    def __init__(self, path):
        self.path = path
        if pathlib.Path(path).exists():
            self.predictor = TabularPredictor.load(path)

    def predict(self, x):
        return self.predictor.predict(
            pd.concat([pd.DataFrame(x), pd.Series([0] * len(x)).rename("y")], axis=1), as_pandas=False
        )


def write_file(dt, pool, stock_list):
    text = (
        pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        + " "
        + dt
        + " "
        + (pool + ":" + ",".join([f"{i}-{get_security_info(i).display_name}" for i in stock_list]))
    ) + "\n"
    with open("/home/<USER>/work/crontabs_two/C端荐股.txt", "a") as f:
        fcntl.flock(f, fcntl.LOCK_EX)
        f.write(text)
        fcntl.flock(f, fcntl.LOCK_UN)
    with open("/home/<USER>/share/b2bsm/选股结果.txt", "a") as f:
        fcntl.flock(f, fcntl.LOCK_EX)
        f.write(text)
        fcntl.flock(f, fcntl.LOCK_UN)


try:
    if pd.Timestamp(datetime.date.today()) not in get_all_trade_days():
        sys.exit(0)

    end_dt = get_last_trade_day()
    start_dt = (end_dt - pd.Timedelta(days=128)).strftime("%Y-%m-%d")
    end_dt = end_dt.strftime("%Y-%m-%d")
    end_dt_timestamp = pd.Timestamp(f"{end_dt} 1500", tz="Asia/Shanghai").value

    while True:
        if re.compile(f"{get_latest_trade_day().strftime('%Y-%m-%d')}.*最新因子数据下载完成").search(
            open("/home/<USER>/work/crontabs/因子数据下载.log", "r").read()
        ):
            break
        time.sleep(1)

    logger.add(
        "/home/<USER>/work/crontabs_two/C端荐股/log",
        rotation="00:00",
        backtrace=True,
        diagnose=True,
        retention="5 days",
    )

    pathlib.Path("/home/<USER>/work/crontabs_two/C端荐股").mkdir(parents=True, exist_ok=True)

    select_stocks = {}
    # region 000300选股
    yf_prj.load("/home/<USER>/work/C端项目-板块推荐/成果/autogluon000300-0530/latest/", globals())
    data = run_construct_dataset(
        pj_construct_dataset,
        start_dt=start_dt,
        end_dt=end_dt,
        factor_mapping=factors,
        **kwargs,
    )
    check(end_dt_timestamp, data)
    logger.info("数据构建完成")
    model = model = DDD(pathlib.Path(path) / "autogluon_to_deploy")
    logger.info(f"已从{pathlib.Path(path) / 'autogluon_to_deploy'}加载模型")
    result, *_ = yf_prj.predict_apply_data(model, data, start_dt, end_dt, path=path, **kwargs, func_pred=lambda x: x[2])
    large_cap_select_stocks = np.isfinite(result.apply(lambda x: x.nlargest(5), axis=1)).loc[end_dt_timestamp]
    large_cap_select_stocks = large_cap_select_stocks.loc[large_cap_select_stocks].index.tolist()
    write_file(end_dt, "沪深300", large_cap_select_stocks)
    select_stocks["沪深300"] = large_cap_select_stocks
    save_predict_result(
        [result],
        file=f"/home/<USER>/work/crontabs_two/C端荐股/{end_dt}_000300_predict_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
    )
    logger.info("000300模型预测完成")
    # endregion

    logger.info("选股程序执行完成")

except Exception as e:
    logger.opt(exception=e).error(e)

import sys

sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/dev")

import pathlib
import pickle
from itertools import chain

import ipywidgets as widgets  # type: ignore
import mplfinance as mpf  # type: ignore
import numpy as np
import pandas as pd
from IPython.display import display  # type: ignore
from loguru import logger
from mindgo_api import custom_sector, get_price, get_security_info, get_trade_days  # type: ignore
from smd_module.utils import (
    calc_trade_day,
    get_all_concept_pct_rank,
    get_all_industries_pct_rank,
    get_concept_stocks_,
    get_industry_stocks_,
    get_price_,
    get_related_concept_pct_rank,
    get_related_industries_pct_rank,
    get_stock_name,
)
from tqdm import tqdm

logger.add("related_sectors.log", retention="7 days")

if (cache_path := pathlib.Path("/home/<USER>/work/database/cache/related_sectors.pkl")).exists():
    with cache_path.open("rb") as f:
        result_cache, quotation_cache = pickle.load(f)
    result_cache = {
        k: v for k, v in result_cache.items() if pd.Timestamp.now() - pd.Timestamp(k[0]) < pd.Timedelta(days=7)
    }
    quotation_cache = {
        k: v for k, v in quotation_cache.items() if pd.Timestamp.now() - pd.Timestamp(k[0]) < pd.Timedelta(days=7)
    }
    with cache_path.open("wb") as f:
        pickle.dump((result_cache, quotation_cache), f)
else:
    result_cache = {}
    quotation_cache = {}


def display_sectors_rank(bar_count=7, date="now"):
    res = {}
    date = get_trade_days(None, date, 1)[-1].strftime("%Y-%m-%d")

    # 常规概念
    tmp_df = get_all_concept_pct_rank(bar_count=bar_count, date=date)["A股_常规概念"]
    tmp_df["concept_rank_change"] = (
        get_all_concept_pct_rank(bar_count=bar_count, date=calc_trade_day(date, -1))["A股_常规概念"]["concept_rank"]
        - tmp_df["concept_rank"]
    )
    res_ = pd.DataFrame(index=tmp_df.index)
    res_["name"] = res_.index.map(lambda x: get_security_info(x).display_name)
    res_["start_date"] = res_.index.map(lambda x: get_security_info(x).start_date.strftime("%Y-%m-%d"))
    res_["concept_pct"] = tmp_df["concept_pct"].apply(lambda x: "{:.2%}".format(x))
    res_["concept_rank"] = tmp_df.apply(
        lambda x: "{:.0f} / {:.0f} ( {:.0f} {} )".format(
            x["concept_rank"],
            x["concept_count"],
            x["concept_rank_change"],
            "↑" if x["concept_rank_change"] >= 0 else "↓",
        ),
        axis=1,
    )
    res_["concept_turnover"] = tmp_df["concept_turnover"].apply(lambda x: "{:.2f}亿".format(x))
    res["A股_常规概念"] = res_.loc[tmp_df.sort_values("concept_pct", ascending=False).index[:20]]
    res["A股_常规概念"]["today_pct"] = res["A股_常规概念"].index.map(
        lambda x: "{:.2%}".format(
            get_price(x, None, date, "1d", ["quote_rate"], bar_count=1)["quote_rate"].iloc[-1] / 100
        )
    )
    res["A股_常规概念"]["num_of_hl"] = res["A股_常规概念"].index.map(
        lambda x: "当日触板：{:.0f}，涨停{:.0f}，累计3日涨停：{:.0f}，总数：{:.0f}".format(
            (
                (
                    d := get_price_(
                        get_concept_stocks_(x, date=date),
                        None,
                        date,
                        "1d",
                        ["close", "high", "high_limit"],
                        skip_paused=False,
                        bar_count=3,
                        is_panel=1,
                    )
                )["high"]
                == d["high_limit"]
            )
            .sum(axis=1)
            .iloc[-1],
            (d["close"] == d["high_limit"]).sum(axis=1).iloc[-1],
            (d["close"] == d["high_limit"]).sum(axis=1).sum(),
            d["close"].shape[1],
        )
    )
    display("## A股_常规概念")
    display(
        res["A股_常规概念"]
        .style.applymap(lambda x: "color: red" if x[-3] == "↑" else "color: green", subset=["concept_rank"])
        .applymap(lambda x: "color: red" if "-" not in x else "color: green", subset=["concept_pct"])
        .applymap(lambda x: "color: red" if "-" not in x else "color: green", subset=["today_pct"])
    )

    # 行业
    for industry in ["中信二级行业", "同花顺二级行业", "申万二级行业"]:
        tmp_df = get_all_industries_pct_rank(bar_count=bar_count, date=date)[industry]
        tmp_df["industry_rank_change"] = (
            get_all_industries_pct_rank(bar_count=bar_count, date=calc_trade_day(date, -1))[industry]["industry_rank"]
            - tmp_df["industry_rank"]
        )
        res_ = pd.DataFrame(index=tmp_df.index)
        res_["name"] = res_.index.map(lambda x: get_security_info(x).display_name)
        res_["industry_pct"] = tmp_df["industry_pct"].apply(lambda x: "{:.2%}".format(x))
        res_["industry_rank"] = tmp_df.apply(
            lambda x: "{:.0f} / {:.0f} ( {:.0f} {} )".format(
                x["industry_rank"],
                x["industry_count"],
                x["industry_rank_change"],
                "↑" if x["industry_rank_change"] >= 0 else "↓",
            ),
            axis=1,
        )
        res_["industry_turnover"] = tmp_df["industry_turnover"].apply(lambda x: "{:.2f}亿".format(x))
        res[industry] = res_.loc[tmp_df.sort_values("industry_pct", ascending=False).index[:10]]
        res[industry]["today_pct"] = res[industry].index.map(
            lambda x: "{:.2%}".format(
                get_price(x, None, date, "1d", ["quote_rate"], bar_count=1)["quote_rate"].iloc[-1] / 100
            )
        )
        res[industry]["num_of_hl"] = res[industry].index.map(
            lambda x: "当日触板：{:.0f}，涨停{:.0f}，累计3日涨停：{:.0f}，总数：{:.0f}".format(
                (
                    (
                        d := get_price_(
                            get_industry_stocks_(x, date=date),
                            None,
                            date,
                            "1d",
                            ["close", "high", "high_limit"],
                            skip_paused=False,
                            bar_count=3,
                            is_panel=1,
                        )
                    )["high"]
                    == d["high_limit"]
                )
                .sum(axis=1)
                .iloc[-1],
                (d["close"] == d["high_limit"]).sum(axis=1).iloc[-1],
                (d["close"] == d["high_limit"]).sum(axis=1).sum(),
                d["close"].shape[1],
            )
        )
        display("## {}".format(industry))
        display(
            res[industry]
            .style.applymap(lambda x: "color: red" if x[-3] == "↑" else "color: green", subset=["industry_rank"])
            .applymap(lambda x: "color: red" if "-" not in x else "color: green", subset=["industry_pct"])
            .applymap(lambda x: "color: red" if "-" not in x else "color: green", subset=["today_pct"])
        )


def calc(symbol_list, bar_count, date, force=False):
    date = get_trade_days(None, date, 1)[-1].strftime("%Y-%m-%d")
    global result_cache, quotation_cache, cache_path
    if (date, bar_count) not in result_cache:
        result_cache[(date, bar_count)] = {}

    if (date, bar_count) not in quotation_cache:
        quotation_cache[(date, bar_count)] = {}

    res = result_cache[(date, bar_count)]
    quotation = quotation_cache[(date, bar_count)]

    # 取大盘指数行情
    for symbol_ in ["000001.SH", "399001.SZ"]:
        if symbol_ in quotation and not force:
            continue
        quotation[symbol_] = get_price(
            symbol_, None, date, "1d", ["open", "high", "low", "close", "volume"], bar_count=bar_count + 60
        )

    # 计算行业、概念相关数据
    for symbol in tqdm(symbol_list):
        if symbol in res and not force:
            continue
        try:
            res[symbol] = {}
            res[symbol]["concept"] = get_related_concept_pct_rank(symbol, bar_count=bar_count, date=date)
            res[symbol]["industry"] = get_related_industries_pct_rank(symbol, bar_count=bar_count, date=date)

            for symbol_ in chain(
                [symbol],
                res[symbol]["concept"]["A股_常规概念"].index if "A股_常规概念" in res[symbol]["concept"] else [],
                res[symbol]["industry"].index,
            ):
                if symbol_ in quotation:
                    continue
                quotation[symbol_] = get_price(
                    symbol_, None, date, "1d", ["open", "high", "low", "close", "volume"], bar_count=bar_count + 60
                )
        except Exception as e:
            logger.error(f"{symbol} 计算失败")
            logger.exception(e)

    with cache_path.open("wb") as f:
        pickle.dump((result_cache, quotation_cache), f)


def main(sector_name, bar_count=7, date="now", compare_days=-1):
    symbol_list = [i for i in custom_sector(sector_name).symbol if i[0] in ["0", "3", "6"]]
    if len(symbol_list) == 0:
        return

    date = get_trade_days(None, date, 1)[-1].strftime("%Y-%m-%d")
    compare_date = calc_trade_day(date, compare_days).strftime("%Y-%m-%d")
    calc(symbol_list, bar_count, date)
    calc(symbol_list, bar_count, compare_date)

    # region 展示左侧边栏，股票列表
    symbol_list.sort()
    symbol_list1 = ["{}<{}>".format(get_stock_name(i), i) for i in symbol_list]
    symbol_select = widgets.Select(
        options=symbol_list1, value=None, disabled=False, layout=widgets.Layout(width="95%", height="100%")
    )

    del_button = widgets.Button(
        description="删除", button_style="warning", layout=widgets.Layout(width="45%", height="25px")
    )
    add_button = widgets.Button(
        description="添加", button_style="info", layout=widgets.Layout(width="45%", height="25px")
    )
    clr_button = widgets.Button(description="清空", button_style="danger")

    def on_del_button_clicked(b):
        custom_sector(sector_name, "pop", [symbol_select.value.split("<")[1][:-1]])

    def on_add_button_clicked(b):
        custom_sector("选股", "append", [symbol_select.value.split("<")[1][:-1]])

    def on_clr_button_clicked(b):
        s = custom_sector(sector_name).symbol
        custom_sector(sector_name, "pop", s)

    del_button.on_click(on_del_button_clicked)
    add_button.on_click(on_add_button_clicked)
    clr_button.on_click(on_clr_button_clicked)
    # endregion

    output2 = widgets.Output(layout=widgets.Layout(width="90%", height="700px"))

    global result_cache, quotation_cache
    res = result_cache[(date, bar_count)]
    quotation = quotation_cache[(date, bar_count)]
    compare_res = result_cache[(compare_date, bar_count)]
    compare_quotation = quotation_cache[(compare_date, bar_count)]

    def on_symbol_change(change):
        output2.clear_output()
        with output2:
            new_symbol = change["new"].split("<")[1][:-1]

            # region 展示行业
            tmp_df = res[new_symbol]["industry"]
            if not tmp_df.empty:
                if compare_res[new_symbol]["industry"].empty:
                    tmp_df["industry_rank_change"] = np.nan
                    tmp_df["stock_rank_change"] = np.nan
                else:
                    tmp_df["industry_rank_change"] = (
                        compare_res[new_symbol]["industry"]["industry_rank"] - tmp_df["industry_rank"]
                    )
                    tmp_df["stock_rank_change"] = (
                        compare_res[new_symbol]["industry"]["stock_rank"] - tmp_df["stock_rank"]
                    )

                res1 = pd.DataFrame(index=tmp_df.index)
                res1["行业名称"] = tmp_df["name"]
                res1["行业分类"] = tmp_df["industry_type"]
                res1[f"{bar_count}日涨跌幅"] = tmp_df["industry_pct"].apply(lambda x: "{:.2%}".format(x))
                res1["行业排名"] = tmp_df.apply(
                    lambda x: "{:.0f} / {:.0f} ( {:.0f} {} )".format(
                        x["industry_rank"],
                        x["industry_count"],
                        x["industry_rank_change"],
                        "↑" if x["industry_rank_change"] >= 0 else "↓",
                    ),
                    axis=1,
                )
                res1[f"{bar_count}日股票涨跌幅"] = tmp_df["stock_pct"].apply(lambda x: "{:.2%}".format(x))
                res1["股票排名"] = tmp_df.apply(
                    lambda x: "{:.0f} / {:.0f} ( {:.0f} {} )".format(
                        x["stock_rank"],
                        x["stock_count"],
                        x["stock_rank_change"],
                        "↑" if x["stock_rank_change"] >= 0 else "↓",
                    ),
                    axis=1,
                )
                res1["领涨股"] = tmp_df["leading_stock"]
                res1[f"{bar_count}日领涨股涨跌幅"] = tmp_df["leading_stock_pct"].apply(lambda x: "{:.2%}".format(x))
                res1.sort_index(ascending=True, inplace=True)
                display(
                    res1.style.applymap(lambda x: "color: red" if x[-3] == "↑" else "color: green", subset=["行业排名"])
                    .applymap(
                        lambda x: "color: red" if "-" not in x else "color: green", subset=[f"{bar_count}日涨跌幅"]
                    )
                    .applymap(lambda x: "color: red" if x[-3] == "↑" else "color: green", subset=["股票排名"])
                    .applymap(
                        lambda x: "color: red" if "-" not in x else "color: green", subset=[f"{bar_count}日股票涨跌幅"]
                    )
                    .applymap(
                        lambda x: "color: red" if "-" not in x else "color: green",
                        subset=[f"{bar_count}日领涨股涨跌幅"],
                    )
                )
                sector_select1 = widgets.Select(
                    options=res1.index,
                    disabled=False,
                    value=None,
                    layout=widgets.Layout(width="95%", height="170px"),
                )
            else:
                sector_select1 = widgets.Select(
                    options=[],
                    disabled=False,
                    value=None,
                    layout=widgets.Layout(width="95%", height="170px"),
                )
            # endregion

            # region 展示概念
            if res[new_symbol]["concept"]:
                tmp_df = res[new_symbol]["concept"]["A股_常规概念"]
                if compare_res[new_symbol]["concept"]:
                    tmp_df["concept_rank_change"] = (
                        compare_res[new_symbol]["concept"]["A股_常规概念"]["concept_rank"] - tmp_df["concept_rank"]
                    )
                    tmp_df["stock_rank_change"] = (
                        compare_res[new_symbol]["concept"]["A股_常规概念"]["stock_rank"] - tmp_df["stock_rank"]
                    )
                else:
                    tmp_df["concept_rank_change"] = np.nan
                    tmp_df["stock_rank_change"] = np.nan

                res2 = pd.DataFrame(index=tmp_df.index)
                res2["概念名称"] = tmp_df["name"]
                res2[f"{bar_count}日涨跌幅"] = tmp_df["concept_pct"]
                res2 = res2.loc[~pd.isna(res2[f"{bar_count}日涨跌幅"])]
                res2 = res2.sort_values(f"{bar_count}日涨跌幅", ascending=False).iloc[:5]
                res2[f"{bar_count}日涨跌幅"] = res2[f"{bar_count}日涨跌幅"].apply(lambda x: "{:.2%}".format(x))
                res2["概念排名"] = tmp_df.apply(
                    lambda x: "{:.0f} / {:.0f} ( {:.0f} {} )".format(
                        x["concept_rank"],
                        x["concept_count"],
                        x["concept_rank_change"],
                        "↑" if x["concept_rank_change"] >= 0 else "↓",
                    ),
                    axis=1,
                )
                res2[f"{bar_count}日股票涨跌幅"] = tmp_df["stock_pct"].apply(lambda x: "{:.2%}".format(x))
                res2["股票排名"] = tmp_df.apply(
                    lambda x: "{:.0f} / {:.0f} ( {:.0f} {} )".format(
                        x["stock_rank"],
                        x["stock_count"],
                        x["stock_rank_change"],
                        "↑" if x["stock_rank_change"] >= 0 else "↓",
                    ),
                    axis=1,
                )
                res2["领涨股"] = tmp_df["leading_stock"]
                res2[f"{bar_count}日领涨股涨跌幅"] = tmp_df["leading_stock_pct"].apply(lambda x: "{:.2%}".format(x))
                display(
                    res2.style.applymap(lambda x: "color: red" if x[-3] == "↑" else "color: green", subset=["概念排名"])
                    .applymap(
                        lambda x: "color: red" if "-" not in x else "color: green", subset=[f"{bar_count}日涨跌幅"]
                    )
                    .applymap(
                        lambda x: "color: red" if x[-3] == "↑" else "color: green", subset=[f"{bar_count}日股票涨跌幅"]
                    )
                    .applymap(lambda x: "color: red" if "-" not in x else "color: green", subset=["股票排名"])
                    .applymap(
                        lambda x: "color: red" if "-" not in x else "color: green",
                        subset=[f"{bar_count}日领涨股涨跌幅"],
                    )
                )
                sector_select2 = widgets.Select(
                    options=res[new_symbol]["concept"]["A股_常规概念"].index,
                    value=None,
                    disabled=False,
                    layout=widgets.Layout(width="95%", height="170px"),
                )
            else:
                sector_select2 = widgets.Select(
                    options=[],
                    value=None,
                    disabled=False,
                    layout=widgets.Layout(width="95%", height="170px"),
                )
            # endregion

            output3 = widgets.Output(layout=widgets.Layout(width="80%", height="366px"))

            def display3(change1):
                """画走势图"""
                output3.clear_output()
                with output3:
                    display(change1["new"])
                    add_plot = [
                        mpf.make_addplot(
                            quotation[new_symbol]["close"] / quotation[new_symbol]["close"].iloc[0] - 1,
                            secondary_y=False,
                        )
                    ]
                    if new_symbol[0] == "6":
                        add_plot.append(
                            mpf.make_addplot(
                                quotation["000001.SH"]["close"] / quotation["000001.SH"]["close"].iloc[0] - 1,
                                linestyle="--",
                                secondary_y=False,
                            )
                        )
                    else:
                        add_plot.append(
                            mpf.make_addplot(
                                quotation["399001.SZ"]["close"] / quotation["399001.SZ"]["close"].iloc[0] - 1,
                                linestyle="--",
                                secondary_y=False,
                            )
                        )

                    sector_quotation = quotation[change1["new"]].copy()
                    sector_quotation.loc[:, ["open", "high", "low", "close"]] /= sector_quotation["close"].iloc[0]
                    sector_quotation.loc[:, ["open", "high", "low", "close"]] -= 1
                    sector_quotation.loc[:, "volume"] /= sector_quotation["volume"].min()
                    sector_quotation.loc[:, "volume"] -= 1
                    mpf.plot(sector_quotation, type="candle", figsize=(10, 3.8), volume=True, addplot=add_plot)

            sector_select1.observe(display3, names="value")
            sector_select2.observe(display3, names="value")
            sector_select1.value = res1.index[0]
            display(
                widgets.HBox(
                    [
                        widgets.VBox(
                            [sector_select1, sector_select2], layout=widgets.Layout(width="15%", height="366px")
                        ),
                        output3,
                    ]
                )
            )

    symbol_select.observe(on_symbol_change, names="value")
    symbol_select.value = symbol_list1[0]
    display(
        widgets.HBox(
            [
                widgets.VBox(
                    [
                        widgets.HBox([del_button, add_button], layout=widgets.Layout(height="32px")),
                        symbol_select,
                        clr_button,
                    ],
                    layout=widgets.Layout(width="15%", height="700px"),
                ),
                output2,
            ]
        )
    )

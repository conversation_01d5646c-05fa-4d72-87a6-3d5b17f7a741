CREATE DATABASE DAILY_QUOTATION;

CREATE DATABASE MINUTE_QUOTATION;

CREATE DATABASE TRADER;

CREATE TABLE IF NOT EXISTS {TABLE_NAME}(
TIMESTAMP BIGINT UNIQUE NOT NULL,
CODE TEXT,
DATE INTEGER,
TIME INTEGER,
OPEN NUMERIC(20,6),
HIGH NUMERIC(20,6),
LOW NUMERIC(20,6),
CLOSE NUMERIC(20,6),
FACTOR NUMERIC(18,4),
VOLUME BIGINT,
TURNOVER NUMERIC(18,4),
TURNOVER_RATE NUMERIC(18,4),
IS_PAUSED BOOLEAN,
UPLIMIT_PRICE NUMERIC(20,6),
DOWNLIMIT_PRICE NUMERIC(20,6),
AVG_PRICE NUMERIC(20,6),
PRE_PRICE NUMERIC(20,6),
QUOTE_RATE NUMERIC(18,4),
AMP_RATE NUMERIC(18,4),
IS_ST BOOLEAN,
<PERSON><PERSON><PERSON> NUMERIC(20,6),
PRE_SETTLE NUMERIC(20,6),
CHAN<PERSON> NUMERIC(20,6),
OPEN_INTEREST BIGINT,
POS_CHANGE BIGINT,
PRE_OPEN_INTEREST BIGINT
);

select timestamp,code,open from ag8888_shfe;

insert into ag8888_shfe(timestamp,code,open) values(300,'ad',100);

insert into ag8888_shfe(timestamp,code,open) values(600,'aud33333x',1014400)
on conflict(timestamp)
do update set (TIMESTAMP,CODE,DATE,TIME,OPEN,HIGH,LOW,CLOSE,FACTOR,VOLUME,TURNOVER,TURNOVER_RATE,IS_PAUSED,UPLIMIT_PRICE,DOWNLIMIT_PRICE,
AVG_PRICE,PRE_PRICE,QUOTE_RATE,AMP_RATE,IS_ST,SETTLE,PRE_SETTLE,CHANGE,OPEN_INTEREST,POS_CHANGE,PRE_OPEN_INTEREST) 
= (excluded.TIMESTAMP,excluded.CODE,excluded.DATE,excluded.TIME,excluded.OPEN,excluded.HIGH,
excluded.LOW,excluded.CLOSE,excluded.FACTOR,excluded.VOLUME,excluded.TURNOVER,excluded.TURNOVER_RATE,
excluded.IS_PAUSED,excluded.UPLIMIT_PRICE,excluded.DOWNLIMIT_PRICE,excluded.AVG_PRICE,excluded.PRE_PRICE,
excluded.QUOTE_RATE,excluded.AMP_RATE,excluded.IS_ST,excluded.SETTLE,excluded.PRE_SETTLE,excluded.CHANGE,
excluded.OPEN_INTEREST,excluded.POS_CHANGE,excluded.PRE_OPEN_INTEREST) ;


./pg_dump.exe -U postgres -C -d minute_quotation -t *_czce | ./psql.exe -h 192.168.1.112 -U postgres -d minute_quotation
### 每分钟都推
**allocate a thread for every output data**
```
while True:
    bar is None,指定bar结束的时间
    把待处理处理了
    while True:循环内完成一个bar的生成,每秒执行一次
        if 没有tick进来：
            if 达到bar结束时间:
                if bar not is None:
                    break
                else:
                    if last_tick:
                        用上一个tick更新
                    else:
                        break
        else:
            if tick无效：
                continue
            if bar is None:
                更新
                if 秒>57:
                    break
            else:
                if 秒<=57，同分钟:
                    更新
                elif 秒>57，同分钟:
                    更新
                    break
                elif 当前tick进入新的分钟:
                    break
    if bar is valid:
        输出
```
**allocate a thread for all output data**
```
input:input_data_label,input_data
output:

while True:
    for every input_data_label:
        for every output_data_label:
            if input_data is valie:
                if not bar is None:
                    update(input_data)
                    if 秒>57:
                        send
                        new bar
                else:
                    if 秒<=57，同分钟:
                        update(input_data)
                    elif 秒>57，同分钟:
                        update(input_data)
                        send
                        new bar
                    elif 当前tick进入新的分钟:
                        send
                        new bar
                        update(input_data)
            else:
                if 达到bar结束时间:
                    if bar:
                        send
                        new bar
                    else:
                        if last_tick:
                            update(last_tick)
                            send
                            new bar
```
+ 093000的tick不作为093100的open

+ 101500tick作为101600open;101600tick作为101700open;101700tick作为101800open;130000tick作为130100open;145700tick作为145800open

+ 150000tick作为150000ohlc

+ 有093000  112959  130000 145700  150000
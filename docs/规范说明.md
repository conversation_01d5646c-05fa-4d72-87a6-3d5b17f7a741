```python
QSymbol = NewType("QSymbol", str)
"""600000.SSE/RU8888.SHFE"""
MindgoSymbol = NewType("MindgoSymbol", str)
"""600000.SH/JD8888"""
TBSymbol = NewType("TBSymbol", str)
"""ru000"""
PureSymbol = NewType("PureSymbol", str)
"600519"
TdsSymbol = NewType("TdsSymbol", str)
"600519USHA"
DataLabel = NewType("DataLabel", str)
"""600519.SSE-MINUTE"""
```

`Bar`的时间标记为后对齐，即09：00：00——09：01：00的标记为090100,日线记为150000


`tqsdk`提供的tick数据SHFE提供5档,CZCE和DCE提供1档
提供的分钟数据时间标记为前对齐, 精确到分钟, 在回测时在分钟开始发一次, 结束发一次; 实盘中每个tick发一次
提供的tick数据只发一次
`datetime`是自然时间

文华财经的加权   对应    天勤量化的指数    对应     TB的指数000

天勤量化提供的bar时间为前对齐，集合竞价不列为独立的K线，开盘第一个K线把集合竞价撮合的成交量合并进去了

2020年之前，个别交易所成交量是按双边计的，天勤已经处理过了，quant也处理过了


from setuptools import setup, find_packages

setup(
    name="task_scheduler",
    version="0.1.0",
    packages=find_packages(where="src"),
    package_dir={"":"src"},
    install_requires=[
        "filelock>=3.0.0",
        "watchdog>=2.0.0",
        "click>=7.0.0",
    ],
    entry_points={
        "console_scripts": [
            "task-monitor=task_scheduler.cli.monitor:main",
        ],
    },
    python_requires=">=3.8",
    author="Yang",
    description="A task scheduling tool for coordinating execution order between independent processes",
)
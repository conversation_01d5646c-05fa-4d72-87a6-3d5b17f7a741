from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Type

import datetype
import gymnasium as gym
import matplotlib.pyplot as plt
import mplfinance as mpf
import numba
import numpy as np
import numpy.typing as npt
import pandas as pd

from ..base import InstrumentInfo, RealInstrumentDataHandler
from ..env import BackTest, BackTestEngine, InstrumentDataHandler, StrategyEnv
from ..utils import RunEnv, run_env, time_cos_encoding, time_sin_encoding

if run_env == RunEnv.SUPERMIND:
    from mindgo_api import get_price  # type: ignore

MIN_IN_A_DAY = 240


class GridEnv(StrategyEnv):
    def __init__(
        self,
        observe_days: int,
        observe_features: List[str],
        feature_freq: int,
        evaluate_days: int,
        action_freq: int,
        test_size: float,
        symbols: List[str],
        start_dt: Optional[str] = None,
        end_dt: Optional[str] = None,
        specify_instrument_info: Optional[Dict[str, Dict[str, float]]] = None,
        **config,
    ):
        """
        交易环境

        参数:
            feature_length (int): 价差回溯的天数
            observe_features (List[str]): 价差回溯的特征，open, high, low, close, volume, turnover
            feature_freq (int): 价差回溯的采样频率
            evaluate_days (int): 交易目标的天数
            action_freq (int): 交易动作的频率
            test_size (float): 测试集的比例
            symbols (List[str]): 证券代码
            start_dt (Optional[str]): 开始日期
            end_dt (Optional[str]): 结束日期
        """
        super().__init__(
            int(observe_days * MIN_IN_A_DAY / feature_freq),
            observe_features,
            feature_freq,
            evaluate_days,
            action_freq,
            test_size,
            symbols,
            start_dt,
            end_dt,
            specify_instrument_info,
            **config,
        )
        self.observe_days = observe_days
        self.target_length = evaluate_days * MIN_IN_A_DAY

    def _bind_data_handler_type(self) -> Type[InstrumentDataHandler]:
        return InstrumentDataHandlerForGrid

    def _define_action_space(self) -> gym.Space:
        return gym.spaces.Box(low=-1, high=1, shape=(1,))

    def _define_observation_space(self) -> gym.Space:
        return gym.spaces.Dict(
            {
                "price": gym.spaces.Box(
                    low=-np.inf, high=np.inf, shape=(self.feature_length, len(self.observe_features) - 6)
                ),
                "time": gym.spaces.Box(low=-np.inf, high=np.inf, shape=(self.feature_length, 6)),
                "other": gym.spaces.Box(low=-np.inf, high=np.inf, shape=(2 + len(self.observe_features),)),
            }
        )

    def _define_record(self):
        def fnc(infos_: List[Dict[str, float]]):
            infos_ = [i for i in infos_ if i]
            return True, {
                "count": np.sum([i["count"] for i in infos_]),
            }

        return fnc


@numba.jit(nopython=True, cache=True, parallel=True)
def flag_bar(vector) -> npt.NDArray[np.int8]:
    # 标记bar当天第一根为1最后一根为-1
    res = np.full(vector.shape, 0, dtype=np.int8)
    res[0] = 1
    res[-1] = -1
    for i in numba.prange(1, vector.shape[0] - 1):
        if vector[i + 1] != vector[i]:
            res[i] = -1
        elif vector[i - 1] != vector[i]:
            res[i] = 1
    return res


@dataclass
class GridStrategyParams:
    up_ratio: float
    down_ratio: float
    buy_num: float
    sell_num: float
    init_holding: float
    close_at_end: bool


class InstrumentDataHandlerForGrid(RealInstrumentDataHandler):
    grid_params: GridStrategyParams

    # 策略数据
    is_first_bar: npt.NDArray[np.int8]

    def __init__(self, symbol, start_dt, end_dt, instrument_info=None, **config):
        super().__init__(symbol, start_dt, end_dt, ["minute"], config.get("forward_days", 380), instrument_info)

        self.grid_params = GridStrategyParams(
            up_ratio=config.get("up_ratio", 0.01),
            down_ratio=config.get("down_ratio", 0.01),
            buy_num=config.get("buy_num", 1000),
            sell_num=config.get("sell_num", 1000),
            init_holding=config.get("init_holding", 10000.0),
            close_at_end=config.get("close_at_end", False),
        )

        self.is_first_bar = flag_bar(self.minute_price_df.index.normalize().to_numpy())

        agg = {
            "open": "first",  # 开盘价取第一个值
            "high": "max",  # 最高价取最大值
            "low": "min",  # 最低价取最小值
            "close": "last",  # 收盘价取最后一个值
            "volume": "sum",  # 成交量求和
            "sin_h": "last",
            "cos_h": "last",
            "sin_m": "last",
            "cos_m": "last",
            "sin_d": "last",
            "cos_d": "last",
        }
        if "open_interest" in self.minute_price_df.columns:
            agg.update({"open_interest": "last"})
        if "turnover" in self.minute_price_df.columns:
            agg.update({"turnover": "sum"})

        rmp_values = self.minute_price_df.resample(f"{config['feature_freq']}min", closed="right", label="right").agg(
            agg
        )
        self.features_data = rmp_values.loc[rmp_values.index.isin(self.minute_price_df.index)]

    def _preprocess_minute_data(self):
        tmp = self.minute_price_df.index.hour
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        hmax = tmp.max() + 1
        self.minute_price_df["sin_h"] = tmp.apply(lambda x: time_sin_encoding(x, hmax))
        self.minute_price_df["cos_h"] = tmp.apply(lambda x: time_cos_encoding(x, hmax))

        tmp = self.minute_price_df.index.minute
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        mmax = tmp.max() + 1
        self.minute_price_df["sin_m"] = tmp.apply(lambda x: time_sin_encoding(x, mmax))
        self.minute_price_df["cos_m"] = tmp.apply(lambda x: time_cos_encoding(x, mmax))

        tmp = self.minute_price_df.index.tz_localize("Asia/Shanghai").day_of_week
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        dmax = tmp.max() + 1
        self.minute_price_df["sin_d"] = tmp.apply(lambda x: time_sin_encoding(x, dmax))
        self.minute_price_df["cos_d"] = tmp.apply(lambda x: time_cos_encoding(x, dmax))

        self.minute_price_df.loc[self.minute_price_df["close"] == 0, "close"] = np.nan
        self.minute_price_df["close"] = self.minute_price_df["close"].ffill()  # 用前一个有效值填充

    def post_init(self, **kwargs):
        # scaler = StandardScaler()
        # scaler.fit(self.features_data.loc[self.features_data.index <= kwargs["mid_dt"]])
        # self.features_data = pd.DataFrame(
        #     scaler.transform(self.features_data), index=self.features_data.index, columns=self.features_data.columns
        # )
        self.features_data = (
            self.features_data - self.features_data.rolling(kwargs["normalize_window"]).mean()
        ) / self.features_data.rolling(kwargs["normalize_window"]).std()

    def _bind_backtest_type(self) -> Type["BackTest"]:
        return GridBackTest

    @staticmethod
    def check(engine: BackTestEngine):
        barcount = []
        for data_handler in engine.data_handlers.values():
            tmp = pd.Series(data_handler.minute_price_df.index.date).value_counts().rename(data_handler.symbol)  # type: ignore
            barcount.append(tmp)
        if len(barcount) == 0:
            raise RuntimeError("No data found")
        elif len(barcount) == 1:
            barcount = barcount[0].to_frame().sort_index()
        else:
            barcount = pd.concat(barcount, axis=1).sort_index()
        df1 = barcount.rolling(
            int(engine.feature_length * engine.feature_freq / MIN_IN_A_DAY), min_periods=1
        ).sum().shift(1) == (engine.feature_length * engine.feature_freq)
        df2 = barcount.rolling(
            window=pd.api.indexers.FixedForwardWindowIndexer(window_size=engine.evaluate_days), min_periods=1
        ).sum() == (engine.evaluate_days * MIN_IN_A_DAY)
        return df1 & df2

    def _time2trade_day(self) -> Tuple[pd.DatetimeIndex, np.ndarray]:
        return self.minute_price_df.index, self.minute_price_df.index.date  # type: ignore


class GridBackTest(BackTest):
    data_handler: InstrumentDataHandlerForGrid
    price: npt.NDArray[np.float64]
    timestamp: npt.NDArray[np.int64]
    is_first_bar: npt.NDArray[np.int8]

    instrument_info: InstrumentInfo
    grid_params: GridStrategyParams

    init_price: float
    init_cash: float

    base_price: float
    holding: float
    available_amount: float
    cash: float

    init_idx: int

    def __init__(
        self,
        data_handler: InstrumentDataHandlerForGrid,
        begin_time: datetype.NaiveDateTime,
        end_time: datetype.NaiveDateTime,
        feature_length,
        observe_features,
        feature_freq,
        evaluate_days,
        action_freq,
    ):
        super().__init__(
            data_handler,
            begin_time,
            end_time,
            feature_length,
            observe_features,
            feature_freq,
            evaluate_days,
            action_freq,
        )

        idx1: int = data_handler.minute_price_df.index.get_loc(begin_time)
        idx2: int = data_handler.minute_price_df.index.get_loc(end_time) + 1

        self.data_handler = data_handler
        self.price = data_handler.last_price[idx1:idx2]
        self.timestamp = data_handler.timestamp[idx1:idx2]
        self.is_first_bar = data_handler.is_first_bar[idx1:idx2]

        self.instrument_info = data_handler.instrument_info
        self.grid_params = data_handler.grid_params

        self.init_price = data_handler.minute_price_df["day_prev_close"].iloc[idx1]
        self.init_cash = self.grid_params.init_holding * self.init_price

        self.base_price = self.init_price
        self.holding = self.grid_params.init_holding
        self.available_amount = self.grid_params.init_holding
        self.cash = self.init_cash

        self.result = np.full((evaluate_days * MIN_IN_A_DAY, 13), 0.0, dtype=np.float64)
        self.init_idx = idx1

    def get_observation(self):
        now = self.data_handler.minute_price_df.index[self.init_idx + self.idx]
        bar_end = np.searchsorted(self.data_handler.features_data.index, now, "right")
        values = self.data_handler.features_data.iloc[bar_end - self.feature_length : bar_end][self.observe_features]

        tmp = np.searchsorted(self.data_handler.minute_price_df.index, now, "right")
        last_bar = self.data_handler.minute_price_df.iloc[tmp - 2]
        current_data = self.data_handler.minute_price_df.iloc[tmp - 1]
        states = (
            self.holding / self.grid_params.init_holding,
            self.base_price / values["close"].iloc[-1],
            *(current_data[["open", "high", "low", "close"]] / last_bar["close"]),
            current_data.at["sin_h"],
            current_data.at["cos_h"],
            current_data.at["sin_m"],
            current_data.at["cos_m"],
            current_data.at["sin_d"],
            current_data.at["cos_d"],
            *(current_data[["volume", "turnover"]] / values[["volume", "turnover"]].mean(axis=0) * self.feature_freq),
        )
        values = values.values
        return {"price": values[:, :-6], "time": values[:, -6:], "other": np.array(states)}

    def _step(self, offset_: Optional[np.ndarray] = None):
        if offset_ is None:
            offset = 0
        else:
            offset = offset_[0]

        offset /= 100 * 4

        i = self.idx

        pre_base_price = self.base_price
        self.base_price *= offset + 1

        self.result[i, 10] = self.base_price  # 当前bar调整过的基准价
        self.result[i, 11] = offset

        if self.is_first_bar[i] == 1:
            self.available_amount = self.holding

        price_change = self.price[i] / self.base_price - 1
        price_change1 = self.price[i] / pre_base_price - 1

        flag = times = slippage_num = 0
        if self.grid_params.close_at_end and i == self.price.shape[0] - 1:
            if self.holding > self.grid_params.init_holding:
                flag = 1
                times = (self.holding - self.grid_params.init_holding) / self.grid_params.buy_num
                self.base_price = self.price[i]
                slippage_num = 1
            elif self.holding < self.grid_params.init_holding:
                flag = -1
                times = (self.grid_params.init_holding - self.holding) / self.grid_params.sell_num
                self.base_price = self.price[i]
                slippage_num = 1
        else:
            if self.is_first_bar[i] == -1:
                flag = times = slippage_num = 0
            elif price_change >= self.grid_params.up_ratio:  # 判断是否穿网
                flag = 1
                times = 1
                self.base_price = self.price[i]
                slippage_num = 1 if price_change1 < self.grid_params.up_ratio else 0
            elif price_change <= -self.grid_params.down_ratio:
                flag = -1
                times = 1
                self.base_price = self.price[i]
                slippage_num = 1 if price_change1 > -self.grid_params.down_ratio else 0

        commission = 0
        trade_price = 0
        volume = amount = 0

        if flag == 1:
            trade_price = self.price[i] - self.instrument_info.minium_price_change * slippage_num
            volume = min(
                self.grid_params.sell_num
                * times
                // self.instrument_info.minium_round_lot
                * self.instrument_info.minium_round_lot,
                self.available_amount,
            )
            if volume > 0:
                amount = volume * trade_price
                self.holding -= volume
                self.available_amount -= volume
                self.cash = (
                    self.cash
                    + amount * (1 - self.instrument_info.stamp_tax)
                    - max(self.instrument_info.commission_ratio * amount, self.instrument_info.minium_commission)
                )
                commission = amount * self.instrument_info.stamp_tax + max(
                    self.instrument_info.commission_ratio * amount, self.instrument_info.minium_commission
                )
        elif flag == -1:
            trade_price = self.price[i] + self.instrument_info.minium_price_change * slippage_num
            volume = (
                self.grid_params.buy_num
                * times
                // self.instrument_info.minium_round_lot
                * self.instrument_info.minium_round_lot
            )
            amount = volume * trade_price
            commission = max(self.instrument_info.commission_ratio * amount, self.instrument_info.minium_commission)

            while self.cash < amount + commission:
                volume -= self.instrument_info.minium_round_lot
                amount = volume * trade_price
                commission = max(self.instrument_info.commission_ratio * amount, self.instrument_info.minium_commission)
                if volume <= 0:
                    break
            if volume > 0:  # 超过最大投入限制则跳过本次
                self.holding += volume
                self.cash = self.cash - amount - commission
            else:
                volume = amount = commission = 0

        self.result[i, 0] = self.timestamp[i]  # 时间戳
        self.result[i, 1] = self.holding  # 持股数量
        self.result[i, 2] = self.cash  # 现金
        self.result[i, 3] = self.holding * self.price[i]  # 持仓市值
        self.result[i, 4] = trade_price  # 成交价
        self.result[i, 5] = volume  # 成交量
        self.result[i, 6] = amount  # 成交额
        self.result[i, 7] = volume and -flag  # 交易方向，1买入，-1卖出
        self.result[i, 8] = commission  # 交易成本：手续费、印花税
        self.result[i, 9] = self.price[i]  # 触发价
        self.result[i, 12] = self.base_price  # 当前bar结束的基准价

        self.idx += 1

    def show(self, result):
        df = pd.DataFrame(
            result,
            columns=[
                "date",
                "holding",
                "cash",
                "market_value",
                "trade_price",
                "volume",
                "amount",
                "side",
                "cost",
                "last_price",
                "adjusted_base_price",
                "offset",
                "bar_end_base_price",
            ],
        )
        df["date"] = pd.to_datetime(df["date"])
        df = df.set_index("date")
        init_cash = self.init_cash
        init_price = self.init_price
        init_asset = init_cash + init_cash
        # 收益曲线
        day_df = df[["cash", "market_value", "last_price"]].resample("D").last().dropna()
        day_df["asset"] = day_df["cash"] + day_df["market_value"]
        peak_v = day_df["asset"].cummax()
        tmp1 = day_df["last_price"].values
        tmp2 = np.array([init_price, *tmp1[:-1]])
        std = (tmp1 / tmp2 - 1).std()
        profit, base_profit = [], []
        for i, x in day_df.iterrows():
            profit.append({"dt": i, "rate": x["asset"] / init_asset - 1})
            base_profit.append({"dt": i, "rate": (x["last_price"] - self.init_price) / self.init_price})
        # 交易明细
        tdf = df[df["side"] != 0]
        trades = [
            {
                "dt": str(i),
                "side": x["side"],
                "price": x["trade_price"],
                "volume": x["volume"],
                "amount": x["amount"],
                "last_price": x["last_price"],
                "cost": x["cost"],
            }
            for i, x in tdf.iterrows()
        ]
        return {
            "symbol": self.instrument_info.symbol,
            "init_date": df.index[0].strftime("%Y-%m-%d"),  # 回测起始日期
            "init_price": init_price,  # 起始基准价格
            "init_cash": init_cash,  # 起始资金
            "init_holding": self.grid_params.init_holding,  # 初始持仓
            "init_asset": init_asset,  # 初始资产
            "buy_num": self.grid_params.buy_num,  # 单笔买入数量
            "sell_num": self.grid_params.sell_num,  # 单笔卖出数量
            "yield": (tmp := (day_df["asset"].iloc[-1] / init_asset - 1)) / std,  # 网格收益率
            "yield_": tmp,  # 网格收益率
            "base_yield": (
                tmp := ((day_df["last_price"].iloc[-1] * self.grid_params.init_holding + init_cash) / init_asset - 1)
            )
            / std,  # 一直持有收益率
            "base_yield_": tmp,
            "cost": df["cost"].sum(),  # 成本
            "asset": day_df["asset"].iloc[-1],  # 期末资产
            "return": df["cash"].iloc[-1] - init_cash * 2 + df["market_value"].iloc[-1],  # 累计收益
            "max_dd": ((day_df["asset"] - peak_v) / peak_v).min(),  # 最大回撤
            "profit": profit,  # 收益曲线
            "base_profit": base_profit,  # 一直持有收益曲线
            "trades": trades,  # 成交明细,side 1买入(触发条件<=)-1 卖出(触发条件>=)
            "count": len(trades),  # 成交笔数
            "adjusted_base_price": df["adjusted_base_price"],
            "offset": df["offset"],
        }

    def get_reward(self) -> Tuple[float, Dict]:
        # if self.idx - 1 - self.action_freq < 0:
        #     pre_holding = self.grid_params.init_holding
        #     pre_cash = self.init_cash
        #     pre_asset = pre_cash * 2
        # else:
        #     pre_holding = self.result[self.idx - 1 - self.action_freq, 1]
        #     pre_cash = self.result[self.idx - 1 - self.action_freq, 2]
        #     pre_asset = pre_holding + pre_cash
        # reward = 100 * (
        #     (self.result[self.idx - 1, 2] + self.result[self.idx - 1, 3]) / pre_asset
        #     - (pre_holding * self.result[self.idx - 1, 9] + pre_cash) / pre_asset
        # )
        # return reward, {} if not self.is_done() else {"reward": reward, "count": np.sum(np.abs(self.result[:, 7]))}

        if not self.is_done():
            return 0.0, {}

        result = self.show(self.result)
        reward = result["yield"] - result["base_yield"]
        return reward, {
            "begin_time": self.begin_time,
            "end_time": self.end_time,
            "reward": reward,
            "details": result,
            "count": result["count"],
        }


def analyse_result(res, plot_every=True):
    if len(res[1]) == 0:
        print("没有交易记录")
        return

    print(f"累积超额收益：{res[0]}")
    a = res[1]
    b = pd.DataFrame([{"yield": i["details"]["yield_"], "base_yield": i["details"]["base_yield_"]} for i in a])
    b = b.cumsum()
    b.plot()
    (b["yield"] - b["base_yield"]).plot(linestyle="--")
    plt.show()

    if not plot_every:
        return
    test_symbol = a[1]["details"]["symbol"]
    for test in a:
        begin_time = test["begin_time"]
        end_time = test["end_time"]

        data = get_price(test_symbol, begin_time, end_time, "1m", ["open", "high", "low", "close"], fq="post")

        signal = pd.DataFrame(test["details"]["trades"])
        signal["dt"] = pd.to_datetime(signal["dt"])

        buy_signal = signal[["dt", "touch"]].loc[signal["side"] == 1].set_index("dt")["touch"]
        buy_signal.index.rename(None, inplace=True)
        buy_signal = buy_signal.reindex(index=data.index)
        sell_signal = signal[["dt", "touch"]].loc[signal["side"] == -1].set_index("dt")["touch"]
        sell_signal.index.rename(None, inplace=True)
        sell_signal = sell_signal.reindex(index=data.index)

        # 绘制K线图
        apds = []
        if np.isfinite(buy_signal).sum() > 0:
            apds.append(
                mpf.make_addplot(buy_signal, scatter=True, markersize=100, marker="^", color="red", label="Buy")
            )
        if np.isfinite(sell_signal).sum() > 0:
            apds.append(
                mpf.make_addplot(sell_signal, scatter=True, markersize=100, marker="v", color="green", label="Sell")
            )
        apds.append(mpf.make_addplot(test["details"]["adjusted_base_price"], color="grey", label="adjusted_base_price"))
        apds.append(mpf.make_addplot(pd.Series([test["details"]["init_price"]] * len(data), index=data.index)))

        # 绘制K线图并添加买卖点
        mpf.plot(
            data,
            type="candle",
            addplot=apds,
            volume=False,
            figsize=(18, 5),
            style="yahoo",
            datetime_format="%Y-%m-%d %H:%M",  # 设置x轴时间格式为年-月-日 时:分
        )
        print("收益{:.4%}, 基准收益{:.4%}".format(test["details"]["yield_"], test["details"]["base_yield_"]))

        # 展示图表
        plt.show()

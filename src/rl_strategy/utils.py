import json
import os
import random
from typing import Optional

import numpy as np
import pandas as pd
import torch
from torch.backends import cudnn

from aichemy.utils import RunEnv, run_env

if run_env == RunEnv.SUPERMIND:
    from mindgo_api import get_all_securities, get_price, get_price_future, get_security_info  # type: ignore
elif run_env == RunEnv.LOCAL:
    from qnt_research.api import get_price


def reseed_everything(seed: Optional[int]):
    if seed is None:
        return

    random.seed(seed)
    os.environ["PYTHONHASHSEED"] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    cudnn.deterministic = True
    cudnn.benchmark = True


def time_sin_encoding(h, period=24):
    # 计算正弦和余弦编码
    if np.isnan(h):
        return np.nan
    sin_encoded = np.sin(2 * np.pi * h / period)
    return sin_encoded


def time_cos_encoding(h, period=24):
    # 计算正弦和余弦编码
    if np.isnan(h):
        return np.nan
    cos_encoded = np.cos(2 * np.pi * h / period)
    return cos_encoded


def prepare_minute_data(stock, st, ed, file=None):
    global run_env
    if run_env == RunEnv.SUPERMIND:
        type_ = get_security_info(stock).type
        if type_ in ["commodity_futures", "futures"]:
            data1 = get_price_future(
                stock,
                st,
                ed,
                "1m",
                ["open", "high", "low", "close", "volume", "turnover", "open_interest"],
                fq="post",  # type: ignore
                skip_paused=False,
            )
        else:
            data1 = get_price(
                stock,
                st,
                ed,
                "1m",
                ["open", "high", "low", "close", "volume", "turnover"],
                fq="post",  # type: ignore
                skip_paused=True,
            )
            data2 = get_price(stock, st, ed, "1d", ["close", "prev_close"], fq="post", skip_paused=True)  # type: ignore
            for i, j in data1.groupby(data1.index.normalize()):
                data1.loc[j.index, "day_close"] = data2.loc[i, "close"]
                data1.loc[j.index, "day_prev_close"] = data2.loc[i, "prev_close"]
        data1.to_csv(file or f"./prices/{stock.replace('.', '_')}_MINUTE.csv")

    elif run_env == RunEnv.LOCAL:
        a = get_price(stock, st, ed, "1m", ["open", "high", "low", "close", "volume", "open_interest"], fq="backward")
        a.index = pd.to_datetime(a.index) + pd.Timedelta(hours=8)
        # a["turnover"] = (a["open"] + a["high"] + a["low"] + a["close"]) / 4 * a["volume"]
        # b = get_price(stock, st, ed, "1d", ["close"], fq="backward")
        # b.index = pd.to_datetime(b.index).normalize()
        # b["prev_close"] = b["close"].shift(1)
        # for i, j in a.groupby(a.index.normalize()):
        #     a.loc[j.index, "day_close"] = b.loc[i, "close"]
        #     a.loc[j.index, "day_prev_close"] = b.loc[i, "prev_close"]
        a.to_csv(file or f"./prices/{stock.replace('.', '_')}_MINUTE.csv")

    else:
        raise Exception("当前运行环境无法下载数据")


def prepare_daily_data(stock, st, ed, file=None):
    global run_env
    if run_env == RunEnv.SUPERMIND:
        type_ = get_security_info(stock).type
        if type_ in ["commodity_futures", "futures"]:
            data1 = get_price_future(
                stock,
                st,
                ed,
                "1d",
                ["open", "high", "low", "close", "prev_close", "volume", "turnover", "open_interest"],
                fq="post",  # type: ignore
                skip_paused=False,
            )
        else:
            data1 = get_price(
                stock,
                st,
                ed,
                "1d",
                ["open", "high", "low", "close", "prev_close", "volume", "turnover"],
                fq="post",  # type: ignore
                skip_paused=True,
            )
        data1.index = data1.index + pd.Timedelta(hours=15)
        data1.to_csv(file or f"./prices/{stock.replace('.', '_')}_DAILY.csv")

    elif run_env == RunEnv.LOCAL:
        a = get_price(stock, st, ed, "1d", ["open", "high", "low", "close", "volume", "open_interest"], fq="backward")
        a.index = pd.to_datetime(a.index) + pd.Timedelta(hours=8)
        # a["turnover"] = (a["open"] + a["high"] + a["low"] + a["close"]) / 4 * a["volume"]
        a["prev_close"] = a["close"].shift(1)
        a.to_csv(file or f"./prices/{stock.replace('.', '_')}_DAILY.csv")

    else:
        raise Exception("当前运行环境无法下载数据")


# TODO
def prepare_instrument_info(symbol=None):
    global run_env
    if run_env == RunEnv.SUPERMIND:
        aa = get_all_securities()[["type", "start_date"]]
        aa["start_date"] = pd.DatetimeIndex(aa["start_date"]).strftime("%Y-%m-%d")
        aa = aa.T.to_dict()
        json.dump(aa, open("sec_info.json", "a"))
    else:
        raise Exception("当前不在supermind运行，无法下载数据")

import pathlib
import pickle
from typing import Callable, Mapping, Optional

import numpy as np
from stable_baselines3.common.callbacks import Base<PERSON>allback

from aichemy.ml.exp_base import MetricsTracker

from .env import StrategyEnv


class Callback(BaseCallback):
    def __init__(
        self,
        env: StrategyEnv,
        save_path: str = "",
        verbose: int = 0,
        decide_metrics: Optional[Callable[[float, Mapping[str, float]], float]] = None,
    ):
        super().__init__(verbose)
        self.env = env
        self.save_path = pathlib.Path(save_path) if save_path else None
        self.matrics_tracker = MetricsTracker(save_path, patience=np.inf, verbose=False, decide_metrics=decide_metrics)
        self.num_rollout = 0

    def _init_callback(self) -> None:
        if self.save_path:
            self.save_path.mkdir(parents=True, exist_ok=True)

    def _on_step(self) -> bool:
        return True

    def _on_rollout_start(self) -> None:
        self.env.on_rollout_start()

    def _on_rollout_end(self) -> None:
        assert self.logger is not None
        self.num_rollout += 1
        self.test_model()

    def test_model(self):
        infos = self.env.test_model(self.model)
        reward_mean: float = float(np.mean([info["reward"] for info in infos if "reward" in info]))
        self.logger.record("test/reward", reward_mean)
        is_better, ret = self.env.record(infos)
        for i, j in ret.items():
            self.logger.record(f"test/{i}", j)
        ret["num_timesteps"] = self.num_timesteps
        self.matrics_tracker.append(self.num_rollout, -reward_mean, ret, "vali")
        if is_better:
            self.save_checkpoint(infos)

    def save_checkpoint(self, infos):
        if not self.save_path:
            return
        path = pathlib.Path(self.save_path, f"{self.num_timesteps}_steps")
        path.mkdir(parents=True, exist_ok=True)
        self.model.save(pathlib.Path(path, "model"))

        with open(pathlib.Path(path, "infos.pkl"), "wb") as f:
            pickle.dump(infos, f)

import torch
from torch import nn


class MultiScaleInput(nn.Module):
    def __init__(self, sample_window, sample_layers):
        super().__init__()
        self.sample_layers = sample_layers
        self.avg_pool = nn.AvgPool1d(sample_window)

    def forward(self, x):
        out = x.transpose(1, 2)
        outs = [out.transpose(1, 2)]
        for i in range(self.sample_layers):
            out = self.avg_pool(out)
            outs.append(out.transpose(1, 2))

        return outs


class Normalize(nn.Module):
    last: torch.Tensor
    mean: torch.Tensor
    std: torch.Tensor

    def __init__(self, feature_size: int, eps=1e-9, affine=True):
        super().__init__()
        self.feature_size = feature_size
        self.eps = eps
        self.affine = affine
        if self.affine:
            self.affine_weight = nn.Parameter(torch.ones(self.feature_size))
            self.affine_bias = nn.Parameter(torch.zeros(self.feature_size))

    def forward(self, x, denormalize=False):
        if denormalize:
            return self.denormalize(x)
        else:
            return self.normalize(x)

    def set_param(self, x):
        dim2reduce = tuple(range(1, x.ndim - 1))
        self.mean = torch.mean(x, dim=dim2reduce, keepdim=True).detach()
        self.std = torch.sqrt(torch.var(x, dim=dim2reduce, keepdim=True, unbiased=False)).detach() + self.eps

    def normalize(self, x):
        self.set_param(x)
        x = x - self.mean
        x = x / self.std
        if self.affine:
            x = x * self.affine_weight
            x = x + self.affine_bias
        return x

    def denormalize(self, x):
        if self.affine:
            x = x - self.affine_bias
            x = x / (self.affine_weight + self.eps)
        x = x * self.std
        x = x + self.mean
        return x


class Embedding(nn.Module):
    def __init__(self, feature_size, hidden_size, dropout):
        super().__init__()
        self.value_embed = nn.Conv1d(
            in_channels=feature_size, out_channels=hidden_size, kernel_size=3,
            padding=1, padding_mode="circular", bias=False
        )
        nn.init.kaiming_normal_(self.value_embed.weight)
        self.value_embed.weight.to(torch.float64)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        out = x.transpose(1, 2)
        out = self.value_embed(out)
        out = out.transpose(1, 2)
        out = self.dropout(out)
        return out


class SeriesDecomposition(nn.Module):
    def __init__(self, average_window):
        super().__init__()
        if not average_window % 2:
            average_window += 1
        self.repeat_size = (average_window - 1) // 2
        self.avg_pool = nn.AvgPool1d(kernel_size=average_window, stride=1)

    def forward(self, x: torch.Tensor):
        head = x[:, :1, :].repeat(1, self.repeat_size, 1)
        tail = x[:, -1:, :].repeat(1, self.repeat_size, 1)
        out = torch.cat([head, x, tail], dim=1)
        out = out.transpose(1, 2)
        out = self.avg_pool(out)
        out = out.transpose(1, 2)
        return x - out, out


class FeedForward(nn.Module):
    def __init__(self, hidden_size, ff_size):
        super().__init__()
        self.fc1 = nn.Linear(hidden_size, ff_size)
        self.fc2 = nn.Linear(ff_size, hidden_size)
        self.activation = nn.GELU()

    def forward(self, x):
        out = self.fc1(x)
        out = self.activation(out)
        out = self.fc2(out)
        return out


class SampleForward(nn.Module):
    def __init__(self, hidden_size, final_size):
        super().__init__()
        self.fc1 = nn.Linear(hidden_size, final_size)
        self.fc2 = nn.Linear(final_size, final_size)
        self.activation = nn.GELU()

    def forward(self, x):
        out = self.fc1(x)
        out = self.activation(out)
        out = self.fc2(out)
        return out


class MultiScaleSeasonMixing(nn.Module):
    def __init__(self, feature_length, sample_window, sample_layers):
        super().__init__()
        self.down_sample_layers = nn.ModuleList([
            SampleForward(
                feature_length // (sample_window ** i),
                feature_length // (sample_window ** (i + 1))
            ) for i in range(sample_layers)
        ])

    def forward(self, xs):
        out = xs[0]
        outs = [out.transpose(1, 2)]
        for x, down_sample_layer in zip(xs[1:], self.down_sample_layers):
            residual = down_sample_layer(out)
            out = x + residual
            outs.append(out.transpose(1, 2))
        return outs


class MultiScaleTrendMixing(nn.Module):
    def __init__(self, feature_length, sample_window, sample_layers):
        super().__init__()
        self.up_sample_layers = nn.ModuleList([
            SampleForward(
                feature_length // (sample_window ** (i + 1)),
                feature_length // (sample_window ** i)
            ) for i in reversed(range(sample_layers))
        ])

    def forward(self, xs):
        xs.reverse()
        out = xs[0]
        outs = [out.transpose(1, 2)]
        for x, up_sample_layer in zip(xs[1:], self.up_sample_layers):
            residual = up_sample_layer(out)
            out = x + residual
            outs.append(out.transpose(1, 2))
        outs.reverse()
        return outs


class PastDecomposer(nn.Module):
    def __init__(self, feature_length, hidden_size, ff_size, dropout, sample_window, sample_layers, average_window):
        super().__init__()
        self.norm = nn.LayerNorm(hidden_size)
        self.dropout = nn.Dropout(dropout)

        self.decomposition_layer = SeriesDecomposition(average_window)

        self.feed_forward = FeedForward(hidden_size, ff_size)

        self.season_layer = MultiScaleSeasonMixing(feature_length, sample_window, sample_layers)
        self.trend_layer = MultiScaleTrendMixing(feature_length, sample_window, sample_layers)

    def forward(self, xs):
        seasons, trends = [], []
        for x in xs:
            season, trend = self.decomposition_layer(x)
            season, trend = self.feed_forward(season), self.feed_forward(trend)
            seasons.append(season.transpose(1, 2))
            trends.append(trend.transpose(1, 2))
        seasons, trends = self.season_layer(seasons), self.trend_layer(trends)
        outs = []
        for x, season, trend in zip(xs, seasons, trends):
            out = season + trend
            outs.append(out[:, :x.shape[1], :])
        return outs


class FuturePredictor(nn.Module):
    def __init__(self, feature_length, label_length, feature_size, hidden_size, sample_window, sample_layers):
        super().__init__()

        self.prediction_layers = nn.ModuleList([
            nn.Linear(feature_length // (sample_window ** i), label_length) for i in range(sample_layers + 1)
        ])
        self.projection_layer = nn.Linear(hidden_size, feature_size, bias=True)
        self.out_layers = nn.ModuleList([
            nn.Linear(feature_length // (sample_window ** i), feature_length // (sample_window ** i))
            for i in range(sample_layers + 1)
        ])
        self.regression_layers = nn.ModuleList([
            nn.Linear(feature_length // (sample_window ** i), label_length)
            for i in range(sample_layers + 1)
        ])

    def forward(self, xs, residual_xs):
        outs = []
        for i, (x, residual_x) in enumerate(zip(xs, residual_xs)):
            out = x.transpose(1, 2)
            out = self.prediction_layers[i](out)
            out = out.transpose(1, 2)
            out = self.projection_layer(out)
            residual_out = residual_x.transpose(1, 2)
            residual_out = self.out_layers[i](residual_out)
            residual_out = self.regression_layers[i](residual_out)
            residual_out = residual_out.transpose(1, 2)
            out = out + residual_out
            outs.append(out)
        return outs


class ClassificationHead(nn.Module):
    def __init__(self, feature_size, label_size, dropout):
        super().__init__()
        self.activation = nn.GELU()
        self.dropout = nn.Dropout(dropout)
        self.head = nn.Linear(feature_size, label_size)

    def forward(self, x):
        out = self.activation(x)
        out = self.dropout(out)
        out = self.head(out)
        return out


class RegressionHead(nn.Module):
    def __init__(self, feature_size, label_size):
        super().__init__()
        self.head = nn.Linear(feature_size, label_size)

    def forward(self, x):
        out = self.head(x)
        out = nn.functional.tanh(out)
        return out


class TimeMixer(nn.Module):
    def __init__(
            self, feature_length, feature_size, label_length, label_size,
            hidden_size=32, ff_size=64, dropout=0.1, encoder_layers=4, sample_window=2, sample_layers=2,
            average_window=8, classification=False
    ):
        super().__init__()
        self.expand_layer = MultiScaleInput(sample_window, sample_layers)
        self.embedding_layer = Embedding(feature_size, hidden_size, dropout)
        self.decomposition_layer = SeriesDecomposition(average_window)
        self.past_decomposers = nn.ModuleList([
            PastDecomposer(feature_length, hidden_size, ff_size, dropout, sample_window, sample_layers, average_window)
            for _ in range(encoder_layers)
        ])
        self.future_preditor = FuturePredictor(
            feature_length, label_length, feature_size, hidden_size, sample_window, sample_layers
        )
        if feature_size == label_size:
            self.final_layer = None
        elif classification:
            self.final_layer = ClassificationHead(feature_size, label_size, dropout)
        else:
            self.final_layer = RegressionHead(feature_size, label_size)

    def forward(self, x):
        xs = self.expand_layer(x)
        seasons, trends = [], []
        for i, x in enumerate(xs):
            # out = self.normalize_layers[i](x)
            out = x
            season, trend = self.decomposition_layer(out)
            season = self.embedding_layer(season)
            seasons.append(season)
            trends.append(trend)

        for past_decomposer in self.past_decomposers:
            seasons = past_decomposer(seasons)

        outs = self.future_preditor(seasons, trends)
        out = torch.stack(outs, dim=-1).sum(dim=-1)
        # out = self.normalize_layers[0](out, True)
        if self.final_layer:
            out = self.final_layer(out)
        return out


if __name__ == '__main__':
    model = TimeMixer(
        128, 8, 1, 1
    )
    input_id = torch.rand(8, 128, 8)
    ret = model(input_id)
    print(ret.shape)

import math

import torch
import torch.nn as nn


class PositionalEmbedding(nn.Module):
    def __init__(self, hidden_size, feature_length):
        super().__init__()
        pe = torch.zeros((feature_length, hidden_size), dtype=torch.float, requires_grad=False)

        position = torch.arange(0, feature_length, dtype=torch.float).unsqueeze(1)
        div_term = (torch.arange(0, hidden_size, 2, dtype=torch.float) * -(math.log(10000.0) / hidden_size)).exp()

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        return self.pe[:, :x.size(1)]


class MultiHeadAttention(nn.Module):
    def __init__(self, hidden_size, heads, dropout_prob):
        super(MultiHeadAttention, self).__init__()
        self.hidden_size = hidden_size
        self.heads = heads
        self.head_dim = hidden_size // heads

        self.scale = 1 / math.sqrt(self.head_dim)

        self.fc_q = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_k = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_v = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_out = nn.Linear(heads * self.head_dim, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.attention_dropout = nn.Dropout(dropout_prob)
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x, cross_x=None, mask=None):
        batch_size, seq_len, _ = x.shape
        query = self.fc_q(x) * self.scale
        query = query.view(batch_size, seq_len, self.heads, self.head_dim).transpose(1, 2).contiguous()

        if cross_x is not None:
            key = self.fc_k(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
        else:
            key = self.fc_k(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()

        prob_shape = (batch_size * self.heads, -1, self.head_dim)
        query, key, value = query.view(*prob_shape), key.view(*prob_shape), value.view(*prob_shape)

        attention = query @ key.transpose(1, 2)

        if mask is not None:
            attention = attention.view(batch_size, self.heads, seq_len, seq_len)
            attention = attention + mask
            attention = attention.view(batch_size * self.heads, seq_len, seq_len)

        attention = torch.softmax(attention, dim=-1)
        attention = self.attention_dropout(attention)

        out = attention @ value
        out = out.view(batch_size, self.heads, seq_len, self.head_dim).transpose(1, 2).contiguous()
        out = out.view(batch_size, seq_len, self.hidden_size)

        out = self.fc_out(out)

        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class FeedForward(nn.Module):
    def __init__(self, hidden_size, ff_size, dropout_prob):
        super(FeedForward, self).__init__()

        self.conv1 = nn.Conv1d(hidden_size, ff_size, kernel_size=1)
        self.conv2 = nn.Conv1d(ff_size, hidden_size, kernel_size=1)

        self.norm = nn.LayerNorm(hidden_size)
        self.activation = nn.GELU()
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x):
        out = x.transpose(-1, 1)
        out = self.conv1(out)
        out = self.activation(out)
        out = self.dropout(out)
        out = self.conv2(out)
        out = out.transpose(-1, 1)
        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class EncoderLayer(nn.Module):
    def __init__(self, hidden_size, heads, ff_size, dropout_prob):
        super(EncoderLayer, self).__init__()
        self.attention = MultiHeadAttention(hidden_size, heads, dropout_prob)
        self.feed_forward = FeedForward(hidden_size, ff_size, dropout_prob)

    def forward(self, x, mask=None):
        out = self.attention(x, mask=mask)
        out = self.feed_forward(out)
        return out


class Embedding(nn.Module):
    def __init__(self, feature_length, feature_size, hidden_size, dropout=0.1):
        super().__init__()

        self.value_embedding = nn.Conv1d(
            feature_size, hidden_size, kernel_size=3, padding=1, padding_mode='circular', bias=False
        )
        nn.init.kaiming_normal_(self.value_embedding.weight)
        self.position_embedding = PositionalEmbedding(hidden_size, feature_length)
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, x):
        out = x.transpose(1, 2)
        out = self.value_embedding(out)
        out = out.transpose(1, 2)
        out += self.position_embedding(x)
        out = self.dropout(out)
        return out


class Transformer(nn.Module):
    def __init__(self, feature_length, feature_size, target_length, target_size, hidden_size, dropout=0.1):
        super().__init__()
        self.task_name = configs.task_name
        self.pred_len = configs.pred_len
        # Embedding
        self.embedding = Embedding(feature_length, feature_size, hidden_size, dropout)
        # Encoder
        self.encoder = Encoder(
            [
                EncoderLayer(
                    AttentionLayer(
                        FullAttention(False, configs.factor, attention_dropout=configs.dropout,
                                      output_attention=False), configs.d_model, configs.n_heads),
                    configs.d_model,
                    configs.d_ff,
                    dropout=configs.dropout,
                    activation=configs.activation
                ) for l in range(configs.e_layers)
            ],
            norm_layer=torch.nn.LayerNorm(configs.d_model)
        )
        # Decoder
        if self.task_name == 'long_term_forecast' or self.task_name == 'short_term_forecast':
            self.dec_embedding = DataEmbedding(configs.dec_in, configs.d_model, configs.embed, configs.freq,
                                               configs.dropout)
            self.decoder = Decoder(
                [
                    DecoderLayer(
                        AttentionLayer(
                            FullAttention(True, configs.factor, attention_dropout=configs.dropout,
                                          output_attention=False),
                            configs.d_model, configs.n_heads),
                        AttentionLayer(
                            FullAttention(False, configs.factor, attention_dropout=configs.dropout,
                                          output_attention=False),
                            configs.d_model, configs.n_heads),
                        configs.d_model,
                        configs.d_ff,
                        dropout=configs.dropout,
                        activation=configs.activation,
                    )
                    for l in range(configs.d_layers)
                ],
                norm_layer=torch.nn.LayerNorm(configs.d_model),
                projection=nn.Linear(configs.d_model, configs.c_out, bias=True)
            )
        if self.task_name == 'imputation':
            self.projection = nn.Linear(configs.d_model, configs.c_out, bias=True)
        if self.task_name == 'anomaly_detection':
            self.projection = nn.Linear(configs.d_model, configs.c_out, bias=True)
        if self.task_name == 'classification':
            self.act = F.gelu
            self.dropout = nn.Dropout(configs.dropout)
            self.projection = nn.Linear(configs.d_model * configs.seq_len, configs.num_class)

    def forecast(self, x_enc, x_mark_enc, x_dec, x_mark_dec):
        # Embedding
        enc_out = self.enc_embedding(x_enc, x_mark_enc)
        enc_out, attns = self.encoder(enc_out, attn_mask=None)

        dec_out = self.dec_embedding(x_dec, x_mark_dec)
        dec_out = self.decoder(dec_out, enc_out, x_mask=None, cross_mask=None)
        return dec_out

    def imputation(self, x_enc, x_mark_enc, x_dec, x_mark_dec, mask):
        # Embedding
        enc_out = self.enc_embedding(x_enc, x_mark_enc)
        enc_out, attns = self.encoder(enc_out, attn_mask=None)

        dec_out = self.projection(enc_out)
        return dec_out

    def anomaly_detection(self, x_enc):
        # Embedding
        enc_out = self.enc_embedding(x_enc, None)
        enc_out, attns = self.encoder(enc_out, attn_mask=None)

        dec_out = self.projection(enc_out)
        return dec_out

    def classification(self, x_enc, x_mark_enc):
        # Embedding
        enc_out = self.enc_embedding(x_enc, None)
        enc_out, attns = self.encoder(enc_out, attn_mask=None)

        # Output
        output = self.act(enc_out)  # the output transformer encoder/decoder embeddings don't include non-linearity
        output = self.dropout(output)
        output = output * x_mark_enc.unsqueeze(-1)  # zero-out padding embeddings
        output = output.reshape(output.shape[0], -1)  # (batch_size, seq_length * d_model)
        output = self.projection(output)  # (batch_size, num_classes)
        return output

    def forward(self, x_enc, x_mark_enc, x_dec, x_mark_dec, mask=None):
        if self.task_name == 'long_term_forecast' or self.task_name == 'short_term_forecast':
            dec_out = self.forecast(x_enc, x_mark_enc, x_dec, x_mark_dec)
            return dec_out[:, -self.pred_len:, :]  # [B, L, D]
        if self.task_name == 'imputation':
            dec_out = self.imputation(x_enc, x_mark_enc, x_dec, x_mark_dec, mask)
            return dec_out  # [B, L, D]
        if self.task_name == 'anomaly_detection':
            dec_out = self.anomaly_detection(x_enc)
            return dec_out  # [B, L, D]
        if self.task_name == 'classification':
            dec_out = self.classification(x_enc, x_mark_enc)
            return dec_out  # [B, N]
        return None
import math
from typing import Dict

import torch
import torch.nn as nn
import gymnasium as gym
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor

from .utils import Normalize


class PositionalEmbedding(nn.Module):
    def __init__(self, hidden_size, feature_length):
        super().__init__()
        pe = torch.zeros((feature_length, hidden_size), dtype=torch.float, requires_grad=False)

        position = torch.arange(0, feature_length, dtype=torch.float).unsqueeze(1)
        div_term = (torch.arange(0, hidden_size, 2, dtype=torch.float) * -(math.log(10000.0) / hidden_size)).exp()

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        pe = pe.unsqueeze(0)
        self.register_buffer("pe", pe)

    def forward(self, x):
        return self.pe[:, : x.size(1)]


class Embedding(nn.Module):
    def __init__(self, feature_length, feature_size, hidden_size, dropout=0.1):
        super().__init__()

        self.value_embedding = nn.Linear(feature_size, hidden_size)
        self.position_embedding = PositionalEmbedding(hidden_size, feature_length)
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, x):
        out = self.value_embedding(x) + self.position_embedding(x)
        out = self.dropout(out)
        return out


class MultiHeadAttention(nn.Module):
    def __init__(self, hidden_size, heads, dropout_prob):
        super(MultiHeadAttention, self).__init__()
        self.hidden_size = hidden_size
        self.heads = heads
        self.head_dim = hidden_size // heads

        self.scale = 1 / math.sqrt(self.head_dim)

        self.fc_q = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_k = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_v = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_out = nn.Linear(heads * self.head_dim, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.attention_dropout = nn.Dropout(dropout_prob)
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x, cross_x=None, mask=None):
        batch_size, seq_len, _ = x.shape
        query = self.fc_q(x) * self.scale
        query = query.view(batch_size, seq_len, self.heads, self.head_dim).transpose(1, 2).contiguous()

        if cross_x is not None:
            key = self.fc_k(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
        else:
            key = self.fc_k(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()

        prob_shape = (batch_size * self.heads, -1, self.head_dim)
        query, key, value = query.view(*prob_shape), key.view(*prob_shape), value.view(*prob_shape)

        attention = query @ key.transpose(1, 2)

        if mask is not None:
            attention = attention.view(batch_size, self.heads, seq_len, seq_len)
            attention = attention + mask
            attention = attention.view(batch_size * self.heads, seq_len, seq_len)

        attention = torch.softmax(attention, dim=-1)
        attention = self.attention_dropout(attention)

        out = attention @ value
        out = out.view(batch_size, self.heads, seq_len, self.head_dim).transpose(1, 2).contiguous()
        out = out.view(batch_size, seq_len, self.hidden_size)

        out = self.fc_out(out)

        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class FeedForward(nn.Module):
    def __init__(self, hidden_size, ff_size, dropout_prob):
        super(FeedForward, self).__init__()

        self.fc1 = nn.Linear(hidden_size, ff_size)
        self.fc2 = nn.Linear(ff_size, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.activation = nn.GELU()
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x):
        out = self.fc1(x)
        out = self.activation(out)
        out = self.dropout(out)
        out = self.fc2(out)
        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class EncoderLayer(nn.Module):
    def __init__(self, hidden_size, heads, ff_size, dropout_prob):
        super(EncoderLayer, self).__init__()
        self.attention = MultiHeadAttention(hidden_size, heads, dropout_prob)
        self.feed_forward = FeedForward(hidden_size, ff_size, dropout_prob)

    def forward(self, x, mask=None):
        out = self.attention(x, mask=mask)
        out = self.feed_forward(out)
        return out


class Transformer(BaseFeaturesExtractor):
    def __init__(self, observation_space: gym.Space, hidden_size=128, ff_size=256, num_layers=2, heads=4, dropout=0.1):
        super().__init__(observation_space, hidden_size)

        assert isinstance(observation_space, gym.spaces.Dict)
        feature_length, feature_size = observation_space["price"].shape
        time_feature_length, time_feature_size = observation_space["time"].shape

        # self.normalize = Normalize()
        # self.normalize = torch.nn.BatchNorm1d(feature_size)
        self.embedding = Embedding(feature_length, feature_size, hidden_size, dropout)
        self.time_embedding = torch.nn.Linear(time_feature_size, hidden_size)
        self.layers = nn.ModuleList([EncoderLayer(hidden_size, heads, ff_size, dropout) for _ in range(num_layers)])

        if "other" in observation_space.keys():
            extra_feature_size = observation_space["other"].shape[0]
            self.fc = nn.Linear(extra_feature_size, hidden_size)

    def forward(self, x: Dict[str, torch.Tensor]) -> torch.Tensor:
        # out = self.normalize(x["price"])
        out = x["price"]
        # out = self.normalize(x["price"].transpose(1, 2)).transpose(1, 2)
        out = self.embedding(out)
        # out += self.fc(x['other']).unsqueeze(1)
        out += self.time_embedding(x["time"])
        for layer in self.layers:
            out = layer(out)

        out = out.mean(dim=1)

        if "other" in x:
            out += self.fc(x["other"])
        return out

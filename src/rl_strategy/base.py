import datetime
import json
import pathlib
import re
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, ClassVar, Dict, List, Literal, Optional, Tuple, Type, Union

import datetype
import numpy as np
import numpy.typing as npt
import pandas as pd
from loguru import logger

from .constant import CLOSE, HIGH, LOW, OPEN, TIME, VOLUME
from .utils import prepare_daily_data, prepare_instrument_info, prepare_minute_data


@dataclass
class InstrumentInfo:
    symbol: str
    commission_ratio: float
    """费率"""
    stamp_tax: float
    """印花税"""
    minium_commission: float
    """最小手续费"""
    minium_round_lot: float
    """最小交易手数"""
    lot_step: float
    """交易手数步长"""
    minium_price_change: float
    """最小价格变化"""


class InstrumentDataHandler(ABC):
    FQ: Literal["pre", "post", None] = "post"
    PRICE_DIR = "prices"

    _symbol: str
    _start_dt0: datetype.NaiveDateTime
    """start datetime, including forward days"""
    _start_dt1: datetype.NaiveDateTime
    """the beginning of the time interval during which the reinforcement learning algorithm samples data"""
    _end_dt: datetype.NaiveDateTime
    """the end of the time interval during which the reinforcement learning algorithm samples data"""

    instrument_info: InstrumentInfo
    minute_price_df: pd.DataFrame
    daily_price_df: pd.DataFrame

    _trade_dates: List[datetime.date]
    """所有交易日，包含预留的时间"""
    _date2time: Dict[datetime.date, List[datetype.NaiveDateTime]]
    """key是date,value是该date第一条和最后一条数据的时间，包含预留的时间"""

    # 根据策略步进频率，保存的数据
    quote: npt.NDArray[np.float64]

    def __init__(
        self,
        symbol: str,
        start_dt: Optional[str],
        end_dt: Optional[str],
        required: List[str],
        forward_days: Union[int, str],
        instrument_info: Optional[Dict[str, Dict[str, float]]],
    ):
        """数据管理

        Args:
            symbol (str): 标的代码
            start_dt (Optional[str]): 有效数据开始时间
            end_dt (Optional[str]): 有效数据结束时间
            required (List[str]): 可以填"minute", "daily"
            forward_days (int): 预留数据天数
            instrument_info (Optional[Dict[str, Dict[str, float]]]): 标的信息
        """
        self._symbol = symbol

        data_start_dt = pd.Timestamp.min
        data_end_dt = pd.Timestamp.max
        if "minute" in required:
            data_file = pathlib.Path(self.PRICE_DIR, f"{symbol.replace('.', '_')}_MINUTE.csv")
            if not data_file.exists():
                prepare_minute_data(symbol, "2020-01-01", pd.Timestamp.now().strftime("%Y-%m-%d"), data_file)
            self.minute_price_df = pd.read_csv(data_file, index_col=0, parse_dates=True)
            if self.minute_price_df.empty:
                raise pd.errors.EmptyDataError(f"{symbol} 分钟数据文件为空")
            tmp = self.minute_price_df.index.indexer_at_time("15:00")
            data_start_dt = max(data_start_dt, self.minute_price_df.index[tmp[0]].normalize())
            data_end_dt = min(data_end_dt, self.minute_price_df.index[tmp[-1]].normalize())

        if "daily" in required:
            data_file = pathlib.Path(self.PRICE_DIR, f"{symbol.replace('.', '_')}_DAILY.csv")
            if not data_file.exists():
                prepare_daily_data(symbol, "2020-01-01", pd.Timestamp.now().strftime("%Y-%m-%d"), data_file)
            self.daily_price_df = pd.read_csv(data_file, index_col=0, parse_dates=True)
            if self.daily_price_df.empty:
                raise pd.errors.EmptyDataError(f"{symbol} 日线数据文件为空")
            data_start_dt = max(data_start_dt, self.daily_price_df.index[0].normalize())
            data_end_dt = min(data_end_dt, self.daily_price_df.index[-1].normalize())

        tmp1, tmp2 = self._time2trade_day()
        date2time = {k: tmp1[np.where(tmp2 == k)[0][[0, -1]]].tolist() for k in np.unique(tmp2[~pd.isna(tmp2)])}  # type: ignore
        trade_dates = sorted(list(date2time.keys()))

        st1 = pd.Timestamp(f"{start_dt}") if start_dt else pd.Timestamp.min
        if isinstance(forward_days, str):
            st0 = pd.Timestamp(f"{forward_days}")
            forward_days = (st1 - st0).days
        else:
            st0 = st1 - pd.Timedelta(days=forward_days)
        et = pd.Timestamp(f"{end_dt}") if end_dt else pd.Timestamp.max

        st0 = max(data_start_dt, st0)
        st1 = st0 + pd.Timedelta(days=forward_days)
        et = min(data_end_dt, et)

        idx = np.searchsorted(trade_dates, st0.date(), "left")
        if idx == len(trade_dates):
            raise pd.errors.OutOfBoundsDatetime(f"指定起始时间0{st0.date()}大于数据结束时间{trade_dates[-1]}")
        st0 = trade_dates[idx]

        idx = np.searchsorted(trade_dates, st1.date(), "left")
        if idx == len(trade_dates):
            raise pd.errors.OutOfBoundsDatetime(f"指定起始时间1{st1.date()}大于数据结束时间{trade_dates[-1]}")
        st1 = trade_dates[idx]

        idx = np.searchsorted(trade_dates, et.date(), "right")
        if idx == 0:
            raise pd.errors.OutOfBoundsDatetime(f"指定结束时间{et.date()}小于数据起始时间{trade_dates[0]}")
        et = trade_dates[idx - 1]

        if not st0 <= st1 <= et:
            raise pd.errors.OutOfBoundsDatetime(f"时间混乱，st0: {st0}, st1: {st1}, et: {et}")

        self._start_dt0, self._start_dt1, self._end_dt = date2time[st0][0], date2time[st1][0], date2time[et][1]

        logger.trace(
            f"symbol: {self._symbol}, start_dt0: {self._start_dt0}, start_dt1: {self._start_dt1}, end_dt: {self._end_dt}"
        )

        # 加载基础数据
        if "minute" in required:
            self._preprocess_minute_data()
            self.minute_price_df = self.minute_price_df.loc[
                (self.minute_price_df.index >= self._start_dt0) & (self.minute_price_df.index <= self._end_dt)
            ]

        if "daily" in required:
            self._preprocess_daily_data()
            self.daily_price_df = self.daily_price_df.loc[
                (self.daily_price_df.index >= self._start_dt0) & (self.daily_price_df.index <= self._end_dt)
            ]

        # 计算交易日相关信息
        self.instrument_info = self._get_instrument_info(instrument_info)

        self._date2time = {k: v for k, v in date2time.items() if k >= st0 and k <= et}
        self._trade_dates = sorted(list(self._date2time.keys()))

        self.quote = np.full((len(self.minute_price_df), 6), np.nan, dtype=np.float64)
        self.quote[:, TIME] = self.minute_price_df.index.astype(np.int64).values
        self.quote[:, OPEN] = self.minute_price_df["open"].to_numpy()
        self.quote[:, HIGH] = self.minute_price_df["high"].to_numpy()
        self.quote[:, LOW] = self.minute_price_df["low"].to_numpy()
        self.quote[:, CLOSE] = self.minute_price_df["close"].to_numpy()
        self.quote[:, VOLUME] = self.minute_price_df["volume"].to_numpy()

    @property
    def trade_dates_of_sampling(self) -> List[datetime.date]:
        """返回用于采样的交易日"""
        t1, t2 = self._start_dt1.date(), self._end_dt.date()
        return [i for i in self._trade_dates if t1 <= i <= t2]  # type: ignore

    @property
    def total_trade_dates(self) -> List[datetime.date]:
        """返回所有交易日，包含了预留数据"""
        return self._trade_dates

    def get_co_time(self, t: datetime.date) -> List[datetype.NaiveDateTime]:
        """返回指定交易日的开始和结束时间，可以取到预留部分"""
        return self._date2time[t]

    @property
    def symbol(self):
        return self._symbol

    def _get_instrument_info(self, instrument_info: Optional[Dict[str, Dict[str, float]]]):
        instrument_info = instrument_info or {}
        return InstrumentInfo(
            symbol=self.symbol,
            commission_ratio=instrument_info.get(self.symbol, {}).get("commission_ratio", 1e-3),
            stamp_tax=instrument_info.get(self.symbol, {}).get("stamp_tax", 0.0),
            minium_commission=instrument_info.get(self.symbol, {}).get("minium_commission", 0.0),
            minium_round_lot=instrument_info.get(self.symbol, {}).get("minium_round_lot", 0.0),
            lot_step=instrument_info.get(self.symbol, {}).get("lot_step", 0.0),
            minium_price_change=instrument_info.get(self.symbol, {}).get("minium_price_change", 0.0),
        )

    def _preprocess_minute_data(self):
        """处理分钟数据，在init中加载分钟数据时执行"""
        pass

    def _preprocess_daily_data(self):
        """处理日频数据，在init中加载日频数据时执行"""
        pass

    def post_init(self, **kwargs):
        """在init后执行，用于生成features以及对features进行预处理"""
        pass

    @abstractmethod
    def _bind_backtest_type(self) -> Type["BackTest"]: ...

    def create_backtest(self, *args, **kwargs) -> "BackTest":
        return self._bind_backtest_type()(self, *args, **kwargs)

    @staticmethod
    @abstractmethod
    def check(*args, **kwargs) -> pd.DataFrame:
        """check whether the datetime is valid for this instrument

        Returns:
            pd.DataFrame: dataframe whose index is datetime, column is symbol and value is bool
        """
        ...

    @abstractmethod
    def _time2trade_day(self) -> Tuple[pd.DatetimeIndex, np.ndarray]:
        """convert datetime to trade day

        Returns:
            Tuple[pd.DatetimeIndex, np.ndarray]: DatetimeIndex and ndarray of trade day
        """
        ...


class RealInstrumentDataHandler(InstrumentDataHandler):
    SEC_INFO_PATH = "sec_info.json"
    SUB_TYPE = {
        "stock": {r"^68(.+)": "stock_stb"},  # 科创板
        "fund": {r"^(5116|5117|5118|5119|1590)(.+)": "fund_cur"},  # 货币型基金
    }
    FEE_INFO = {
        "stock": {
            "commission_ratio": 0.0005,
            "stamp_tax": 0.0005,
            "minium_commission": 5,
            "minium_round_lot": 100,
            "lot_step": 100,
            "minium_price_change": 1e-2,
        },
        "stock_stb": {
            "commission_ratio": 0.0005,
            "stamp_tax": 0.0005,
            "minium_commission": 5,
            "minium_round_lot": 200,
            "lot_step": 1,
            "minium_price_change": 1e-2,
        },
        "fund": {
            "commission_ratio": 0.0005,
            "stamp_tax": 0,
            "minium_commission": 0,
            "minium_round_lot": 100,
            "lot_step": 100,
            "minium_price_change": 1e-3,
        },
        "fund_cur": {
            "commission_ratio": 0,
            "stamp_tax": 0,
            "minium_commission": 0,
            "minium_round_lot": 100,
            "lot_step": 100,
            "minium_price_change": 1e-3,
        },
        "cbond": {
            "commission_ratio": 0.0002,
            "stamp_tax": 0,
            "minium_commission": 1,
            "minium_round_lot": 10,
            "lot_step": 10,
            "minium_price_change": 1e-3,
        },
    }
    if not pathlib.Path(SEC_INFO_PATH).exists():
        prepare_instrument_info()
    sec_info: ClassVar[Dict] = json.load(open(SEC_INFO_PATH, "r"))

    @classmethod
    def get_sinfo(cls, stype, symbol):
        stype = "fund" if stype in ["etf", "lof", "qdii", "fja", "fjb", "fund"] else stype
        for k, v in cls.SUB_TYPE.get(stype, {}).items():
            if re.match(k, symbol):
                return cls.FEE_INFO[v]
        return cls.FEE_INFO[stype]

    def _get_instrument_info(self, instrument_info: Optional[Dict[str, Dict[str, float]]] = None):
        if self.symbol not in self.sec_info.keys():
            prepare_instrument_info(self.symbol)
        sec_info = self.sec_info[self.symbol]
        assert (pd.Timestamp.now() - pd.Timestamp(sec_info["start_date"])).days >= 30, (  # type: ignore
            "上市时间不足1月不支持回测"
        )  # 产品要求
        fee_info = self.get_sinfo(sec_info["type"], self.symbol)
        return InstrumentInfo(
            symbol=self.symbol,
            commission_ratio=fee_info.get("commission_ratio", 2e-4),
            stamp_tax=fee_info.get("stamp_tax", 5e-4),
            minium_commission=fee_info.get("minium_commission", 5.0),
            minium_round_lot=fee_info.get("minium_round_lot", 100),
            lot_step=fee_info.get("lot_step", 100),
            minium_price_change=fee_info.get("minium_price_change", 1e-2),
        )


class BackTest(ABC):
    data_handler: InstrumentDataHandler
    begin_time: datetype.NaiveDateTime
    end_time: datetype.NaiveDateTime

    feature_length: int
    observe_features: List[str]
    feature_freq: int
    evaluate_days: int
    action_freq: int

    result: npt.NDArray
    idx: int

    actions: List

    def __init__(
        self,
        data_handler: InstrumentDataHandler,
        begin_time: datetype.NaiveDateTime,
        end_time: datetype.NaiveDateTime,
        feature_length: int,
        observe_features: List[str],
        feature_freq: int,
        evaluate_days: int,
        action_freq: int,
    ):
        self.data_handler = data_handler
        self.begin_time = begin_time
        self.end_time = end_time

        self.feature_length = feature_length
        self.observe_features = observe_features
        self.feature_freq = feature_freq
        self.evaluate_days = evaluate_days
        self.action_freq = action_freq

        self.idx = 0
        self.actions = []

    def step(self, action: Optional[Any]):
        """The reinforcement learning algorithm moves forward one step, takes one action, and then proceeds for N steps without taking any further actions.

        Args:
            action (Optional[Any]): the action taken by the reinforcement learning algorithm
        """
        self.actions.append(action)
        self._step(action)

        if not self.is_done() and self.action_freq - 1 > 0:
            i = 0
            while True:
                tmp = self._step()
                if self.is_done():
                    break
                i += 1
                if tmp and i >= self.action_freq - 1:
                    break

    def is_done(self) -> bool:
        """check whether the backtest is done

        Returns:
            bool: True if the backtest is done, otherwise False
        """
        return self.idx >= self.result.shape[0]

    @abstractmethod
    def _step(self, *args, **kwargs) -> bool:
        """execute strategy logic

        Returns:
            bool: True if the strategy needs more actions provided by algorithm, otherwise False
        """
        ...

    @abstractmethod
    def get_reward(self) -> Tuple[float, Dict]:
        """get reward and info"""
        ...

    @abstractmethod
    def get_observation(self) -> Dict[str, np.ndarray]: ...

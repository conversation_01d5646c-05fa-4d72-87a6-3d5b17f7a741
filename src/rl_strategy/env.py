import datetime
import traceback
from abc import ABC, abstractmethod
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
from enum import Enum
from typing import Any, Callable, List, Mapping, Optional, Tuple, Type

import datetype
import gymnasium as gym
import numpy as np
import pandas as pd
from loguru import logger
from stable_baselines3.common.base_class import BaseAlgorithm

from aichemy.utils import is_notebook

from .base import BackTest, InstrumentDataHandler
from .utils import reseed_everything

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm


class StrategyEnv(gym.Env, ABC):
    feature_length: int
    observe_features: List[str]
    feature_freq: int
    evaluate_days: int
    action_freq: int
    instrument_info: Optional[Mapping[str, Mapping[str, float]]]
    config: Mapping

    action_space: gym.Space
    observation_space: gym.Space
    record: Callable
    engine: "BackTestEngine"

    def __init__(
        self,
        feature_length: int,
        observe_features: List[str],
        feature_freq: int,
        evaluate_days: int,
        action_freq: int,
        test_size: float,
        symbols: List[str],
        start_dt: Optional[str] = None,
        end_dt: Optional[str] = None,
        specify_instrument_info: Optional[Mapping[str, Mapping[str, float]]] = None,
        **config,
    ):
        """
        交易环境

        参数:
            feature_length (int): 价差回溯的天数
            observe_features (List[str]): 价差回溯的特征，open, high, low, close, volume, turnover
            feature_freq (int): 价差回溯的采样频率
            evaluate_days (int): 交易目标的天数
            action_freq (int): 交易动作的频率
            test_size (float): 测试集的比例
            symbols (List[str]): 证券代码
            start_dt (Optional[str]): 开始日期
            end_dt (Optional[str]): 结束日期
            specify_instrument_info (Optional[Mapping[str, Mapping[str, float]]]): 指定的标的信息，比如交易费率等
        """
        super().__init__()
        self.feature_length = feature_length
        self.observe_features = observe_features
        self.feature_freq = feature_freq
        self.evaluate_days = evaluate_days
        self.action_freq = action_freq
        self.instrument_info = specify_instrument_info
        self.config = config

        self.action_space = self._define_action_space()
        self.observation_space = self._define_observation_space()
        self.record = self._define_record()
        self.engine = BackTestEngine(
            feature_length,
            observe_features,
            feature_freq,
            evaluate_days,
            action_freq,
            test_size,
            self._bind_data_handler_type(),
            symbols,
            start_dt,
            end_dt,
            specify_instrument_info,
            **config,
        )

    def create_test_env(
        self,
        symbols: List[str],
        start_dt: Optional[str],
        end_dt: Optional[str],
        specify_instrument_info: Optional[Mapping[str, Mapping[str, float]]] = None,
        **config,
    ):
        return self.__class__(
            self.feature_length,
            self.observe_features,
            self.feature_freq,
            self.evaluate_days,
            self.action_freq,
            1.0,
            symbols,
            start_dt,
            end_dt,
            specify_instrument_info or self.instrument_info,
            **(config or self.config),
        )

    @abstractmethod
    def _bind_data_handler_type(self) -> Type[InstrumentDataHandler]:
        """绑定data handler类型"""
        ...

    @abstractmethod
    def _define_action_space(self) -> gym.Space:
        """定义动作空间"""
        ...

    @abstractmethod
    def _define_observation_space(self) -> gym.Space:
        """定义观测空间"""
        ...

    def _define_record(self) -> Callable:
        def fnc(*args, **kwargs):
            return True, {}

        return fnc

    def step(self, action):
        obs, reward, done, info = self.engine.step_env(action)
        return obs, reward, done, False, info

    def reset(self, *, seed=None, options=None):
        reseed_everything(seed)
        obs, info = self.engine.reset_env()
        return obs, info

    def test_model(self, model: Optional[BaseAlgorithm]):
        self.engine.eval()
        infos = []
        while True:
            obs, info = self.engine.reset_env()
            if info:
                break
            while True:
                if model is not None:
                    action, _ = model.predict(obs, deterministic=True)  # type: ignore
                    obs, _, done, info = self.engine.step_env(action)
                else:
                    obs, _, done, info = self.engine.step_env(None)
                infos.append(info)
                if done:
                    break
        self.engine.train()
        return infos

    def on_rollout_start(self):
        pass


class EngineMode(Enum):
    UNINITIALIZED = 0
    TRAIN = 1
    TEST = 2


def _sub_process_create_engine(args):
    instrument_data_handler_type, symbol, start_dt, end_dt, specify_instrument_info, config = args
    try:
        engine = instrument_data_handler_type(symbol, start_dt, end_dt, specify_instrument_info, **config)
        return engine
    except Exception:
        logger.warning(traceback.format_exc())
        logger.warning(f"数据加载失败，symbol：{symbol}")
        return None


class BackTestEngine:
    mode: EngineMode

    feature_length: int
    observe_features: List[str]
    feature_freq: int
    evaluate_days: int
    action_freq: int

    symbols: List[str]
    dates: List[datetime.date]
    """所有symbol的交易日汇总"""
    data_handlers: Mapping[str, InstrumentDataHandler]
    date_symbol_valid: pd.DataFrame

    date_interval: int
    train_size: int

    symbol_index: int
    date_index: int
    bct: BackTest

    current_date_index: int
    current_symbol_index: int
    current_bct: BackTest

    def __init__(
        self,
        feature_length: int,
        observe_features: List[str],
        feature_freq: int,
        evaluate_days: int,
        action_freq: int,
        test_size: float,
        instrument_data_handler_type: Type[InstrumentDataHandler],
        symbols: List[str],
        start_dt: Optional[str],
        end_dt: Optional[str],
        specify_instrument_info: Optional[Mapping[str, Mapping[str, float]]],
        **config,
    ):
        self.mode = EngineMode.UNINITIALIZED

        self.feature_length = feature_length
        self.observe_features = observe_features
        self.feature_freq = feature_freq
        self.evaluate_days = evaluate_days
        self.action_freq = action_freq

        self.symbols = []
        self.data_handlers = {}

        config = config.copy()
        config.update(
            {
                "feature_length": feature_length,
                "observe_features": observe_features,
                "feature_freq": feature_freq,
                "evaluate_days": evaluate_days,
                "action_freq": action_freq,
            }
        )
        if (n_core := min(len(symbols), config.get("n_core", 1))) > 1:
            with ProcessPoolExecutor(max_workers=min(n_core, len(symbols))) as p:
                futures = [
                    p.submit(
                        _sub_process_create_engine,
                        (instrument_data_handler_type, symbol, start_dt, end_dt, specify_instrument_info, config),
                    )
                    for symbol in symbols
                ]
                res = [future.result() for future in tqdm(as_completed(futures), total=len(symbols))]
        else:
            res = [
                _sub_process_create_engine(
                    (instrument_data_handler_type, symbol, start_dt, end_dt, specify_instrument_info, config)
                )
                for symbol in tqdm(symbols, total=len(symbols))
            ]
        for i, j in zip(symbols, res):
            if j is not None:
                self.symbols.append(i)
                self.data_handlers[i] = j

        self.register_date_symbol_valid(None, instrument_data_handler_type)

        self.train_size = len(self.dates) - int(test_size * len(self.dates))
        self.date_interval = 0

        self.date_index = len(self.dates)
        self.symbol_index = len(self.symbols)

        self.current_date_index = len(self.dates)
        self.current_symbol_index = len(self.symbols)

        self.train()
        logger.info(
            "初始化完成，采样日期数量为：{}，采样标的数量为：{}，训练区间为：{}~{}，样本量为：{}，验证区间为：{}~{}".format(
                len(self.dates),
                len(self.symbols),
                *self.get_train_interval(),
                self.num_samples,
                *self.get_valid_interval(),
            )
        )

    def register_date_symbol_valid(
        self,
        date_symbol_valid: Optional[pd.DataFrame],
        instrument_data_handler_type: Optional[Type[InstrumentDataHandler]],
    ):
        dates = []
        for data_handler in self.data_handlers.values():
            dates.extend(data_handler.trade_dates_of_sampling)
        self.dates = sorted(list(set(dates)))

        if date_symbol_valid is None and instrument_data_handler_type is not None:
            if hasattr(self, "date_symbol_valid"):
                raise AssertionError("date_symbol_valid已经存在，不能再次执行instrument_data_handler_type.check")
            date_symbol_valid = instrument_data_handler_type.check(self)
            self.date_symbol_valid = date_symbol_valid.reindex(index=self.dates, columns=self.symbols, fill_value=False)

        elif date_symbol_valid is not None and instrument_data_handler_type is None:
            if not hasattr(self, "date_symbol_valid"):
                raise AssertionError("date_symbol_valid不存在，必须先执行instrument_data_handler_type.check")
            date_symbol_valid = date_symbol_valid.reindex(
                index=self.date_symbol_valid.index, columns=self.date_symbol_valid.columns, fill_value=False
            )
            self.date_symbol_valid = date_symbol_valid & self.date_symbol_valid

        else:
            raise ValueError("date_symbol_valid和instrument_data_handler_type必须且只能填写一个")

    def post_init(self, **kargs):
        for i in self.data_handlers.values():
            i.post_init(**kargs)

    def get_train_interval(self) -> Tuple[Optional[datetime.date], Optional[datetime.date]]:
        """return begin_date and end_date of time interval for training in which both endpoints are included"""
        tmp = self.date_symbol_valid.any(axis=1)
        tmp = tmp.loc[tmp.index.isin(self.dates[: self.train_size])]
        return tmp.where(tmp).first_valid_index(), tmp.where(tmp).last_valid_index()  # type: ignore

    def get_valid_interval(self) -> Tuple[Optional[datetime.date], Optional[datetime.date]]:
        """return begin_date and end_date of time interval for validation in which both endpoints are included"""
        tmp = self.date_symbol_valid.any(axis=1)
        tmp = tmp.loc[tmp.index.isin(self.dates[self.train_size :])]
        return tmp.where(tmp).first_valid_index(), tmp.where(tmp).last_valid_index()  # type: ignore

    @property
    def num_samples(self):
        """return total number of samples for training, determined by symbol and date"""
        tmp_ = self.get_train_interval()
        tmp = self.date_symbol_valid.loc[
            (tmp_[0] <= self.date_symbol_valid.index) & (self.date_symbol_valid.index <= tmp_[1])
        ]
        return np.sum(tmp.to_numpy())

    @property
    def num_valid_samples(self):
        tmp_ = self.get_valid_interval()
        tmp = self.date_symbol_valid.loc[
            (tmp_[0] <= self.date_symbol_valid.index) & (self.date_symbol_valid.index <= tmp_[1])
        ]
        return np.sum(tmp.to_numpy())

    @property
    def data_handler(self):
        return self.data_handlers[self.symbol]

    @property
    def symbol(self):
        return self.symbols[self.symbol_index]

    @property
    def date(self):
        return self.dates[self.date_index]

    def train(self):
        if not self.mode.value == EngineMode.TRAIN.value:
            self.date_interval = 1
            if self.mode.value == EngineMode.UNINITIALIZED.value:
                self.date_index = 0
                self.symbol_index = -1
            else:
                self.date_index = self.current_date_index
                self.symbol_index = self.current_symbol_index
                if hasattr(self, "current_bct"):
                    self.bct = self.current_bct

            self.mode = EngineMode.TRAIN

    def eval(self):
        if self.mode.value == EngineMode.TRAIN.value:
            if len(self.dates) - self.train_size < self.evaluate_days:
                raise RuntimeError("测试区间太短，需要大于等于{}个交易日".format(self.evaluate_days))
            self.date_interval = self.evaluate_days
            self.current_date_index, self.current_symbol_index = self.date_index, self.symbol_index
            if hasattr(self, "bct"):
                self.current_bct = self.bct
            self.date_index, self.symbol_index = self.train_size, -1

            self.mode = EngineMode.TEST

    def _index_shift(self) -> Mapping:
        """移动索引，先移动symbol，遍历完symbol再移动date，如果date超出范围，则重置date_index，并且返回的info中包含end_date，否则info为空"""
        if np.sum(self.date_symbol_valid.to_numpy()) == 0:
            raise AssertionError("没有可用样本")

        info = {}
        while True:
            self.symbol_index += 1
            if len(self.symbols) == self.symbol_index:
                self.symbol_index = 0
                self.date_index += self.date_interval

            if self.mode.value == EngineMode.TRAIN.value:
                if self.date_index >= self.train_size:
                    self.date_index = 0
                    info["end_date"] = self.dates[min(self.date_index, len(self.dates) - 1)]
            elif self.mode == EngineMode.TEST:
                if len(self.dates) - self.date_index < self.evaluate_days:
                    self.date_index = self.train_size
                    info["end_date"] = self.dates[min(self.date_index, len(self.dates) - 1)]

            if self.date_symbol_valid.at[self.date, self.symbol]:
                break

        return info

    def reset_env(self) -> Tuple[Mapping[str, np.ndarray], Mapping]:
        """用于重置环境\n
        先移动索引，再创建backtest，最后返回observation和info\n
        如果date超出范围，则重置date_index，并且返回的info中包含end_date，否则info为空

        Returns:
            Tuple[Mapping[str, np.ndarray], Mapping]: observation, info
        """
        while True:
            info = self._index_shift()

            try:
                bar_begin: datetype.NaiveDateTime = self.data_handler.get_co_time(self.dates[self.date_index])[0]
                bar_end: datetype.NaiveDateTime = self.data_handler.get_co_time(
                    self.dates[self.date_index + self.evaluate_days - 1]
                )[1]
                if bar_begin >= bar_end:
                    raise

                self.bct = self.data_handler.create_backtest(
                    bar_begin,
                    bar_end,
                    self.feature_length,
                    self.observe_features,
                    self.feature_freq,
                    self.evaluate_days,
                    self.action_freq,
                )
                obs = self.bct.get_observation()
                if isinstance(obs, dict):
                    for k, v in obs.items():
                        if not np.all(np.isfinite(v)):
                            raise pd.errors.DataError(k)
                elif isinstance(obs, tuple):
                    for i, j in enumerate(obs):
                        if not np.all(np.isfinite(j)):
                            raise pd.errors.DataError(i)
                else:
                    if not np.all(np.isfinite(obs)):
                        raise pd.errors.DataError
                return obs, info

            except pd.errors.DataError as e:
                logger.warning(
                    f"{self.mode.name} {self.date} {self.symbol} 跳过，原因是observation[{str(e)}]中有异常值"
                )

            except Exception:
                logger.trace(traceback.format_exc())
                logger.warning(f"{self.mode.name} {self.date} {self.symbol} 跳过")

            self.date_symbol_valid.at[self.date, self.symbol] = False

    def step_env(self, action: Optional[Any]) -> Tuple[Optional[Any], float, bool, Mapping]:
        """用于步进环境，如果done，则返回的info不为空，否则info为空

        Args:
            action (Optional[Any]): 动作

        Returns:
            Tuple[Optional[Any], float, bool, Mapping]: observation, reward, done, info
        """
        self.bct.step(action)
        reward, info = self.bct.get_reward()
        done = self.bct.is_done()

        if done:
            obs = None
        else:
            obs = self.bct.get_observation()
        return obs, reward, done, info

# -*- coding:utf-8 -*-    --------------Ashare 股票行情数据双核心版( https://github.com/mpquant/Ashare )
import asyncio
import datetime
import json
import threading
from typing import Literal

import aiohttp
import pandas as pd
import requests


async def async_http_get(url):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.text()


def get_price_day_tx(code, end_date="", count=10, frequency="1d"):  # 日线获取
    """获取腾讯日线"""
    unit = "week" if frequency in "1w" else "month" if frequency in "1M" else "day"  # 判断日线，周线，月线
    if end_date:
        end_date = end_date.strftime("%Y-%m-%d") if isinstance(end_date, datetime.date) else end_date.split(" ")[0]
    end_date = "" if end_date == datetime.datetime.now().strftime("%Y-%m-%d") else end_date  # 如果日期今天就变成空
    URL = f"http://web.ifzq.gtimg.cn/appstock/app/fqkline/get?param={code},{unit},,{end_date},{count},qfq"
    st = json.loads(requests.get(URL).content)
    ms = "qfq" + unit
    stk = st["data"][code]
    buf = stk[ms] if ms in stk else stk[unit]  # 指数返回不是qfqday,是day
    df = pd.DataFrame(buf, columns=["time", "open", "close", "high", "low", "volume"], dtype="float")
    df.time = pd.to_datetime(df.time)
    df.set_index(["time"], inplace=True)
    df.index.name = ""  # 处理索引
    return df


async def async_get_price_day_tx(code, end_date="", count=10, frequency="1d"):  # 日线获取
    """异步, 获取腾讯日线"""
    unit = "week" if frequency in "1w" else "month" if frequency in "1M" else "day"  # 判断日线，周线，月线
    if end_date:
        end_date = end_date.strftime("%Y-%m-%d") if isinstance(end_date, datetime.date) else end_date.split(" ")[0]
    end_date = "" if end_date == datetime.datetime.now().strftime("%Y-%m-%d") else end_date  # 如果日期今天就变成空
    URL = f"http://web.ifzq.gtimg.cn/appstock/app/fqkline/get?param={code},{unit},,{end_date},{count},qfq"
    res = await async_http_get(URL)
    st = json.loads(res)
    ms = "qfq" + unit
    stk = st["data"][code]
    buf = stk[ms] if ms in stk else stk[unit]  # 指数返回不是qfqday,是day
    df = pd.DataFrame(buf, columns=["time", "open", "close", "high", "low", "volume"], dtype="float")
    df.time = pd.to_datetime(df.time)
    df.set_index(["time"], inplace=True)
    df.index.name = ""  # 处理索引
    return df


def get_price_min_tx(code, end_date=None, count=10, frequency="1d"):  # 分钟线获取
    """获取腾讯分钟线"""
    ts = int(frequency[:-1]) if frequency[:-1].isdigit() else 1  # 解析K线周期数
    if end_date:
        end_date = end_date.strftime("%Y-%m-%d") if isinstance(end_date, datetime.date) else end_date.split(" ")[0]
    URL = f"http://ifzq.gtimg.cn/appstock/app/kline/mkline?param={code},m{ts},,{count}"
    st = json.loads(requests.get(URL).content)
    buf = st["data"][code]["m" + str(ts)]
    df = pd.DataFrame(buf, columns=["time", "open", "close", "high", "low", "volume", "n1", "n2"])
    df = df[["time", "open", "close", "high", "low", "volume"]]
    df[["open", "close", "high", "low", "volume"]] = df[["open", "close", "high", "low", "volume"]].astype("float")
    df.time = pd.to_datetime(df.time)
    df.set_index(["time"], inplace=True)
    df.index.name = ""  # 处理索引
    df.loc[df.index[-1], "close"] = float(st["data"][code]["qt"][code][3])  # 最新基金数据是3位的

    # 腾讯的分钟K线包含0930，把这个K线合并到0931中
    tmp = df.loc[list(map(lambda x: x.strftime("%H:%M") == "09:30", df.index))]
    tmp.index = pd.Index(map(lambda x: x.strftime("%Y-%m-%d"), tmp.index))
    tmp1 = tmp["open"].to_dict()
    tmp2 = tmp["volume"].to_dict()
    for i in df.loc[list(map(lambda x: x.strftime("%H:%M") == "09:31", df.index))].index:
        if (ii := i.strftime("%Y-%m-%d")) in tmp.index:
            df.loc[i, "open"] = tmp1[ii]
            df.loc[i, "volume"] += tmp2[ii]

    df = df.loc[list(map(lambda x: x.time() != datetime.time(9, 30), df.index))]
    return df


async def async_get_price_min_tx(code, end_date=None, count=10, frequency="1d"):  # 分钟线获取
    """异步，获取腾讯分钟线"""
    ts = int(frequency[:-1]) if frequency[:-1].isdigit() else 1  # 解析K线周期数
    if end_date:
        end_date = end_date.strftime("%Y-%m-%d") if isinstance(end_date, datetime.date) else end_date.split(" ")[0]
    URL = f"http://ifzq.gtimg.cn/appstock/app/kline/mkline?param={code},m{ts},,{count}"
    res = await async_http_get(URL)
    st = json.loads(res)
    buf = st["data"][code]["m" + str(ts)]
    df = pd.DataFrame(buf, columns=["time", "open", "close", "high", "low", "volume", "n1", "n2"])
    df = df[["time", "open", "close", "high", "low", "volume"]]
    df[["open", "close", "high", "low", "volume"]] = df[["open", "close", "high", "low", "volume"]].astype("float")
    df.time = pd.to_datetime(df.time)
    df.set_index(["time"], inplace=True)
    df.index.name = ""  # 处理索引
    df.loc[df.index[-1], "close"] = float(st["data"][code]["qt"][code][3])  # 最新基金数据是3位的

    # 腾讯的分钟K线包含0930，把这个K线合并到0931中
    tmp = df.loc[list(map(lambda x: x.strftime("%H:%M") == "09:30", df.index))]
    tmp.index = pd.Index(map(lambda x: x.strftime("%Y-%m-%d"), tmp.index))
    tmp1 = tmp["open"].to_dict()
    tmp2 = tmp["volume"].to_dict()
    for i in df.loc[list(map(lambda x: x.strftime("%H:%M") == "09:31", df.index))].index:
        if (ii := i.strftime("%Y-%m-%d")) in tmp.index:
            df.loc[i, "open"] = tmp1[ii]
            df.loc[i, "volume"] += tmp2[ii]

    df = df.loc[list(map(lambda x: x.time() != datetime.time(9, 30), df.index))]
    return df


# sina新浪全周期获取函数，分钟线 5m,15m,30m,60m  日线1d=240m   周线1w=1200m  1月=7200m
def get_price_sina(code, end_date="", count=10, frequency="60m"):  # 新浪全周期获取函数
    frequency = frequency.replace("1d", "240m").replace("1w", "1200m").replace("1M", "7200m")
    mcount = count
    ts = int(frequency[:-1]) if frequency[:-1].isdigit() else 1  # 解析K线周期数
    if (end_date != "") & (frequency in ["240m", "1200m", "7200m"]):
        end_date = (
            pd.to_datetime(end_date).to_pydatetime() if not isinstance(end_date, datetime.datetime) else end_date
        )  # 转换成datetime
        unit = 4 if frequency == "1200m" else 29 if frequency == "7200m" else 1  # 4,29多几个数据不影响速度
        count = count + (datetime.datetime.now() - end_date).days // unit  # 结束时间到今天有多少天自然日(肯定 >交易日)
    URL = f"http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol={code}&scale={ts}&ma=5&datalen={count}"
    dstr = json.loads(requests.get(URL).content)
    df = pd.DataFrame(dstr, columns=["day", "open", "high", "low", "close", "volume"])
    df["open"] = df["open"].astype(float)
    df["high"] = df["high"].astype(float)
    # 转换数据类型
    df["low"] = df["low"].astype(float)
    df["close"] = df["close"].astype(float)
    df["volume"] = df["volume"].astype(float)
    df.day = pd.to_datetime(df.day)
    df.set_index(["day"], inplace=True)
    df.index.name = ""  # 处理索引
    if (end_date != "") and (frequency in ["240m", "1200m", "7200m"]):
        return df[df.index <= end_date][-mcount:]  # 日线带结束时间先返回
    return df


# sina新浪全周期获取函数，分钟线 5m,15m,30m,60m  日线1d=240m   周线1w=1200m  1月=7200m
async def async_get_price_sina(code, end_date="", count=10, frequency="60m"):  # 新浪全周期获取函数
    frequency = frequency.replace("1d", "240m").replace("1w", "1200m").replace("1M", "7200m")
    mcount = count
    ts = int(frequency[:-1]) if frequency[:-1].isdigit() else 1  # 解析K线周期数
    if (end_date != "") & (frequency in ["240m", "1200m", "7200m"]):
        end_date = (
            pd.to_datetime(end_date).to_pydatetime() if not isinstance(end_date, datetime.datetime) else end_date
        )  # 转换成datetime
        unit = 4 if frequency == "1200m" else 29 if frequency == "7200m" else 1  # 4,29多几个数据不影响速度
        count = count + (datetime.datetime.now() - end_date).days // unit  # 结束时间到今天有多少天自然日(肯定 >交易日)
        # print(code,end_date,count)
    URL = f"http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol={code}&scale={ts}&ma=5&datalen={count}"
    res = await async_http_get(URL)
    dstr = json.loads(res)
    df = pd.DataFrame(dstr, columns=["day", "open", "high", "low", "close", "volume"])
    df["open"] = df["open"].astype(float)
    df["high"] = df["high"].astype(float)
    # 转换数据类型
    df["low"] = df["low"].astype(float)
    df["close"] = df["close"].astype(float)
    df["volume"] = df["volume"].astype(float)
    df.day = pd.to_datetime(df.day)
    df.set_index(["day"], inplace=True)
    df.index.name = ""  # 处理索引
    if (end_date != "") & (frequency in ["240m", "1200m", "7200m"]):
        return df[df.index <= end_date][-mcount:]  # 日线带结束时间先返回
    return df


def get_price(code, end_date="", count=10, frequency="1d", fields=[]):  # 对外暴露只有唯一函数，这样对用户才是最友好的
    xcode = code.replace(".XSHG", "").replace(".XSHE", "")  # 证券代码编码兼容处理
    xcode = "sh" + xcode if ("XSHG" in code) else "sz" + xcode if ("XSHE" in code) else code

    if frequency in ["1d", "1w", "1M"]:  # 1d日线  1w周线  1M月线
        try:
            return get_price_sina(xcode, end_date=end_date, count=count, frequency=frequency)  # 主力
        except Exception:
            return get_price_day_tx(xcode, end_date=end_date, count=count, frequency=frequency)  # 备用

    if frequency in ["1m", "5m", "15m", "30m", "60m"]:  # 分钟线 ,1m只有腾讯接口  5分钟5m   60分钟60m
        if frequency in "1m":
            # 腾讯1m有9:30的bar, 为当天开盘价
            return get_price_min_tx(xcode, end_date=end_date, count=count, frequency=frequency)
        else:
            try:
                return get_price_sina(xcode, end_date=end_date, count=count, frequency=frequency)  # 主力
            except Exception:
                return get_price_min_tx(xcode, end_date=end_date, count=count, frequency=frequency)  # 备用


async def async_get_price(
    code, end_date="", count=10, frequency: Literal["1d", "1w", "1M", "1m", "5m", "15m", "30m", "60m"] = "1m", fields=[]
):  # 对外暴露只有唯一函数，这样对用户才是最友好的
    if frequency not in ["1d", "1w", "1M", "1m", "5m", "15m", "30m", "60m"]:
        raise ValueError
    xcode = code.replace(".XSHG", "").replace(".XSHE", "")  # 证券代码编码兼容处理
    xcode = "sh" + xcode if ("XSHG" in code) else "sz" + xcode if ("XSHE" in code) else code

    if frequency in ["1d", "1w", "1M"]:  # 1d日线  1w周线  1M月线
        try:
            return await async_get_price_sina(xcode, end_date=end_date, count=count, frequency=frequency)  # 主力
        except Exception:
            return await async_get_price_day_tx(xcode, end_date=end_date, count=count, frequency=frequency)  # 备用
    else:
        if frequency in "1m":
            return await async_get_price_min_tx(xcode, end_date=end_date, count=count, frequency=frequency)
        else:
            try:
                return await async_get_price_sina(xcode, end_date=end_date, count=count, frequency=frequency)  # 主力
            except Exception:
                return await async_get_price_min_tx(xcode, end_date=end_date, count=count, frequency=frequency)  # 备用


class AShare:
    def __init__(self, callback):
        self._callback = callback
        self._code_list = set()
        self._thread_rlock = threading.RLock()

        self._q: asyncio.Queue
        threading.Thread(target=self._sub_thread, daemon=True).start()

    async def _async_callback(self):
        cache = {}
        current_dt = {}
        while True:
            data = await self._q.get()
            if data["code"] not in cache.keys():
                cache[data["code"]] = data
            if data["code"] not in current_dt.keys():
                current_dt[data["code"]] = data["datetime"].replace(hour=0, minute=0, second=0)

            if (
                ((tmp := cache[data["code"]]["datetime"]) < data["datetime"])
                or (
                    tmp.time() == datetime.time(11, 30)
                    and tmp.replace(hour=11, minute=29, second=55) <= datetime.datetime.now()
                )
                or (
                    tmp.time() == datetime.time(15, 0)
                    and tmp.replace(hour=14, minute=59, second=55) <= datetime.datetime.now()
                )
            ):
                if cache[data["code"]]["datetime"] > current_dt[data["code"]]:
                    self._callback(cache[data["code"]])
                    current_dt[data["code"]] = cache[data["code"]]["datetime"]
            if cache[data["code"]]["datetime"] <= data["datetime"]:
                cache[data["code"]].update(**data)

    async def _async_batch_get(self):
        while True:
            try:
                with self._thread_rlock:
                    code_list = list(self._code_list.copy())
                if datetime.time(9, 15) <= datetime.datetime.now().time() <= datetime.time(15, 3):
                    await asyncio.wait_for(asyncio.gather(*[self._async_get(i) for i in code_list]), timeout=3)
            except Exception:
                pass
            else:
                await asyncio.sleep(2)

    async def _async_get(self, code):
        try:
            data: pd.DataFrame = await async_get_price(code, count=2, frequency="1m")
            data["datetime"] = data.index
            data["code"] = code
            if data["datetime"].iloc[-1].time() == datetime.time(9, 30):
                data = data.iloc[:-1, :]
            elif data["datetime"].iloc[0].time() == datetime.time(9, 30):
                data.at[data.index[-1], "open"] = data.at[data.index[0], "open"]
                data.at[data.index[-1], "volume"] += data.at[data.index[0], "volume"]
                data = data.iloc[1:, :]
            data1 = data.to_dict("records")
            for i in data1:
                await self._q.put(i)
        except Exception:
            pass

    async def _async_run(self):
        self._q = asyncio.Queue()
        asyncio.create_task(self._async_batch_get())
        await self._async_callback()

    def _sub_thread(self):
        asyncio.run(self._async_run())

    def add_sub(self, code):
        with self._thread_rlock:
            self._code_list.add(code)

    def del_sub(self, code):
        with self._thread_rlock:
            self._code_list.remove(code)


if __name__ == "__main__":
    pd.set_option("display.max_rows", None)
    df = get_price("sh600000", count=6000, frequency="5m")  # 支持'1d'日, '1w'周, '1M'月
    print("上证指数日线行情\n", df)
    # print(df.index)

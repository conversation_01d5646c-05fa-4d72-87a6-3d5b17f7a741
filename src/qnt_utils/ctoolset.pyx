import datetime as dt
import numpy as np


cpdef object generate_datetime(long date,long time):
    cdef long year=<long>(date/10000)
    cdef long month=<long>(date/100-year*100)
    cdef long day=<long>(date-year*10000-month*100)
    cdef long hour=<long>(time/10000000)
    cdef long minute=<long>(time/100000-hour*100)
    cdef long second=<long>(time/1000-hour*10000-minute*100)
    cdef long millisecond=<long>1000*(time-hour*10000000-minute*100000-second*1000)
    return dt.datetime(year,month,day,hour,minute,second,millisecond)


ctypedef unsigned char NP_BOOL

cpdef long backward_search_true(NP_BOOL[:] nd, long n):
    cdef long t=0
    cdef long i
    for i in range(-1,-len(nd),-1):
        if nd[i]:
            t+=1
        if t==n:
            return len(nd)+i
    return -1
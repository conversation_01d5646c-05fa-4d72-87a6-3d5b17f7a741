import base64
import ctypes
import pathlib
import platform
import sys
from typing import Optional, Union

import pandas as pd

from .enums import Exchange, FinancialAsset
from .label import QSymbol, Symbol


def encrypt(s: str) -> str:
    return "encrypted:" + base64.b64encode(s.encode("utf8")).decode("utf8")


def decrypt(s: str) -> str:
    if s[:10] == "encrypted:":
        return base64.b64decode(s[10:]).decode("utf8")
    else:
        return s


def to_nstimestamp(date_time: Union[str, pd.Timestamp], tz="Asia/Shanghai") -> int:
    if isinstance(date_time, str):
        return int(pd.Timestamp(date_time, tz=tz).timestamp() * 1e9)
    else:
        return int(date_time.timestamp() * 1e9)


def from_nstimestamp(ts: int, tz="Asia/Shanghai") -> pd.Timestamp:
    return pd.Timestamp.fromtimestamp(ts / 1e9, tz=tz)


def malloc_trim():
    if platform.system() == "Linux":
        ctypes.CDLL("libc.so.6").malloc_trim(0)
    else:
        pass


def show_memory(unit="KB", threshold=0):
    """查看变量占用内存情况

    :param unit: 显示的单位，可为`B`,`KB`,`MB`,`GB`
    :param threshold: 仅显示内存数值大于等于threshold的变量
    """
    from sys import getsizeof

    scale = {"B": 1, "KB": 1024, "MB": 1048576, "GB": 1073741824}["KB"]
    for i in list(globals().keys()) + list(locals().keys()):
        memory = eval("getsizeof({})".format(i)) // scale
        if memory >= 0:
            print(i, memory)


def generate_strategy_name():
    """根据策略文件名自动生成策略名"""
    return ".".join(pathlib.Path(sys.argv[0]).parts[-2:])


def get_financial_asset(symbol: Optional[QSymbol]) -> FinancialAsset:
    # TODO:根据symbol判断financial_asset
    ex = Symbol.get_exchange(symbol)
    if ex == Exchange.SSE:
        if symbol[:3] in [
            "500",
            "501",
            "502",
            "505",
            "506",
            "508",
            "510",
            "511",
            "512",
            "513",
            "515",
            "516",
            "517",
            "518",
            "550",
            "560",
            "561",
            "562",
            "563",
        ]:
            return FinancialAsset.FUND
        elif symbol[:3] in ["600", "601", "603", "605", "688"]:
            return FinancialAsset.STOCK
        raise
    elif ex == Exchange.SZSE:
        if symbol[:3] in ["120"]:
            return FinancialAsset.BOND
        elif (
            any([symbol_match(symbol, prefix) for prefix in ["000", "002", "003", "004"]])
            or symbol_match(symbol, None, "001200", "001999")
            or symbol_match(symbol, None, "300000", "309799")
        ):
            return FinancialAsset.STOCK
        elif any([symbol_match(symbol, prefix) for prefix in ["158", "159", "16", "17", "180", "184"]]):
            return FinancialAsset.FUND
        raise

    else:
        return FinancialAsset.FUTURES


def symbol_match(symbol, prefix, start: Optional[str] = None, end: Optional[str] = None):
    if prefix is not None:
        return symbol.startswith(prefix)
    elif start is not None and end is not None:
        return int(start) <= int(symbol.split(".")[0]) <= int(end)


def view(df: pd.DataFrame):
    from IPython.display import display

    res = df.copy()
    res.index = res.index.map(from_nstimestamp)
    display(res)


def compare_versions(version1: str, version2: str) -> int:
    """对比两个版本号

    Args:
        version1 (str): 如"1.8.2.0228"
        version2 (str): 如"1.9"

    Returns:
        int: -1:version1<version2, 0:version1=version2, 1:version1>version2
    """

    # 将版本号字符串拆分成列表，并将每个部分转换为整数
    v1_parts = [int(part) for part in version1.split(".")]
    v2_parts = [int(part) for part in version2.split(".")]

    # 获取两个版本号的最大长度
    max_length = max(len(v1_parts), len(v2_parts))

    # 填充较短的版本号列表，使其长度相同
    v1_parts.extend([0] * (max_length - len(v1_parts)))
    v2_parts.extend([0] * (max_length - len(v2_parts)))

    # 逐个比较每个部分
    for v1, v2 in zip(v1_parts, v2_parts):
        if v1 > v2:
            return 1  # version1 大于 version2
        elif v1 < v2:
            return -1  # version1 小于 version2

    return 0  # 两个版本号相等

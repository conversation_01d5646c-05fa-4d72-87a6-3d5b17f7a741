import datetime
import re
from typing import Dict, List, Optional, Tuple, Union

import pandas as pd

from .enums import BasicData, Destination, Exchange
from .typedef import (
    ASSymbol,
    DataLabel,
    EmSymbol,
    IFindSymbol,
    MindgoSymbol,
    PureSymbol,
    QSymbol,
    TBSymbol,
    TdsSymbol,
    TqSymbol,
)


def generate_data_label(symbol: QSymbol, basic_data: Union[BasicData, str]) -> DataLabel:
    """生成data_label

    Args:
        symbol (QSymbol): RU8888.SHFE
        basic_data (Union[BasicData, str]): MINUTE等

    Returns:
        DataLabel: RU8888.SHFE-MINUTE
    """
    basic_data = basic_data if isinstance(basic_data, str) else basic_data.name
    return DataLabel(f"{symbol}-{basic_data}")


def parse_data_label(data_label: DataLabel) -> Tuple[QSymbol, BasicData]:
    """解析data_label

    Args:
        data_label (DataLabel): RU8888.SHFE-MINUTE

    Returns:
        <PERSON><PERSON>[QSymbol, BasicData]: (RU8888.SHFE, MINUTE)
    """
    tmp1, tmp2 = data_label.split("-")
    return QSymbol(tmp1), BasicData[tmp2]


class Symbol:
    """
    quant标准的symbol是代码+交易所,如601012.SSE
    期货指数为8888,主连为9999,字母都要大写
    """

    all_symbols: List[QSymbol] = []
    special_futures: List[Dict[str, str]] = []

    @classmethod
    def get_exchange(cls, symbol: QSymbol) -> Exchange:
        exchange = symbol.split(".")[1]
        return Exchange[exchange]

    @classmethod
    def get_pure_symbol(cls, symbol: QSymbol) -> PureSymbol:
        return PureSymbol(symbol.split(".")[0])

    @classmethod
    def get_destination(cls, symbol: QSymbol) -> List[Destination]:
        exchange = cls.get_exchange(symbol)
        if exchange in [Exchange.SHFE, Exchange.CFFEX, Exchange.DCE, Exchange.CZCE, Exchange.INE]:
            return [Destination.TIANQIN]
        elif exchange in [Exchange.SSE, Exchange.SZSE]:
            return [Destination.ASHARE, Destination.REDIS]
        else:
            return []

    @classmethod
    def qs_from_tds(cls, symbol: TdsSymbol) -> Optional[QSymbol]:
        tmp1, tmp2 = symbol[:-4], symbol[-4:]
        if tmp2[1:-1] == "SH":
            return QSymbol(tmp1 + "." + "SSE")
        elif tmp2[1:-1] == "SZ":
            return QSymbol(tmp1 + "." + "SZSE")

    @classmethod
    def qs_to_mindgo(cls, symbol: QSymbol) -> Optional[MindgoSymbol]:
        exchange = cls.get_exchange(symbol)
        if exchange == Exchange.SSE:
            return MindgoSymbol(symbol.replace("SSE", "SH"))
        elif exchange == Exchange.SZSE:
            return MindgoSymbol(symbol.replace("SZSE", "SZ"))
        elif exchange in [Exchange.SHFE, Exchange.CFFEX, Exchange.DCE, Exchange.CZCE, Exchange.INE]:
            return MindgoSymbol(symbol[: symbol.find(".")])

    # todo 目前只实现了股票的转换
    @classmethod
    def qs_from_mindgo(cls, symbol: MindgoSymbol) -> Optional[QSymbol]:
        if "SH" in symbol:
            return QSymbol(symbol.replace("SSE", "SH"))
        elif "SZ" in symbol:
            return QSymbol(symbol.replace("SZSE", "SZ"))

    @classmethod
    def qs_to_tb(cls, symbol: QSymbol) -> TBSymbol:
        exchange = cls.get_exchange(symbol)
        if exchange in [Exchange.SHFE, Exchange.CFFEX, Exchange.DCE, Exchange.CZCE, Exchange.INE]:
            obj = re.findall("[A-Za-z]+", symbol)[0]
            number = re.findall("[0-9]+", symbol)[0]
            if number == "8888":
                return TBSymbol(obj.lower() + "000")
            elif number == "9999":
                return TBSymbol(obj.lower() + "888")
            else:
                return TBSymbol(obj.lower() + number)
        else:
            return TBSymbol("")

    @classmethod
    def qs_to_tq(cls, symbol: QSymbol) -> Optional[TqSymbol]:
        exchange = cls.get_exchange(symbol)
        if exchange in [Exchange.SHFE, Exchange.CFFEX, Exchange.DCE, Exchange.CZCE, Exchange.INE, Exchange.GFEX]:
            pure_symbol, _ = symbol.split(".")
            obj = re.findall("[A-Za-z]+", pure_symbol)[0]
            number = re.findall("[0-9]+", pure_symbol)[0]
            if number == "8888":
                if exchange in [Exchange.SHFE, Exchange.DCE, Exchange.INE, Exchange.GFEX]:
                    return TqSymbol("KQ.i@%s.%s" % (exchange.name, obj.lower()))
                elif exchange in [Exchange.CFFEX, Exchange.CZCE]:
                    return TqSymbol("KQ.i@%s.%s" % (exchange.name, obj.upper()))
            elif number == "9999":
                if exchange in [Exchange.SHFE, Exchange.DCE, Exchange.INE, Exchange.GFEX]:
                    return TqSymbol("KQ.m@%s.%s" % (exchange.name, obj.lower()))
                elif exchange in [Exchange.CFFEX, Exchange.CZCE]:
                    return TqSymbol("KQ.m@%s.%s" % (exchange.name, obj.upper()))
            else:
                if exchange in [Exchange.SHFE, Exchange.DCE, Exchange.INE, Exchange.GFEX]:
                    return TqSymbol("%s.%s%s" % (exchange.name, obj.lower(), number))
                elif exchange == Exchange.CFFEX:
                    return TqSymbol("%s.%s%s" % (exchange.name, obj.upper(), number))
                elif exchange == Exchange.CZCE:
                    return TqSymbol("%s.%s%s" % (exchange.name, obj.upper(), number[-3:]))

    @classmethod
    def qs_from_tq(
        cls, symbol: TqSymbol, dt: Optional[str] = None, verify_duplicate: bool = False
    ) -> Optional[QSymbol]:
        """_summary_

        Args:
            symbol (TqSymbol):
            dt (Optional[str], optional): 在dt日的symbol是指向哪个上市合约. Defaults to None.

        Returns:
            Optional[QSymbol]: _description_
        """
        from qnt_research.api import get_contract_info

        symbol_ = re.sub(r"KQ.m@|KQ.i@", "", symbol)
        exchange, pure_symbol = symbol_.split(".")
        if dt is None:
            dt_ = pd.Timestamp.now(tz="Asia/Shanghai").to_pydatetime().date()
        else:
            dt_ = pd.Timestamp(dt).date()
        if "KQ.m@" in symbol:
            ret = QSymbol("{}9999.{}".format(pure_symbol.upper(), exchange.upper()))
        elif "KQ.i@" in symbol:
            ret = QSymbol("{}8888.{}".format(pure_symbol.upper(), exchange.upper()))
        else:
            if exchange == "CZCE":
                year1 = dt_.year
                y2 = int(pure_symbol[-3])
                year2 = year1 // 10  # year1 = 2020; year2 = 202
                for i in range(-1, (year2 % 10) + 1, 1):
                    y1 = year2 - i
                    ret = QSymbol(
                        "{}{:>02}{}.{}".format(
                            pure_symbol[:-3].upper(), (y1 * 10 + y2) % 100, pure_symbol[-2:], exchange.upper()
                        )
                    )
                    if (info := get_contract_info(ret)) is None or info["listing_date"] > dt_:
                        continue
                    else:
                        break
            else:
                ret = QSymbol("{}.{}".format(pure_symbol.upper(), exchange.upper()))
        if verify_duplicate:
            from qnt_research.api import get_all_futures

            if len(cls.special_futures) == 0:
                a = get_all_futures()
                a = a.loc[a["futures_code"].apply(lambda x: x.endswith("-1"))][["futures_code", "exchange"]]
                a["futures_code"] = a["futures_code"].apply(lambda x: x.replace("-1", ""))
                cls.special_futures = a.to_dict("records")

            for i in cls.special_futures:
                if ret.startswith(i["futures_code"]) and ret.endswith(i["exchange"]):
                    ret = Symbol.verify_duplicate_symbol(ret, dt_)
                    break
        return ret

    @classmethod
    def verify_duplicate_symbol(
        cls, symbol_maybe_wrong: str, dt: Union[str, datetime.date, datetime.datetime]
    ) -> Optional[QSymbol]:
        """判断在dt日的symbol是指向哪个合约, 要求dt时symbol上市, 比如FU1901.SHFE, 在2018-7-16前后分别存在FU1901-1和FU1901。
        因为存在一些特殊情况，比较燃油重新上市之后，代码没有变更，但是实际上完全是新的合约了，而天勤没有做这个区分

        Args:
            symbol_maybe_wrong (QSymbol): 证券代码，可能是错误的
            dt (str): _description_

        Returns:
            Optional[QSymbol]: _description_
        """
        from qnt_research.api import get_all_qcodes, get_contract_info

        if len(cls.all_symbols) == 0:
            cls.all_symbols = get_all_qcodes()["symbol"].index.map(lambda x: "{}.{}".format(*x)).tolist()
        contract_code, exchange = symbol_maybe_wrong.split(".")
        t1 = [i for i in cls.all_symbols if i.startswith(contract_code) and i.endswith(exchange)]
        t1 = (
            pd.DataFrame({i: get_contract_info(i) for i in t1})
            .T[["listing_date", "delisting_date"]]
            .sort_values("listing_date")
        )
        t1 = t1.loc[t1["listing_date"] <= pd.Timestamp(dt).date()]
        return None if t1.empty else t1.index[t1["listing_date"].argmax()]

    @staticmethod
    def qs_from_em(symbol: EmSymbol) -> QSymbol:
        exchange = {"HA": "SSE", "SA": "SZSE", "B": "BSE"}.get(symbol.split(".")[-1])
        return QSymbol("{}.{}".format(symbol.split(".")[0], exchange))

    @staticmethod
    def qs_to_ifind(symbol: QSymbol) -> IFindSymbol:
        def fnc(symbol):
            a, exchange = symbol.split(".")
            dt = re.findall("[0-9]+", a)[0]
            fut = a.replace(dt, "")
            return fut, dt, exchange

        fut, dt, exchange = fnc(symbol)
        if dt == "9999":
            dt_ = "ZL"
        else:
            dt_ = dt
        exchange_ = {
            "SSE": "SH",  # 上交所
            "SZSE": "SZ",  # 深交所
            "SHFE": "SHF",  # 上期所
            "CFFEX": "CFE",  # 中金所
            "DCE": "DCE",  # 大商所
            "CZCE": "CZC",  # 郑商所
            "INE": "SHF",  # 上期能源
            "BSE": "BJ",  # 北交所
            "GFEX": "GFE",  # 广期所
        }.get(exchange)
        return IFindSymbol("{}{}.{}".format(fut, dt_, exchange_))

    @staticmethod
    def qs_from_as(symbol: ASSymbol) -> QSymbol:
        market, code = symbol[:2], symbol[2:]
        return QSymbol("{}.{}".format(code, {"sh": "SSE", "sz": "SZSE"}[market]))

    @staticmethod
    def qs_to_as(symbol: QSymbol) -> ASSymbol:
        code, market = symbol.split(".")
        return ASSymbol("{}{}".format({"SSE": "sh", "SZSE": "sz"}[market], code))

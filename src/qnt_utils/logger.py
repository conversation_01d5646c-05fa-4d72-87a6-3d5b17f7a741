import base64
import datetime
import hashlib
import hmac
import logging
import pathlib
import time
import urllib.parse
from functools import wraps
from logging.handlers import HTTPHandler
from loguru import logger
import requests

WEBHOOK_LEVEL = logger.level("WEBHOOK", no=100)


def message_push(summary: str, content: str) -> None:
    """微信推送

    Args:
        summary (str): 摘要
        content (str): 内容
    """
    try:
        url = "http://wxpusher.zjiecode.com/api/send/message"
        myobj = {
            "appToken": "AT_ySpaoM5w9jJbt7dyxsk7BQF2Z1b2SqUf",
            "content": content,
            "summary": summary,
            "contentType": 1,
            "uids": ["UID_A97RxScBvVA9K2lJcUSpgTLzEmwi"],
            "url": "http://wxpusher.zjiecode.com",
        }
        requests.post(url, json=myobj)
    except Exception as e:
        pass


def dingtalk_sign(secret: str) -> tuple[str, str]:
    timestamp = str(round(time.time() * 1000))
    secret_enc = secret.encode("utf-8")
    string_to_sign = "{}\n{}".format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode("utf-8")
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
    return timestamp, sign


def webhook_push(url: str, content: str, secret: str = "") -> None:
    """webhook推送消息

    Args:
        url (str): url
        content (str): 内容
        secret (str): 密钥
    """
    try:
        if secret:
            timestamp, sign = dingtalk_sign(secret)
            url = f"{url}&timestamp={timestamp}&sign={sign}"
        requests.post(
            url=url,
            headers={"Content-Type": "application/json"},
            json={"msgtype": "text", "text": {"content": content}},
        )
    except Exception:
        pass


class WXPushHandler(HTTPHandler):
    def __init__(self):
        logging.Handler.__init__(self)

    def emit(self, record: logging.LogRecord) -> None:
        message_push(record.msg, " ")


class WebhookHandlerForLoguru(logging.Handler):
    def __init__(self, url: str, secret: str = ""):
        logging.Handler.__init__(self)
        self._url = url
        self._secret = secret

    def emit(self, record: logging.LogRecord) -> None:
        webhook_push(self._url, record.msg, self._secret)


class WebhookHandler(logging.Handler):
    def __init__(self, urls: list[str]):
        logging.Handler.__init__(self)
        self._urls = urls

    def emit(self, record: logging.LogRecord) -> None:
        for url in self._urls:
            webhook_push(url, f"{record.asctime} - {record.name} - {record.levelname} {record.message}")


class Logger:
    _LOG_LEVEL = {
        "NOTSET": logging.NOTSET,
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }
    _loggers = []

    def __init__(
        self,
        name: str,
        logger_log_level: str = "INFO",
        log_dir: str = "log",
        stream_handler: tuple[bool, str] = (True, "INFO"),
        file_handler: tuple[bool, str] = (False, "INFO"),
        wxpush_handler: tuple[bool, str] = (False, "INFO"),
        webhook_handler: tuple[bool, str, list[str]] = (False, "INFO", []),
    ):
        """日志工具

        Args:
            name (str): 名称
            logger_log_level (str, optional): logger级别. Defaults to "INFO".
            log_dir (str, optional): 日志目录. Defaults to "log".
            stream_handler (tuple[bool, str], optional): (是否开启，日志级别). Defaults to (True, "INFO").
            file_handler (tuple[bool, str], optional): (是否开启，日志级别). Defaults to (False, "INFO").
            wxpush_handler (tuple[bool, str], optional): (是否开启，日志级别). Defaults to (False, "INFO").
            webhook_handler (tuple[bool, str, list[str], optional): (是否开启，日志级别，目标地址). Defaults to (False, "INFO").
        """
        self._name = name
        self._log_dir = log_dir
        self._stream_handler: bool = stream_handler[0]
        self._file_handler: bool = file_handler[0]
        self._wxpush_handler: bool = wxpush_handler[0]
        self._webhook_handler: bool = webhook_handler[0]
        self._log_level = {
            "logger_log_level": logger_log_level,
            "sh_log_level": stream_handler[1],
            "fh_log_level": file_handler[1],
            "wx_log_level": wxpush_handler[1],
            "wh_log_level": webhook_handler[1],
        }

        self._logger_date = None
        self._fh = None
        self._logger_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s %(message)s")

        if name in self._loggers:
            self._logger = logging.getLogger(name)
            return
        else:
            self._logger = logging.getLogger(name)
            self._loggers.append(name)
        self._logger.setLevel(self._LOG_LEVEL[logger_log_level])

        if self._stream_handler:
            sh = logging.StreamHandler()
            sh.setLevel(self._LOG_LEVEL[self._log_level["sh_log_level"]])
            sh.setFormatter(self._logger_formatter)
            self._logger.addHandler(sh)
        if self._wxpush_handler:
            wx = WXPushHandler()
            wx.setLevel(self._LOG_LEVEL[self._log_level["wx_log_level"]])
            self._logger.addHandler(wx)
        if self._webhook_handler:
            wh = WebhookHandler(webhook_handler[2])
            wh.setLevel(self._LOG_LEVEL[self._log_level["wh_log_level"]])
            self._logger.addHandler(wh)

    @staticmethod
    def _check_file_handler(fnc):
        """FileHandler的存储路径是  日期/name.log"""

        @wraps(fnc)
        def wrapper(self, *args, **kwargs):
            if self._file_handler and self._logger_date != datetime.datetime.now().strftime("%Y-%m-%d"):
                if self._fh is not None:
                    self._logger.removeHandler(self._fh)
                (tmp_log_dir := pathlib.Path(self._log_dir, datetime.datetime.now().strftime("%Y-%m-%d"))).mkdir(
                    parents=True, exist_ok=True
                )
                self._logger_date = datetime.datetime.now().strftime("%Y-%m-%d")
                self._fh = logging.FileHandler(pathlib.Path(tmp_log_dir, self._logger.name + ".log"))
                self._fh.setLevel(self._log_level["fh_log_level"])
                self._fh.setFormatter(self._logger_formatter)
                self._logger.addHandler(self._fh)
            return fnc(self, *args, **kwargs)

        return wrapper

    @_check_file_handler
    def debug(self, *args, **kwargs):
        return self._logger.debug(*args, **kwargs)

    @_check_file_handler
    def info(self, *args, **kwargs):
        return self._logger.info(*args, **kwargs)

    @_check_file_handler
    def warning(self, *args, **kwargs):
        return self._logger.warning(*args, **kwargs)

    @_check_file_handler
    def error(self, *args, **kwargs):
        return self._logger.error(*args, **kwargs)

    @_check_file_handler
    def critical(self, *args, **kwargs):
        return self._logger.critical(*args, **kwargs)

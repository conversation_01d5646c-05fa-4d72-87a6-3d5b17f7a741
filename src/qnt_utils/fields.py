from typing import TypedDict

# 由于bar数据中有nan, volume\open_interest\pos_change如果为int, astype时会报错
BAR_FIELDS = {
    "code": "U15",  # 等于symbol
    "date": "int",
    "time": "int",
    "open": "float",
    "high": "float",
    "low": "float",
    "close": "float",
    "factor": "float",
    "volume": "float",
    "turnover": "float",
    "turnover_rate": "float",
    "is_paused": "bool",
    "uplimit_price": "float",
    "downlimit_price": "float",
    "avg_price": "float",
    "pre_price": "float",
    "quote_rate": "float",
    "amp_rate": "float",
    "is_st": "bool",
    "settle": "float",
    "pre_settle": "float",
    "change": "float",
    "open_interest": "float",
    "pos_change": "float",
    "pre_open_interest": "float",
}


class Bar(TypedDict):
    code: str
    date: int
    time: int
    open: float
    high: float
    low: float
    close: float
    factor: float
    volume: float
    turnover: float
    turnover_rate: float
    is_paused: bool
    uplimit_price: float
    downlimit_price: float
    avg_price: float
    pre_price: float
    quote_rate: float
    amp_rate: float
    is_st: bool
    settle: float
    pre_settle: float
    change: float
    open_interest: float
    pos_change: float
    pre_open_interest: float

class TickSnapshot(TypedDict):
    market: str
    code: str
    codename: str
    status: str
    date: int
    time: int
    pre_price: float
    open_price: float
    high_price: float
    low_price: float
    new_price: float
    volume: float
    turnover: float
    trade_num: int
    bidorder_price: list[float]
    bidorder_volume: list[float]
    bid_numorders: list[int]
    askorder_price: list[float]
    askorder_volume: list[float]
    ask_numorders: list[int]
    totalbid_volume: int
    totalask_volume: int
    avgbid_price: float
    avgask_price: float
    uplimit_price: float
    downlimit_price: float
    pe: float
    iopv: float
    withdraw_buynum: int
    withdraw_buyvolume: int
    withdraw_buyturnover: float
    withdraw_sellnum: int
    withdraw_sellvolume: int
    withdraw_sellturnover: float
    totalbid_num: int
    totalask_num: int
    bidorder_num: int
    askorder_num: int
    inner_volume: int
    outer_volume: int
    aftermarket_volume: int
    aftermarket_volmount: int
    avg_price: float
    open_interest: float
    pre_open_interest: float

class TickOrder(TypedDict):
    market: str
    code: str
    date: int
    time: int
    index: int
    biz_index: int
    channel: int
    price: float
    volume: int
    side: str
    type: str

class TickTransaction(TypedDict):
    market: str
    code: str
    date: int
    time: int
    index: int
    biz_index: int
    channel: int
    price: float
    volume: int
    buy_index: int
    sell_index: int
    type: str
    bsflag: str

class TickSuperSnapshot(TypedDict):
    market: str
    code: str
    date: int
    time: int
    new_price: float
    bidorder_price: list[float]
    bidorder_volume: list[int]
    askorder_price: list[float]
    askorder_volume: list[int]

# todo 需要调整为key=字段,value=type
TICK_SNAPSHOT_FIELDS = [
    "market",
    "code",
    "codename",
    "status",
    "date",
    "time",
    "pre_price",
    "open_price",
    "high_price",
    "low_price",
    "new_price",
    "volume",
    "turnover",
    "trade_num",
    "bidorder_price",
    "bidorder_volume",
    "bid_numorders",
    "askorder_price",
    "askorder_volume",
    "ask_numorders",
    "totalbid_volume",
    "totalask_volume",
    "avgbid_price",
    "avgask_price",
    "uplimit_price",
    "downlimit_price",
    "pe",
    "iopv",
    "withdraw_buynum",
    "withdraw_buyvolume",
    "withdraw_buyturnover",
    "withdraw_sellnum",
    "withdraw_sellvolume",
    "withdraw_sellturnover",
    "totalbid_num",
    "totalask_num",
    "bidorder_num",
    "askorder_num",
    "inner_volume",
    "outer_volume",
    "aftermarket_volume",
    "aftermarket_volmount",
    "avg_price",
    "open_interest",
    "pre_open_interest",
]

TICK_ORDER_FIELDS = [
    "market",
    "code",
    "date",
    "time",
    "index",
    "biz_index",
    "channel",
    "price",
    "volume",
    "side",
    "type",
]

TICK_TRANSACTION_FIELDS = [
    "market",
    "code",
    "date",
    "time",
    "index",
    "biz_index",
    "channel",
    "price",
    "volume",
    "buy_index",
    "sell_index",
    "type",
    "bsflag",
]

TICK_SUPER_SNAPSHOT_FIELDS = [
    "market",
    "code",
    "date",
    "time",
    "new_price",
    "bidorder_price",
    "bidorder_volume",
    "askorder_price",
    "askorder_volume",
]

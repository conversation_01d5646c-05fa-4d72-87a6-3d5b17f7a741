import uuid
from typing import Optional

from kazoo.client import Kazoo<PERSON><PERSON>
from kazoo.exceptions import NodeExistsError


class ZKHandler:
    def __init__(self, path: str, hosts: str, client_id: Optional[str] = None, ephemeral: bool = True):
        self.path = path
        self.ephemeral = ephemeral
        self.client_id = client_id or str(uuid.uuid1())

        self.zk = KazooClient(hosts=hosts)
        self.zk.start()
        self._ensure_path()

    def _ensure_path(self):
        self.zk.ensure_path(self.path)
        try:
            self.zk.create(f"{self.path}/{self.client_id}", ephemeral=self.ephemeral)
        except NodeExistsError:
            pass

    def write(self, data: str):
        self.zk.set(f"{self.path}/{self.client_id}", data.encode("utf-8"))

    def get(self):
        return self.zk.get(f"{self.path}/{self.client_id}")[0].decode("utf-8")

    def __del__(self):
        self.zk.stop()
        self.zk.close()

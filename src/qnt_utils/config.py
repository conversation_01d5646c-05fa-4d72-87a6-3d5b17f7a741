import os
import pathlib

import tomli

_gl_config = {}
if (_cnf_path := pathlib.Path(os.getenv("QNT_CONFIG_PATH", "./config"), "config.toml")).exists():
    with open(_cnf_path, "rb") as f:
        _gl_config.update(tomli.load(f))
else:
    raise FileNotFoundError("config.toml not found")


def edit_config(new_cfg, original_cfg=_gl_config):
    for key, value in new_cfg.items():
        if not isinstance(value, dict):
            original_cfg[key] = value
        else:
            edit_config(value, original_cfg[key])

def get_config():
    return _gl_config.copy()
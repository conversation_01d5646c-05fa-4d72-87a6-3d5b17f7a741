from enum import Enum
from typing import Union


class CommunicatorMessageType(Enum):
    HEARTBEAT = 0
    POSITIONSUPDATE = 1
    PORTFOLIOUPDATE = 2
    QUERY = 3
    INSTRUCTION = 4
    INFO = 5


class QEnum(Enum):
    @classmethod
    def efrom(cls, x: Union["QEnum", int, str]):
        if isinstance(x, str):
            return cls[x]
        elif isinstance(x, int):
            return cls(x)
        else:
            return x


class OrderType(QEnum):
    MORDER = 1  # 母单
    CORDER = 2  # 子单


class StatusCode(QEnum):
    SUCCESS = 0  # 成功
    CASH_NOT_ENOUGH = 1  # 资金不足
    SEC_NOT_ENOUGH = 2  # 证券不足
    AMOUNT_IS_ZERO = 3  # 下单数量为0
    CANCEL_ORDER_FAILED = 4  # 撤单失败
    FAILED = 5  # 失败


class Exchange(QEnum):
    SSE = 0  # 上交所
    SZSE = 1  # 深交所
    SHFE = 2  # 上期所
    CFFEX = 3  # 中金所
    DCE = 4  # 大商所
    CZCE = 5  # 郑商所
    INE = 6  # 上期能源
    BSE = 7  # 北交所
    GFEX = 8  # 广期所


class PositionType(QEnum):
    LONG = 0
    SHORT = 1


class Destination(QEnum):
    TIANQIN = 0
    ASHARE = 1
    REDIS = 2


class BasicData(QEnum):
    SNAPSHOT = 0
    ORDER = 1
    TRANS = 2
    QUEUE = 3
    SUPER_STOCK = 4
    MINUTE = 5
    DAY = 6


class Action(QEnum):
    ADD_SUB = 0
    DEL_SUB = 1


class FinancialAsset(QEnum):
    STOCK = 0
    INDEX = 1
    FUND = 2
    BOND = 3
    FUTURES = 4
    OPTION = 5


class StrategyMode(QEnum):
    BACKTEST = 1
    TRADE = 2
    SYNC = 3


# buy open对应sell close;sell open 对应buy close
class Direction(QEnum):
    BUY = 0
    SELL = 1
    CREDIT_BUY = 2
    CREDIT_SELL = 3

    def is_buy(self):
        return self.value % 2 == 0

    def is_sell(self):
        return self.value % 2 == 1


class OffSet(QEnum):
    OPEN = 0
    CLOSE = 1
    CLOSETODAY = 2

    def is_open(self):
        return self.value == 0

    def is_close(self):
        return self.value != 0


class PriceType(QEnum):
    MARKET = 0
    LIMIT = 1


class OrderStatus(QEnum):
    # WAITING->SCRAPPED
    # WAITING->ALIVE
    # ALIVE->FINISHED
    # ALIVE->CLOSED
    ALIVE = 0  # 订单存活
    FINISHED = 1  # 订单完成
    SCRAPPED = 2  # 废单
    CANCELLED = 3  # 订单关闭，那被取消
    ORDER_TBC = 4  # 下单待确认
    CANCEL_TBC = 5  # 撤单待确认

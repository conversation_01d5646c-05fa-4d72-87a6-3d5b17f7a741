import pathlib
import pickle
from collections import defaultdict
from functools import lru_cache
from typing import Dict

import numpy as np
import pandas as pd
from mindgo_api import *
from tqdm import tqdm

from aichemy.factor.utils import preprocessing
from aichemy.database_manager import DatabaseManager
from . import env

gl_factor_table_name = env.gl_sql_api.get_hxfactors_info()[["因子名称", "数据表"]].set_index("因子名称")["数据表"].to_dict()
"""dict, 保存所有因子对应的数据表，key为因子名称，value为数据表"""


def _process_hxfactors(data):
    """用于处理hxfactors，以time为索引，去掉code, rtime列"""
    data.set_index("time", drop=True, append=False, inplace=True)
    data.index.name = None
    data.drop(["code", "rtime"], axis=1, inplace=True)
    return data


def dump_hxfactors(start_dt, end_dt):
    """落地保存hxfactors"""
    for table in tqdm(env.gl_sql_api.get_hxfactors_info()["数据表"].unique(), desc="落地hxfactors"):
        data = env.gl_sql_api.get_hxfactors_api(start_dt, end_dt, table, 1, 10000000)
        dt = data["time"].max()
        data = {k: _process_hxfactors(v.copy()) for k, v in data.groupby(["code"])}
        with open(pathlib.Path(env.gl_original_data_path, table), "wb") as f:
            pickle.dump(data, f)
        print(f"{table} 最后日期为 {dt}")


def dump_quotation(start_dt, end_dt):
    """落地保存行情数据"""
    trade_days = get_trade_days(start_dt, end_dt)
    stocks = set()
    for i in tqdm(trade_days, desc="汇总各个交易日的股票"):
        stocks = stocks.union(set(get_all_securities("stock", i).index))
    stocks = [i for i in stocks if not "BJ" in i]

    data = get_price(
        stocks,
        start_dt,
        end_dt,
        "1d",
        ["open", "high", "low", "close", "volume", "turnover", "is_paused", "is_st", "high_limit", "low_limit", "prev_close"],
        fq="pre",
        skip_paused=False,  # 保存数据时不跳过停牌，在后续读取数据时把停牌数据置为nan，这样可以从is_paused区分出停牌还是未上市
    )
    dt = data["000001.SZ"].index[-1].strftime("%Y-%m-%d")
    with open(pathlib.Path(env.gl_original_data_path, "quotation"), "wb") as f:
        pickle.dump(data, f)
    print(f"quotation 最后日期为 {dt}")


class LazyTHSFactorDict(defaultdict):
    def __init__(self):
        super().__init__(None)

    def __missing__(self, key: str) -> pd.DataFrame:
        """当key不存在时，调用该方法，从本地加载因子

        Args:
            key (str): 因子名称

        Returns:
            pd.DataFrame: 因子数据, index为date, columns为symbol
        """
        table_name = gl_factor_table_name[key]
        table_data = self.load_table(table_name)
        self[key] = preprocessing(table_data[key].unstack().sort_index())
        return self[key]

    @staticmethod
    @lru_cache(maxsize=3)
    def load_table(table_name: str) -> Dict[str, pd.DataFrame]:
        """从本地加载数据表

        Args:
            table_name (str): 表名, 如hxf060

        Returns:
            Dict[str,pd.DataFrame]: key为symbol,value为pd.DataFrame, 其中index为date, columns为因子名称
        """
        with open(pathlib.Path(env.gl_original_data_path, table_name), "rb") as f:
            tmp = pickle.load(f)
        for k, v in tmp.items():
            v["symbol"] = k
            v.reset_index(inplace=True)
        tmp = pd.concat(tmp.values(), axis=0)
        # 原先的index为datetime.date, 无时区
        tmp["index"] = pd.to_datetime(tmp["index"]).astype(int) - int(8 * 3600 * 1e9)
        tmp.set_index(["index", "symbol"], verify_integrity=True, inplace=True)
        tmp.index.rename((None, None), inplace=True)
        return tmp


gl_ths_factors_dict = LazyTHSFactorDict()
gl_quotation_dict: Dict[str, pd.DataFrame] = {}
"""key为open, high, low, close, volume, turnover等，value为pd.DataFrame，index为date，columns为symbol"""
gl_factors_dict = DatabaseManager()


def init_data():
    gl_factors_dict.bind_db(env.gl_original_data_path + "factor.h5")

    with open(pathlib.Path(env.gl_original_data_path, "quotation"), "rb") as f:
        tmp = pickle.load(f)
    for k, v in tmp.items():
        v["symbol"] = k
        v.reset_index(inplace=True)
    tmp = pd.concat(tmp.values(), axis=0)
    # 原来的时间是0点，没有时区
    tmp["index"] = tmp["index"].astype(int) + int(7 * 3600 * 1e9)
    # tmp["index"] = tmp["index"].apply(lambda x: to_nstimestamp(x.replace(hour=15), "Asia/Shanghai"))
    tmp.set_index(["index", "symbol"], verify_integrity=True, inplace=True)
    tmp.index.rename((None, None), inplace=True)
    for key in tmp.keys():
        gl_quotation_dict[key] = tmp[key].unstack().sort_index()
    multipler = gl_quotation_dict["is_paused"].replace(1, np.nan) + 1
    for key in gl_quotation_dict.keys():
        # 可以通过is_paused，来判断数据为nan是因为停牌还是未上市
        if key != "is_paused":
            gl_quotation_dict[key] *= multipler

import copy
import math
import pathlib
import random
import re
import shelve
from functools import lru_cache, reduce
from multiprocessing import Manager, RLock
from typing import Dict, Iterable, List, Literal, Mapping, Optional, Sequence, Tuple, Union

import numpy as np
import pandas as pd
from loguru import logger

from aichemy.factor_analyse.analyse import orthogonalize_factor
from aichemy.utils import acquire_flock, is_notebook, slice_dataset, to_nstimestamp

from .utils import (
    get_930open_df,
    get_all_securities_,
    get_stock_related_industries,
    query_ashare_outstanding_share_table,
    query_liquid_market_cap_table,
    query_market_cap_table,
)

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm
try:
    from mindgo_api import factor as smd_factor  # type: ignore
    from mindgo_api import (  # type: ignore
        get_all_securities,
        get_all_trade_days,
        get_money_flow_step,
        get_open_api,
        get_price,
        get_trade_days,
        query,
        run_query,
    )
except ImportError:
    pass

HXFACTOR = [
    "量价强化学习类",
    "情绪强化学习类",
    "集合竞价量价类",
    "Tick快照量价类",
    "分钟频量价技术指标类",
    "日频量价技术指标类",
    "Tick快照深度模型类",
    "Transformer量价预测类",
    "新闻预测文本训练类",
    "逐笔成交频次量价类",
    "逐笔委托频次量价类",
]
OTHER_FACTOR = ["市值因子", "行业因子", "行业量价因子", "资金流", "财务因子", "行情", "风险暴露"]
TEST_HXFACTOR = ["000300.SH", "000852.SH", "000905.SH"]
EXCEPTED_FACTOR = {
    "量价强化学习类": ["RL_DAY_MF_ALPHA6", "RL_DAY_MF_ALPHA11", "RL_DAY_MF_ALPHA7"],
    "日频量价技术指标类": ["DAY_PV_VVR"],
    "集合竞价量价类": ["OB_BT_NBOC", "SS_RD_CA1R"],
    "新闻预测文本训练类": ["NEWS_BUZZ_IWF", "NEWS_BUZZ_ICQF"],
}

DEFAULT_FACTOR_CACHE_FILE = "/home/<USER>/work/share/simu/yangfan/factors.h5"
DEFAULT_INDUSTRY_DICT_CACHE_FILE = "/home/<USER>/work/share/simu/yangfan/industry_dict.db"


manager = Manager()
shared_dict = manager.dict()
mp_lock = RLock()


def encode_thscode(thscode: str) -> int:
    if thscode.endswith(".SH"):
        return int(f"1{thscode[:6]}")
    elif thscode.endswith(".SZ"):
        return int(f"2{thscode[:6]}")
    elif thscode.endswith(".BJ"):
        return int(f"3{thscode[:6]}")
    else:
        raise ValueError(f"thscode {thscode} 格式错误")


def decode_thscode(thscode: int) -> str:
    market = thscode // 1000000
    code = thscode % 1000000
    if market == 1:
        return f"{code:0>6}.SH"
    elif market == 2:
        return f"{code:0>6}.SZ"
    elif market == 3:
        return f"{code:0>6}.BJ"
    else:
        raise ValueError(f"thscode {thscode} 格式错误")


def to_key(database: str, name1: str, name2: str, shift: Optional[int] = None) -> str:
    if shift is not None:
        return f"/{database}/{name1}/{name2}/{shift}"
    else:
        return f"/{database}/{name1}/{name2}"


def from_key(key: str) -> Optional[Tuple[str, str, str, int]]:
    """返回值分别为database, name1, name2, shift"""
    if (tmp := re.search(r"^/([^/]+)/([^/]+)/([^/]+)(?:/(\d)+)?$", key)) is not None:
        db, n1, n2, shift = tmp.groups()
        return db, n1, n2, int(shift or "0")
    return None


@lru_cache
def get_hxfactor_dict() -> Dict[str, List[str]]:
    """从数据库中获取因子字典，如{'量价强化学习类': ['RL_DAY_MF_ALPHA6', 'RL_DAY_MF_ALPHA11', 'RL_DAY_MF_ALPHA7']}"""
    factor_dict = {}
    open_api = get_open_api("share:research")
    for i in HXFACTOR:
        data = open_api.get_hxfactors_data(i, "now", "now", fields=None)
        factor_dict[i] = data.columns.difference(["time", "code"]).tolist()
    test = open_api.get_hxfactors_test()
    for i in TEST_HXFACTOR:
        factor_dict[i] = test.query(f'基准指数=="{i}"')["因子名称"].tolist()
    return factor_dict


@lru_cache
def get_factor_dict(
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE, database: str = "raw"
) -> Mapping[str, Sequence[str]]:
    """获取目前已经保存到本地的因子字典

    Args:
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.

    Returns:
        Dict[str, List[str]]: {'量价强化学习类': ['RL_DAY_MF_ALPHA6', 'RL_DAY_MF_ALPHA11', 'RL_DAY_MF_ALPHA7']}
    """
    factor_cache_path = pathlib.Path(factor_cache_file)
    factor_cache_path.parent.mkdir(parents=True, exist_ok=True)
    ret = {}
    with acquire_flock(factor_cache_path.with_suffix(".lock"), "SH"):
        if factor_cache_path.exists():
            with pd.HDFStore(factor_cache_file, mode="r") as store:
                for key in store.keys():
                    if (tmp := from_key(key)) is not None:
                        db, n1, n2, _ = tmp
                        if db == database:
                            ret.setdefault(n1, set()).add(n2)
    return {k: list(v) for k, v in ret.items()}


def del_key(key: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE):
    with acquire_flock(pathlib.Path(factor_cache_file).with_suffix(".lock"), "EX"):
        with pd.HDFStore(factor_cache_file, mode="a") as store:
            store.remove(key)  # type: ignore
    get_factor_dict.cache_clear()


def del_factor(
    name1: str, name2: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE, database: str = "raw"
) -> None:
    """删除因子

    Args:
        name1 (str): 因子大类
        name2 (str): 因子名称
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
    """
    key = to_key(database, name1, name2)
    del_key(key)


def align_datetime_index(index: pd.DatetimeIndex) -> pd.DatetimeIndex:
    """要求index已经normalize"""
    # if not index.difference(gl_all_trade_days).empty:
    #     raise RuntimeError(f"因子数据中包含非交易日数据，日期为：{index.difference(gl_all_trade_days)}")
    gl_all_trade_days = get_all_trade_days()
    return gl_all_trade_days[(gl_all_trade_days >= index.min()) & (gl_all_trade_days <= index.max())].sort_values()


def dump_price(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE):
    fields = [
        "open",
        "close",
        "low",
        "high",
        "volume",
        "turnover",
        "turnover_rate",
        "high_limit",
        "low_limit",
        "avg_price",
    ]
    tmp = get_price(
        get_all_securities_(start_dt, end_dt),
        start_dt,
        end_dt,
        "1d",
        fields,
        fq="post",
        skip_paused=True,
        is_panel=True,
    )
    for field in fields:
        data = tmp[field].reindex(index=align_datetime_index(tmp[field].index))
        data.index = (pd.to_datetime(data.index) + pd.Timedelta(hours=7)).astype(int)
        save_data_to_store(to_key("raw", "行情", field), data, store_file=factor_cache_file)

    open930 = get_930open_df(start_dt, end_dt)
    open930 = open930.reindex(index=align_datetime_index(open930.index))
    open930.index = (pd.to_datetime(open930.index) + pd.Timedelta(hours=7)).astype(int)
    save_data_to_store(to_key("raw", "行情", "930open"), open930, store_file=factor_cache_file)


def dump_hxfactor(
    name1: str,
    name2: Optional[List],
    save_mode: int,
    start_dt: str,
    end_dt: str,
    chunk_size: int = 50,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
) -> None:
    """保存因子

    Args:
        name1 (str): 因子大类
        name2 (Optional[List]): 因子名称,None表示获取所有
        save_mode (int): 0 - 跳过已经保存的因子，只保存新的因子，1 - 所有因子全都重新保存，2 - 已经保存的因子做增量更新
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        chunk_size (int, optional): 分块大小. Defaults to 10.
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
    """
    all_hxf0 = get_hxfactor_dict().get(name1, [])  # 获取数据库里所有因子的列表
    all_hxf1 = get_factor_dict(factor_cache_file).get(name1, [])  # 获取本地缓存的因子列表

    if save_mode == 0:
        if name2 is None:
            hxf = [i for i in all_hxf0 if i not in all_hxf1]
        else:
            hxf = [i for i in name2 if i not in all_hxf1]
    elif save_mode == 1:
        if name2 is None:
            hxf = all_hxf0
        else:
            hxf = name2
    elif save_mode == 2:
        if name2 is None:
            hxf = all_hxf1
        else:
            hxf = [i for i in name2 if i in all_hxf1]

    n = math.ceil(len(hxf) / chunk_size)
    # 分块来取数据，不然数据量很大
    for i in tqdm(range(n), desc=f"{name1}"):
        # 从数据库中取数据
        open_api = get_open_api("share:research")
        if name1 in TEST_HXFACTOR:
            data = open_api.get_hxfactors_test(start_dt, end_dt, fields=hxf[i::n])
        else:
            data = open_api.get_hxfactors_data(name1, start_dt, end_dt, fields=hxf[i::n])
        data.set_index(["time", "code"], drop=True, append=False, inplace=True)
        columns = data.columns.to_list()
        for factor_name in columns:
            d: pd.DataFrame = data[factor_name].copy().unstack()
            d.index.rename(None, inplace=True)
            d.columns.rename(None, inplace=True)

            d.index = pd.to_datetime(d.index)
            if name1 == "集合竞价量价类":
                d = d.reindex(index=align_datetime_index(d.index))
                d.index = (d.index + pd.Timedelta(hours=1, minutes=25)).astype(int)
            else:
                gl_all_trade_days = get_all_trade_days()
                d.index = gl_all_trade_days[
                    np.searchsorted(gl_all_trade_days.to_numpy(), d.index.to_numpy(), "left") - 1
                ]
                d = d.reindex(index=align_datetime_index(d.index))
                d.index = (d.index + pd.Timedelta(hours=7)).astype(int)

            key = to_key("raw", name1, factor_name)
            if save_mode == 0 or save_mode == 1:
                save_data_to_store(key, d.drop(["920002.SZ"], axis=1, errors="ignore"), factor_cache_file, mode="w")
            else:
                save_data_to_store(key, d.drop(["920002.SZ"], axis=1, errors="ignore"), factor_cache_file, mode="a")


def dump_market_cap_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    def fnc(mct_: pd.DataFrame, start_dt: str, end_dt: str):
        mct = mct_.copy()
        mct.index = pd.to_datetime(mct.index)
        mct = mct.reindex(index=align_datetime_index(mct.index))
        mct.index = (mct.index + pd.Timedelta(hours=7)).astype(int)
        st = to_nstimestamp(f"{start_dt} 000000")
        ed = to_nstimestamp(f"{end_dt} 150000")
        mct = mct.loc[(mct.index >= st) & (mct.index <= ed)]
        return mct

    # 市值因子是包含A股、B股、港股等的总市值
    # 流通市值因子是包含A股、B股等的流通市值
    for func, name in [(query_market_cap_table, "市值因子"), (query_liquid_market_cap_table, "流通市值因子")]:
        mct = func()
        mct = fnc(mct, start_dt, end_dt)
        save_data_to_store(to_key("raw", "市值因子", name), mct, factor_cache_file)

    mct = query_ashare_outstanding_share_table()
    mct = fnc(mct, start_dt, end_dt)
    close_df = get_price(mct.columns.to_list(), start_dt, end_dt, "1d", ["close"], fq=None, is_panel=True)["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)
    save_data_to_store(to_key("raw", "市值因子", "A股流通市值"), close_df * mct, factor_cache_file)


def dump_money_flow_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    fields = [
        "act_buy_xl",
        "pas_buy_xl",
        "act_buy_l",
        "pas_buy_l",
        "act_buy_m",
        "pas_buy_m",
        "act_sell_xl",
        "pas_sell_xl",
        "act_sell_l",
        "pas_sell_l",
        "act_sell_m",
        "pas_sell_m",
        "buy_l",
        "sell_l",
        "dde_l",
        "net_flow_rate",
        "l_net_value",
    ]
    data = get_money_flow_step(
        get_all_securities_(start_dt, end_dt),
        start_date=start_dt,
        end_date=end_dt,
        fre_step="1d",
        fields=fields,
        is_panel=True,
    )
    for i in fields:
        j = data[i].copy()
        j.index = pd.to_datetime(j.index)
        j = j.reindex(index=align_datetime_index(j.index))
        j.index = (j.index + pd.Timedelta(hours=7)).astype(int)
        save_data_to_store(to_key("raw", "资金流", i), j, factor_cache_file)


def dump_industry_factor(
    start_dt: str,
    end_dt: str,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file: str = DEFAULT_INDUSTRY_DICT_CACHE_FILE,
) -> None:
    """保存行业因子

    Args:
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        industry_dict_cache_file (str, optional): 行业字典缓存文件. Defaults to DEFAULT_INDUSTRY_DICT_CACHE_FILE.
    """
    res = []
    for td in get_trade_days(start_dt, end_dt):  # type: ignore
        industry_dict = _get_industry_dict(td, industry_dict_cache_file)
        res.append(pd.DataFrame(industry_dict).loc["同花顺二级行业"].rename(td))
    res1: pd.DataFrame = pd.concat(res, axis=1).T  # index为日期，columns为股票代码，values为industry_index_thscode
    # index为nstimestamp，columns为股票代码
    res1.index = pd.to_datetime(res1.index)
    res1 = res1.reindex(index=align_datetime_index(res1.index))
    res1.index = (res1.index + pd.Timedelta(hours=7)).astype(int)

    fields = ["open", "close", "high", "low", "volume", "turnover"]
    res3 = {field: pd.DataFrame(np.nan, index=res1.index, columns=res1.columns) for field in fields}

    for industry_index_thscode in [i for i in set(res1.values.flatten()) if isinstance(i, str)]:
        dddd = res1 == industry_index_thscode
        res2 = dddd.copy().astype(float)
        save_data_to_store(to_key("raw", "行业因子", industry_index_thscode), res2, factor_cache_file)

        d = get_price(industry_index_thscode, start_dt, end_dt, "1d", fields, skip_paused=True, fq="post")
        d.index = (pd.to_datetime(d.index) + pd.Timedelta(hours=7)).astype(int)  # index为nstimestamp，columns为字段

        for field in fields:
            idx, col = d.index, res3[field].columns
            tmp = pd.DataFrame(d[field].to_numpy().reshape(-1, 1).repeat(len(col), axis=1), index=idx, columns=col)
            tmp = tmp.reindex(index=res3[field].index, fill_value=np.nan)
            res3[field][dddd] = tmp[dddd]

    for field in fields:
        save_data_to_store(to_key("raw", "行业量价因子", field), res3[field], factor_cache_file)


def dump_financial_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    cols = """pe
pe_ttm
pb
pcf_cash_flow_ttm
ps
ps_ttm
dividend_rate
dividend_rate_12_months
market_cap
capitalization
current_market_cap
circulating_cap
weighted_roe
overall_assets_net_income_ratio
roa
net_profit_margin_on_sales
gross_sales_profits
ratio_of_sales_to_cost
net_profit_div_income
opt_profit_div_income
before_tax_profit_div_income
opt_cost_div_income
sale_cost_div_income
administration_cost_div_income
financing_cost_div_income
impairment_loss_div_income
basic_pey_ear_growth_ratio
diluted_peg_rowth_ratio
net_cashflow_psg_rowth_ratio
overall_income_growth_ratio
opt_income_growth_ratio
opt_profit_growth_ratio
total_profit_growth_ratio
net_profit_growth_ratio
parent_company_profit_growth_ratio
parent_company_share_holders_net_profit_growth_ratio
net_cashflow_from_opt_act_growth_ratio
diluted_net_asset_growth_ratio
cash_cycle
days_sales_of_inventory
days_sales_outstanding
days_payable_outstanding
turnover_days_of_current_assets
inventory_turnover_ratio
turnover_ratio_of_receivable
turnover_ratio_of_account_payable
turnover_of_current_assets
turnover_of_fixed_assets
turnover_of_overall_assets
current_ratio
quick_ratio
conservative_quick_ratio
equity_ratio
equity_liabilities_attributable_to_shareholders_of_parent_company
equity_int_liabilities_attributable_to_shareholders_of_parent_company
tangible_assets_liabilities
tangible_assets_int_liabilities
tangible_assets_net_liabilities
ebitda_liabilities
netcashflows_from_opt_act_int_liabilities
netcashflows_from_opt_act_net_liabilities
long_term_debt_to_opt_capital_ratio
net_debt_equity
int_debt_equity""".split("\n")

    for i in tqdm(range(5), desc="财务因子"):
        aaa = cols[i::5]
        data = run_query(
            query(
                smd_factor.symbol, smd_factor.date, *[getattr(smd_factor, j) for j in aaa if hasattr(smd_factor, j)]
            ).filter(smd_factor.date.between(f"{start_dt}", f"{end_dt}"))
        )
        data.columns = data.columns.str.replace(r"^factor_", "", regex=True)
        data.drop(["id"], axis=1, inplace=True, errors="ignore")
        data = data.set_index(["date", "symbol"])
        data.index.rename((None, None), inplace=True)
        for col in data.columns:
            tmp = data[col].copy().unstack()
            tmp.index = pd.to_datetime(tmp.index)
            tmp = tmp.reindex(index=align_datetime_index(tmp.index))
            tmp.index = (tmp.index + pd.Timedelta(hours=7)).astype(int)
            save_data_to_store(to_key("raw", "财务因子", col), tmp, factor_cache_file)


def save_data_to_store(key: str, data: pd.DataFrame, store_file: str = DEFAULT_FACTOR_CACHE_FILE, mode: str = "a"):
    data = data.copy().sort_index()
    # 数据中可能存在缺失值，处理方式是丢弃最后几行全是缺失值的数据，中间的保留，因为有些因子在9:10分更新，但是在这之后数据库已经有记录了
    finite_rows = np.where(~data.isna().all(axis=1))[0]
    if finite_rows.size == 0:
        logger.info(f"{key} is empty")
        return
    else:
        data = data.iloc[: finite_rows.max() + 1]

    data.columns = data.columns.map(encode_thscode)
    with acquire_flock(pathlib.Path(store_file).with_suffix(".lock"), "EX"):
        with pd.HDFStore(store_file, mode="a") as store:
            if key in store:
                if mode == "a":
                    tmp: pd.DataFrame = store.get(key)  # type: ignore
                    nan_rows = tmp.index[tmp.isna().all(axis=1)]
                    new_symbols = data.columns.difference(tmp.columns)
                    rewrite_rows = data.index.intersection(nan_rows)
                    if new_symbols.size > 0 or rewrite_rows.size > 0:
                        if new_symbols.size > 0:
                            logger.info(f"{key} 新增 symbol: {','.join(str(symbol) for symbol in new_symbols)}")
                        if rewrite_rows.size > 0:
                            logger.info(f"{key} 重写 {','.join(str(row) for row in rewrite_rows)} 行")
                        store.remove(key)  # type: ignore
                        data = pd.concat([tmp.loc[tmp.index.difference(data.index)], data], axis=0).sort_index()
                        store.put(key=key, value=data, format="table")
                    else:
                        data = data.loc[~data.index.isin(tmp.index)].reindex(columns=tmp.columns)
                        store.append(key, data)
                else:
                    store.remove(key)  # type: ignore
                    store.put(key=key, value=data, format="table")
            else:
                store.put(key=key, value=data, format="table")
        if not data.empty:
            text = "{}保存完成, mode：{}, 时间：{} - {}".format(
                key,
                mode,
                *map(
                    lambda x: pd.to_datetime(x, utc=True).tz_convert("Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S"),
                    [data.index.min(), data.index.max()],
                ),
            )
            logger.info(text)
        else:
            logger.info(f"{key}本地数据已存在，跳过！")
    get_factor_dict.cache_clear()


class FactorOrthogonalizer:
    def __init__(
        self,
        x_name: Optional[Sequence[Tuple[str, str, str]]] = None,
        factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
    ) -> None:
        self.x_list = []
        self.factor_cache_file = factor_cache_file
        self.orth_name = None

        # 如果提供了x_name，使用它；否则使用默认值
        if x_name:
            self.x_name = x_name
        else:
            self.x_name = [("raw", "行业因子", i) for i in get_factor_dict()["行业因子"]] + [
                ("raw", "市值因子", "A股流通市值")
            ]

        # 检查是否已存在相同的x_name配置
        existing_config = self._find_existing_config()
        if existing_config:
            self.orth_name = existing_config

        # 如果没有找到匹配的配置或加载失败，创建新配置
        if not self.orth_name:
            self.orth_name = self._generate_new_orth_name()
            self._save_to_hdf()

        # 初始化并保存
        for db, n1, n2 in self.x_name:
            tmp = get_factor(n1, n2, database=db)
            self.x_list.append(tmp if not n1 == "市值因子" else np.log(tmp))
        self.idx = reduce(lambda x, y: x.intersection(y), [i.index for i in self.x_list]).sort_values(ascending=True)

    @classmethod
    def _get_config_df(
        cls, factor_cache_file: str, mode: Literal["a", "w", "r", "r+"] = "r"
    ) -> Tuple[Optional[pd.DataFrame], Optional[pd.HDFStore]]:
        """获取配置表DataFrame和HDFStore对象

        Args:
            factor_cache_file (str): 因子缓存文件路径
            mode (Literal['a', 'w', 'r', 'r+']): 打开模式，默认为"r"只读模式

        Returns:
            Tuple[Optional[pd.DataFrame], Optional[pd.HDFStore]]: 配置表DataFrame和HDFStore对象
        """
        try:
            store = pd.HDFStore(factor_cache_file, mode=mode)
            config_key = "/orth/config"

            if config_key not in store:
                return None, store

            config_df = store.get(config_key)
            return config_df, store
        except Exception as e:
            logger.error(f"获取配置表失败: {e}")
            return None, None

    @classmethod
    def get_orths(cls, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> Optional[pd.DataFrame]:
        """获取所有正交化配置

        Args:
            factor_cache_file (str): 因子缓存文件路径

        Returns:
            Optional[pd.DataFrame]: 所有正交化配置
        """
        with acquire_flock(pathlib.Path(factor_cache_file).with_suffix(".lock"), "SH"):
            config_df, store = cls._get_config_df(factor_cache_file)
            if store is not None:
                store.close()
            return config_df

    def _find_existing_config(self) -> Optional[str]:
        """查找是否已存在相同的x_name配置

        Returns:
            Optional[str]: 如果找到匹配的配置，返回对应的orth_name；否则返回None
        """
        try:
            with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "SH"):
                config_df, store = self.__class__._get_config_df(self.factor_cache_file)
                if store is not None:
                    try:
                        if config_df is None or config_df.empty:
                            return None

                        # 使用向量化操作检查是否有匹配的配置
                        x_name_str = str(sorted(self.x_name))
                        matches = config_df[config_df["x_name_str"] == x_name_str]

                        if not matches.empty:
                            return matches.iloc[0]["orth_name"]
                    finally:
                        store.close()
            return None
        except Exception as e:
            logger.error(f"查找正交化配置失败: {e}")
            return None

    def _generate_new_orth_name(self) -> str:
        """生成新的orth_name

        Returns:
            str: 新生成的orth_name
        """
        try:
            with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "SH"):
                config_df, store = self.__class__._get_config_df(self.factor_cache_file)
                if store is not None:
                    try:
                        if config_df is not None and not config_df.empty:
                            # 使用正则表达式和向量化操作提取数字部分
                            orth_names = config_df["orth_name"].tolist()
                            max_num = 0

                            # 使用列表推导式和正则表达式提取数字

                            nums = [
                                int(re.search(r"\d+", name).group())
                                for name in orth_names
                                if name.startswith("orth") and re.search(r"\d+", name)
                            ]

                            if nums:
                                max_num = max(nums)

                            return f"orth{max_num + 1}"
                    finally:
                        store.close()
            return "orth0"  # 如果没有找到配置或出错，返回默认值
        except Exception as e:
            logger.error(f"生成新的orth_name失败: {e}")
            return "orth0"

    def _save_to_hdf(self) -> None:
        """将x_name和orth_name信息保存到HDF文件中，只有在新增orth_name时才执行保存"""
        with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "EX"):
            config_df, store = self.__class__._get_config_df(self.factor_cache_file, mode="a")
            if store is not None:
                try:
                    config_key = "/orth/config"
                    x_name_str = str(sorted(self.x_name))

                    if config_df is not None and not config_df.empty:
                        # 使用向量化操作检查是否已存在相同的orth_name
                        if self.orth_name in config_df["orth_name"].values:
                            logger.info(f"orth_name {self.orth_name} 已存在，不进行修改")
                            return

                        # 使用向量化操作检查是否已存在相同的x_name配置
                        if (config_df["x_name_str"] == x_name_str).any():
                            matching_row = config_df[config_df["x_name_str"] == x_name_str].iloc[0]
                            logger.info(f"相同的x_name配置已存在于orth_name {matching_row['orth_name']}，不进行保存")
                            return

                        # 新增配置
                        new_config = pd.DataFrame({"orth_name": [self.orth_name], "x_name_str": [x_name_str]})
                        config_df = pd.concat([config_df, new_config], ignore_index=True)
                    else:
                        # 创建新的配置表
                        config_df = pd.DataFrame({"orth_name": [self.orth_name], "x_name_str": [x_name_str]})

                    # 保存配置
                    store.put(key=config_key, value=config_df, format="table")
                    logger.info(f"新的正交化配置 {self.orth_name} 已保存到: {config_key}")
                finally:
                    store.close()

    def _load_from_hdf(self) -> bool:
        """从HDF文件中加载x_name信息

        Returns:
            bool: 是否成功加载
        """
        try:
            with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "SH"):
                config_df, store = self.__class__._get_config_df(self.factor_cache_file)
                if store is not None:
                    store.close()
                    if config_df is not None:
                        # 使用向量化操作查找匹配的orth_name
                        match = config_df[config_df["orth_name"] == self.orth_name]
                        if not match.empty:
                            # 加载x_name列表
                            self.x_name = eval(match["x_name_str"].iloc[0])

                            # 加载因子数据
                            self._load_factor_data()
                            return True
            return False
        except Exception as e:
            logger.error(f"从HDF加载正交化配置失败: {e}")
            return False

    def _load_factor_data(self) -> None:
        """加载因子数据"""
        self.x_list = []
        for db, n1, n2 in self.x_name:
            try:
                tmp = get_factor(n1, n2, database=db)
                self.x_list.append(tmp if not n1 == "市值因子" else np.log(tmp))
            except Exception as e:
                logger.error(f"加载因子数据失败 ({db}, {n1}, {n2}): {e}")
                raise

        if self.x_list:
            self.idx = reduce(lambda x, y: x.intersection(y), [i.index for i in self.x_list]).sort_values(
                ascending=True
            )
        else:
            logger.warning("没有成功加载任何因子数据")
            self.idx = pd.Index([])

    @classmethod
    def from_orth_name(
        cls, orth_name: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE
    ) -> "FactorOrthogonalizer":
        """通过orth_name从HDF文件中还原实例

        Args:
            orth_name (str): 正交化配置名称
            factor_cache_file (str): 因子缓存文件路径

        Returns:
            FactorOrthogonalizer: 正交化器实例
        """
        # 创建一个空实例
        instance = cls.__new__(cls)
        instance.x_list = []
        instance.factor_cache_file = factor_cache_file
        instance.orth_name = orth_name

        # 加载配置
        if instance._load_from_hdf():
            logger.info(f"从HDF文件中加载了正交化配置: {instance.orth_name}")
            return instance
        else:
            logger.error(f"无法从HDF文件中加载正交化配置: {orth_name}")
            return None

    def orthogonalize_factor(
        self, factor_df: Union[str, pd.DataFrame], start_dt: str = None, end_dt: str = None, dump: bool = False
    ) -> pd.DataFrame:
        """对因子df进行市值和行业因子正交化

        Args:
            factor_df (Union[str, pd.DataFrame]): 待正交化的因子数据，或者因子数据的HDF键名
            start_dt (str, optional): 开始日期，格式为'YYYY-MM-DD'。默认为None，表示使用全部数据
            end_dt (str, optional): 结束日期，格式为'YYYY-MM-DD'。默认为None，表示使用全部数据
            dump (bool, optional): 是否将正交化后的因子数据保存到HDF文件中。默认为False

        Returns:
            pd.DataFrame: 正交化后的因子数据
        """
        if dump:
            if not isinstance(factor_df, str):
                raise ValueError("dump=True时，factor_df必须为HDF键名")
        if isinstance(factor_df, str):
            db, l1n, l2n, shift = from_key(factor_df)
            factor_df = get_factor(l1n, l2n, database=db, shift=shift)
        # 根据日期范围筛选数据
        factor_df = slice_dataset(factor_df, start_dt and f"{start_dt} 000000", end_dt and f"{end_dt} 235959")
        # 获取交集索引
        idx = self.idx.intersection(factor_df.index)

        # 对因子进行正交化
        res = orthogonalize_factor(factor_df.loc[idx], [i.loc[idx] for i in self.x_list], 1, 0, 1, False)
        if dump:
            key = to_key(self.orth_name, l1n, l2n)
            save_data_to_store(key, res, self.factor_cache_file)
        return res


def _get_industry_dict(dt, industry_dict_cache_file: str) -> Dict[str, Dict[str, str]]:
    """返回指定日期每个股票的行业字典，如{'000995.SZ': {'同花顺二级行业': '881133.TI', '申万二级行业': '801125.SL', '中信二级行业': 'CI005156.CI'}}"""
    # 2021年7月30日，同花顺二级行业分类调整，旧的行业代码映射不到指数代码，这里需要手动做映射
    old_industry_code_dict = {
        "T0203": "881107.TI",
        "T0602": "881116.TI",
        "T0704": "881120.TI",
        "T1001": "881129.TI",
        "T1902": "881156.TI",
        "T1903": "881157.TI",
        "T2201": "881162.TI",
        "T2202": "881163.TI",
    }
    dt = pd.Timestamp(dt).strftime("%Y-%m-%d")
    industry_dict_cache_path = pathlib.Path(industry_dict_cache_file)
    industry_dict_cache_path.parent.mkdir(parents=True, exist_ok=True)

    industry_dict = {}
    flag = False
    with acquire_flock(industry_dict_cache_path.with_suffix(".lock"), "SH"):
        with shelve.open(industry_dict_cache_file) as db:
            # industry_dict - {'股票代码': {'同花顺二级行业': '881133.TI', '申万二级行业': '801125.SL', '中信二级行业': 'CI005156.CI'}}
            if dt in db:
                industry_dict = db[dt]
                flag = True

    if not flag:
        for symbol in tqdm(get_all_securities("stock", dt).index.tolist(), desc=dt):
            try:
                industry_dict[symbol] = dict(
                    [(i[1], i[0] if i[0] is not None else i[3]) for i in get_stock_related_industries(symbol, dt)]
                )
            except Exception:
                print(f"{dt} {symbol} 没有获取到相关行业")
        with acquire_flock(industry_dict_cache_path.with_suffix(".lock"), "EX"):
            with shelve.open(industry_dict_cache_file) as db:
                db[dt] = industry_dict

    for k in industry_dict.keys():
        if "同花顺二级行业" not in industry_dict[k]:
            print("{} {} 未获取到同花顺二级行业代码".format(dt, k))
            continue
        if industry_dict[k]["同花顺二级行业"] in old_industry_code_dict:
            industry_dict[k]["同花顺二级行业"] = old_industry_code_dict[industry_dict[k]["同花顺二级行业"]]
    return industry_dict


def get_factor(
    name1: str,
    name2: str,
    shift: int = 0,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
    start_dt: Optional[str] = None,
    end_dt: Optional[str] = None,
    cached: bool = False,
    database: str = "raw",
) -> pd.DataFrame:
    """取因子数据

    Args:
        name1 (str): 因子大类名称
        name2 (str): 因子名称

        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        start_dt (Optional[str], optional): 开始日期，None则取到最早，为None时如果因子数据不存在则报错. Defaults to None.
        end_dt (Optional[str], optional): 结束日期，None则取到最后，为None时如果因子数据不存在则报错. Defaults to None.

    Raises:
        ValueError: 当前缓存数据中没有所需的因子，需要传入start_dt 和 end_dt 用于提取数据

    Returns:
        pd.DataFrame: 因子数据
    """
    if not pathlib.Path(factor_cache_file).exists():
        return pd.DataFrame()

    factor_dict = get_factor_dict(factor_cache_file, database)
    if name1 not in factor_dict or name2 not in factor_dict[name1]:
        return pd.DataFrame()

    df = _get_factor(factor_cache_file, database, name1, name2, cached).shift(shift)
    df = slice_dataset(df, start_dt and f"{start_dt} 000000", end_dt and f"{end_dt} 235959", "both")
    return df


def _get_factor(factor_cache_file: str, database: str, name1: str, name2: str, cached: bool) -> pd.DataFrame:
    key = to_key(database, name1, name2)
    if cached and key in shared_dict:
        return shared_dict[key].copy()

    with acquire_flock(pathlib.Path(factor_cache_file).with_suffix(".lock"), "SH"):
        with pd.HDFStore(factor_cache_file, mode="r") as store:
            df = store[key].sort_index().copy()
            df.columns = df.columns.map(decode_thscode)

    with mp_lock:
        if cached and key not in shared_dict:
            shared_dict[key] = df
    return df  # type: ignore


def check_db(database: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> Mapping[str, Sequence[str]]:
    factor_dict = get_factor_dict(factor_cache_file, database)
    result = {}
    for n1 in factor_dict:
        for n2 in factor_dict[n1]:
            data = get_factor(n1, n2, database=database)
            if data.empty:
                result[n1] = []
                logger.warning(f"{n1} {n2} 为空")
            else:
                idx = pd.to_datetime(data.dropna(how="all", axis=0).index).normalize()
                tmp = align_datetime_index(pd.to_datetime(data.index).normalize())
                tmp = tmp.difference(idx)
                if not tmp.empty:
                    result.setdefault(n1, []).extend(tmp)
                    logger.warning(f"{n1} {n2} 有缺失数据：{tmp}")
    return result


def factor_df_gnt(
    date_interval: Iterable[Tuple[str, str]],
    factor_list: Iterable[Tuple[str, str, str, str]] = [("raw", "*", "*", "0")],
    masks: Optional[Iterable] = None,
):
    """hxf因子生成器，遍历所有hxf因子，除了集合竞价量价类和Transformer量价预测类"""
    for db, level1_names, level2_names_, shifts_ in factor_list:
        factor_dict = get_factor_dict(DEFAULT_FACTOR_CACHE_FILE, database=db)
        if level1_names == "*":
            level1_names = HXFACTOR + ["资金流", "财务因子"]
        else:
            level1_names = level1_names.split(",")

        for l1n in level1_names:
            if level2_names_ == "*":
                level2_names = list(copy.deepcopy(factor_dict.get(l1n, [])))
            else:
                level2_names = level2_names_.split(",")
            shifts = list(map(int, shifts_.split(",")))

            if l1n in ["集合竞价量价类", "Transformer量价预测类"]:
                continue
            random.shuffle(level2_names)
            for factor in level2_names:
                factor_df = get_factor(name1=l1n, name2=factor, shift=0, database=db)
                for mask_call in masks or [lambda x: (x, {})]:
                    factor_df, info = mask_call(factor_df)
                    for s in shifts:
                        factor_df_shift = factor_df.shift(s)
                        key = to_key(db, l1n, factor, s)
                        for start_dt, end_dt in date_interval:
                            yield (
                                (start_dt, end_dt),
                                key,
                                slice_dataset(factor_df_shift, f"{start_dt} 090000", f"{end_dt} 150000", "both"),
                                info,
                            )

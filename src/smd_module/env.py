from enum import Enum
from typing import Literal

from DataAPI.openapi.open_api import OpenAPI

gl_sql_api = OpenAPI()
gl_original_data_path = "./"


class Env(Enum):
    TRAIN = "H"
    TEST = "R"


gl_env = Env.TRAIN
gl_original_data_path = ""
gl_model_path = ""


def set_env(env: Literal["H", "R"]):
    """设置当前运行环境

    Args:
        env (Literal[&quot;H&quot;, &quot;R&quot;]): H - 使用历史数据建模， R - 使用最新数据预测
    """
    global gl_env, gl_original_data_path
    gl_env = Env(env)

    if gl_env == Env.TRAIN:
        gl_original_data_path = "/home/<USER>/work/database/original_data/history/"
    else:
        gl_original_data_path = "/home/<USER>/work/database/original_data/recent/"

import copy
import math
import pathlib
import pickle
import time
from multiprocessing.dummy import Pool
from typing import Callable, Dict, List, Optional, Tuple, Union

import lmdb
import numpy as np
import pandas as pd
from tqdm import tqdm  # type: ignore

from .utils import calc_trade_day


# 创建一个新的环境
def create_env(db_path):
    # 打开一个新的环境
    return lmdb.open(
        db_path,
        map_size=40 * 1024 * 1024 * 1024,  # 数据库的最大大小，这里是1GB
        subdir=False,
        meminit=False,
        map_async=True,
    )


def iterate_keys(env):
    """遍历数据库中的所有键"""
    keys = []
    with env.begin() as txn:
        cursor = txn.cursor()
        for key, _ in cursor:
            keys.append(key.decode())
    return keys


# 插入数据到数据库
def insert_data(env, key, value):
    with env.begin(write=True) as txn:
        txn.put(key.encode(), pickle.dumps(value))


# 获取数据
def get_data(env, key):
    with env.begin() as txn:
        value = txn.get(key.encode())
        return pickle.loads(value) if value is not None else None


def split_list(input_list, n):
    # 计算每个子列表的大小
    k, m = divmod(len(input_list), n)
    # 使用列表推导式生成 N 个子列表
    ret = [input_list[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n)]
    return [i for i in ret if len(i) > 0]


class Cache:
    def __init__(
        self,
        start_dt: str,
        end_dt: str,
        freq: int = 1,
        include_open: bool = False,
        get_data_api: Optional[Callable] = None,
        path: str = ".",
    ):
        """初始化

        Args:
            end_dt (str): 行情截止日期
            freq (int, optional): 数据频率, 分钟. Defaults to 1.
            include_open (bool, optional): 是否包含开盘价. Defaults to False.
            get_data_api (Optional[Callable], optional): 获取数据接口. Defaults to None.
        """
        self.start_dt = start_dt
        self.end_dt = end_dt
        self.freq = freq
        self.include_open = include_open
        self.get_data_api = get_data_api or self._default_get_data

        self.cache = create_env(str(pathlib.Path(path, f"{freq}m_{1 if include_open else 0}.lmdb")))
        self.db_keys = iterate_keys(self.cache)

    @staticmethod
    def _default_get_data(start_dt: str, end_dt: str, symbols_need_get_price: List[str], freq: int, include_open: bool):
        from mindgo_api import get_price

        tmp = get_price(
            symbols_need_get_price,
            f"{start_dt} 0000",
            f"{end_dt} 1500",
            "1m",
            ["close", "open"],
            fq="post",
            skip_paused=True,
            is_panel=True,
        )

        close_df = tmp["close"]
        open_df = tmp["open"]

        data = close_df.loc[close_df.index.map(lambda x: (x.minute % freq == 0))]
        if include_open:
            d2 = open_df.loc[open_df.index.map(lambda x: x.minute == 31 and x.hour == 9)]
            d2.index = d2.index - pd.Timedelta(minutes=1)
            data: pd.DataFrame = pd.concat([data, d2], axis=0).sort_index()

        return data

    def get(self, symbol: Union[List[str], str]) -> List[Tuple]:
        """获取数据

        Args:
            symbol (str): 股票代码
            start_dt (str): 行情数据开始时间
            freq (str, optional): 数据频率. Defaults to "1m".

        Returns:
            List[Tuple]: _description_
        """
        symbols_need_get_price = []
        symbol = symbol if isinstance(symbol, list) else [symbol]
        for i in symbol:
            if i not in self.db_keys:
                symbols_need_get_price.append(i)

        if len(symbols_need_get_price) > 0:
            data = self.get_data_api(self.start_dt, self.end_dt, symbols_need_get_price, self.freq, self.include_open)

            if "dt" not in self.db_keys:
                idx = data.index
                insert_data(self.cache, "dt", idx)
            else:
                idx = get_data(self.cache, "dt")
                data = data.reindex(index=idx, fill_value=np.nan)

            for i in symbols_need_get_price:
                insert_data(self.cache, i, data[i].tolist())
            self.db_keys = iterate_keys(self.cache)

        dt = get_data(self.cache, "dt").to_pydatetime().tolist()
        st = np.searchsorted(dt, pd.Timestamp(f"{self.start_dt} 0000").to_pydatetime(), "left")
        ed = np.searchsorted(dt, pd.Timestamp(f"{self.end_dt} 1500").to_pydatetime(), "right")
        dt = dt[st:ed]
        with Pool(min(10, len(symbol))) as p:
            res = p.starmap(get_data, [(self.cache, i) for i in symbol])
        return dict(zip(symbol, [(dt, i[st:ed]) for i in res]))


def backtest_per_stock(args_lst: List[Dict], symbol: str, data: List[Tuple]):
    from strategy_lib import PyTask

    tasks = {}
    for args in args_lst:
        ctime = args["ctime"]
        task = PyTask(**args["create"])
        for i in args["open"]:
            task.register_open_position(i)
        for i in args.get("close", []):
            task.register_close_position(i)
        tasks[ctime] = task

    cost_basis = {k: np.nan for k in tasks}
    trade_record = {k: {} for k in tasks}
    for dt, last_price in data:
        if pd.isna(last_price):
            continue
        finish = []
        for ctime, task in tasks.items():
            signal = task.on_data(dt, last_price)
            if dt > ctime:
                if signal > 0 and trade_record[ctime] == {}:
                    task.on_instruction(dt, last_price, 500)
                    task.on_order_reply(dt, last_price, 500)
                    cost_basis[ctime] = last_price * 1.002
                    trade_record[ctime].update(
                        {
                            "ctime": ctime.strftime("%Y%m%d %H%M"),
                            "symbol": symbol,
                            "open_dt": dt.strftime("%Y%m%d %H%M"),
                            "open_price": cost_basis[ctime],
                        }
                    )
                if signal < 0 and trade_record[ctime] != {}:
                    task.on_instruction(dt, last_price, -500)
                    task.on_order_reply(dt, last_price, -500)
                    trade_record[ctime].update(
                        {
                            "close_dt": dt.strftime("%Y%m%d %H%M"),
                            "close_price": last_price * 0.998,
                            "profit": last_price * 0.998 / cost_basis[ctime] - 1,
                        }
                    )
                    finish.append(ctime)
        tasks = {k: v for k, v in tasks.items() if k not in finish}
        if len(tasks) == 0:
            break
    else:
        for ctime in tasks:
            if "open_dt" in trade_record[ctime] and "close_dt" not in trade_record[ctime]:
                trade_record[ctime].update(
                    {
                        "close_dt": dt.strftime("%Y%m%d %H%M"),
                        "close_price": last_price * 0.998,
                        "profit": last_price * 0.998 / cost_basis[ctime] - 1,
                    }
                )
    return trade_record.values()


def backtest(select_stocks: Dict[str, List[Dict]], quotation_cache: Cache, p, chunk_num=5):
    log = []
    result = pd.Series()

    symbol_ctime_map = {}
    for k, v in select_stocks.items():
        for vv in v:
            if vv["symbol"] not in symbol_ctime_map:
                symbol_ctime_map[vv["symbol"]] = []
            symbol_ctime_map[vv["symbol"]].append(k)

    keys_list = split_list(list(symbol_ctime_map.keys()), chunk_num)

    for key in keys_list:
        tmp = {k: v for k, v in symbol_ctime_map.items() if k in key}
        data = quotation_cache.get(key)

        for symbol, ctime_lst in tqdm(tmp.items(), total=len(tmp)):
            args_lst = []
            for ctime in ctime_lst:
                end_dt = calc_trade_day(ctime, 5).strftime("%Y%m%d 1500")
                end_dt = pd.Timestamp(end_dt).to_pydatetime()
                p1 = copy.deepcopy(p)
                p1["create"]["end_dt"] = end_dt
                p1["ctime"] = pd.Timestamp(ctime).to_pydatetime()
                args_lst.append(p1)
            res_lst = backtest_per_stock(args_lst, symbol, zip(data[symbol]))
            for res in res_lst:
                if len(res) > 0:
                    log.append(res)
                    if res["close_dt"] not in result.index:
                        result[res["close_dt"]] = 0
                    result[res["close_dt"]] += res["profit"]

    return result, log


def plot(r):
    rr = r.copy()
    rr.index = pd.to_datetime(rr.index)
    rr.sort_index().cumsum().plot(figsize=(17.5, 5))


def stats(r2, dropout=0.0, months=1):
    ret = {}
    r2 = pd.DataFrame(r2) if not isinstance(r2, pd.DataFrame) else r2
    # r2 = r2.copy()
    # r2["ctime1"] = r2["ctime"].astype(int)
    # r2 = r2.groupby(["symbol", "close_dt"]).apply(lambda x: x.nsmallest(5, "ctime1")).reset_index(drop=True)
    r2 = pd.concat([j.copy().sort_values("ctime").iloc[:5] for _, j in r2.groupby(["symbol", "close_dt"])], axis=0)

    rr = r2
    rr["year"] = rr["ctime"].apply(lambda x: x.year + ((x.month - 1) // months) * months / 12)
    rr["date"] = rr["ctime"].apply(lambda x: x.year * 10000 + x.month * 100 + x.day)
    # tmp1 = rr.groupby("year")["profit"].median()
    # print(tmp1.cumsum())
    tmp2 = []
    for _, j in rr.groupby("year"):
        mean_profit_per_day = []
        for _, jj in j.groupby("date"):
            tmp = jj["profit"].values
            tmp = tmp[np.isfinite(tmp)]
            sorted_data = np.sort(tmp)
            n = len(sorted_data)
            remove_count = int(n * dropout)
            data_without_extremes = sorted_data[: n - remove_count]
            data_without_extremes = data_without_extremes[np.isfinite(data_without_extremes)]
            this_day_average_profit = np.mean(data_without_extremes)
            mean_profit_per_day.append(this_day_average_profit)
        mean_profit_per_day = np.array(mean_profit_per_day)
        mean_profit_per_day = mean_profit_per_day[np.isfinite(mean_profit_per_day)]

        if len(mean_profit_per_day) > 0:
            tmp2.append((len(mean_profit_per_day), np.nanmean(mean_profit_per_day)))

    weights, values = zip(*tmp2)
    weights = np.array(weights)
    values = np.array(values)
    average = np.average(values, weights=weights)
    # 计算每个值与加权平均值之差的平方
    variance = np.average((values - average) ** 2, weights=weights)
    # 标准差是方差的平方根
    weight_std = np.sqrt(variance)

    ret["sharp"] = average / weight_std
    # ret["sharp"] = tmp2.mean() / tmp2.std()

    ret["日均收益"] = average
    ret["有效交易天数"] = np.sum(weights)
    ret["累计日收益"] = average * ret["有效交易天数"]
    ret["平均收益"] = r2["profit"].mean()
    ret["中位数收益"] = r2["profit"].median()
    # ret["(盈)平均收益"] = r2["profit"].loc[r2["profit"] > 0].mean()
    # ret["(亏)平均收益"] = r2["profit"].loc[r2["profit"] <= 0].mean()
    # ret["(盈)中位数收益"] = r2["profit"].loc[r2["profit"] > 0].median()
    # ret["(亏)中位数收益"] = r2["profit"].loc[r2["profit"] <= 0].median()
    tmp = r2.groupby("symbol")["profit"].sum()
    ret["个股平均收益"] = tmp.mean()
    ret["个股中位数收益"] = tmp.median()
    ret["个股胜率"] = tmp.apply(lambda x: (x > 0)).mean()
    ret["交易次数"] = r2["profit"].count()
    ret["总收益"] = r2["profit"].sum()
    ret["胜率"] = (r2["profit"] > 0).mean()
    ret["盈亏比"] = r2["profit"].loc[r2["profit"] > 0].mean() / abs(r2["profit"].loc[r2["profit"] <= 0].mean())
    return ret


def plot2(r):
    rr = r.groupby("close_dt")["profit"].sum()
    rr.sort_index().cumsum().plot(figsize=(17.5, 5))


def rbacktest(select_stocks: Dict[str, List[Dict]], quotation_cache: Cache, p, chunk_size=200, **kwargs):
    from strategy_lib import Backtest

    trade_record = []
    equity_sr = pd.Series()

    symbol_ctime_map = {}  # {'600000.SH': ['20240926 0930']}
    for k, v in select_stocks.items():
        for vv in v:
            if vv["symbol"] not in symbol_ctime_map:
                symbol_ctime_map[vv["symbol"]] = []
            symbol_ctime_map[vv["symbol"]].append(k)

    keys_list = split_list(
        list(symbol_ctime_map.keys()), math.ceil(len(symbol_ctime_map.keys()) / chunk_size)
    )  # [['600000.SH'], ['601012.SH']]

    bck = Backtest()
    init = False
    for key in keys_list:
        tmp = {k: v for k, v in symbol_ctime_map.items() if k in key}
        data = quotation_cache.get(key)

        for symbol, ctime_lst in tmp.items():
            if not init:
                bck.register_dts(data[symbol][0])
                init = True
            for e, ctime in enumerate(ctime_lst):
                end_dt = calc_trade_day(ctime, kwargs.get("deadline", 5), kwargs.get("all_trade_days", None)).strftime(
                    "%Y%m%d 1500"
                )
                end_dt = pd.Timestamp(end_dt).to_pydatetime()
                a = bck.register_args(
                    e == 0,
                    pd.Timestamp(ctime).to_pydatetime(),
                    (p["create"]["is_t0"], end_dt, p["create"]["force_close_after_deadline"], p["create"]["open_side"]),
                    p["open"],
                    p["close"],
                )
            res_lst = bck.run(data[symbol][1], kwargs.get("cost", 1.5 / 1000))

            for res in res_lst:
                if len(res) > 0:
                    res["symbol"] = symbol
                    trade_record.append(res)
                    if res["close_dt"] not in equity_sr.index:
                        equity_sr[res["close_dt"]] = 0
                    equity_sr[res["close_dt"]] += res["profit"]
    trade_record = pd.DataFrame(trade_record)
    trade_record["close_dt"] = pd.to_datetime(trade_record["close_dt"] * 1e9)
    trade_record["open_dt"] = pd.to_datetime(trade_record["open_dt"] * 1e9)
    trade_record["ctime"] = pd.to_datetime(trade_record["ctime"] * 1e9)
    return equity_sr.sort_index(), trade_record[
        ["ctime", "symbol", "open_dt", "open_price", "close_dt", "close_price", "profit"]
    ].sort_values(by="ctime").reset_index(drop=True)


def batch_rbacktest(select_stocks: Dict[str, List[Dict]], quotation_cache: Cache, p_dict, chunk_size=200, **kwargs):
    from strategy_lib import Backtest

    trade_record = {k: [] for k in p_dict}

    symbol_ctime_map = {}
    for k, v in select_stocks.items():
        for vv in v:
            if vv["symbol"] not in symbol_ctime_map:
                symbol_ctime_map[vv["symbol"]] = []
            symbol_ctime_map[vv["symbol"]].append(k)

    symbol_slice_list = split_list(list(symbol_ctime_map.keys()), math.ceil(len(symbol_ctime_map.keys()) / chunk_size))

    bck = Backtest()
    init = False
    for e, symbol_slice in enumerate(symbol_slice_list):
        tmp = {k: v for k, v in symbol_ctime_map.items() if k in symbol_slice}
        data = quotation_cache.get(symbol_slice)

        for symbol, ctime_lst in tqdm(tmp.items(), total=len(tmp), desc=f"{e+1}/{len(symbol_slice_list)}"):
            if not init:
                bck.register_dts(data[symbol][0])
                init = True

            restart = 0
            for ctime in ctime_lst:
                end_dt = calc_trade_day(ctime, kwargs.get("deadline", 1), kwargs.get("all_trade_days", None)).strftime(
                    "%Y%m%d 1500"
                )
                end_dt = pd.Timestamp(end_dt).to_pydatetime()
                ctime_ = pd.Timestamp(ctime).to_pydatetime()
                for args_key, p in p_dict.items():
                    a = bck.register_args_map(
                        restart == 0,
                        args_key,
                        ctime_,
                        (
                            p["create"]["is_t0"],
                            end_dt,
                            p["create"]["force_close_after_deadline"],
                            p["create"]["open_side"],
                        ),
                        p["open"],
                        p["close"],
                    )
                    restart += 1
            res_dict: Dict = bck.batch_run(data[symbol][1], kwargs.get("cost", 1.5 / 1000))

            for args_key, res_ in res_dict.items():
                for res in res_:
                    if len(res) > 0:
                        res["symbol"] = symbol
                        trade_record[args_key].append(res)

    for args_key in trade_record:
        trade_record[args_key] = pd.DataFrame(trade_record[args_key])
        if not trade_record[args_key].empty:
            trade_record[args_key]["close_dt"] = pd.to_datetime(trade_record[args_key]["close_dt"] * 1e9)
            trade_record[args_key]["open_dt"] = pd.to_datetime(trade_record[args_key]["open_dt"] * 1e9)
            trade_record[args_key]["ctime"] = pd.to_datetime(trade_record[args_key]["ctime"] * 1e9)
            trade_record[args_key] = (
                trade_record[args_key][
                    ["ctime", "symbol", "side", "open_dt", "open_price", "close_dt", "close_price", "profit"]
                ]
                .sort_values(by="ctime")
                .reset_index(drop=True)
            )

    return trade_record

import os
import time
from typing import Dict, List, Set, Tuple
from datetime import datetime

from .models import Task, TaskStatus


def format_time_delta(start_time: datetime, end_time: datetime = None) -> str:
    """格式化时间差
    
    Args:
        start_time: 开始时间
        end_time: 结束时间，默认为当前时间
        
    Returns:
        str: 格式化后的时间差字符串
    """
    if end_time is None:
        end_time = datetime.now()
    
    delta = end_time - start_time
    total_seconds = delta.total_seconds()
    
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    if hours > 0:
        return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
    elif minutes > 0:
        return f"{int(minutes)}m {int(seconds)}s"
    else:
        return f"{int(seconds)}s"


def build_dependency_graph(tasks: List[Task]) -> Dict[str, Set[str]]:
    """构建任务依赖图
    
    Args:
        tasks: 任务列表
        
    Returns:
        Dict[str, Set[str]]: 依赖图，键为任务ID，值为依赖该任务的任务ID集合
    """
    # 初始化依赖图
    graph = {task.id: set() for task in tasks}
    
    # 构建依赖关系
    for task in tasks:
        for dep_id in task.dependencies:
            if dep_id in graph:
                graph[dep_id].add(task.id)
    
    return graph


def detect_dependency_cycle(tasks: List[Task]) -> List[str]:
    """检测任务依赖中是否存在循环依赖
    
    Args:
        tasks: 任务列表
        
    Returns:
        List[str]: 循环依赖的任务ID列表，如果不存在循环依赖则返回空列表
    """
    # 构建任务ID到任务对象的映射
    task_map = {task.id: task for task in tasks}
    
    # 构建邻接表表示的图
    graph = {task.id: set(task.dependencies) for task in tasks}
    
    # 使用DFS检测循环
    visited = set()  # 所有访问过的节点
    path = set()     # 当前路径上的节点
    cycle = []       # 存储检测到的循环
    
    def dfs(node):
        if node in path:
            # 找到循环，记录循环路径
            cycle.append(node)
            return True
        
        if node in visited:
            return False
        
        visited.add(node)
        path.add(node)
        
        for neighbor in graph.get(node, []):
            if neighbor in task_map and dfs(neighbor):
                if not cycle or cycle[0] != cycle[-1]:  # 如果循环还没有闭合
                    cycle.append(node)
                return True
        
        path.remove(node)
        return False
    
    # 对每个未访问的节点进行DFS
    for task_id in graph:
        if task_id not in visited and dfs(task_id):
            # 找到循环，调整循环顺序使其闭合
            i = cycle.index(cycle[-1])
            return cycle[i:] + cycle[:i]
    
    return []  # 没有检测到循环


def get_task_execution_order(tasks: List[Task]) -> List[str]:
    """获取任务的执行顺序（拓扑排序）
    
    Args:
        tasks: 任务列表
        
    Returns:
        List[str]: 任务ID的执行顺序列表
    """
    # 构建任务ID到任务对象的映射
    task_map = {task.id: task for task in tasks}
    
    # 构建邻接表和入度表
    graph = {task.id: [] for task in tasks}
    in_degree = {task.id: 0 for task in tasks}
    
    for task in tasks:
        for dep_id in task.dependencies:
            if dep_id in graph:
                graph[dep_id].append(task.id)
                in_degree[task.id] += 1
    
    # 拓扑排序
    queue = [task_id for task_id, degree in in_degree.items() if degree == 0]
    result = []
    
    while queue:
        task_id = queue.pop(0)
        result.append(task_id)
        
        for neighbor in graph[task_id]:
            in_degree[neighbor] -= 1
            if in_degree[neighbor] == 0:
                queue.append(neighbor)
    
    # 如果结果长度小于任务数，说明存在循环依赖
    if len(result) < len(tasks):
        # 将剩余的任务添加到结果中（这些任务可能存在循环依赖）
        remaining = set(task_map.keys()) - set(result)
        result.extend(list(remaining))
    
    return result
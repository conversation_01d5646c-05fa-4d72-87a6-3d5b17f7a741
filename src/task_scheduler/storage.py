import json
import os
import time
from typing import List, Optional, Dict, Any
from filelock import FileLock

from .models import Task, TaskStatus


class TaskStorage:
    """任务存储类，负责任务信息的持久化和读取"""
    
    def __init__(self, file_path: str):
        """初始化任务存储
        
        Args:
            file_path: 任务状态文件路径
        """
        self.file_path = file_path
        self.lock_path = f"{file_path}.lock"
        
        # 确保文件存在
        self._ensure_file_exists()
    
    def _ensure_file_exists(self) -> None:
        """确保任务状态文件存在，如果不存在则创建"""
        if not os.path.exists(self.file_path):
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(os.path.abspath(self.file_path)), exist_ok=True)
            
            # 创建空的任务状态文件
            with open(self.file_path, 'w') as f:
                json.dump({"tasks": []}, f)
    
    def _read_file(self) -> Dict[str, Any]:
        """读取任务状态文件
        
        Returns:
            Dict[str, Any]: 文件内容
        """
        try:
            with FileLock(self.lock_path):
                with open(self.file_path, 'r') as f:
                    return json.load(f)
        except json.JSONDecodeError:
            # 如果文件为空或格式错误，返回空任务列表
            return {"tasks": []}
    
    def _write_file(self, data: Dict[str, Any]) -> None:
        """写入任务状态文件
        
        Args:
            data: 要写入的数据
        """
        with FileLock(self.lock_path):
            with open(self.file_path, 'w') as f:
                json.dump(data, f, indent=2)
    
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务
        
        Returns:
            List[Task]: 任务列表
        """
        data = self._read_file()
        return [Task.from_dict(task_data) for task_data in data.get("tasks", [])]
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取指定任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Task]: 任务对象，不存在则返回None
        """
        tasks = self.get_all_tasks()
        for task in tasks:
            if task.id == task_id:
                return task
        return None
    
    def get_tasks_by_name(self, name: str) -> List[Task]:
        """根据名称获取任务
        
        Args:
            name: 任务名称
            
        Returns:
            List[Task]: 匹配的任务列表
        """
        tasks = self.get_all_tasks()
        return [task for task in tasks if task.name == name]
    
    def get_completed_task_ids(self) -> List[str]:
        """获取所有已完成任务的ID
        
        Returns:
            List[str]: 已完成任务ID列表
        """
        tasks = self.get_all_tasks()
        return [task.id for task in tasks if task.status == TaskStatus.COMPLETED]
    
    def save_task(self, task: Task) -> bool:
        """保存任务
        
        Args:
            task: 任务对象
            
        Returns:
            bool: 操作是否成功
        """
        data = self._read_file()
        
        # 检查任务是否已存在
        for existing_task in data.get("tasks", []):
            if existing_task.get("id") == task.id:
                return False  # 任务已存在，不能保存
        
        # 添加新任务
        data.setdefault("tasks", []).append(task.to_dict())
        self._write_file(data)
        return True
    
    def update_task(self, task: Task) -> bool:
        """更新任务
        
        Args:
            task: 任务对象
            
        Returns:
            bool: 操作是否成功
        """
        data = self._read_file()
        
        # 查找并更新任务
        for i, existing_task in enumerate(data.get("tasks", [])):
            if existing_task.get("id") == task.id:
                data["tasks"][i] = task.to_dict()
                self._write_file(data)
                return True
        
        return False  # 任务不存在，无法更新
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 操作是否成功
        """
        data = self._read_file()
        
        # 查找并删除任务
        for i, existing_task in enumerate(data.get("tasks", [])):
            if existing_task.get("id") == task_id:
                data["tasks"].pop(i)
                self._write_file(data)
                return True
        
        return False  # 任务不存在，无法删除
    
    def clear_all_tasks(self) -> None:
        """清空所有任务"""
        self._write_file({"tasks": []})
    
    def watch_for_changes(self, task_id: str, check_interval: float = 0.5, timeout: Optional[float] = None) -> bool:
        """监视任务状态变化
        
        Args:
            task_id: 要监视的任务ID
            check_interval: 检查间隔（秒）
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            bool: 任务是否已完成
        """
        start_time = time.time()
        
        while True:
            # 检查是否超时
            if timeout is not None and time.time() - start_time > timeout:
                return False
            
            # 获取任务状态
            task = self.get_task(task_id)
            if task is None:
                return False  # 任务不存在
            
            # 检查任务是否已完成
            if task.status == TaskStatus.COMPLETED:
                return True
            
            # 检查任务是否失败
            if task.status == TaskStatus.FAILED:
                return False
            
            # 等待一段时间后再次检查
            time.sleep(check_interval)
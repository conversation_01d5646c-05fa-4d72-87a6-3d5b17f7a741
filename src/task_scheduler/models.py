from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
import uuid
import os


class TaskStatus(str, Enum):
    """任务状态枚举"""

    WAITING = "waiting"  # 等待中（等待依赖任务完成）
    READY = "ready"  # 就绪（依赖已满足，可以开始执行）
    RUNNING = "running"  # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败


@dataclass
class Task:
    """任务数据模型"""

    name: str  # 任务名称
    dependencies: List[str] = field(default_factory=list)  # 前置任务ID列表
    id: str = field(default_factory=lambda: str(uuid.uuid4()))  # 任务唯一标识符
    status: TaskStatus = TaskStatus.WAITING  # 任务状态
    process_id: int = field(default_factory=lambda: os.getpid())  # 执行该任务的进程ID
    created_at: datetime = field(default_factory=datetime.now)  # 创建时间
    started_at: Optional[datetime] = None  # 开始执行时间
    completed_at: Optional[datetime] = None  # 完成时间
    error_message: Optional[str] = None  # 错误信息（如果失败）
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据

    def to_dict(self) -> Dict[str, Any]:
        """将任务转换为字典，用于序列化"""
        return {
            "id": self.id,
            "name": self.name,
            "dependencies": self.dependencies,
            "status": self.status.value,
            "process_id": self.process_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "error_message": self.error_message,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """从字典创建任务，用于反序列化"""
        # 处理日期时间字段
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None
        started_at = datetime.fromisoformat(data["started_at"]) if data.get("started_at") else None
        completed_at = datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None

        return cls(
            id=data["id"],
            name=data["name"],
            dependencies=data.get("dependencies", []),
            status=TaskStatus(data["status"]),
            process_id=data.get("process_id"),
            created_at=created_at,
            started_at=started_at,
            completed_at=completed_at,
            error_message=data.get("error_message"),
            metadata=data.get("metadata", {}),
        )

    def is_ready(self, completed_tasks: List[str]) -> bool:
        """检查任务是否准备好执行（所有依赖都已完成）

        Args:
            completed_tasks: 已完成任务的ID列表

        Returns:
            bool: 是否准备好执行
        """
        # 如果没有依赖，或者所有依赖都已完成，则准备好执行
        return not self.dependencies or all(dep in completed_tasks for dep in self.dependencies)

    def start(self) -> None:
        """标记任务开始执行"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()

    def complete(self) -> None:
        """标记任务完成"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()

    def fail(self, error_message: Optional[str] = None) -> None:
        """标记任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message

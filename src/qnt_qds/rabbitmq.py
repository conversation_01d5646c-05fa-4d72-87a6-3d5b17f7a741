import pika
from pika.adapters import blocking_connection

from qnt_utils.enums import BasicData

from ._globals import gl_config
from .protos import qds_pb2


class RabbitMQPusher:
    def __init__(self, *data_type) -> None:
        self._date_type = data_type
        self._connection: pika.BlockingConnection
        self._channel: blocking_connection.BlockingChannel
        self._create_connection()

    def _create_connection(self):
        self._connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=gl_config["rabbitmq"]["host"],
                port=gl_config["rabbitmq"]["port"],
                connection_attempts=3,
                retry_delay=5,
                heartbeat=0,
            )
        )
        self._channel = self._connection.channel()
        if BasicData.MINUTE in self._date_type or "MINUTE" in self._date_type:
            self._channel.exchange_declare(exchange="minute", exchange_type="fanout")
        if BasicData.SNAPSHOT in self._date_type or "SNAPSHOT" in self._date_type:
            self._channel.exchange_declare(exchange="snapshot", exchange_type="fanout")

    def on_minute(self, data: dict) -> None:
        try:
            msg = qds_pb2.QuoteData(
                basic_data=qds_pb2.BasicData.MINUTE, qd_minute=qds_pb2.QDMinute(**data)
            ).SerializeToString()
            self._channel.basic_publish(exchange="minute", routing_key="", body=msg)
        except:
            if not self._connection.is_open or not self._channel.is_open:
                try:
                    self._channel.close()
                except:
                    pass
                try:
                    self._connection.close()
                except:
                    pass
            self._create_connection()

            msg = qds_pb2.QuoteData(
                basic_data=qds_pb2.BasicData.MINUTE, qd_minute=qds_pb2.QDMinute(**data)
            ).SerializeToString()
            self._channel.basic_publish(exchange="minute", routing_key="", body=msg)

    def on_snapshot(self, data: dict) -> None:
        try:
            msg = qds_pb2.QuoteData(
                basic_data=qds_pb2.BasicData.SNAPSHOT, qd_snapshot=qds_pb2.QDSnapshot(**data)
            ).SerializeToString()
            self._channel.basic_publish(exchange="snapshot", routing_key="", body=msg)
        except:
            if not self._connection.is_open or not self._channel.is_open:
                try:
                    self._channel.close()
                except:
                    pass
                try:
                    self._connection.close()
                except:
                    pass
            self._create_connection()

            msg = qds_pb2.QuoteData(
                basic_data=qds_pb2.BasicData.SNAPSHOT, qd_snapshot=qds_pb2.QDSnapshot(**data)
            ).SerializeToString()
            self._channel.basic_publish(exchange="snapshot", routing_key="", body=msg)

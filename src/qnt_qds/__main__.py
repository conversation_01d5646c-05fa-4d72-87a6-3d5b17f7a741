import pathlib

from loguru import logger

from qnt_qds.server import Server
from qnt_utils.config import get_config
from qnt_utils.logger import WebhookHandlerForLoguru

logger.add(
    WebhookHandlerForLoguru(
        "https://oapi.dingtalk.com/robot/send?access_token=78c845d3da845986d6c75063a6a38371fd08b15d5d3d75086110a12ee136a664",
        "SEC88e0357a90376df97f317a31735856c2e0f88817197d4f53f482afbc6b8f7899",
    ),
    level="WEBHOOK",
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | QDS - <level>{message}</level>",
    enqueue=True,
)
if get_config()["qds"]["settings"]["is_debug"]:
    logger.add(
        pathlib.Path(get_config()["qds"]["settings"]["log_path"], "qds.log"),
        level="DEBUG",
        rotation="00:00",
        retention="7 days",
    )
else:
    logger.add(
        pathlib.Path(get_config()["qds"]["settings"]["log_path"], "qds.log"),
        level="INFO",
        rotation="00:00",
        retention="7 days",
    )


if __name__ == "__main__":
    try:
        s = Server()
        s.run()
    except Exception as e:
        logger.exception(e)
        logger.log("WEBHOOK", "QDS崩溃")

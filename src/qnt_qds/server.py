import json
import queue
import time
from typing import Dict

from kazoo.client import <PERSON><PERSON><PERSON><PERSON><PERSON>
from kazoo.recipe.watchers import ChildrenWatch, DataWatch
from loguru import logger

from qnt_utils.enums import Destination
from qnt_utils.label import Symbol
from qnt_utils.toolset import decrypt

from . import struct
from ._globals import gl_config
from .data_engine.adapter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TianQinAdapter
from .data_engine.base_data_server import BasicDataServer

ZK_PATH = "/subscriptions"


class ZKListener:
    def __init__(self) -> None:
        # 内部缓存订阅信息
        self.cached_subscriptions = {}
        self.instruction_queue = queue.Queue()
        # 用于存储子节点数据监听器的字典
        self.data_watches = {}
        # 创建KazooClient实例并连接到ZooKeeper
        self.zk = KazooClient(hosts=f"{gl_config['zookeeper']['host']}:{gl_config['zookeeper']['port']}")
        self.zk.start()
        # 设置子节点变化监听
        ChildrenWatch(self.zk, ZK_PATH, func=self.children_change_callback)

    def data_change_callback(self, data, stat, *args):
        res = {}
        for child in self.zk.get_children(ZK_PATH):
            data = self.zk.get(f"{ZK_PATH}/{child}")[0]
            if not data:
                continue
            subinfo = json.loads(data)
            for key, value in subinfo.items():
                if key not in res:
                    res[key] = set()
                res[key] = res[key].union(value)

        # 与缓存进行对比
        if res != self.cached_subscriptions:
            for key, value in res.items():
                add_sub = list(value - self.cached_subscriptions.get(key, set()))
                if add_sub:
                    self.instruction_queue.put(("ADD_SUB", key, add_sub))
            for key, value in self.cached_subscriptions.items():
                del_sub = list(value - res.get(key, set()))
                if del_sub:
                    self.instruction_queue.put(("DEL_SUB", key, del_sub))
            self.cached_subscriptions = res

        return True

    def children_change_callback(self, children):
        children = [f"{ZK_PATH}/{child}" for child in children]

        # 为每个子节点重新设置数据监听器
        to_add = set(children) - self.data_watches.keys()
        for path in to_add:
            self.data_watches[path] = DataWatch(self.zk, path, func=self.data_change_callback)

        to_del = self.data_watches.keys() - set(children)
        for path in to_del:
            watch = self.data_watches.pop(path)
            del watch

    def __del__(self):
        self.zk.stop()
        self.zk.close()


class Server:
    def __init__(self):
        """用于管理请求, 所有请求集中于此并根据请求的数据转发向对应的数据引擎, 内部包括一个转发请求的线程"""
        logger.info("Server has been initiated")
        self._engines: Dict[str, BasicDataServer] = {}
        """数据引擎的字典"""
        if "ashare" in gl_config["qds"] and gl_config["qds"]["ashare"]["available"]:
            self._engines["AShareDataServer"] = BasicDataServer(AShareAdapter)
            logger.info("AShareDataServer has been created")
        if "tq_sdk" in gl_config["qds"] and gl_config["qds"]["tq_sdk"]["available"]:
            self._engines["TqDataServer"] = BasicDataServer(
                TianQinAdapter,
                account=gl_config["qds"]["tq_sdk"]["account"],
                password=decrypt(gl_config["qds"]["tq_sdk"]["password"]),
            )
            logger.info("TqDataServer has been created")

        self._zk_listener: ZKListener = ZKListener()
        """ZooKeeper监听器"""

    def _send_instruction_to_data_engine(self, msg: struct.Request):
        """将请求转发给数据引擎, 一个请求可能包括多个目标数据服务

        Args:
            msg: 已解析的请求
        """
        send_to = []
        for i in msg.subinfo.stock_list:
            destinations = Symbol.get_destination(i)
            if Destination.TIANQIN in destinations:
                send_to.append("TqDataServer")
            if Destination.ASHARE in destinations:
                send_to.append("AShareDataServer")

            for i in send_to:
                if i in self._engines.keys():
                    self._engines[i].handle_subscription_request(msg)

    def run(self):
        """发送请求的线程, 根据客户端消息的类型和目标服务, 将消息发送到数据接收服务和数据推送服务"""
        while True:
            action, basic_data, symbol_list = self._zk_listener.instruction_queue.get()
            subinfo = struct.SubInfo(action, basic_data, symbol_list)
            msg = struct.Request(timestamp=time.time_ns(), subinfo=subinfo, host="0.0.0.0", port=1, pid=1)
            logger.info(f"Server has received {msg}")
            self._send_instruction_to_data_engine(msg)

from dataclasses import dataclass

import pandas as pd

from qnt_utils.enums import Action, BasicData
from qnt_utils.label import QSymbol

from .protos import qds_pb2


@dataclass
class SubInfo:
    action: Action
    basic_data: BasicData
    stock_list: list[QSymbol]

    def __init__(self, action: Action | str, basic_data: BasicData | str, stock_list: list[QSymbol]):
        """订阅信息

        Args:
            action (Action | str): 增加或删除订阅
            basic_data (BasicData | str): 数据类型
            stock_list (list[QSymbol]): 股票列表，如果是增加订阅，为空会报错，如果是删除订阅，为空表示删除所有订阅
        """
        self.action: Action = action if isinstance(action, Action) else Action[action]
        self.basic_data: BasicData = basic_data if isinstance(basic_data, BasicData) else BasicData[basic_data]
        self.stock_list: list[QSymbol] = stock_list

    @classmethod
    def from_pb(cls, r: qds_pb2.SubInfo):
        return cls(action=Action(r.action), basic_data=BasicData(r.basic_data), stock_list=list(r.stock_list))

    def __str__(self) -> str:
        return "{} {} {}".format(self.action.name, self.basic_data.name, ",".join(self.stock_list))


@dataclass
class Request:
    timestamp: int
    subinfo: SubInfo
    host: str
    port: int
    pid: int

    def __init__(self, timestamp: int, subinfo: SubInfo, host: str, port: int, pid: int):
        self.timestamp = timestamp
        self.subinfo = subinfo
        self.host = host
        self.port = port
        self.pid = pid

    @classmethod
    def from_pb(cls, r: qds_pb2.Request):
        return cls(timestamp=r.timestamp, subinfo=SubInfo.from_pb(r.subinfo), host=r.host, port=r.port, pid=r.pid)

    def __str__(self) -> str:
        return "request from addr: {}:{} pid: {} send at {}. subinfo: {}".format(
            self.host,
            self.port,
            self.pid,
            pd.Timestamp.fromtimestamp(self.timestamp / 1e9).strftime("%Y-%m-%d %H:%M:%S"),
            self.subinfo,
        )

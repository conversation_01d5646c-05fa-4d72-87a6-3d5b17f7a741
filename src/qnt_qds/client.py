import json
import threading
import time
from typing import Any, Callable, Iterable, Optional, overload

import pika
from loguru import logger

from qnt_qds.protos import qds_pb2
from qnt_utils.enums import Action, BasicData
from qnt_utils.fields import BAR_FIELDS, TICK_SNAPSHOT_FIELDS, Bar, TickSnapshot
from qnt_utils.zk_handler import ZKHandler

from .struct import SubInfo


@overload
def parse_from_realtime_data(data: qds_pb2.QDMinute, fileds: Iterable[str]) -> Bar: ...


@overload
def parse_from_realtime_data(data: qds_pb2.QDSnapshot, fileds: Iterable[str]) -> TickSnapshot: ...


def parse_from_realtime_data(data, fileds):  # type: ignore
    result = {}
    for i in fileds:
        result[i] = getattr(data, i)
    return result


def _default_callback(data: Any) -> None:
    pass


class DataClient:
    def __init__(
        self,
        zk_hosts: str = "localhost:2181",
        mq_host: Optional[str] = "localhost:5672",
        fnc_snapshot: Optional[Callable[[TickSnapshot], None]] = None,
        fnc_minute: Optional[Callable[[Bar], None]] = None,
        zk_ephemeral: bool = True,
    ):
        """初始化数据客户端。

        Args:
            zk_hosts (str, optional): Zookeeper地址，可以指定id，如"user@localhost:2181"。 Defaults to "localhost:2181".
            mq_host (Optional[str], optional): RabbitMQ地址，为None时接收数据。 Defaults to "localhost:5672".
            fnc_snapshot (Optional[Callable[[TickSnapshot], None]], optional): 快照数据回调函数。 Defaults to None.
            fnc_minute (Optional[Callable[[Bar], None]], optional): 分钟数据回调函数。 Defaults to None.
            zk_ephemeral (bool, optional): 是否为zk临时节点。 Defaults to True.
        """
        if mq_host is not None:
            host, port = mq_host.split(":")
            self._mq_host = host
            self._mq_port = int(port)
        else:
            self._mq_host = None
            self._mq_port = None

        self._fnc_snapshot = fnc_snapshot or _default_callback
        self._fnc_minute = fnc_minute or _default_callback

        self.zk = ZKHandler("/subscriptions", *zk_hosts.split("@")[::-1], ephemeral=zk_ephemeral)
        if not zk_ephemeral:
            try:
                tmp = json.loads(self.zk.get())
                self.subscriptions = {k: set(v) for k, v in tmp.items()}
            except Exception:
                self.subscriptions = {"MINUTE": set(), "SNAPSHOT": set()}
        else:
            self.subscriptions = {"MINUTE": set(), "SNAPSHOT": set()}
        logger.info(f"DataClient({self.zk.client_id}) has connected to Zookeeper successfully.")

        self._tlock = threading.RLock()
        if mq_host is not None:
            threading.Thread(target=self._recv, daemon=True).start()

    def _recv(self):
        if self._mq_host is None or self._mq_port is None:
            logger.warning("DataClient is not configured to receive data.")
            return

        minute_last_dt = {}
        snapshot_last_dt = {}

        def on_minute(ch, method, properties, body):
            msg = qds_pb2.QuoteData()
            msg.ParseFromString(body)

            with self._tlock:
                if msg.qd_minute.code not in self.subscriptions["MINUTE"]:
                    return

            if (tmp := (msg.qd_minute.date * 1e9 + msg.qd_minute.time)) > minute_last_dt.get(msg.qd_minute.code, 0):
                data: Bar = parse_from_realtime_data(msg.qd_minute, BAR_FIELDS.keys())
                self._fnc_minute(data)
                minute_last_dt[msg.qd_minute.code] = tmp

        def on_snapshot(ch, method, properties, body):
            msg = qds_pb2.QuoteData()
            msg.ParseFromString(body)

            with self._tlock:
                if msg.qd_snapshot.code not in self.subscriptions["SNAPSHOT"]:
                    return

            if (tmp := (msg.qd_snapshot.date * 1e9 + msg.qd_snapshot.time)) > snapshot_last_dt.get(
                msg.qd_snapshot.code, 0
            ):
                data = parse_from_realtime_data(msg.qd_snapshot, TICK_SNAPSHOT_FIELDS)
                self._fnc_snapshot(data)
                snapshot_last_dt[msg.qd_snapshot.code] = tmp

        while True:
            try:
                connection = pika.BlockingConnection(pika.ConnectionParameters(host=self._mq_host, port=self._mq_port))
                channel = connection.channel()

                channel.exchange_declare(exchange="minute", exchange_type="fanout")
                result = channel.queue_declare(queue="", exclusive=True, auto_delete=True)
                queue_name = result.method.queue
                channel.queue_bind(exchange="minute", queue=queue_name, routing_key="")
                channel.basic_consume(queue=queue_name, on_message_callback=on_minute, auto_ack=True)

                channel.exchange_declare(exchange="snapshot", exchange_type="fanout")
                result = channel.queue_declare(queue="", exclusive=True, auto_delete=True)
                queue_name = result.method.queue
                channel.queue_bind(exchange="snapshot", queue=queue_name, routing_key="")
                channel.basic_consume(queue=queue_name, on_message_callback=on_snapshot, auto_ack=True)

                logger.info("DataClient has connected to RabbitMQ successfully.")
                channel.start_consuming()
            except Exception:
                import traceback

                traceback.print_exc()
                logger.warning("DataClient is trying to reconnect to RabbitMQ...")
                time.sleep(5)
                continue

    def _publish(self):
        with self._tlock:
            subscriptions_json = json.dumps({k: list(v) for k, v in self.subscriptions.items()})
            self.zk.write(subscriptions_json)

    def batch_subscribe(self, *subinfos: SubInfo) -> bool:
        """订阅或取消订阅指定数据类型的一系列标的。

        Raises:
            ValueError: 数据类型无效。
            ValueError: 订阅时标的列表为空。

        Returns:
            bool: 如果成功订阅或取消订阅则返回True，如果订阅已存在则返回False。
        """
        changed = False
        for subinfo in subinfos:
            action, data_type, symbols = subinfo.action, subinfo.basic_data.name, subinfo.stock_list
            with self._tlock:
                if data_type not in self.subscriptions:
                    raise ValueError(f"Invalid data type: {data_type}")

                if action == Action.ADD_SUB:
                    if len(symbols) == 0:
                        raise ValueError("Symbols list is empty")
                    for symbol in symbols:
                        if symbol not in self.subscriptions[data_type]:
                            self.subscriptions[data_type].add(symbol)
                            changed = True

                elif action == Action.DEL_SUB:
                    if len(symbols) == 0:
                        self.subscriptions[data_type] = set()
                        changed = True
                    else:
                        for symbol in symbols:
                            if symbol in self.subscriptions[data_type]:
                                self.subscriptions[data_type].remove(symbol)
                                changed = True

        if changed:
            self._publish()

        return changed

    def subscribe(self, action: Action | str, data_type: str | BasicData, symbols: list[str]) -> bool:
        """订阅或取消订阅指定数据类型的一系列标的。

        Args:
            action (Action | str): 订阅或取消订阅操作。
            data_type (str | BasicData): 数据类型。
            symbols (list[str]): 标的列表。

        Raises:
            ValueError: 数据类型无效。
            ValueError: 订阅时标的列表为空。

        Returns:
            bool: 如果成功订阅或取消订阅则返回True，如果订阅已存在则返回False。
        """
        action = action if isinstance(action, Action) else Action[action]
        data_type = data_type.name if isinstance(data_type, BasicData) else data_type
        with self._tlock:
            if data_type not in self.subscriptions:
                raise ValueError(f"Invalid data type: {data_type}")

            changed = False
            if action == Action.ADD_SUB:
                if len(symbols) == 0:
                    raise ValueError("Symbols list is empty")
                for symbol in symbols:
                    if symbol not in self.subscriptions[data_type]:
                        self.subscriptions[data_type].add(symbol)
                        changed = True

            elif action == Action.DEL_SUB:
                if len(symbols) == 0:
                    self.subscriptions[data_type] = set()
                    changed = True
                else:
                    for symbol in symbols:
                        if symbol in self.subscriptions[data_type]:
                            self.subscriptions[data_type].remove(symbol)
                            changed = True

        if changed:
            self._publish()

        return changed

import os
import pathlib

import tomli

gl_config = {}
if (_cnf_path := pathlib.Path(os.getenv("QNT_CONFIG_PATH", "./config"), "config.toml")).exists():
    with open(_cnf_path, "rb") as f:
        _cnf = tomli.load(f)
        if "qds" in _cnf:
            gl_config["qds"] = _cnf["qds"]
            if "settings" not in _cnf["qds"]:
                raise RuntimeError("Missing required config items: qds.settings")
            gl_config["rabbitmq"] = _cnf.get("rabbitmq", {"host": "localhost", "port": 5672})
            gl_config["zookeeper"] = _cnf.get("zookeeper", {"host": "localhost", "port": 2181})
        else:
            raise RuntimeError("Missing required config items: qds")
else:
    raise FileNotFoundError("config.toml not found")

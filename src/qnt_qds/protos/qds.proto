syntax = "proto3";

enum BasicData{
  SNAPSHOT = 0;
  ORDER = 1;
  TRANSACTION = 2;
  QUEUE = 3;
  SUPER_STOCK = 4;
  MINUTE = 5;
  DAY = 6;
}

enum Action{
  ADD_SUB = 0;
  DEL_SUB = 1;
}

message SubInfo{
  Action action = 1;
  BasicData basic_data = 2;
  repeated string stock_list = 3;
}

message Request{
  int64 timestamp = 1;
  SubInfo subinfo = 2;
  string host = 3;
  int32 port = 4;
  int32 pid = 5;
}

message QuoteTime{
  uint64 produce_time = 1;
  uint64 send_time = 2;
  uint64 recv_time = 3;
  uint64 calc_time = 4;
}

message QuoteData
{
  QuoteTime quote_time = 1; //行情时间
  BasicData basic_data = 2;     //间隔
  QDSnapshot qd_snapshot = 3; //股票行情
  QDTransaction qd_transaction = 4; //逐笔成交
  QDOrder qd_order = 5; //逐笔委托
  QDSuperStock qd_super_stock = 6; // 超级盘口
  QDMinute  qd_minute = 7;      //K线数据
}

//K线数据
message QDMinute{
  string code = 1;              //代码
  int32 date = 2;               //日期
  int32 time = 3;               //时间
  double open = 4;              //开盘价
  double high = 5;              //最高价
  double low = 6;               //最低价
  double close = 7;             //收盘价
  double factor = 8;           //复权因子
  double volume = 9;            //成交量
  double turnover = 10;         //成交金额
  double turnover_rate = 11;    //换手率
  bool is_paused = 12;          //是否停牌
  double uplimit_price = 13;    //涨停价
  double downlimit_price = 14;  //跌停价
  double avg_price = 15;        //成交均价
  double pre_price = 16;       //昨收价
  double quote_rate = 17;       //涨跌幅
  double amp_rate = 18;         //振幅
  bool is_st = 19;              //是否ST
  double settle = 20;           //结算价
  double pre_settle = 21;       //前结算价
  double change = 22;           //涨跌（元）
  double open_interest = 23;     //持仓
  double pos_change = 24;        //仓差
  double pre_open_interest = 25; //前持仓量
}

//证券快照
message QDSnapshot
{
  string market = 1;        //市场
  string code = 2;        //代码
  string codename = 3;      //代码名字
  string status = 4;      //当前状态，比如集合竞价、休市等
  int32 date = 5;          //YYYYMMDD 行情发生的日期
  int32 time = 6;          //HHMMSSsss
  double pre_price = 7;           //昨收价
  double open_price = 8;      //开盘价
  double high_price = 9;      //最高价
  double low_price = 10;      //最低价
  double new_price = 11;      //最新价
  double volume = 12;        //成交量 股票：股； 权证：份； 债券：手
  double turnover = 13;      //成交金额
  int64 trade_num = 14;        //成交笔数
  repeated double bidorder_price = 15;  //申买价，从买一到买十
  repeated double bidorder_volume = 16;  //申买量，从买一到买十
  repeated int64 bid_numorders = 17;    //委托笔数，从买一到买十
  repeated double askorder_price = 18;  //申卖价，从卖一到卖十
  repeated double askorder_volume = 19;  //申卖量，从卖一到卖十
  repeated int64 ask_numorders = 20;    //委托笔数，从卖一到卖十
  int64 totalbid_volume = 21;    //总委买量
  int64 totalask_volume = 22;    //总委卖量
  double avgbid_price = 23;    //加权平均委买价
  double avgask_price = 24;    //加权平均委卖价
  double uplimit_price = 25;    //涨停价
  double downlimit_price = 26;    //跌停价
  double pe = 27;          //市盈率
  double iopv = 28;        //上交所深交所LOF动态净值、深交所LOF动态净值
  int32 withdraw_buynum = 29;    //买入撤单笔数
  int64 withdraw_buyvolume = 30;    //买入撤单数量
  double withdraw_buyturnover = 31;  //买入撤单金额
  int32 withdraw_sellnum = 32;    //卖出撤单笔数
  int64 withdraw_sellvolume = 33;    //卖出撤单量
  double withdraw_sellturnover = 34;  //卖出撤单金额
  int32 totalbid_num = 35;      //买入总笔数
  int32 totalask_num = 36;      //卖出总笔数
  int32 bidorder_num = 37;      //买方委托价位数
  int32 askorder_num = 38 ;      //卖方委托价位数
  int64 inner_volume = 39;      //内盘成交量
  int64 outer_volume = 40;      //外盘成交量
  int64 aftermarket_volume = 41;    //盘后成交量
  int32 aftermarket_volmount = 42;  //盘后成交次数
  double avg_price = 43;    //成交均价
  double open_interest = 44;   //持仓量
  double pre_open_interest = 45;   //前持仓量
}

//逐笔委托
message QDOrder
{
  string market = 11;
  string code = 1; //证券代码 600000
  int32 date = 2; //YYYYMMDD 委托发生的日期
  int32 time = 3; //HHMMSSsss
  int32 index = 4; //委托序号
  int32 biz_index = 5; //业务序号
  int32 channel = 6; //通道号
  double price = 7; //委托价格
  int32 volume = 8; //委托量
  string side = 9; //1=买，2=卖，G=借入，F=借出
  string type = 10; //订单类别，1=市价，2=限价，U=本方最优
}

//逐笔成交
message QDTransaction
{
  string market = 13;
  string code = 1; //证券代码 600000
  int32 date = 2; //YYYYMMDD 成交的日期
  int32 time = 3; //HHMMSSsss
  int32 index = 4; //成交序号
  int32 biz_index = 5; //业务序号
  int32 channel = 6; //通道号
  double price = 7; //成交价格
  int32 volume = 8; //成交量
  int32 buy_index = 9; //买方订单号
  int32 sell_index = 10; //卖方订单号
  string type = 11; //成交类型,F=成交，C=撤单
  string bsflag = 12; //买卖标志,B-外盘,主动买,；S-内盘,主动卖,；N-未知
}

//超级盘口
message QDSuperStock
{
  string market = 1;
  string code = 2; // 证券代码 600000
  int32 date = 3; //YYYYMMDD 委托发生的日期
  int32 time = 4; //HHMMSSsss
  double new_price = 5; //最新价
  repeated double bidorder_price = 6; //申买价，从买一到买十
  repeated int64 bidorder_volume = 7; //申买量，从买一到买十
  repeated double askorder_price = 8; //申卖价，从卖一到卖十
  repeated int64 askorder_volume = 9; //申卖量，从卖一到卖十
}
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class BasicData(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    SNAPSHOT: _ClassVar[BasicData]
    ORDER: _ClassVar[BasicData]
    TRANSACTION: _ClassVar[BasicData]
    QUEUE: _ClassVar[BasicData]
    SUPER_STOCK: _ClassVar[BasicData]
    MINUTE: _ClassVar[BasicData]
    DAY: _ClassVar[BasicData]

class Action(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []
    ADD_SUB: _ClassVar[Action]
    DEL_SUB: _ClassVar[Action]
SNAPSHOT: BasicData
ORDER: BasicData
TRANSACTION: BasicData
QUEUE: BasicData
SUPER_STOCK: BasicData
MINUTE: BasicData
DAY: BasicData
ADD_SUB: Action
DEL_SUB: Action

class SubInfo(_message.Message):
    __slots__ = ["action", "basic_data", "stock_list"]
    ACTION_FIELD_NUMBER: _ClassVar[int]
    BASIC_DATA_FIELD_NUMBER: _ClassVar[int]
    STOCK_LIST_FIELD_NUMBER: _ClassVar[int]
    action: Action
    basic_data: BasicData
    stock_list: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, action: _Optional[_Union[Action, str]] = ..., basic_data: _Optional[_Union[BasicData, str]] = ..., stock_list: _Optional[_Iterable[str]] = ...) -> None: ...

class Request(_message.Message):
    __slots__ = ["timestamp", "subinfo", "host", "port", "pid"]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    SUBINFO_FIELD_NUMBER: _ClassVar[int]
    HOST_FIELD_NUMBER: _ClassVar[int]
    PORT_FIELD_NUMBER: _ClassVar[int]
    PID_FIELD_NUMBER: _ClassVar[int]
    timestamp: int
    subinfo: SubInfo
    host: str
    port: int
    pid: int
    def __init__(self, timestamp: _Optional[int] = ..., subinfo: _Optional[_Union[SubInfo, _Mapping]] = ..., host: _Optional[str] = ..., port: _Optional[int] = ..., pid: _Optional[int] = ...) -> None: ...

class QuoteTime(_message.Message):
    __slots__ = ["produce_time", "send_time", "recv_time", "calc_time"]
    PRODUCE_TIME_FIELD_NUMBER: _ClassVar[int]
    SEND_TIME_FIELD_NUMBER: _ClassVar[int]
    RECV_TIME_FIELD_NUMBER: _ClassVar[int]
    CALC_TIME_FIELD_NUMBER: _ClassVar[int]
    produce_time: int
    send_time: int
    recv_time: int
    calc_time: int
    def __init__(self, produce_time: _Optional[int] = ..., send_time: _Optional[int] = ..., recv_time: _Optional[int] = ..., calc_time: _Optional[int] = ...) -> None: ...

class QuoteData(_message.Message):
    __slots__ = ["quote_time", "basic_data", "qd_snapshot", "qd_transaction", "qd_order", "qd_super_stock", "qd_minute"]
    QUOTE_TIME_FIELD_NUMBER: _ClassVar[int]
    BASIC_DATA_FIELD_NUMBER: _ClassVar[int]
    QD_SNAPSHOT_FIELD_NUMBER: _ClassVar[int]
    QD_TRANSACTION_FIELD_NUMBER: _ClassVar[int]
    QD_ORDER_FIELD_NUMBER: _ClassVar[int]
    QD_SUPER_STOCK_FIELD_NUMBER: _ClassVar[int]
    QD_MINUTE_FIELD_NUMBER: _ClassVar[int]
    quote_time: QuoteTime
    basic_data: BasicData
    qd_snapshot: QDSnapshot
    qd_transaction: QDTransaction
    qd_order: QDOrder
    qd_super_stock: QDSuperStock
    qd_minute: QDMinute
    def __init__(self, quote_time: _Optional[_Union[QuoteTime, _Mapping]] = ..., basic_data: _Optional[_Union[BasicData, str]] = ..., qd_snapshot: _Optional[_Union[QDSnapshot, _Mapping]] = ..., qd_transaction: _Optional[_Union[QDTransaction, _Mapping]] = ..., qd_order: _Optional[_Union[QDOrder, _Mapping]] = ..., qd_super_stock: _Optional[_Union[QDSuperStock, _Mapping]] = ..., qd_minute: _Optional[_Union[QDMinute, _Mapping]] = ...) -> None: ...

class QDMinute(_message.Message):
    __slots__ = ["code", "date", "time", "open", "high", "low", "close", "factor", "volume", "turnover", "turnover_rate", "is_paused", "uplimit_price", "downlimit_price", "avg_price", "pre_price", "quote_rate", "amp_rate", "is_st", "settle", "pre_settle", "change", "open_interest", "pos_change", "pre_open_interest"]
    CODE_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    OPEN_FIELD_NUMBER: _ClassVar[int]
    HIGH_FIELD_NUMBER: _ClassVar[int]
    LOW_FIELD_NUMBER: _ClassVar[int]
    CLOSE_FIELD_NUMBER: _ClassVar[int]
    FACTOR_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    TURNOVER_FIELD_NUMBER: _ClassVar[int]
    TURNOVER_RATE_FIELD_NUMBER: _ClassVar[int]
    IS_PAUSED_FIELD_NUMBER: _ClassVar[int]
    UPLIMIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    DOWNLIMIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    AVG_PRICE_FIELD_NUMBER: _ClassVar[int]
    PRE_PRICE_FIELD_NUMBER: _ClassVar[int]
    QUOTE_RATE_FIELD_NUMBER: _ClassVar[int]
    AMP_RATE_FIELD_NUMBER: _ClassVar[int]
    IS_ST_FIELD_NUMBER: _ClassVar[int]
    SETTLE_FIELD_NUMBER: _ClassVar[int]
    PRE_SETTLE_FIELD_NUMBER: _ClassVar[int]
    CHANGE_FIELD_NUMBER: _ClassVar[int]
    OPEN_INTEREST_FIELD_NUMBER: _ClassVar[int]
    POS_CHANGE_FIELD_NUMBER: _ClassVar[int]
    PRE_OPEN_INTEREST_FIELD_NUMBER: _ClassVar[int]
    code: str
    date: int
    time: int
    open: float
    high: float
    low: float
    close: float
    factor: float
    volume: float
    turnover: float
    turnover_rate: float
    is_paused: bool
    uplimit_price: float
    downlimit_price: float
    avg_price: float
    pre_price: float
    quote_rate: float
    amp_rate: float
    is_st: bool
    settle: float
    pre_settle: float
    change: float
    open_interest: float
    pos_change: float
    pre_open_interest: float
    def __init__(self, code: _Optional[str] = ..., date: _Optional[int] = ..., time: _Optional[int] = ..., open: _Optional[float] = ..., high: _Optional[float] = ..., low: _Optional[float] = ..., close: _Optional[float] = ..., factor: _Optional[float] = ..., volume: _Optional[float] = ..., turnover: _Optional[float] = ..., turnover_rate: _Optional[float] = ..., is_paused: bool = ..., uplimit_price: _Optional[float] = ..., downlimit_price: _Optional[float] = ..., avg_price: _Optional[float] = ..., pre_price: _Optional[float] = ..., quote_rate: _Optional[float] = ..., amp_rate: _Optional[float] = ..., is_st: bool = ..., settle: _Optional[float] = ..., pre_settle: _Optional[float] = ..., change: _Optional[float] = ..., open_interest: _Optional[float] = ..., pos_change: _Optional[float] = ..., pre_open_interest: _Optional[float] = ...) -> None: ...

class QDSnapshot(_message.Message):
    __slots__ = ["market", "code", "codename", "status", "date", "time", "pre_price", "open_price", "high_price", "low_price", "new_price", "volume", "turnover", "trade_num", "bidorder_price", "bidorder_volume", "bid_numorders", "askorder_price", "askorder_volume", "ask_numorders", "totalbid_volume", "totalask_volume", "avgbid_price", "avgask_price", "uplimit_price", "downlimit_price", "pe", "iopv", "withdraw_buynum", "withdraw_buyvolume", "withdraw_buyturnover", "withdraw_sellnum", "withdraw_sellvolume", "withdraw_sellturnover", "totalbid_num", "totalask_num", "bidorder_num", "askorder_num", "inner_volume", "outer_volume", "aftermarket_volume", "aftermarket_volmount", "avg_price", "open_interest", "pre_open_interest"]
    MARKET_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    CODENAME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    PRE_PRICE_FIELD_NUMBER: _ClassVar[int]
    OPEN_PRICE_FIELD_NUMBER: _ClassVar[int]
    HIGH_PRICE_FIELD_NUMBER: _ClassVar[int]
    LOW_PRICE_FIELD_NUMBER: _ClassVar[int]
    NEW_PRICE_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    TURNOVER_FIELD_NUMBER: _ClassVar[int]
    TRADE_NUM_FIELD_NUMBER: _ClassVar[int]
    BIDORDER_PRICE_FIELD_NUMBER: _ClassVar[int]
    BIDORDER_VOLUME_FIELD_NUMBER: _ClassVar[int]
    BID_NUMORDERS_FIELD_NUMBER: _ClassVar[int]
    ASKORDER_PRICE_FIELD_NUMBER: _ClassVar[int]
    ASKORDER_VOLUME_FIELD_NUMBER: _ClassVar[int]
    ASK_NUMORDERS_FIELD_NUMBER: _ClassVar[int]
    TOTALBID_VOLUME_FIELD_NUMBER: _ClassVar[int]
    TOTALASK_VOLUME_FIELD_NUMBER: _ClassVar[int]
    AVGBID_PRICE_FIELD_NUMBER: _ClassVar[int]
    AVGASK_PRICE_FIELD_NUMBER: _ClassVar[int]
    UPLIMIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    DOWNLIMIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    PE_FIELD_NUMBER: _ClassVar[int]
    IOPV_FIELD_NUMBER: _ClassVar[int]
    WITHDRAW_BUYNUM_FIELD_NUMBER: _ClassVar[int]
    WITHDRAW_BUYVOLUME_FIELD_NUMBER: _ClassVar[int]
    WITHDRAW_BUYTURNOVER_FIELD_NUMBER: _ClassVar[int]
    WITHDRAW_SELLNUM_FIELD_NUMBER: _ClassVar[int]
    WITHDRAW_SELLVOLUME_FIELD_NUMBER: _ClassVar[int]
    WITHDRAW_SELLTURNOVER_FIELD_NUMBER: _ClassVar[int]
    TOTALBID_NUM_FIELD_NUMBER: _ClassVar[int]
    TOTALASK_NUM_FIELD_NUMBER: _ClassVar[int]
    BIDORDER_NUM_FIELD_NUMBER: _ClassVar[int]
    ASKORDER_NUM_FIELD_NUMBER: _ClassVar[int]
    INNER_VOLUME_FIELD_NUMBER: _ClassVar[int]
    OUTER_VOLUME_FIELD_NUMBER: _ClassVar[int]
    AFTERMARKET_VOLUME_FIELD_NUMBER: _ClassVar[int]
    AFTERMARKET_VOLMOUNT_FIELD_NUMBER: _ClassVar[int]
    AVG_PRICE_FIELD_NUMBER: _ClassVar[int]
    OPEN_INTEREST_FIELD_NUMBER: _ClassVar[int]
    PRE_OPEN_INTEREST_FIELD_NUMBER: _ClassVar[int]
    market: str
    code: str
    codename: str
    status: str
    date: int
    time: int
    pre_price: float
    open_price: float
    high_price: float
    low_price: float
    new_price: float
    volume: float
    turnover: float
    trade_num: int
    bidorder_price: _containers.RepeatedScalarFieldContainer[float]
    bidorder_volume: _containers.RepeatedScalarFieldContainer[float]
    bid_numorders: _containers.RepeatedScalarFieldContainer[int]
    askorder_price: _containers.RepeatedScalarFieldContainer[float]
    askorder_volume: _containers.RepeatedScalarFieldContainer[float]
    ask_numorders: _containers.RepeatedScalarFieldContainer[int]
    totalbid_volume: int
    totalask_volume: int
    avgbid_price: float
    avgask_price: float
    uplimit_price: float
    downlimit_price: float
    pe: float
    iopv: float
    withdraw_buynum: int
    withdraw_buyvolume: int
    withdraw_buyturnover: float
    withdraw_sellnum: int
    withdraw_sellvolume: int
    withdraw_sellturnover: float
    totalbid_num: int
    totalask_num: int
    bidorder_num: int
    askorder_num: int
    inner_volume: int
    outer_volume: int
    aftermarket_volume: int
    aftermarket_volmount: int
    avg_price: float
    open_interest: float
    pre_open_interest: float
    def __init__(self, market: _Optional[str] = ..., code: _Optional[str] = ..., codename: _Optional[str] = ..., status: _Optional[str] = ..., date: _Optional[int] = ..., time: _Optional[int] = ..., pre_price: _Optional[float] = ..., open_price: _Optional[float] = ..., high_price: _Optional[float] = ..., low_price: _Optional[float] = ..., new_price: _Optional[float] = ..., volume: _Optional[float] = ..., turnover: _Optional[float] = ..., trade_num: _Optional[int] = ..., bidorder_price: _Optional[_Iterable[float]] = ..., bidorder_volume: _Optional[_Iterable[float]] = ..., bid_numorders: _Optional[_Iterable[int]] = ..., askorder_price: _Optional[_Iterable[float]] = ..., askorder_volume: _Optional[_Iterable[float]] = ..., ask_numorders: _Optional[_Iterable[int]] = ..., totalbid_volume: _Optional[int] = ..., totalask_volume: _Optional[int] = ..., avgbid_price: _Optional[float] = ..., avgask_price: _Optional[float] = ..., uplimit_price: _Optional[float] = ..., downlimit_price: _Optional[float] = ..., pe: _Optional[float] = ..., iopv: _Optional[float] = ..., withdraw_buynum: _Optional[int] = ..., withdraw_buyvolume: _Optional[int] = ..., withdraw_buyturnover: _Optional[float] = ..., withdraw_sellnum: _Optional[int] = ..., withdraw_sellvolume: _Optional[int] = ..., withdraw_sellturnover: _Optional[float] = ..., totalbid_num: _Optional[int] = ..., totalask_num: _Optional[int] = ..., bidorder_num: _Optional[int] = ..., askorder_num: _Optional[int] = ..., inner_volume: _Optional[int] = ..., outer_volume: _Optional[int] = ..., aftermarket_volume: _Optional[int] = ..., aftermarket_volmount: _Optional[int] = ..., avg_price: _Optional[float] = ..., open_interest: _Optional[float] = ..., pre_open_interest: _Optional[float] = ...) -> None: ...

class QDOrder(_message.Message):
    __slots__ = ["market", "code", "date", "time", "index", "biz_index", "channel", "price", "volume", "side", "type"]
    MARKET_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    BIZ_INDEX_FIELD_NUMBER: _ClassVar[int]
    CHANNEL_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    SIDE_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    market: str
    code: str
    date: int
    time: int
    index: int
    biz_index: int
    channel: int
    price: float
    volume: int
    side: str
    type: str
    def __init__(self, market: _Optional[str] = ..., code: _Optional[str] = ..., date: _Optional[int] = ..., time: _Optional[int] = ..., index: _Optional[int] = ..., biz_index: _Optional[int] = ..., channel: _Optional[int] = ..., price: _Optional[float] = ..., volume: _Optional[int] = ..., side: _Optional[str] = ..., type: _Optional[str] = ...) -> None: ...

class QDTransaction(_message.Message):
    __slots__ = ["market", "code", "date", "time", "index", "biz_index", "channel", "price", "volume", "buy_index", "sell_index", "type", "bsflag"]
    MARKET_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    BIZ_INDEX_FIELD_NUMBER: _ClassVar[int]
    CHANNEL_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    BUY_INDEX_FIELD_NUMBER: _ClassVar[int]
    SELL_INDEX_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    BSFLAG_FIELD_NUMBER: _ClassVar[int]
    market: str
    code: str
    date: int
    time: int
    index: int
    biz_index: int
    channel: int
    price: float
    volume: int
    buy_index: int
    sell_index: int
    type: str
    bsflag: str
    def __init__(self, market: _Optional[str] = ..., code: _Optional[str] = ..., date: _Optional[int] = ..., time: _Optional[int] = ..., index: _Optional[int] = ..., biz_index: _Optional[int] = ..., channel: _Optional[int] = ..., price: _Optional[float] = ..., volume: _Optional[int] = ..., buy_index: _Optional[int] = ..., sell_index: _Optional[int] = ..., type: _Optional[str] = ..., bsflag: _Optional[str] = ...) -> None: ...

class QDSuperStock(_message.Message):
    __slots__ = ["market", "code", "date", "time", "new_price", "bidorder_price", "bidorder_volume", "askorder_price", "askorder_volume"]
    MARKET_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    NEW_PRICE_FIELD_NUMBER: _ClassVar[int]
    BIDORDER_PRICE_FIELD_NUMBER: _ClassVar[int]
    BIDORDER_VOLUME_FIELD_NUMBER: _ClassVar[int]
    ASKORDER_PRICE_FIELD_NUMBER: _ClassVar[int]
    ASKORDER_VOLUME_FIELD_NUMBER: _ClassVar[int]
    market: str
    code: str
    date: int
    time: int
    new_price: float
    bidorder_price: _containers.RepeatedScalarFieldContainer[float]
    bidorder_volume: _containers.RepeatedScalarFieldContainer[int]
    askorder_price: _containers.RepeatedScalarFieldContainer[float]
    askorder_volume: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, market: _Optional[str] = ..., code: _Optional[str] = ..., date: _Optional[int] = ..., time: _Optional[int] = ..., new_price: _Optional[float] = ..., bidorder_price: _Optional[_Iterable[float]] = ..., bidorder_volume: _Optional[_Iterable[int]] = ..., askorder_price: _Optional[_Iterable[float]] = ..., askorder_volume: _Optional[_Iterable[int]] = ...) -> None: ...

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: qds.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\tqds.proto\"V\n\x07SubInfo\x12\x17\n\x06\x61\x63tion\x18\x01 \x01(\x0e\x32\x07.Action\x12\x1e\n\nbasic_data\x18\x02 \x01(\x0e\x32\n.BasicData\x12\x12\n\nstock_list\x18\x03 \x03(\t\"`\n\x07Request\x12\x11\n\ttimestamp\x18\x01 \x01(\x03\x12\x19\n\x07subinfo\x18\x02 \x01(\x0b\x32\x08.SubInfo\x12\x0c\n\x04host\x18\x03 \x01(\t\x12\x0c\n\x04port\x18\x04 \x01(\x05\x12\x0b\n\x03pid\x18\x05 \x01(\x05\"Z\n\tQuoteTime\x12\x14\n\x0cproduce_time\x18\x01 \x01(\x04\x12\x11\n\tsend_time\x18\x02 \x01(\x04\x12\x11\n\trecv_time\x18\x03 \x01(\x04\x12\x11\n\tcalc_time\x18\x04 \x01(\x04\"\xf6\x01\n\tQuoteData\x12\x1e\n\nquote_time\x18\x01 \x01(\x0b\x32\n.QuoteTime\x12\x1e\n\nbasic_data\x18\x02 \x01(\x0e\x32\n.BasicData\x12 \n\x0bqd_snapshot\x18\x03 \x01(\x0b\x32\x0b.QDSnapshot\x12&\n\x0eqd_transaction\x18\x04 \x01(\x0b\x32\x0e.QDTransaction\x12\x1a\n\x08qd_order\x18\x05 \x01(\x0b\x32\x08.QDOrder\x12%\n\x0eqd_super_stock\x18\x06 \x01(\x0b\x32\r.QDSuperStock\x12\x1c\n\tqd_minute\x18\x07 \x01(\x0b\x32\t.QDMinute\"\xcd\x03\n\x08QDMinute\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x02 \x01(\x05\x12\x0c\n\x04time\x18\x03 \x01(\x05\x12\x0c\n\x04open\x18\x04 \x01(\x01\x12\x0c\n\x04high\x18\x05 \x01(\x01\x12\x0b\n\x03low\x18\x06 \x01(\x01\x12\r\n\x05\x63lose\x18\x07 \x01(\x01\x12\x0e\n\x06\x66\x61\x63tor\x18\x08 \x01(\x01\x12\x0e\n\x06volume\x18\t \x01(\x01\x12\x10\n\x08turnover\x18\n \x01(\x01\x12\x15\n\rturnover_rate\x18\x0b \x01(\x01\x12\x11\n\tis_paused\x18\x0c \x01(\x08\x12\x15\n\ruplimit_price\x18\r \x01(\x01\x12\x17\n\x0f\x64ownlimit_price\x18\x0e \x01(\x01\x12\x11\n\tavg_price\x18\x0f \x01(\x01\x12\x11\n\tpre_price\x18\x10 \x01(\x01\x12\x12\n\nquote_rate\x18\x11 \x01(\x01\x12\x10\n\x08\x61mp_rate\x18\x12 \x01(\x01\x12\r\n\x05is_st\x18\x13 \x01(\x08\x12\x0e\n\x06settle\x18\x14 \x01(\x01\x12\x12\n\npre_settle\x18\x15 \x01(\x01\x12\x0e\n\x06\x63hange\x18\x16 \x01(\x01\x12\x15\n\ropen_interest\x18\x17 \x01(\x01\x12\x12\n\npos_change\x18\x18 \x01(\x01\x12\x19\n\x11pre_open_interest\x18\x19 \x01(\x01\"\xe2\x07\n\nQDSnapshot\x12\x0e\n\x06market\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08\x63odename\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x05 \x01(\x05\x12\x0c\n\x04time\x18\x06 \x01(\x05\x12\x11\n\tpre_price\x18\x07 \x01(\x01\x12\x12\n\nopen_price\x18\x08 \x01(\x01\x12\x12\n\nhigh_price\x18\t \x01(\x01\x12\x11\n\tlow_price\x18\n \x01(\x01\x12\x11\n\tnew_price\x18\x0b \x01(\x01\x12\x0e\n\x06volume\x18\x0c \x01(\x01\x12\x10\n\x08turnover\x18\r \x01(\x01\x12\x11\n\ttrade_num\x18\x0e \x01(\x03\x12\x16\n\x0e\x62idorder_price\x18\x0f \x03(\x01\x12\x17\n\x0f\x62idorder_volume\x18\x10 \x03(\x01\x12\x15\n\rbid_numorders\x18\x11 \x03(\x03\x12\x16\n\x0e\x61skorder_price\x18\x12 \x03(\x01\x12\x17\n\x0f\x61skorder_volume\x18\x13 \x03(\x01\x12\x15\n\rask_numorders\x18\x14 \x03(\x03\x12\x17\n\x0ftotalbid_volume\x18\x15 \x01(\x03\x12\x17\n\x0ftotalask_volume\x18\x16 \x01(\x03\x12\x14\n\x0c\x61vgbid_price\x18\x17 \x01(\x01\x12\x14\n\x0c\x61vgask_price\x18\x18 \x01(\x01\x12\x15\n\ruplimit_price\x18\x19 \x01(\x01\x12\x17\n\x0f\x64ownlimit_price\x18\x1a \x01(\x01\x12\n\n\x02pe\x18\x1b \x01(\x01\x12\x0c\n\x04iopv\x18\x1c \x01(\x01\x12\x17\n\x0fwithdraw_buynum\x18\x1d \x01(\x05\x12\x1a\n\x12withdraw_buyvolume\x18\x1e \x01(\x03\x12\x1c\n\x14withdraw_buyturnover\x18\x1f \x01(\x01\x12\x18\n\x10withdraw_sellnum\x18  \x01(\x05\x12\x1b\n\x13withdraw_sellvolume\x18! \x01(\x03\x12\x1d\n\x15withdraw_sellturnover\x18\" \x01(\x01\x12\x14\n\x0ctotalbid_num\x18# \x01(\x05\x12\x14\n\x0ctotalask_num\x18$ \x01(\x05\x12\x14\n\x0c\x62idorder_num\x18% \x01(\x05\x12\x14\n\x0c\x61skorder_num\x18& \x01(\x05\x12\x14\n\x0cinner_volume\x18\' \x01(\x03\x12\x14\n\x0couter_volume\x18( \x01(\x03\x12\x1a\n\x12\x61\x66termarket_volume\x18) \x01(\x03\x12\x1c\n\x14\x61\x66termarket_volmount\x18* \x01(\x05\x12\x11\n\tavg_price\x18+ \x01(\x01\x12\x15\n\ropen_interest\x18, \x01(\x01\x12\x19\n\x11pre_open_interest\x18- \x01(\x01\"\xb1\x01\n\x07QDOrder\x12\x0e\n\x06market\x18\x0b \x01(\t\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x02 \x01(\x05\x12\x0c\n\x04time\x18\x03 \x01(\x05\x12\r\n\x05index\x18\x04 \x01(\x05\x12\x11\n\tbiz_index\x18\x05 \x01(\x05\x12\x0f\n\x07\x63hannel\x18\x06 \x01(\x05\x12\r\n\x05price\x18\x07 \x01(\x01\x12\x0e\n\x06volume\x18\x08 \x01(\x05\x12\x0c\n\x04side\x18\t \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\"\xe0\x01\n\rQDTransaction\x12\x0e\n\x06market\x18\r \x01(\t\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x02 \x01(\x05\x12\x0c\n\x04time\x18\x03 \x01(\x05\x12\r\n\x05index\x18\x04 \x01(\x05\x12\x11\n\tbiz_index\x18\x05 \x01(\x05\x12\x0f\n\x07\x63hannel\x18\x06 \x01(\x05\x12\r\n\x05price\x18\x07 \x01(\x01\x12\x0e\n\x06volume\x18\x08 \x01(\x05\x12\x11\n\tbuy_index\x18\t \x01(\x05\x12\x12\n\nsell_index\x18\n \x01(\x05\x12\x0c\n\x04type\x18\x0b \x01(\t\x12\x0e\n\x06\x62sflag\x18\x0c \x01(\t\"\xbd\x01\n\x0cQDSuperStock\x12\x0e\n\x06market\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x03 \x01(\x05\x12\x0c\n\x04time\x18\x04 \x01(\x05\x12\x11\n\tnew_price\x18\x05 \x01(\x01\x12\x16\n\x0e\x62idorder_price\x18\x06 \x03(\x01\x12\x17\n\x0f\x62idorder_volume\x18\x07 \x03(\x03\x12\x16\n\x0e\x61skorder_price\x18\x08 \x03(\x01\x12\x17\n\x0f\x61skorder_volume\x18\t \x03(\x03*f\n\tBasicData\x12\x0c\n\x08SNAPSHOT\x10\x00\x12\t\n\x05ORDER\x10\x01\x12\x0f\n\x0bTRANSACTION\x10\x02\x12\t\n\x05QUEUE\x10\x03\x12\x0f\n\x0bSUPER_STOCK\x10\x04\x12\n\n\x06MINUTE\x10\x05\x12\x07\n\x03\x44\x41Y\x10\x06*\"\n\x06\x41\x63tion\x12\x0b\n\x07\x41\x44\x44_SUB\x10\x00\x12\x0b\n\x07\x44\x45L_SUB\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'qds_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_BASICDATA']._serialized_start=2600
  _globals['_BASICDATA']._serialized_end=2702
  _globals['_ACTION']._serialized_start=2704
  _globals['_ACTION']._serialized_end=2738
  _globals['_SUBINFO']._serialized_start=13
  _globals['_SUBINFO']._serialized_end=99
  _globals['_REQUEST']._serialized_start=101
  _globals['_REQUEST']._serialized_end=197
  _globals['_QUOTETIME']._serialized_start=199
  _globals['_QUOTETIME']._serialized_end=289
  _globals['_QUOTEDATA']._serialized_start=292
  _globals['_QUOTEDATA']._serialized_end=538
  _globals['_QDMINUTE']._serialized_start=541
  _globals['_QDMINUTE']._serialized_end=1002
  _globals['_QDSNAPSHOT']._serialized_start=1005
  _globals['_QDSNAPSHOT']._serialized_end=1999
  _globals['_QDORDER']._serialized_start=2002
  _globals['_QDORDER']._serialized_end=2179
  _globals['_QDTRANSACTION']._serialized_start=2182
  _globals['_QDTRANSACTION']._serialized_end=2406
  _globals['_QDSUPERSTOCK']._serialized_start=2409
  _globals['_QDSUPERSTOCK']._serialized_end=2598
# @@protoc_insertion_point(module_scope)

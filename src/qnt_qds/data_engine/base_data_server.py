import multiprocessing
import time
from typing import Callable, Dict, List, Optional, Tuple, Type

import pandas as pd

from qnt_utils.enums import Action, BasicData
from qnt_utils.label import DataLabel, QSymbol, generate_data_label, parse_data_label

from .. import struct
from .adapter import Adapter


def _get_io_data_label(
    basic_data: BasicData, symbol: QSymbol | str, is_merge: bool
) -> tuple[Optional[DataLabel], Optional[DataLabel]]:
    """生成数据标签

    Args:
        _basic_data: 基础数据类型
        symbol: 股票代码

    Returns:
        output data label,input data label
    """
    if is_merge:
        if basic_data.value <= 5:
            b1 = basic_data
            b2 = basic_data
        elif basic_data.value == 6:
            b1 = BasicData(6)
            b2 = BasicData(1)
        else:
            return None, None
    else:
        if basic_data.value <= 6:
            b1 = basic_data
            b2 = basic_data
        else:
            return None, None
    return generate_data_label(symbol, b1), generate_data_label(symbol, b2)


class SubManager:
    """在数据对象中创建的订阅请求管理对象, 内置一个请求处理的线程, 记录下游发送的订阅和取消订阅的请求, 记为output_data_label;
    如果output_data_label对应的input_data_label不存在, 则向上游订阅这一类型的数据;
    如果某一input_data_label下没有output_data_label, 则向上游取消订阅这一类型的数据"""

    def __init__(self, is_merge=False):
        self._subrequests_queue: multiprocessing.Queue[struct.Request] = multiprocessing.Queue()
        """待处理请求队列"""
        self._sub: Callable[[Action | str, BasicData | str, List[QSymbol | str]], None]
        """数据生成对象中对于订阅行为的处理函数, 如果是基础数据对象, 则向服务端发送订阅查询请求, 如果是合成数据对象, 则是生成请求, 发送到请求管理对象"""
        self._is_merge = is_merge

    def register_sub_fnc(self, sub_fnc):
        self._sub = sub_fnc

    def enqueue_subscription_request(self, request: struct.Request):
        self._subrequests_queue.put(request)

    def handle_subinfo(self):
        """处理请求的子线程"""
        sub_dict: Dict[DataLabel, Dict[DataLabel, List[Tuple[str, int]]]] = {}
        """订阅信息表, {input_data_label:{output_data_label:[(下游IP和port),...]}}"""
        while True:
            while self._subrequests_queue.qsize():
                request: struct.Request = self._subrequests_queue.get()
                if request.subinfo.action == Action.ADD_SUB:
                    for i in request.subinfo.stock_list:
                        output_data_label, input_data_label = _get_io_data_label(
                            request.subinfo.basic_data, i, self._is_merge
                        )
                        if output_data_label is None or input_data_label is None:
                            continue
                        if input_data_label not in sub_dict.keys():
                            sub_dict[input_data_label] = {}
                            self._sub(Action.ADD_SUB, parse_data_label(input_data_label)[1], [i])
                        if output_data_label not in sub_dict[input_data_label].keys():
                            sub_dict[input_data_label][output_data_label] = []
                        if (request.host, request.port) not in sub_dict[input_data_label][output_data_label]:
                            sub_dict[input_data_label][output_data_label].append((request.host, request.port))
                elif request.subinfo.action == Action.DEL_SUB:
                    for i in request.subinfo.stock_list:
                        output_data_label, input_data_label = _get_io_data_label(
                            request.subinfo.basic_data, i, self._is_merge
                        )
                        if output_data_label is None or input_data_label is None:
                            continue
                        if input_data_label not in sub_dict.keys():
                            continue
                        if output_data_label not in sub_dict[input_data_label].keys():
                            continue
                        if (request.host, request.port) in sub_dict[input_data_label][output_data_label]:
                            sub_dict[input_data_label][output_data_label].remove((request.host, request.port))
                        if len(sub_dict[input_data_label][output_data_label]) == 0:
                            sub_dict[input_data_label].pop(output_data_label)
                        if len(sub_dict[input_data_label]) == 0:
                            sub_dict.pop(input_data_label)
                            self._sub(Action.DEL_SUB, parse_data_label(input_data_label)[1], [i])
            time.sleep(0.1)


class Timer:
    def __init__(self):
        """计时器,用于根据tick时间校准市场时间"""
        self._time = None
        self._target_time = None

    def get_time(self) -> Optional[pd.Timestamp]:
        """获取当前市场时间"""
        if self._time is None or self._target_time is None:
            return None
        else:
            return self._time + (pd.Timestamp.now() - self._target_time)

    def get_int_time(self) -> Optional[int]:
        """获取当前市场时间"""
        tmp = self.get_time()
        if tmp is None:
            return None
        return int(tmp.hour * 10000000 + tmp.minute * 100000 + tmp.second * 1000 + tmp.microsecond / 1000)

    def set_time(self, data: dict):
        """用于设置市场时间, 如果市场时间慢于行情数据时间, 则把市场时间调整到和行情数据时间一致

        Args:
            data: 行情数据时间
        """

        time_str = "%s %012s" % (data["date"], data["time"] * 1000)
        market_time = pd.Timestamp(time_str)
        if self._time is None or ((tmp := self.get_time()) is not None and tmp < market_time):
            self._time = market_time
            self._target_time = pd.Timestamp.now()


class BasicDataServer:
    def __init__(self, quotation_api_type: Type, **quote_api_kwargs):
        """基础数据生成对象"""
        self._qapi: Type = quotation_api_type
        self._sub_manager = SubManager()

        self._p: multiprocessing.Process
        self._quote_api_kwargs = quote_api_kwargs

    def run(self):
        """启动订阅管理对象, 在第一个订阅请求到达之后调用"""
        qapi: Adapter = self._qapi(**self._quote_api_kwargs)
        self._sub_manager.register_sub_fnc(qapi.subscribe)
        self._sub_manager.handle_subinfo()

    def handle_subscription_request(self, request: struct.Request):
        """如果订阅管理对象未启动, 则先启动; 然后将请求转发给订阅管理对象进行处理"""
        if not hasattr(self, "_p"):
            self._p = multiprocessing.Process(target=self.run, daemon=True)
            self._p.start()
        self._sub_manager.enqueue_subscription_request(request)

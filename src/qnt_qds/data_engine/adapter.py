import datetime as dt
import queue
import threading
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List

import pandas as pd
from loguru import logger
from tqsdk import BacktestFinished, TqApi, TqAuth, TqBacktest, TqTimeoutError

from ashare_api import AShare
from qnt_qds.rabbitmq import Rabbit<PERSON><PERSON><PERSON>usher
from qnt_research.api import is_within_trading_time
from qnt_utils.enums import Action, BasicData
from qnt_utils.label import DataLabel, QSymbol, Symbol, generate_data_label, parse_data_label

from .._globals import gl_config


class Adapter(ABC):
    @abstractmethod
    def subscribe(self, action: Action | str, basic_data: str | BasicData, symbols: List[QSymbol | str]) -> None:
        """回调函数，注册到SubManager中，用于处理订阅请求

        Args:
            action (Action | str): 订阅或取消订阅
            basic_data (str | BasicData): 基础数据类型
            symbols (List[QSymbol  |  str]): 股票代码列表
        """
        pass


class AShareAdapter(Adapter, AShare):
    def __init__(self):
        self._rbmq = RabbitMQPusher(BasicData.MINUTE)
        self._fnc_minute = self._rbmq.on_minute
        AShare.__init__(self, self._on_minute)

    def subscribe(self, action: Action | str, basic_data: str | BasicData, symbols: List[QSymbol | str]) -> None:
        action_ = action if isinstance(action, Action) else Action[action]
        basic_data_ = basic_data if isinstance(basic_data, BasicData) else BasicData[basic_data]

        # AShareAdapter只支持分钟数据
        if basic_data_ != BasicData.MINUTE:
            return

        for symbol in symbols:
            if action_ == Action.ADD_SUB:
                AShare.add_sub(self, Symbol.qs_to_as(symbol))
            elif action_ == Action.DEL_SUB:
                AShare.del_sub(self, Symbol.qs_to_as(symbol))

    def _on_minute(self, data: Dict):
        res = {}
        res["open"] = data["open"]
        res["high"] = data["high"]
        res["low"] = data["low"]
        res["close"] = data["close"]
        res["volume"] = data["volume"]
        res["date"] = int(data["datetime"].strftime("%Y%m%d"))
        res["time"] = int(data["datetime"].strftime("%H%M%S000"))
        res["code"] = Symbol.qs_from_as(data["code"])

        # 检查数据延迟
        current_time = pd.Timestamp.now().timestamp()
        delay = current_time - data["datetime"].timestamp()

        if delay > 30:
            logger.warning(f"Data delay detected for code {res['code']}: {delay:.2f} seconds")

        self._fnc_minute(res)


class TianQinAdapter(Adapter):
    def __init__(self, account, password) -> None:
        self._account = account
        self._password = password
        self._rbmq_snapshot = RabbitMQPusher(BasicData.SNAPSHOT)
        self._fnc_snapshot = self._rbmq_snapshot.on_snapshot
        self._rbmq_minute = RabbitMQPusher(BasicData.MINUTE)
        self._fnc_minute = self._rbmq_minute.on_minute

        self._subrequests_queue: queue.Queue[tuple[Action, BasicData, List[QSymbol | str]]] = queue.Queue()
        threading.Thread(target=self._sub_thread, daemon=True).start()

    def subscribe(self, action: Action | str, basic_data: str | BasicData, symbols: List[QSymbol | str]) -> None:
        action_ = action if isinstance(action, Action) else Action[action]
        basic_data_ = basic_data if isinstance(basic_data, BasicData) else BasicData[basic_data]
        self._subrequests_queue.put((action_, basic_data_, symbols))

    def _sub_thread(self):
        underlying_info: Dict[DataLabel, Dict] = {}
        quote_obj_dict: Dict[DataLabel, Any] = {}
        try_times = 0
        debug_config = gl_config["qds"]["tq_sdk"]["debug"]
        while True:
            if not debug_config["available"] and not is_within_trading_time(
                "SHFE", pd.Timestamp.now(tz="Asia/Shanghai"), 600
            ):
                time.sleep(60)
                continue

            # 初始化, 登录成功则重置尝试次数, 否则尝试次数加1, 如果尝试次数达到2次则抛出异常
            try:
                if debug_config["available"]:
                    tq_api = TqApi(
                        backtest=TqBacktest(
                            start_dt=dt.datetime.strptime(debug_config["start_dt"], "%Y%m%d").date(),
                            end_dt=dt.datetime.strptime(debug_config["end_dt"], "%Y%m%d").date(),
                        ),
                        auth=TqAuth(self._account, self._password),
                    )

                else:
                    tq_api = TqApi(auth=TqAuth(self._account, self._password))
                try_times = 0
            except Exception:
                try_times += 1
                if try_times == 2:
                    raise RuntimeError("TianQinAdapter: login failed")
                continue

            # 登录成功后, 开始处理业务
            try:
                while True:
                    # 处理订阅请求, 生成数据对象, 并且获取合约信息
                    while self._subrequests_queue.qsize() > 0:
                        action, basic_data, symbols = self._subrequests_queue.get()
                        for symbol in symbols:
                            tqsymbol = Symbol.qs_to_tq(symbol)
                            if not tqsymbol:
                                continue
                            data_label = generate_data_label(symbol, basic_data)
                            if action == Action.ADD_SUB:
                                if basic_data == BasicData.SNAPSHOT:
                                    quote_obj_dict[data_label] = tq_api.get_tick_serial(symbol=tqsymbol, data_length=1)
                                    underlying_info[data_label] = (
                                        tq_api.query_symbol_info(tqsymbol).iloc[-1, :].to_dict()
                                    )
                                elif basic_data == BasicData.MINUTE:
                                    quote_obj_dict[data_label] = tq_api.get_kline_serial(
                                        symbol=tqsymbol,
                                        duration_seconds=60,
                                        data_length=2,
                                    )
                                    underlying_info[data_label] = (
                                        tq_api.query_symbol_info(tqsymbol).iloc[-1, :].to_dict()
                                    )
                                underlying_info[data_label]["end_time"] = []
                                for i in ["trading_time_day", "trading_time_night"]:
                                    if underlying_info[data_label][i] is not None:
                                        for j in underlying_info[data_label][i]:
                                            j[0] = pd.Timestamp(j[0]).time()
                                            if j[1] == "26:30:00":
                                                j[1] = pd.Timestamp("02:30:00").time()
                                            elif j[1] == "25:00:00":
                                                j[1] = pd.Timestamp("01:00:00").time()
                                            else:
                                                j[1] = pd.Timestamp(j[1]).time()
                                            underlying_info[data_label]["end_time"].append(j[1])

                    # 处理数据
                    if tq_api.wait_update(time.time() + 0.1):
                        for output_data_label, value2 in quote_obj_dict.items():
                            if not tq_api.is_changing(value2):
                                continue
                            tmp_symbol, tmp_basic_data = parse_data_label(output_data_label)
                            if tmp_basic_data == BasicData.SNAPSHOT:
                                # 处理快照数据
                                data = value2.iloc[-1, :]
                                tmp_datetime = pd.Timestamp.fromtimestamp(data["datetime"] / 1e9)
                                res: dict[str, Any] = {}
                                res["market"] = underlying_info[output_data_label]["exchange_id"]
                                res["code"] = tmp_symbol
                                res["date"] = int(tmp_datetime.strftime("%Y%m%d"))
                                res["time"] = int(tmp_datetime.strftime("%H%M%S%f")[:-3])
                                res["new_price"] = data["last_price"]
                                res["avg_price"] = data["average"]
                                res["high_price"] = data["highest"]
                                res["low_price"] = data["lowest"]
                                res["bidorder_price"] = [
                                    data["bid_price1"],
                                    data["bid_price2"],
                                    data["bid_price3"],
                                    data["bid_price4"],
                                    data["bid_price5"],
                                ]
                                res["bidorder_volume"] = [
                                    data["bid_volume1"],
                                    data["bid_volume2"],
                                    data["bid_volume3"],
                                    data["bid_volume4"],
                                    data["bid_volume5"],
                                ]
                                res["askorder_price"] = [
                                    data["ask_price1"],
                                    data["ask_price2"],
                                    data["ask_price3"],
                                    data["ask_price4"],
                                    data["ask_price5"],
                                ]
                                res["askorder_volume"] = [
                                    data["ask_volume1"],
                                    data["ask_volume2"],
                                    data["ask_volume3"],
                                    data["ask_volume4"],
                                    data["ask_volume5"],
                                ]
                                res["volume"] = data["volume"]
                                res["turnover"] = data["amount"]
                                res["open_interest"] = data["open_interest"]
                                res["uplimit_price"] = underlying_info[output_data_label]["upper_limit"]
                                res["downlimit_price"] = underlying_info[output_data_label]["lower_limit"]
                                res["pre_price"] = underlying_info[output_data_label]["pre_close"]
                                res["pre_open_interest"] = underlying_info[output_data_label]["pre_open_interest"]
                                self._fnc_snapshot(res)
                            elif tmp_basic_data == BasicData.MINUTE:
                                # 处理分钟数据
                                tmp_time = pd.Timestamp.now()
                                if any(
                                    [
                                        tmp_time.time() < i < (tmp_time + pd.Timedelta(seconds=3)).time()
                                        for i in underlying_info[output_data_label]["end_time"]
                                    ]
                                ):
                                    data = value2.iloc[-1, :]
                                else:
                                    if tq_api.is_changing(value2.iloc[-1, :], "datetime"):
                                        data = value2.iloc[-2, :]
                                    else:
                                        data = None
                                if data is not None:
                                    tmp_datetime = pd.Timestamp.fromtimestamp(data["datetime"] / 1e9) + pd.Timedelta(
                                        minutes=1
                                    )
                                    res: dict[str, Any] = {}
                                    res["code"] = tmp_symbol
                                    res["date"] = int(tmp_datetime.strftime("%Y%m%d"))
                                    res["time"] = int(tmp_datetime.strftime("%H%M%S%f")[:-3])
                                    res["open"] = data["open"]
                                    res["high"] = data["high"]
                                    res["low"] = data["low"]
                                    res["close"] = data["close"]
                                    res["volume"] = data["volume"]
                                    res["open_interest"] = data["close_oi"]
                                    res["uplimit_price"] = underlying_info[output_data_label]["upper_limit"]
                                    res["downlimit_price"] = underlying_info[output_data_label]["lower_limit"]
                                    res["pre_settle"] = underlying_info[output_data_label]["pre_settlement"]
                                    res["pre_price"] = underlying_info[output_data_label]["pre_close"]
                                    res["pre_open_interest"] = underlying_info[output_data_label]["pre_open_interest"]
                                    self._fnc_minute(res)
            except BacktestFinished:
                pass
            except TqTimeoutError:
                for i in quote_obj_dict.keys():
                    symbol, basic_data = parse_data_label(i)
                    self._subrequests_queue.put((Action.ADD_SUB, basic_data, [symbol]))
            except Exception as e:
                logger.exception(e)
                for i in quote_obj_dict.keys():
                    symbol, basic_data = parse_data_label(i)
                    self._subrequests_queue.put((Action.ADD_SUB, basic_data, [symbol]))
            finally:
                tq_api.close()
                quote_obj_dict = {}

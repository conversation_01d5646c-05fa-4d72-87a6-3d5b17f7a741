import json
from functools import lru_cache

import requests


@lru_cache
def _get_gplist_from_ig507():
    try:
        return json.loads(requests.get("https://ig507.com/data/base/gplist?licence=").text)
    except:
        return []


def _get_gplist():
    for _ in range(3):
        ret = _get_gplist_from_ig507()
        if len(ret) > 0:
            break
        _get_gplist_from_ig507.cache_clear()
    return ret


@lru_cache
def _get_fundlist_from_ig507():
    try:
        return json.loads(requests.get("https://ig507.com/data/base/jjdm?licence=").text)
    except:
        return []


def _get_fundlist():
    for _ in range(3):
        ret = _get_fundlist_from_ig507()
        if len(ret) > 0:
            break
        _get_fundlist_from_ig507.cache_clear()
    return ret


def get_stock_name(symbol: str) -> str | None:
    """根据symbol获取股票名称"""
    tmp = _get_gplist()
    stock_name_map = {"{}.{}".format(i["dm"], {"sh": "SSE", "sz": "SZSE"}[i["jys"]]): i["mc"] for i in tmp}
    fund_name_map = {i["dm"]: i["mc"] for i in _get_fundlist()}
    return stock_name_map.get(symbol, fund_name_map.get(symbol.split(".")[0], None))


def symbol_compelted(code: str) -> str:
    """证券代码自动补全

    Args:
        code (str): 证券代码，6位

    Returns:
        str: 完整的symbol，如600000.SSE
    """
    tmp = _get_gplist()
    symbol_map = {i["dm"]: "{}.{}".format(i["dm"], {"sh": "SSE", "sz": "SZSE"}[i["jys"]]) for i in tmp}
    return symbol_map[code]

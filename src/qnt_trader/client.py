import json
import threading
import time
from functools import wraps
from typing import Optional

import grpc
from google.protobuf import json_format
from loguru import logger

from qnt_utils.enums import Direction, OffSet, PriceType, StatusCode
from qnt_utils.label import QSymbol

from .protos import qnt_trader_pb2, qnt_trader_pb2_grpc


def _auto_complete(fnc):
    """自动填写个别参数"""

    @wraps(fnc)
    def wrapper(self: "TraderClient", *args, **kwargs):
        return fnc(
            self,
            *args,
            **kwargs,
            timestamp=time.time_ns(),
            account=self._account,
            is_credit=self._is_credit,
            source=self._source,
        )

    return wrapper


class TraderClient:
    def __init__(self, account: str, is_credit: bool, source: str, host: str, port: int):
        self._account = account
        self._is_credit = is_credit
        self._source = source
        self._host = host
        self._port = port
        self._channel = grpc.insecure_channel(
            "{}:{}".format(host, port),
            options=[
                ("grpc.enable_retries", 1),
                (
                    "grpc.service_config",
                    json.dumps(
                        {
                            "methodConfig": [
                                {
                                    "name": [{}],
                                    "retryPolicy": {
                                        "maxAttempts": 5,
                                        "initialBackoff": "0.1s",
                                        "maxBackoff": "30s",
                                        "backoffMultiplier": 5,
                                        "retryableStatusCodes": ["UNAVAILABLE"],
                                    },
                                }
                            ]
                        }
                    ),
                ),
            ],
        )
        self._stub = qnt_trader_pb2_grpc.TraderStub(self._channel)

    @property
    def account(self):
        return self._account, self._is_credit

    @_auto_complete
    def register_order_push(self, callback, **kwargs):
        """注册成交推送的回调

        Args:
            callback (function): 回调函数
        """

        def fnc(callback, kwargs):
            logger.info("thread of receiving order push messages is running.")
            while True:
                try:
                    with grpc.insecure_channel(
                        "{}:{}".format(self._host, self._port),
                        options=[
                            ("grpc.keepalive_time_ms", 8000),
                            ("grpc.keepalive_timeout_ms", 5000),
                            ("grpc.http2.max_pings_without_data", 0),
                            ("grpc.keepalive_permit_without_calls", 1),
                        ],
                    ) as channel:
                        logger.info("start receiving order push messages.")
                        stub = qnt_trader_pb2_grpc.TraderStub(channel)
                        for i in stub.order_push(qnt_trader_pb2.OrderPushRequest(**kwargs)):
                            for j in json_format.MessageToDict(
                                i, including_default_value_fields=True, preserving_proto_field_name=True
                            )["orders"]:
                                callback(j)
                except Exception:
                    logger.warning("order push connection is unavailable! Waiting to retry...")
                    time.sleep(5)

        threading.Thread(target=fnc, args=(callback, kwargs), daemon=True).start()

    @property
    @_auto_complete
    def portfolio(self, **kwargs) -> tuple[StatusCode, Optional[dict]]:
        """账户详情"""
        response = self._stub.portfolio(qnt_trader_pb2.PortfolioRequest(**kwargs))
        if response.status == 0:
            return (
                StatusCode.SUCCESS,
                json_format.MessageToDict(
                    response, including_default_value_fields=True, preserving_proto_field_name=True
                )["portfolio"],
            )
        else:
            return StatusCode.FAILED, None

    @property
    @_auto_complete
    def positions(self, **kwargs) -> tuple[StatusCode, Optional[dict]]:
        """持仓"""

        def fnc(res: dict) -> dict:
            ret = {}
            for i in res:
                if i["symbol"] not in ret.keys():
                    ret[i["symbol"]] = {}
                if i["position_type"] not in ret[i["symbol"]].keys():
                    ret[i["symbol"]][i["position_type"]] = {}
                ret[i["symbol"]][i["position_type"]] = i
            return ret

        response = self._stub.positions(qnt_trader_pb2.PositionsRequest(**kwargs))
        if response.status == 0:
            return (
                StatusCode.SUCCESS,
                fnc(
                    json_format.MessageToDict(
                        response, including_default_value_fields=True, preserving_proto_field_name=True
                    )["positions"]
                ),
            )
        else:
            return StatusCode.FAILED, None

    @_auto_complete
    def order(
        self,
        symbol: QSymbol,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: str | PriceType = "LIMIT",
        group: str = "",
        **kwargs,
    ) -> tuple[StatusCode, Optional[str]]:
        """下单

        Args:
            symbol (QSymbol): 证券代码
            direction (str | Direction): 方向
            offset (str | OffSet): 开平标志
            amount (float): 数量
            price (float): 价格
            price_type (str | PriceType, optional): 价格类型. Defaults to PriceType.LIMIT.
            group (str): 组. Defaults to "".

        Returns:
            tuple[StatusCode, Optional[str]]: 状态码, 单号
        """
        direction1 = Direction.efrom(direction)
        offset1 = OffSet.efrom(offset)
        price_type1 = PriceType.efrom(price_type)
        response = self._stub.order(
            qnt_trader_pb2.OrderRequest(
                symbol=symbol,
                price=price,
                amount=amount,
                direction=qnt_trader_pb2.Direction.Name(direction1.value),
                offset=qnt_trader_pb2.OffSet.Name(offset1.value),
                price_type=qnt_trader_pb2.PriceType.Name(price_type1.value),
                group=group,
                **kwargs,
            )
        )
        if response.status == 0:
            return (
                StatusCode.SUCCESS,
                json_format.MessageToDict(
                    response, including_default_value_fields=True, preserving_proto_field_name=True
                )["order_id"],
            )
        else:
            return StatusCode.FAILED, None

    @_auto_complete
    def cancel_order(self, order_id: str, **kwargs) -> tuple[StatusCode, bool]:
        """撤单

        Args:
            order_id (str): 状态码, 单号

        Returns:
            tuple[StatusCode, bool]: 是否成功
        """
        response = self._stub.cancel_order(qnt_trader_pb2.CancelOrderRequest(order_id=order_id, **kwargs))
        if response.status == 0:
            return (StatusCode.SUCCESS, True)
        else:
            return (StatusCode.FAILED, False)

    @_auto_complete
    def get_orders(self, start_date: str, end_date: str, **kwargs) -> tuple[StatusCode, Optional[list[dict]]]:
        response = self._stub.get_orders(
            qnt_trader_pb2.GetOrdersRequest(start_date=start_date, end_date=end_date, **kwargs)
        )
        if response.status == 0:
            return (
                StatusCode.SUCCESS,
                json_format.MessageToDict(
                    response, including_default_value_fields=True, preserving_proto_field_name=True
                )["orders"],
            )
        else:
            return (StatusCode.FAILED, None)

    def close(self):
        self._channel.close()

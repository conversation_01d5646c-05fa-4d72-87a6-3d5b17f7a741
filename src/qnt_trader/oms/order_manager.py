import asyncio
import copy
import datetime
from typing import Dict

import pandas as pd
from loguru import logger

from qnt_utils.enums import OrderStatus
from qnt_utils.toolset import decrypt

from .._globals import gl_config
from ..adapters.base import TradeAPIAdapter
from ..adapters.em_adapter import EMCreditTradeAPIAdapter, EMTradeAPIAdapter
from ..adapters.gm_adapter import GMTradeAPIAdapter
from ..protos import qnt_trader_pb2
from .order import Order
from .sql_handler import SQLHandlerOrmOfTrader


def _modify_portfolio(trade_api: TradeAPIAdapter, portfolio: Dict):
    portfolio = copy.deepcopy(portfolio)
    portfolio.update(
        {
            "date": pd.Timestamp.now(tz="Asia/Shanghai").strftime("%Y-%m-%d"),
            "account": trade_api.account,
            "is_credit": trade_api.is_credit,
        }
    )
    return portfolio


class OrderManager:
    def __init__(self, trade_api: TradeAPIAdapter, sql_handler: SQLHandlerOrmOfTrader):
        self._trade_api: TradeAPIAdapter = trade_api
        """交易接口对象"""
        self._sql_handler: SQLHandlerOrmOfTrader = sql_handler
        """操作数据库的handler"""
        self._waiting_corders = asyncio.Queue()
        """子单队列, 下单生成的初始子单和接口查询返回的子单, 都放入队列中待处理"""
        self._morders_dict: Dict[str, Order] = {}
        """保存母单对象的dict, key为order_id, value为Order对象"""
        self._corders_dict = {}
        """保存子单对象的dict, key为子单id, value为子单状态dict"""
        self._running_time = self._trade_api.get_near_trade_time()["next"]
        """运行时间"""
        self._async_lock = asyncio.Lock()

        logger.info(
            " TradeAPI: {}, 运行时间: {} ---- {}".format(
                self._trade_api.__class__.__name__, self._running_time["open"], self._running_time["close"]
            )
        )

        asyncio.create_task(self._coroutine_get_child_orders())
        asyncio.create_task(self._coroutine_get_portfolio())
        asyncio.create_task(self._coroutine_update_child_orders())
        asyncio.create_task(self._coroutine_review())

    @property
    def trade_api(self):
        return self._trade_api

    async def _coroutine_review(self):
        """协程, 收盘时review，确认当日是否还有未成交的委托"""

        # 在开盘前查一次账户状态，确认账户状态是否正常，并且通过webhook推送账户状态

        portfolio = await self.get_portfolio()
        if portfolio is None:
            logger.log("WEBHOOK", "{}初始化失败，当前账户状态为空".format(self._trade_api.__class__.__name__))
        else:
            logger.log(
                "WEBHOOK", "{}初始化完成，当前账户状态为{}".format(self._trade_api.__class__.__name__, portfolio)
            )
            await self._sql_handler.upsert("portfolio", [_modify_portfolio(self._trade_api, portfolio)])

        async with self._async_lock:
            current_close_time = self._running_time["close"]
            current_open_time = self._running_time["open"]

        need_check_portfolio_before_open = True
        need_review_portfolio_after_close = True
        need_review_orders_after_close = True

        while True:
            now = pd.Timestamp.now(tz="Asia/Shanghai")

            # 盘前检查trader账户状态
            if now > current_open_time - pd.Timedelta(seconds=40):
                if need_check_portfolio_before_open:
                    need_check_portfolio_before_open = False
                    portfolio = await self.get_portfolio()
                    if portfolio is None:
                        logger.log(
                            "WEBHOOK", "{}盘前初始化失败，当前账户状态为空".format(self._trade_api.__class__.__name__)
                        )
                    else:
                        logger.log(
                            "WEBHOOK",
                            "{}盘前初始化完成，当前账户状态为{}".format(self._trade_api.__class__.__name__, portfolio),
                        )
                        await self._sql_handler.upsert("portfolio", [_modify_portfolio(self._trade_api, portfolio)])

            # 盘后检查trader账户状态和委托
            if now > (current_close_time + pd.Timedelta(minutes=gl_config["trader"]["settings"]["review_time"])):
                if need_review_portfolio_after_close:
                    need_review_portfolio_after_close = False
                    portfolio = await self.get_portfolio()
                    if portfolio is None:
                        logger.log(
                            "WEBHOOK", "{}盘后检查失败，当前账户状态为空".format(self._trade_api.__class__.__name__)
                        )
                    else:
                        logger.log(
                            "WEBHOOK",
                            "{}盘后检查完成，当前账户状态为{}".format(self._trade_api.__class__.__name__, portfolio),
                        )
                        await self._sql_handler.upsert("portfolio", [_modify_portfolio(self._trade_api, portfolio)])

                if need_review_orders_after_close:
                    need_review_orders_after_close = False
                    unfinish = {}
                    finish = {}
                    for _, order in self._morders_dict.items():
                        order_info = order.info
                        if order_info["trade_amount"] != order_info["amount"]:
                            if order_info["symbol"] not in unfinish.keys():
                                unfinish[order_info["symbol"]] = {"amount": 0, "trade_amount": 0}
                            unfinish[order_info["symbol"]]["amount"] += order_info["amount"]
                            unfinish[order_info["symbol"]]["trade_amount"] += order_info["trade_amount"]
                        else:
                            if order_info["symbol"] not in finish.keys():
                                finish[order_info["symbol"]] = {"amount": 0, "trade_amount": 0}
                            finish[order_info["symbol"]]["amount"] += order_info["amount"]
                            finish[order_info["symbol"]]["trade_amount"] += order_info["trade_amount"]

                    if len(finish) > 0:
                        message = "{}盘后检查完成，以下委托已全部成交:\n".format(self._trade_api.__class__.__name__)
                        for symbol, v in finish.items():
                            message += f"\t{symbol}: 委托{v['amount']}, 成交{v['trade_amount']}\n"
                        logger.log("WEBHOOK", message)

                    if len(unfinish) > 0:
                        message = "{}盘后检查完成，以下委托未全部成交:\n".format(self._trade_api.__class__.__name__)
                        for symbol, v in unfinish.items():
                            message += f"\t{symbol}: 委托{v['amount']}, 成交{v['trade_amount']}\n"
                        logger.log("WEBHOOK", message)

                    if len(unfinish) == 0 and len(finish) == 0:
                        logger.info("{}盘后检查完成，当日无委托".format(self._trade_api.__class__.__name__))

            async with self._async_lock:
                if self._running_time["close"] != current_close_time:
                    current_close_time = self._running_time["close"]
                    current_open_time = self._running_time["open"]

                    need_review_portfolio_after_close = True
                    need_review_orders_after_close = True
                    need_check_portfolio_before_open = True

            await asyncio.sleep(1)

    async def _coroutine_update_child_orders(self):
        """协程, 根据查回来的委托，更新存储的子单状态"""

        # 初始化, 先读取当日未完成的委托
        res = await self._sql_handler.get_orders(
            self._trade_api.get_near_trade_time()["last"]["close"],
            pd.Timestamp.now(tz="Asia/Shanghai"),
            self._trade_api.account,
            self._trade_api.is_credit,
            True,
        )
        res1 = filter(lambda x: x["order_type"] == "M", res)
        res2 = filter(lambda x: x["order_type"] == "C", res)
        for order in res1:
            self._morders_dict[order["order_id"]] = Order(None, self, order)
        for order in res2:
            self._morders_dict[order["order_id"]].add_child_order(order)
            self._corders_dict[order["api_order_id"]] = order

        # 处理后续生成的委托
        while True:
            # 盘中更新委托状态
            while self._waiting_corders.qsize() > 0:
                co: Dict = await self._waiting_corders.get()
                # 无效子单的处理
                if (aoi := co["api_order_id"]) == "":
                    self._morders_dict[co["order_id"]].update_info()
                    await self._sql_handler.upsert("orders", [co, self._morders_dict[co["order_id"]].info])
                    continue

                if "order_id" in co.keys():  # 如果co里有order_id, 说明是执行下单操作生成的初始子单
                    if aoi not in self._corders_dict.keys():
                        self._corders_dict[aoi] = co

                else:  # 如果co里没有order_id，说明是交易接口查询返回的委托信息
                    if aoi in self._corders_dict.keys() and self._corders_dict[aoi]["status"] not in [
                        OrderStatus.FINISHED,
                        OrderStatus.SCRAPPED,
                        OrderStatus.CANCELLED,
                    ]:
                        self._corders_dict[aoi].update(co)

                # 更新母单状态，并更新到数据库中
                if aoi in self._corders_dict.keys():
                    self._morders_dict[self._corders_dict[aoi]["order_id"]].update_info()
                    await self._sql_handler.upsert(
                        "orders",
                        [self._corders_dict[aoi], self._morders_dict[self._corders_dict[aoi]["order_id"]].info],
                    )

            # 盘后清算
            now_time = pd.Timestamp.now(tz="Asia/Shanghai")
            if now_time > (self._running_time["close"] + pd.Timedelta(minutes=15)):
                # 等到真正收盘后，才清算当天的委托，而不是一轮运行结束之后清算，因为期货分成了夜盘日盘2
                if self._running_time["close"].time() == datetime.time(15, 0):
                    logger.info(
                        "TradeAPI: {}, 当前时间已到{}, 当日委托执行清算.".format(
                            self._trade_api.__class__.__name__, now_time
                        )
                    )

                    # 把所有未完成的委托标记为CANCELLED状态
                    for aoi in self._corders_dict.keys():
                        if self._corders_dict[aoi]["status"] in [
                            OrderStatus.ORDER_TBC,
                            OrderStatus.ALIVE,
                            OrderStatus.CANCEL_TBC,
                        ]:
                            self._corders_dict[aoi]["status"] = OrderStatus.CANCELLED
                            self._corders_dict[aoi]["m_dt"] = now_time.strftime("%Y-%m-%d %H:%M:%S.%f")
                            self._morders_dict[self._corders_dict[aoi]["order_id"]].update_info()
                            await self._sql_handler.upsert(
                                "orders",
                                [self._corders_dict[aoi], self._morders_dict[self._corders_dict[aoi]["order_id"]].info],
                            )

                # 这一轮的运行时间结束之后，更新下一轮的运行时间
                self._corders_dict = {}
                self._morders_dict = {}
                async with self._async_lock:
                    self._running_time = self._trade_api.get_near_trade_time()["next"]
                logger.info(
                    " TradeAPI: {}, 运行时间: {} ---- {}".format(
                        self._trade_api.__class__.__name__, self._running_time["open"], self._running_time["close"]
                    )
                )

            await asyncio.sleep(0.25)

    async def _coroutine_get_child_orders(self):
        """协程, 定时从交易接口获取委托信息，并放进待处理队列"""
        if isinstance(self._trade_api, (EMTradeAPIAdapter, EMCreditTradeAPIAdapter)):
            sleep_time = 120
        else:
            sleep_time = 0.25

        while True:
            await asyncio.sleep(sleep_time)

            if (
                self._running_time["open"] - pd.Timedelta(seconds=40)
                <= pd.Timestamp.now(tz="Asia/Shanghai")
                <= (self._running_time["close"] + pd.Timedelta(seconds=130))
            ):
                # 如果当前存在未完成的子单，才去查柜台
                for _, co in self._corders_dict.items():
                    if co["status"] not in [OrderStatus.FINISHED, OrderStatus.SCRAPPED, OrderStatus.CANCELLED]:
                        await self.put_order(*([] if (res := self.trade_api.get_orders())[0] != 0 else res[1]))
                        logger.debug("TradeAPI: {} 更新委托状态.".format(self._trade_api.__class__.__name__))
                        break

    async def _coroutine_get_portfolio(self):
        """协程, 定时从交易接口获取委托信息，并放进待处理队列"""
        if isinstance(self._trade_api, (EMTradeAPIAdapter, EMCreditTradeAPIAdapter)):
            sleep_time = 60
        else:
            sleep_time = 0.25

        while True:
            await asyncio.sleep(sleep_time)

            if (
                self._running_time["open"] - pd.Timedelta(seconds=40)
                <= pd.Timestamp.now(tz="Asia/Shanghai")
                <= (self._running_time["close"] + pd.Timedelta(seconds=130))
            ):
                portfolio = await self.get_portfolio()
                if portfolio is not None:
                    await self._sql_handler.upsert("portfolio", [_modify_portfolio(self._trade_api, portfolio)])

    async def get_portfolio(self):
        if isinstance(self._trade_api, GMTradeAPIAdapter):
            _, portfolio = await self._trade_api.portfolio
        else:
            _, portfolio = self._trade_api.portfolio
        return portfolio

    async def put_order(self, *orders):
        for order in orders:
            await self._waiting_corders.put(order)

    async def create_order(self, request):
        """创建委托, 异步下单"""
        order = Order(request, self)
        self._morders_dict[order.order_id] = order
        await order.async_execute()
        return 0, order.order_id

    async def cancel_order(self, request):
        await self._morders_dict[request.order_id].async_cancel()
        return 0

    async def get_orders(self, request):
        if isinstance(request, qnt_trader_pb2.GetOrdersRequest):
            # 如果是client主动查询，需要直接从数据库里获取，因为可能会涉及到当日之前或当日未启动服务时的委托
            res = await self._sql_handler.get_orders(
                pd.Timestamp(request.start_date, tz="Asia/Shanghai"),
                pd.Timestamp(request.end_date, tz="Asia/Shanghai").replace(hour=15),
                request.account,
                request.is_credit,
            )
        else:
            # 如果不是client主动查询，直接从内存里获取，这时获取主要是为了推送变动的委托状态
            res = [v.info for _, v in self._morders_dict.items()]
        tmp = [i.name for i in qnt_trader_pb2.Order.DESCRIPTOR.fields]
        res = [{k: v for k, v in i.items() if k in tmp} for i in res]
        return 0, res


class OMAggregator:
    def __init__(self, trade_apis):
        self._sql_handler = SQLHandlerOrmOfTrader(
            username=decrypt(gl_config["database"]["username"]),
            password=decrypt(gl_config["database"]["password"]),
            host=gl_config["database"]["host"],
            port=gl_config["database"]["port"],
        )
        self._order_managers = {k: OrderManager(v, self._sql_handler) for k, v in trade_apis.items()}

    async def create_order(self, request):
        return await self._order_managers[(request.account, request.is_credit)].create_order(request)

    async def cancel_order(self, request):
        return await self._order_managers[(request.account, request.is_credit)].cancel_order(request)

    async def get_orders(self, request):
        return await self._order_managers[(request.account, request.is_credit)].get_orders(request)

import asyncio
import inspect
import uuid
from typing import Dict, Optional

import pandas as pd
from loguru import logger

from qnt_utils.enums import Direction, Exchange, FinancialAsset, OffSet, OrderStatus, PositionType, PriceType
from qnt_utils.label import QSymbol, Symbol
from qnt_utils.toolset import get_financial_asset

from ..protos import qnt_trader_pb2


class Order:
    def __init__(self, request: Optional[qnt_trader_pb2.OrderRequest], order_manager, info: Optional[Dict] = None):
        self._om = order_manager
        if request is not None:
            self._order_id = str(uuid.uuid1())
            self._crt_req = request
            self._child_orders = []
            self._info = {
                "c_dt": (tmp := pd.Timestamp.now(tz="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S.%f")),
                "m_dt": tmp,
                "symbol": self._crt_req.symbol,
                "name": "",
                "direction": Direction(self._crt_req.direction),
                "offset": OffSet(self._crt_req.offset),
                "amount": int(self._crt_req.amount),
                "price": self._crt_req.price,
                "status": OrderStatus.ORDER_TBC,
                "price_type": PriceType.LIMIT,
                "trade_price": 0,
                "trade_amount": 0,
                "order_id": self.order_id,
                "account": self._crt_req.account,
                "is_credit": self._crt_req.is_credit,
                "group": self._crt_req.group,
                "api_order_id": "",
                "order_type": "M",  # M-母单, C-子单
            }
        else:
            if info is None:
                raise ValueError
            self._order_id = info["order_id"]
            self._crt_req = None
            self._child_orders = []
            self._info = info
        self._exe_task = None
        self._cnc_task = None

    @property
    def order_id(self):
        return self._order_id

    @property
    def child_orders(self):
        return self._child_orders

    @property
    def info(self):
        return self._info.copy()

    def add_child_order(self, child_order):
        self._child_orders.append(child_order)

    def update_info(self):
        """更新母单状态，"""
        if self._info["status"] not in [OrderStatus.ALIVE, OrderStatus.CANCEL_TBC]:
            return
        tmp = pd.DataFrame(self._child_orders)
        if self._exe_task is None or self._exe_task.done():
            if all(tmp["status"] == OrderStatus.FINISHED):
                status = OrderStatus.FINISHED
            elif all(tmp["status"] == OrderStatus.SCRAPPED):
                status = OrderStatus.SCRAPPED
            elif all(
                tmp["status"].apply(lambda x: x in [OrderStatus.FINISHED, OrderStatus.SCRAPPED, OrderStatus.CANCELLED])
            ):
                status = OrderStatus.CANCELLED
            else:
                status = self._info["status"]
        else:
            status = OrderStatus.ALIVE
        ata = tmp["trade_amount"].sum()
        if self._info["status"] != status or self._info["trade_amount"] != ata:
            self._info.update(
                m_dt=pd.Timestamp.now(tz="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S.%f"),
                name=tmp["name"].iloc[-1],
                status=status,
                trade_amount=ata,
                trade_price=(tmp.apply(lambda x: x["trade_price"] * x["trade_amount"], axis=1).sum() / ata)
                if ata > 0
                else 0,
            )

    async def async_execute(self):
        """创建协程，用于异步执行下单任务"""
        if self._exe_task is None:
            self._info["status"] = OrderStatus.ALIVE
            self._exe_task = asyncio.create_task(self._coroutine_execute())

    async def async_cancel(self):
        """创建协程，用于异步执行取消订单任务"""
        if not self._info["status"] == OrderStatus.ALIVE:
            return
        if self._exe_task is not None:
            self._exe_task.cancel()
            try:
                await self._exe_task
            except asyncio.CancelledError:
                pass
            self._info["status"] = OrderStatus.CANCEL_TBC
            self._cnc_task = asyncio.create_task(self._coroutine_cancel())

    async def _coroutine_execute(self):
        """协程，执行订单"""
        if self._crt_req is None:
            return

        symbol = self._crt_req.symbol
        direction = Direction(self._crt_req.direction)
        offset = OffSet(self._crt_req.offset)
        amount = self._crt_req.amount
        price = self._crt_req.price
        price_type = PriceType(self._crt_req.price_type)

        cos = []
        # 如果是上期所或者上期能源的期货平仓，需要考虑是否要平今
        if (
            get_financial_asset(symbol) == FinancialAsset.FUTURES
            and offset != OffSet.OPEN
            and Symbol.get_exchange(symbol) in [Exchange.SHFE, Exchange.INE]
        ):
            status_code, positions = self._om.trade_api.positions
            if status_code != 0:
                return
            if direction == Direction.BUY:
                underlying_position = positions[symbol][PositionType.SHORT]
            else:
                underlying_position = positions[symbol][PositionType.LONG]
            # TODO 根据平今折扣率确认先平今还是先平昨
            if 1:
                v1 = min(amount, underlying_position["amount_today_available"])
                cos.append(await self._place_order(symbol, direction, OffSet.CLOSETODAY, v1, price, price_type))
                if amount - v1 > 0:
                    cos.append(await self._place_order(symbol, direction, OffSet.CLOSE, amount - v1, price, price_type))
            else:
                v1 = min(amount, underlying_position["amount_his_available"])
                cos.append(await self._place_order(symbol, direction, OffSet.CLOSE, v1, price, price_type))
                if amount - v1 > 0:
                    cos.append(
                        await self._place_order(symbol, direction, OffSet.CLOSETODAY, amount - v1, price, price_type)
                    )
        else:
            cos.append(await self._place_order(symbol, direction, offset, amount, price, price_type))
        for co in cos:
            await self._om.put_order(co)
        logger.info(
            "{trade_api} 以{price} {trade_type} {symbol} {amount}股, 执行完毕".format(
                trade_api=self._om.trade_api.__class__.__name__,
                price=self._crt_req.price,
                symbol=self._crt_req.symbol,
                amount=self._crt_req.amount,
                trade_type={
                    Direction.BUY: "买入",
                    Direction.SELL: "卖出",
                    Direction.CREDIT_BUY: "融资买入",
                    Direction.CREDIT_SELL: "融券卖出",
                }.get(Direction(self._crt_req.direction)),
            )
        )

    async def _place_order(
        self,
        symbol: QSymbol | str,
        direction: Direction,
        offset: OffSet,
        amount: float,
        price: float,
        price_type: PriceType,
    ):
        """最小下单操作，期货平今和算法下单等场景下会执行多次下单"""
        kwargs = dict(
            symbol=symbol, direction=direction, offset=offset, amount=amount, price=price, price_type=price_type
        )
        if inspect.iscoroutinefunction(self._om.trade_api.order):
            status, res = await self._om.trade_api.order(**kwargs)
        else:
            status, res = self._om.trade_api.order(**kwargs)
        (co := self._info.copy()).update(
            c_dt=(tmp := pd.Timestamp.now(tz="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S.%f")),
            m_dt=tmp,
            direction=direction,
            offset=offset,
            amount=amount,
            price=price,
            status=OrderStatus.ORDER_TBC if status == 0 else OrderStatus.SCRAPPED,
            price_type=price_type,
            trade_price=0,
            trade_amount=0,
            api_order_id=res if status == 0 else "",
            order_type="C",
        )
        self.add_child_order(co)
        return co

    async def _coroutine_cancel(self):
        """协程，取消订单"""
        for i in self.child_orders:
            if i["status"] in [OrderStatus.ALIVE, OrderStatus.ORDER_TBC]:
                self._om.trade_api.cancel_order(i["api_order_id"])

                logger.info(
                    "{trade_api} 撤销 以{price} {trade_type} {symbol} {amount}股".format(
                        trade_api=self._om.trade_api.__class__.__name__,
                        price=i["price"],
                        symbol=i["symbol"],
                        amount=i["amount"],
                        trade_type={
                            Direction.BUY: "买入",
                            Direction.SELL: "卖出",
                            Direction.CREDIT_BUY: "融资买入",
                            Direction.CREDIT_SELL: "融券卖出",
                        }.get(i["direction"]),
                    )
                )

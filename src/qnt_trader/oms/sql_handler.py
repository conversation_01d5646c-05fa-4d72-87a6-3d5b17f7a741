import asyncio
import time
from typing import Dict, List, Optional, Union

import pandas as pd
from loguru import logger
from sqlalchemy import and_, select
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.dialects.postgresql import insert as pg_insert
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import DeclarativeBase, mapped_column
from sqlalchemy.sql.expression import null
from sqlalchemy.types import CHAR, BigInteger, Boolean, Date, DateTime, Numeric, String

from qnt_utils.enums import Direction, OffSet, OrderStatus, PriceType


class _Base(DeclarativeBase):
    @classmethod
    def columns(cls):
        return cls.__table__.c.keys()

    @classmethod
    def primary_keys(cls):
        return cls.__table__.primary_key.columns.keys()


class Orders(_Base):
    __tablename__ = "orders"
    ctime = mapped_column(BigInteger, default=time.time)
    mtime = mapped_column(BigInteger, default=time.time, onupdate=time.time)
    c_dt = mapped_column(DateTime(True))
    m_dt = mapped_column(DateTime(True))
    symbol = mapped_column(String(20))
    name = mapped_column(String(60))
    direction = mapped_column(String(15))
    offset = mapped_column(String(15))
    price = mapped_column(Numeric(20, 6))
    amount = mapped_column(Numeric(20, 6))
    status = mapped_column(String(15))
    price_type = mapped_column(String(10))
    trade_price = mapped_column(Numeric(20, 6))
    trade_amount = mapped_column(Numeric(20, 6))
    order_id = mapped_column(UUID, primary_key=True)
    api_order_id = mapped_column(String(50), primary_key=True)
    account = mapped_column(String(20))
    is_credit = mapped_column(Boolean)
    group = mapped_column(String(20))
    order_type = mapped_column(CHAR, primary_key=True)


class Portfolio(_Base):
    __tablename__ = "portfolio"
    ctime = mapped_column(BigInteger, default=time.time)
    mtime = mapped_column(BigInteger, default=time.time, onupdate=time.time)
    date = mapped_column(Date, primary_key=True)
    account = mapped_column(String(20), primary_key=True)
    is_credit = mapped_column(Boolean, primary_key=True)
    asset = mapped_column(Numeric(20, 6))
    debt = mapped_column(Numeric(20, 6))
    equity = mapped_column(Numeric(20, 6))
    available_cash = mapped_column(Numeric(20, 6))
    frozen_cash = mapped_column(Numeric(20, 6))
    market_value = mapped_column(Numeric(20, 6))
    margin = mapped_column(Numeric(20, 6))


class SQLHandlerOrm:
    def __init__(self, username=None, password=None, host="localhost", port=5432, database=None):
        url = URL.create(
            drivername="postgresql+asyncpg",
            username=username,
            password=password,
            host=host,
            port=port,
            database=database,
        )
        self._engine = create_async_engine(url)
        self._tabs = {}

    @property
    def tabs(self):
        return self._tabs

    async def upsert(self, table, ins_tmp: Union[List[Dict], pd.DataFrame], index_elements: Optional[List[str]] = None):
        table1 = self._tabs[table] if isinstance(table, str) else table
        if not isinstance(ins_tmp, pd.DataFrame):
            ins_tmp = pd.DataFrame(ins_tmp)
        if ins_tmp.empty:
            return
        ins_tmp = ins_tmp[list(set(table1.columns()).intersection(set(ins_tmp.columns)))].copy()
        ins_tmp.fillna(null(), inplace=True)
        ins_tmp = [v.to_dict() for _, v in ins_tmp.iterrows()]
        index_elements = table1.primary_keys() if index_elements is None else index_elements
        async with self._engine.begin() as conn:
            for i in range(0, len(ins_tmp), 100000):
                insert_stmt = pg_insert(table1).values(ins_tmp[i : min(len(ins_tmp), i + 100000)])
                upsert_stmt = insert_stmt.on_conflict_do_update(
                    index_elements=index_elements,
                    set_={i: j for i, j in insert_stmt.excluded.items() if i not in ["ctime"]},
                )
                await conn.execute(upsert_stmt)
                await conn.commit()


class SQLHandlerOrmOfTrader(SQLHandlerOrm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs, database="trader")
        logger.info("connect to trader database successfully")
        self._tabs["orders"] = Orders
        self._tabs["portfolio"] = Portfolio
        self._init_task = asyncio.create_task(self._metadata_create())

    async def _metadata_create(self):
        async with self._engine.begin() as conn:
            await conn.run_sync(_Base.metadata.create_all)

    async def upsert(
        self,
        table: Union[str, DeclarativeBase],
        ins_tmp: Union[List[Dict], pd.DataFrame],
        index_elements: Optional[List[str]] = None,
    ):
        await self._init_task
        if not isinstance(ins_tmp, pd.DataFrame):
            ins_tmp = pd.DataFrame(ins_tmp)
        if table == "orders":
            ins_tmp["c_dt"] = ins_tmp["c_dt"].apply(lambda x: pd.Timestamp(x, tz="Asia/Shanghai"))
            ins_tmp["m_dt"] = ins_tmp["m_dt"].apply(lambda x: pd.Timestamp(x, tz="Asia/Shanghai"))
            ins_tmp["direction"] = ins_tmp["direction"].apply(lambda x: x.name)
            ins_tmp["offset"] = ins_tmp["offset"].apply(lambda x: x.name)
            ins_tmp["status"] = ins_tmp["status"].apply(lambda x: x.name)
            ins_tmp["price_type"] = ins_tmp["price_type"].apply(lambda x: x.name)
        elif table == "portfolio":
            ins_tmp["date"] = ins_tmp["date"].apply(lambda x: pd.Timestamp(x, tz="Asia/Shanghai").date())
        await super().upsert(table, ins_tmp, index_elements)

    async def get_orders(
        self,
        start_date: Union[str, pd.Timestamp],
        end_date: Union[str, pd.Timestamp],
        account: str,
        is_credit: bool,
        contain_child_orders: bool = False,
    ) -> List:
        """查询数据库落地的委托信息

        Args:
            start_date (Union[str, pd.Timestamp]): 起始时间
            end_date (Union[str, pd.Timestamp]): 截止时间
            account (str): 账号
            is_credit (bool): 是否是信用账户
            contain_child_orders (bool, optional): 是否包含出了子单. Defaults to False.

        Returns:
            List: 委托信息
        """
        await self._init_task
        start_date = pd.Timestamp(start_date, tz="Asia/Shanghai") if isinstance(start_date, str) else start_date
        end_date = pd.Timestamp(end_date, tz="Asia/Shanghai") if isinstance(end_date, str) else end_date
        sql = select(Orders).where(
            and_(
                Orders.account == account,
                Orders.is_credit == is_credit,
                start_date <= Orders.c_dt,
                Orders.c_dt <= end_date,
                Orders.order_type.in_(["M", "C"]) if contain_child_orders else Orders.order_type.in_(["M"]),
            )
        )
        async with self._engine.begin() as conn:
            res = await conn.execute(sql)
        res = pd.DataFrame(res.mappings().all())
        if res.empty:
            return []
        res = res.drop(columns=["ctime", "mtime"])
        res["c_dt"] = res["c_dt"].apply(lambda x: x.astimezone(tz="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S.%f"))
        res["m_dt"] = res["m_dt"].apply(lambda x: x.astimezone(tz="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S.%f"))
        res["direction"] = res["direction"].apply(lambda x: Direction[x])
        res["offset"] = res["offset"].apply(lambda x: OffSet[x])
        res["status"] = res["status"].apply(lambda x: OrderStatus[x])
        res["price_type"] = res["price_type"].apply(lambda x: PriceType[x])
        res = res.astype(
            {"amount": float, "price": float, "trade_amount": float, "trade_price": float, "order_id": str}
        )
        return [i.to_dict() for _, i in res.iterrows()]

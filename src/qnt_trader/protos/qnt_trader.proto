syntax = "proto3";
package qnt_trader;

// The greeting service definition.
service Trader {

    rpc portfolio (PortfolioRequest) returns (PortfolioReply) {}

    rpc positions (PositionsRequest) returns (PositionsReply) {}

    rpc order (OrderRequest) returns (OrderReply) {}

    rpc cancel_order (CancelOrderRequest) returns (CancelOrderReply) {}

    rpc get_orders (GetOrdersRequest) returns (GetOrdersReply) {}

    rpc order_push (OrderPushRequest) returns (stream OrderPushReply) {}
}

message OrderPushRequest{
    uint64 timestamp = 1;
    string source = 2;
    string account = 3;
    bool is_credit = 4;
}
message OrderPushReply{
    repeated Order orders = 1;
}


message PortfolioRequest {
    uint64 timestamp = 1;
    string source = 2;
    string account = 3;
    bool is_credit = 4;
}
message Portfolio{
    double asset = 1;                               //资产
    double debt = 2;                                //负债
    double equity = 3;                              //权益
    double available_cash = 4;                      //可用现金
    double frozen_cash = 5;                         //冻结资金
    double market_value = 6;                        //持仓市值
    double margin = 7;                              //期货保证金占用
    double available_credit = 8;                    //可用保证金，用于信用账户
    double interest_of_financed_funds = 9;          //融资利率，用于信用账户
    double interest_of_financed_bonds = 10;         //融券利率，用于信用账户
}
message PortfolioReply {
    int32 status = 1;
    Portfolio portfolio = 2;
}



message PositionsRequest {
    uint64 timestamp = 1;
    string source = 2;
    string account = 3;
    bool is_credit = 4;
}
message Position{
    string symbol = 1;                              //证券代码
    double amount = 2;                              //持仓数量
    double available = 3;                           //可用
    double frozen = 4;                              //冻结
    double amount_today = 5;                        //今仓
    double amount_his = 6;                          //昨仓
    double cost_basis = 7;                          //成本价
    double last_price = 8;                          //最新价
    double margin = 9;                              //保证金占用
    double market_value = 10;                       //市值
    double profit = 11;                             //浮动盈亏
    double profit_rate = 12;                        //利润率
    string name = 13;                               //证券名称
    PositionType position_type = 14;                //多仓or空仓
    double amount_today_available = 15;             //今仓可用
    double amount_his_available = 16;               //昨仓可用

}
message PositionsReply {
    int32 status = 1;
    repeated Position positions = 2;
}



message OrderRequest {
    uint64 timestamp = 1;
    string source = 2;
    string account = 3;                             //账号
    string symbol = 4;                              //证券代码
    double price = 5;                               //价格
    double amount = 6;                              //数量
    Direction direction = 7;                        //买卖方向
    OffSet offset = 8;                              //开平标志
    PriceType price_type = 9;                       //价格类型
    bool is_credit = 10;                            //是否信用账户
    string group = 11;
}
message OrderReply {
    int32 status = 1;
    string order_id = 2;
}



message CancelOrderRequest {
    uint64 timestamp = 1;
    string source = 2;
    string account = 3;
    bool is_credit = 4;
    string order_id = 5;                            //trader分配的单号
}
message CancelOrderReply {
    int32 status = 1;
}



message GetOrdersRequest{
    uint64 timestamp = 1;
    string source = 2;
    string account = 3;
    bool is_credit = 4;
    string start_date = 5;
    string end_date = 6;
}
message Order{
    string c_dt = 1;                               //下单时间
    string symbol = 2;                              //证券代码
    string name = 3;                                //证券名称
    Direction direction = 4;                        //买卖方向
    OffSet offset = 5;                              //开平标志
    double amount = 6;                              //下单数量
    double price = 7;                               //下单价格
    OrderStatus status = 8;                         //委托状态
    PriceType price_type = 9;                       //价格类型
    double trade_price = 10;                        //成交价格
    double trade_amount = 11;                       //成交数量
    string order_id = 12;                           //trader分配的order_id
    string m_dt = 13;                              //更新时间
    string account = 14;                            //账户
    bool is_credit = 15; 
}
message GetOrdersReply{
    int32 status = 1;
    repeated Order orders = 2;
}


enum PositionType{
    LONG = 0;
    SHORT = 1;
}

enum PriceType{
    MARKET = 0;
    LIMIT = 1;
}

enum Direction{
    BUY = 0;
    SELL = 1;
    CREDIT_BUY = 2;
    CREDIT_SELL = 3;
}

enum OffSet{
    OPEN = 0;
    CLOSE = 1;
    CLOSETODAY = 2;
}

// 母单
// 创建时ORDER_TBC
// 开始执行ORDER_TBC->ALIVE
// 子单
// 下单成功ORDER_TBC, 失败SCRAPPED
enum OrderStatus{
    ALIVE = 0;  // 订单存活
    FINISHED = 1;  // 订单完成
    SCRAPPED = 2;  // 废单
    CANCELLED = 3;  // 订单关闭，那被取消
    ORDER_TBC = 4;  // 下单待确认
    CANCEL_TBC = 5; // 撤单待确认
}
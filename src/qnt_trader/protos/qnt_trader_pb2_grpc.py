# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import qnt_trader_pb2 as qnt__trader__pb2


class TraderStub(object):
    """The greeting service definition."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.portfolio = channel.unary_unary(
            "/qnt_trader.Trader/portfolio",
            request_serializer=qnt__trader__pb2.PortfolioRequest.SerializeToString,
            response_deserializer=qnt__trader__pb2.PortfolioReply.FromString,
        )
        self.positions = channel.unary_unary(
            "/qnt_trader.Trader/positions",
            request_serializer=qnt__trader__pb2.PositionsRequest.SerializeToString,
            response_deserializer=qnt__trader__pb2.PositionsReply.FromString,
        )
        self.order = channel.unary_unary(
            "/qnt_trader.Trader/order",
            request_serializer=qnt__trader__pb2.OrderRequest.SerializeToString,
            response_deserializer=qnt__trader__pb2.OrderReply.FromString,
        )
        self.cancel_order = channel.unary_unary(
            "/qnt_trader.Trader/cancel_order",
            request_serializer=qnt__trader__pb2.CancelOrderRequest.SerializeToString,
            response_deserializer=qnt__trader__pb2.CancelOrderReply.FromString,
        )
        self.get_orders = channel.unary_unary(
            "/qnt_trader.Trader/get_orders",
            request_serializer=qnt__trader__pb2.GetOrdersRequest.SerializeToString,
            response_deserializer=qnt__trader__pb2.GetOrdersReply.FromString,
        )
        self.order_push = channel.unary_stream(
            "/qnt_trader.Trader/order_push",
            request_serializer=qnt__trader__pb2.OrderPushRequest.SerializeToString,
            response_deserializer=qnt__trader__pb2.OrderPushReply.FromString,
        )


class TraderServicer(object):
    """The greeting service definition."""

    def portfolio(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def positions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def order(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def cancel_order(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def get_orders(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def order_push(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_TraderServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "portfolio": grpc.unary_unary_rpc_method_handler(
            servicer.portfolio,
            request_deserializer=qnt__trader__pb2.PortfolioRequest.FromString,
            response_serializer=qnt__trader__pb2.PortfolioReply.SerializeToString,
        ),
        "positions": grpc.unary_unary_rpc_method_handler(
            servicer.positions,
            request_deserializer=qnt__trader__pb2.PositionsRequest.FromString,
            response_serializer=qnt__trader__pb2.PositionsReply.SerializeToString,
        ),
        "order": grpc.unary_unary_rpc_method_handler(
            servicer.order,
            request_deserializer=qnt__trader__pb2.OrderRequest.FromString,
            response_serializer=qnt__trader__pb2.OrderReply.SerializeToString,
        ),
        "cancel_order": grpc.unary_unary_rpc_method_handler(
            servicer.cancel_order,
            request_deserializer=qnt__trader__pb2.CancelOrderRequest.FromString,
            response_serializer=qnt__trader__pb2.CancelOrderReply.SerializeToString,
        ),
        "get_orders": grpc.unary_unary_rpc_method_handler(
            servicer.get_orders,
            request_deserializer=qnt__trader__pb2.GetOrdersRequest.FromString,
            response_serializer=qnt__trader__pb2.GetOrdersReply.SerializeToString,
        ),
        "order_push": grpc.unary_stream_rpc_method_handler(
            servicer.order_push,
            request_deserializer=qnt__trader__pb2.OrderPushRequest.FromString,
            response_serializer=qnt__trader__pb2.OrderPushReply.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler("qnt_trader.Trader", rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class Trader(object):
    """The greeting service definition."""

    @staticmethod
    def portfolio(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/qnt_trader.Trader/portfolio",
            qnt__trader__pb2.PortfolioRequest.SerializeToString,
            qnt__trader__pb2.PortfolioReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def positions(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/qnt_trader.Trader/positions",
            qnt__trader__pb2.PositionsRequest.SerializeToString,
            qnt__trader__pb2.PositionsReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def order(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/qnt_trader.Trader/order",
            qnt__trader__pb2.OrderRequest.SerializeToString,
            qnt__trader__pb2.OrderReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def cancel_order(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/qnt_trader.Trader/cancel_order",
            qnt__trader__pb2.CancelOrderRequest.SerializeToString,
            qnt__trader__pb2.CancelOrderReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def get_orders(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/qnt_trader.Trader/get_orders",
            qnt__trader__pb2.GetOrdersRequest.SerializeToString,
            qnt__trader__pb2.GetOrdersReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def order_push(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/qnt_trader.Trader/order_push",
            qnt__trader__pb2.OrderPushRequest.SerializeToString,
            qnt__trader__pb2.OrderPushReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

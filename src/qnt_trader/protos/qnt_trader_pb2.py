# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: qnt_trader.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10qnt_trader.proto\x12\nqnt_trader\"Y\n\x10OrderPushRequest\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\x12\x11\n\tis_credit\x18\x04 \x01(\x08\"3\n\x0eOrderPushReply\x12!\n\x06orders\x18\x01 \x03(\x0b\x32\x11.qnt_trader.Order\"Y\n\x10PortfolioRequest\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\x12\x11\n\tis_credit\x18\x04 \x01(\x08\"\xed\x01\n\tPortfolio\x12\r\n\x05\x61sset\x18\x01 \x01(\x01\x12\x0c\n\x04\x64\x65\x62t\x18\x02 \x01(\x01\x12\x0e\n\x06\x65quity\x18\x03 \x01(\x01\x12\x16\n\x0e\x61vailable_cash\x18\x04 \x01(\x01\x12\x13\n\x0b\x66rozen_cash\x18\x05 \x01(\x01\x12\x14\n\x0cmarket_value\x18\x06 \x01(\x01\x12\x0e\n\x06margin\x18\x07 \x01(\x01\x12\x18\n\x10\x61vailable_credit\x18\x08 \x01(\x01\x12\"\n\x1ainterest_of_financed_funds\x18\t \x01(\x01\x12\"\n\x1ainterest_of_financed_bonds\x18\n \x01(\x01\"J\n\x0ePortfolioReply\x12\x0e\n\x06status\x18\x01 \x01(\x05\x12(\n\tportfolio\x18\x02 \x01(\x0b\x32\x15.qnt_trader.Portfolio\"Y\n\x10PositionsRequest\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\x12\x11\n\tis_credit\x18\x04 \x01(\x08\"\xe7\x02\n\x08Position\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\x0e\n\x06\x61mount\x18\x02 \x01(\x01\x12\x11\n\tavailable\x18\x03 \x01(\x01\x12\x0e\n\x06\x66rozen\x18\x04 \x01(\x01\x12\x14\n\x0c\x61mount_today\x18\x05 \x01(\x01\x12\x12\n\namount_his\x18\x06 \x01(\x01\x12\x12\n\ncost_basis\x18\x07 \x01(\x01\x12\x12\n\nlast_price\x18\x08 \x01(\x01\x12\x0e\n\x06margin\x18\t \x01(\x01\x12\x14\n\x0cmarket_value\x18\n \x01(\x01\x12\x0e\n\x06profit\x18\x0b \x01(\x01\x12\x13\n\x0bprofit_rate\x18\x0c \x01(\x01\x12\x0c\n\x04name\x18\r \x01(\t\x12/\n\rposition_type\x18\x0e \x01(\x0e\x32\x18.qnt_trader.PositionType\x12\x1e\n\x16\x61mount_today_available\x18\x0f \x01(\x01\x12\x1c\n\x14\x61mount_his_available\x18\x10 \x01(\x01\"I\n\x0ePositionsReply\x12\x0e\n\x06status\x18\x01 \x01(\x05\x12\'\n\tpositions\x18\x02 \x03(\x0b\x32\x14.qnt_trader.Position\"\x8c\x02\n\x0cOrderRequest\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\x12\x0e\n\x06symbol\x18\x04 \x01(\t\x12\r\n\x05price\x18\x05 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12(\n\tdirection\x18\x07 \x01(\x0e\x32\x15.qnt_trader.Direction\x12\"\n\x06offset\x18\x08 \x01(\x0e\x32\x12.qnt_trader.OffSet\x12)\n\nprice_type\x18\t \x01(\x0e\x32\x15.qnt_trader.PriceType\x12\x11\n\tis_credit\x18\n \x01(\x08\x12\r\n\x05group\x18\x0b \x01(\t\".\n\nOrderReply\x12\x0e\n\x06status\x18\x01 \x01(\x05\x12\x10\n\x08order_id\x18\x02 \x01(\t\"m\n\x12\x43\x61ncelOrderRequest\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\x12\x11\n\tis_credit\x18\x04 \x01(\x08\x12\x10\n\x08order_id\x18\x05 \x01(\t\"\"\n\x10\x43\x61ncelOrderReply\x12\x0e\n\x06status\x18\x01 \x01(\x05\"\x7f\n\x10GetOrdersRequest\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\x12\x11\n\tis_credit\x18\x04 \x01(\x08\x12\x12\n\nstart_date\x18\x05 \x01(\t\x12\x10\n\x08\x65nd_date\x18\x06 \x01(\t\"\xe3\x02\n\x05Order\x12\x0c\n\x04\x63_dt\x18\x01 \x01(\t\x12\x0e\n\x06symbol\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12(\n\tdirection\x18\x04 \x01(\x0e\x32\x15.qnt_trader.Direction\x12\"\n\x06offset\x18\x05 \x01(\x0e\x32\x12.qnt_trader.OffSet\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12\r\n\x05price\x18\x07 \x01(\x01\x12\'\n\x06status\x18\x08 \x01(\x0e\x32\x17.qnt_trader.OrderStatus\x12)\n\nprice_type\x18\t \x01(\x0e\x32\x15.qnt_trader.PriceType\x12\x13\n\x0btrade_price\x18\n \x01(\x01\x12\x14\n\x0ctrade_amount\x18\x0b \x01(\x01\x12\x10\n\x08order_id\x18\x0c \x01(\t\x12\x0c\n\x04m_dt\x18\r \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x0e \x01(\t\x12\x11\n\tis_credit\x18\x0f \x01(\x08\"C\n\x0eGetOrdersReply\x12\x0e\n\x06status\x18\x01 \x01(\x05\x12!\n\x06orders\x18\x02 \x03(\x0b\x32\x11.qnt_trader.Order*#\n\x0cPositionType\x12\x08\n\x04LONG\x10\x00\x12\t\n\x05SHORT\x10\x01*\"\n\tPriceType\x12\n\n\x06MARKET\x10\x00\x12\t\n\x05LIMIT\x10\x01*?\n\tDirection\x12\x07\n\x03\x42UY\x10\x00\x12\x08\n\x04SELL\x10\x01\x12\x0e\n\nCREDIT_BUY\x10\x02\x12\x0f\n\x0b\x43REDIT_SELL\x10\x03*-\n\x06OffSet\x12\x08\n\x04OPEN\x10\x00\x12\t\n\x05\x43LOSE\x10\x01\x12\x0e\n\nCLOSETODAY\x10\x02*b\n\x0bOrderStatus\x12\t\n\x05\x41LIVE\x10\x00\x12\x0c\n\x08\x46INISHED\x10\x01\x12\x0c\n\x08SCRAPPED\x10\x02\x12\r\n\tCANCELLED\x10\x03\x12\r\n\tORDER_TBC\x10\x04\x12\x0e\n\nCANCEL_TBC\x10\x05\x32\xbd\x03\n\x06Trader\x12G\n\tportfolio\x12\x1c.qnt_trader.PortfolioRequest\x1a\x1a.qnt_trader.PortfolioReply\"\x00\x12G\n\tpositions\x12\x1c.qnt_trader.PositionsRequest\x1a\x1a.qnt_trader.PositionsReply\"\x00\x12;\n\x05order\x12\x18.qnt_trader.OrderRequest\x1a\x16.qnt_trader.OrderReply\"\x00\x12N\n\x0c\x63\x61ncel_order\x12\x1e.qnt_trader.CancelOrderRequest\x1a\x1c.qnt_trader.CancelOrderReply\"\x00\x12H\n\nget_orders\x12\x1c.qnt_trader.GetOrdersRequest\x1a\x1a.qnt_trader.GetOrdersReply\"\x00\x12J\n\norder_push\x12\x1c.qnt_trader.OrderPushRequest\x1a\x1a.qnt_trader.OrderPushReply\"\x00\x30\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'qnt_trader_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _POSITIONTYPE._serialized_start=2133
  _POSITIONTYPE._serialized_end=2168
  _PRICETYPE._serialized_start=2170
  _PRICETYPE._serialized_end=2204
  _DIRECTION._serialized_start=2206
  _DIRECTION._serialized_end=2269
  _OFFSET._serialized_start=2271
  _OFFSET._serialized_end=2316
  _ORDERSTATUS._serialized_start=2318
  _ORDERSTATUS._serialized_end=2416
  _ORDERPUSHREQUEST._serialized_start=32
  _ORDERPUSHREQUEST._serialized_end=121
  _ORDERPUSHREPLY._serialized_start=123
  _ORDERPUSHREPLY._serialized_end=174
  _PORTFOLIOREQUEST._serialized_start=176
  _PORTFOLIOREQUEST._serialized_end=265
  _PORTFOLIO._serialized_start=268
  _PORTFOLIO._serialized_end=505
  _PORTFOLIOREPLY._serialized_start=507
  _PORTFOLIOREPLY._serialized_end=581
  _POSITIONSREQUEST._serialized_start=583
  _POSITIONSREQUEST._serialized_end=672
  _POSITION._serialized_start=675
  _POSITION._serialized_end=1034
  _POSITIONSREPLY._serialized_start=1036
  _POSITIONSREPLY._serialized_end=1109
  _ORDERREQUEST._serialized_start=1112
  _ORDERREQUEST._serialized_end=1380
  _ORDERREPLY._serialized_start=1382
  _ORDERREPLY._serialized_end=1428
  _CANCELORDERREQUEST._serialized_start=1430
  _CANCELORDERREQUEST._serialized_end=1539
  _CANCELORDERREPLY._serialized_start=1541
  _CANCELORDERREPLY._serialized_end=1575
  _GETORDERSREQUEST._serialized_start=1577
  _GETORDERSREQUEST._serialized_end=1704
  _ORDER._serialized_start=1707
  _ORDER._serialized_end=2062
  _GETORDERSREPLY._serialized_start=2064
  _GETORDERSREPLY._serialized_end=2131
  _TRADER._serialized_start=2419
  _TRADER._serialized_end=2864
# @@protoc_insertion_point(module_scope)

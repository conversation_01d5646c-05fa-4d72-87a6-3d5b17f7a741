from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

ALIVE: OrderStatus
BUY: Direction
CANCELLED: OrderStatus
CANCEL_TBC: OrderStatus
CLOSE: OffSet
CLOSETODAY: OffSet
CREDIT_BUY: Direction
CREDIT_SELL: Direction
DESCRIPTOR: _descriptor.FileDescriptor
FINISHED: OrderStatus
LIMIT: PriceType
LONG: PositionType
MARKET: PriceType
OPEN: OffSet
ORDER_TBC: OrderStatus
SCRAPPED: OrderStatus
SELL: Direction
SHORT: PositionType

class CancelOrderReply(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: int
    def __init__(self, status: _Optional[int] = ...) -> None: ...

class CancelOrderRequest(_message.Message):
    __slots__ = ["account", "is_credit", "order_id", "source", "timestamp"]
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    IS_CREDIT_FIELD_NUMBER: _ClassVar[int]
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    account: str
    is_credit: bool
    order_id: str
    source: str
    timestamp: int
    def __init__(self, timestamp: _Optional[int] = ..., source: _Optional[str] = ..., account: _Optional[str] = ..., is_credit: bool = ..., order_id: _Optional[str] = ...) -> None: ...

class GetOrdersReply(_message.Message):
    __slots__ = ["orders", "status"]
    ORDERS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    orders: _containers.RepeatedCompositeFieldContainer[Order]
    status: int
    def __init__(self, status: _Optional[int] = ..., orders: _Optional[_Iterable[_Union[Order, _Mapping]]] = ...) -> None: ...

class GetOrdersRequest(_message.Message):
    __slots__ = ["account", "end_date", "is_credit", "source", "start_date", "timestamp"]
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    IS_CREDIT_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    account: str
    end_date: str
    is_credit: bool
    source: str
    start_date: str
    timestamp: int
    def __init__(self, timestamp: _Optional[int] = ..., source: _Optional[str] = ..., account: _Optional[str] = ..., is_credit: bool = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ...) -> None: ...

class Order(_message.Message):
    __slots__ = ["account", "amount", "c_dt", "direction", "is_credit", "m_dt", "name", "offset", "order_id", "price", "price_type", "status", "symbol", "trade_amount", "trade_price"]
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    C_DT_FIELD_NUMBER: _ClassVar[int]
    DIRECTION_FIELD_NUMBER: _ClassVar[int]
    IS_CREDIT_FIELD_NUMBER: _ClassVar[int]
    M_DT_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    OFFSET_FIELD_NUMBER: _ClassVar[int]
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    PRICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    TRADE_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    TRADE_PRICE_FIELD_NUMBER: _ClassVar[int]
    account: str
    amount: float
    c_dt: str
    direction: Direction
    is_credit: bool
    m_dt: str
    name: str
    offset: OffSet
    order_id: str
    price: float
    price_type: PriceType
    status: OrderStatus
    symbol: str
    trade_amount: float
    trade_price: float
    def __init__(self, c_dt: _Optional[str] = ..., symbol: _Optional[str] = ..., name: _Optional[str] = ..., direction: _Optional[_Union[Direction, str]] = ..., offset: _Optional[_Union[OffSet, str]] = ..., amount: _Optional[float] = ..., price: _Optional[float] = ..., status: _Optional[_Union[OrderStatus, str]] = ..., price_type: _Optional[_Union[PriceType, str]] = ..., trade_price: _Optional[float] = ..., trade_amount: _Optional[float] = ..., order_id: _Optional[str] = ..., m_dt: _Optional[str] = ..., account: _Optional[str] = ..., is_credit: bool = ...) -> None: ...

class OrderPushReply(_message.Message):
    __slots__ = ["orders"]
    ORDERS_FIELD_NUMBER: _ClassVar[int]
    orders: _containers.RepeatedCompositeFieldContainer[Order]
    def __init__(self, orders: _Optional[_Iterable[_Union[Order, _Mapping]]] = ...) -> None: ...

class OrderPushRequest(_message.Message):
    __slots__ = ["account", "is_credit", "source", "timestamp"]
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    IS_CREDIT_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    account: str
    is_credit: bool
    source: str
    timestamp: int
    def __init__(self, timestamp: _Optional[int] = ..., source: _Optional[str] = ..., account: _Optional[str] = ..., is_credit: bool = ...) -> None: ...

class OrderReply(_message.Message):
    __slots__ = ["order_id", "status"]
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    order_id: str
    status: int
    def __init__(self, status: _Optional[int] = ..., order_id: _Optional[str] = ...) -> None: ...

class OrderRequest(_message.Message):
    __slots__ = ["account", "amount", "direction", "group", "is_credit", "offset", "price", "price_type", "source", "symbol", "timestamp"]
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    DIRECTION_FIELD_NUMBER: _ClassVar[int]
    GROUP_FIELD_NUMBER: _ClassVar[int]
    IS_CREDIT_FIELD_NUMBER: _ClassVar[int]
    OFFSET_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    PRICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    account: str
    amount: float
    direction: Direction
    group: str
    is_credit: bool
    offset: OffSet
    price: float
    price_type: PriceType
    source: str
    symbol: str
    timestamp: int
    def __init__(self, timestamp: _Optional[int] = ..., source: _Optional[str] = ..., account: _Optional[str] = ..., symbol: _Optional[str] = ..., price: _Optional[float] = ..., amount: _Optional[float] = ..., direction: _Optional[_Union[Direction, str]] = ..., offset: _Optional[_Union[OffSet, str]] = ..., price_type: _Optional[_Union[PriceType, str]] = ..., is_credit: bool = ..., group: _Optional[str] = ...) -> None: ...

class Portfolio(_message.Message):
    __slots__ = ["asset", "available_cash", "available_credit", "debt", "equity", "frozen_cash", "interest_of_financed_bonds", "interest_of_financed_funds", "margin", "market_value"]
    ASSET_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_CASH_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_CREDIT_FIELD_NUMBER: _ClassVar[int]
    DEBT_FIELD_NUMBER: _ClassVar[int]
    EQUITY_FIELD_NUMBER: _ClassVar[int]
    FROZEN_CASH_FIELD_NUMBER: _ClassVar[int]
    INTEREST_OF_FINANCED_BONDS_FIELD_NUMBER: _ClassVar[int]
    INTEREST_OF_FINANCED_FUNDS_FIELD_NUMBER: _ClassVar[int]
    MARGIN_FIELD_NUMBER: _ClassVar[int]
    MARKET_VALUE_FIELD_NUMBER: _ClassVar[int]
    asset: float
    available_cash: float
    available_credit: float
    debt: float
    equity: float
    frozen_cash: float
    interest_of_financed_bonds: float
    interest_of_financed_funds: float
    margin: float
    market_value: float
    def __init__(self, asset: _Optional[float] = ..., debt: _Optional[float] = ..., equity: _Optional[float] = ..., available_cash: _Optional[float] = ..., frozen_cash: _Optional[float] = ..., market_value: _Optional[float] = ..., margin: _Optional[float] = ..., available_credit: _Optional[float] = ..., interest_of_financed_funds: _Optional[float] = ..., interest_of_financed_bonds: _Optional[float] = ...) -> None: ...

class PortfolioReply(_message.Message):
    __slots__ = ["portfolio", "status"]
    PORTFOLIO_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    portfolio: Portfolio
    status: int
    def __init__(self, status: _Optional[int] = ..., portfolio: _Optional[_Union[Portfolio, _Mapping]] = ...) -> None: ...

class PortfolioRequest(_message.Message):
    __slots__ = ["account", "is_credit", "source", "timestamp"]
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    IS_CREDIT_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    account: str
    is_credit: bool
    source: str
    timestamp: int
    def __init__(self, timestamp: _Optional[int] = ..., source: _Optional[str] = ..., account: _Optional[str] = ..., is_credit: bool = ...) -> None: ...

class Position(_message.Message):
    __slots__ = ["amount", "amount_his", "amount_his_available", "amount_today", "amount_today_available", "available", "cost_basis", "frozen", "last_price", "margin", "market_value", "name", "position_type", "profit", "profit_rate", "symbol"]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_HIS_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_HIS_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_TODAY_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_TODAY_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    COST_BASIS_FIELD_NUMBER: _ClassVar[int]
    FROZEN_FIELD_NUMBER: _ClassVar[int]
    LAST_PRICE_FIELD_NUMBER: _ClassVar[int]
    MARGIN_FIELD_NUMBER: _ClassVar[int]
    MARKET_VALUE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    POSITION_TYPE_FIELD_NUMBER: _ClassVar[int]
    PROFIT_FIELD_NUMBER: _ClassVar[int]
    PROFIT_RATE_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_FIELD_NUMBER: _ClassVar[int]
    amount: float
    amount_his: float
    amount_his_available: float
    amount_today: float
    amount_today_available: float
    available: float
    cost_basis: float
    frozen: float
    last_price: float
    margin: float
    market_value: float
    name: str
    position_type: PositionType
    profit: float
    profit_rate: float
    symbol: str
    def __init__(self, symbol: _Optional[str] = ..., amount: _Optional[float] = ..., available: _Optional[float] = ..., frozen: _Optional[float] = ..., amount_today: _Optional[float] = ..., amount_his: _Optional[float] = ..., cost_basis: _Optional[float] = ..., last_price: _Optional[float] = ..., margin: _Optional[float] = ..., market_value: _Optional[float] = ..., profit: _Optional[float] = ..., profit_rate: _Optional[float] = ..., name: _Optional[str] = ..., position_type: _Optional[_Union[PositionType, str]] = ..., amount_today_available: _Optional[float] = ..., amount_his_available: _Optional[float] = ...) -> None: ...

class PositionsReply(_message.Message):
    __slots__ = ["positions", "status"]
    POSITIONS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    positions: _containers.RepeatedCompositeFieldContainer[Position]
    status: int
    def __init__(self, status: _Optional[int] = ..., positions: _Optional[_Iterable[_Union[Position, _Mapping]]] = ...) -> None: ...

class PositionsRequest(_message.Message):
    __slots__ = ["account", "is_credit", "source", "timestamp"]
    ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    IS_CREDIT_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    account: str
    is_credit: bool
    source: str
    timestamp: int
    def __init__(self, timestamp: _Optional[int] = ..., source: _Optional[str] = ..., account: _Optional[str] = ..., is_credit: bool = ...) -> None: ...

class PositionType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []

class PriceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []

class Direction(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []

class OffSet(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []

class OrderStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = []

import os
import pathlib

import tomli

gl_config = {}
if (_cnf_path := pathlib.Path(os.getenv("QNT_CONFIG_PATH", "./config"), "config.toml")).exists():
    with open(_cnf_path, "rb") as f:
        _cnf = tomli.load(f)
        if "database" in _cnf:
            gl_config["database"] = _cnf["database"]
        else:
            raise RuntimeError("Missing required config items: database")
        if "trader" in _cnf:
            gl_config["trader"] = _cnf["trader"]
            if "settings" not in _cnf["trader"]:
                raise RuntimeError("Missing required config items: trader.settings")
        else:
            raise RuntimeError("Missing required config items: trader")
else:
    raise FileNotFoundError("config.toml not found")


gl_trade_params = {}


async def get_close_today_discount():
    # TODO: 爬虫获取最新的平今手续费折扣
    return {}


async def update_close_today_discount():
    """协程, 更新平今手续费折扣"""
    while True:
        if gl_trade_params.get("close_today_discount", None) is None:
            gl_trade_params["close_today_discount"] = await get_close_today_discount()

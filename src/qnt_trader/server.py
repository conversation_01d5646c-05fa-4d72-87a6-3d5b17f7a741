import asyncio
from enum import Enum
from typing import Any

import grpc
import pandas as pd

from qnt_utils.toolset import decrypt

from ._globals import gl_config
from .adapters.base import TradeAPIAdapter
from .adapters.em_adapter import EMCreditTradeAPIAdapter, EMTradeAPIAdapter
from .adapters.gm_adapter import GMTradeAPIAdapter
from .adapters.tq_adapter import TQTradeAPIAdapter
from .oms.order_manager import OMAggregator
from .protos import qnt_trader_pb2, qnt_trader_pb2_grpc

from loguru import logger


def _convert_enum(value) -> dict[str, Any]:
    return {k: (v.value if isinstance(v, Enum) else v) for k, v in value.items()}


class Trader(qnt_trader_pb2_grpc.TraderServicer):
    def __init__(self):
        self._trade_apis: dict[tuple, TradeAPIAdapter] = {}
        if "em_trade_api" in gl_config["trader"] and gl_config["trader"]["em_trade_api"]["available"]:
            self._trade_apis[(account, False)] = EMTradeAPIAdapter(
                (account := gl_config["trader"]["em_trade_api"]["account"]),
                decrypt(gl_config["trader"]["em_trade_api"]["password"]),
            )
        if "em_credit_trade_api" in gl_config["trader"] and gl_config["trader"]["em_credit_trade_api"]["available"]:
            self._trade_apis[(account, True)] = EMCreditTradeAPIAdapter(
                (account := gl_config["trader"]["em_credit_trade_api"]["account"]),
                decrypt(gl_config["trader"]["em_credit_trade_api"]["password"]),
            )
        if "tq_trade_api" in gl_config["trader"] and gl_config["trader"]["tq_trade_api"]["available"]:
            self._trade_apis[(account, False)] = TQTradeAPIAdapter(
                gl_config["trader"]["tq_trade_api"]["tq_auth"]["account"],
                decrypt(gl_config["trader"]["tq_trade_api"]["tq_auth"]["password"]),
                gl_config["trader"]["tq_trade_api"]["broker"]["name"],
                (account := gl_config["trader"]["tq_trade_api"]["broker"]["account"]),
                decrypt(gl_config["trader"]["tq_trade_api"]["broker"]["password"]),
            )
        if "gm_trade_api" in gl_config["trader"] and gl_config["trader"]["gm_trade_api"]["available"]:
            self._trade_apis[("gm", False)] = GMTradeAPIAdapter()
        self._oma = OMAggregator(self._trade_apis)

    async def order_push(self, request, context):
        """成交推送是把当前快照和上一快照有差异的委托记录推送出去"""

        def compare(df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
            if df1.empty:
                return df1
            in_index = list(df1.index.intersection(df2.index))
            new = df1.drop(in_index)
            old = df1.loc[in_index, :]
            return df1.loc[new.index.union(old.compare(df2.loc[in_index, :]).index), :]

        async def get_snapshot():
            _, orders = await self._oma.get_orders(request)
            return pd.DataFrame(
                orders,
                columns=[i.name for i in qnt_trader_pb2.Order.DESCRIPTOR.fields],
            ).set_index(["order_id"], drop=False)

        origin = await get_snapshot()
        while True:
            await asyncio.sleep(0.2)
            new = await get_snapshot()
            res = compare(new, origin)
            if not res.empty:
                yield qnt_trader_pb2.OrderPushReply(
                    orders=[qnt_trader_pb2.Order(**_convert_enum(i)) for _, i in res.iterrows()]
                )
            origin = new

    async def portfolio(self, request, context):
        """查询账户资产现金等，直接通过TradeAPI获取"""
        trade_api = self._trade_apis[(request.account, request.is_credit)]
        if isinstance(trade_api, GMTradeAPIAdapter):
            status, portfolio = await trade_api.portfolio
        else:
            status, portfolio = trade_api.portfolio
        return qnt_trader_pb2.PortfolioReply(
            status=status, portfolio=qnt_trader_pb2.Portfolio(**_convert_enum(portfolio))
        )

    async def positions(self, request, context):
        """查询账户持仓，直接通过TradeAPI获取"""
        trade_api = self._trade_apis[(request.account, request.is_credit)]
        if isinstance(trade_api, GMTradeAPIAdapter):
            status, positions = await trade_api.positions
        else:
            status, positions = trade_api.positions
        return qnt_trader_pb2.PositionsReply(
            status=status,
            positions=[
                qnt_trader_pb2.Position(**_convert_enum(i)) for _, ii in positions.items() for _, i in ii.items()
            ],
        )

    async def order(self, request, context):
        logger.info("receive order request")
        status, order_id = await self._oma.create_order(request)
        return qnt_trader_pb2.OrderReply(status=status, order_id=order_id)

    async def cancel_order(self, request, context):
        status = await self._oma.cancel_order(request)
        return qnt_trader_pb2.CancelOrderReply(status=status)

    async def get_orders(self, request, context):
        status, res = await self._oma.get_orders(request)
        return qnt_trader_pb2.GetOrdersReply(
            status=status,
            orders=[qnt_trader_pb2.Order(**_convert_enum(i)) for i in res],
        )


async def serve():
    server_options = [
        ("grpc.keepalive_time_ms", 20000),
        ("grpc.keepalive_timeout_ms", 10000),
        ("grpc.http2.min_ping_interval_without_data_ms", 5000),
        ("grpc.http2.max_pings_without_data", 0),
        ("grpc.keepalive_permit_without_calls", 1),
    ]
    server = grpc.aio.server(options=server_options)
    qnt_trader_pb2_grpc.add_TraderServicer_to_server(Trader(), server)
    addr = "0.0.0.0:{}".format(gl_config["trader"]["settings"]["port"])
    server.add_insecure_port(addr)
    logger.info(f"starting server on {addr}")
    await server.start()
    await server.wait_for_termination()

from functools import wraps


def trade_api_exc(fnc):
    @wraps(fnc)
    def wrapper(self, *args, **kwargs):
        try:
            res = fnc(self, *args, **kwargs)
        except Exception as e:
            return -1, None
        else:
            return 0, res

    return wrapper


def trade_api_exc_async(fnc):
    @wraps(fnc)
    async def wrapper(self, *args, **kwargs):
        try:
            res = await fnc(self, *args, **kwargs)
        except Exception as e:
            return -1, None
        else:
            return 0, res

    return wrapper


class QntValueError(Exception):
    def __init__(self, *args, **kwargs):
        self._error_code = 1
        super().__init__(*args, **kwargs)

    @property
    def error_code(self):
        return self._error_code

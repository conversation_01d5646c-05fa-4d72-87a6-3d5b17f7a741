from abc import ABC, abstractmethod
from typing import Dict, List

import numpy as np
import pandas as pd

from qnt_research.api import get_trade_day_open_time, get_trade_days
from qnt_utils.enums import Direction, OffSet, PositionType, PriceType
from qnt_utils.label import QSymbol

from ..exceptions import trade_api_exc


class TradeAPIAdapter(ABC):
    MARKET = ["SSE", "SZSE"]
    """可交易的市场"""

    def __init__(self, account: str, password: str, is_credit: bool) -> None:
        self._account = account
        self._password = password
        self._is_credit = is_credit

    @property
    def account(self):
        return self._account

    @property
    def is_credit(self):
        return self._is_credit

    @property
    @trade_api_exc
    @abstractmethod
    def portfolio(self) -> Dict:
        pass

    @property
    @trade_api_exc
    @abstractmethod
    def positions(self) -> Dict:
        pass

    @trade_api_exc
    @abstractmethod
    def order(
        self,
        symbol: QSymbol | str,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: PriceType = PriceType.LIMIT,
    ) -> str:
        pass

    @trade_api_exc
    @abstractmethod
    def cancel_order(self, api_order_id: str) -> None:
        pass

    @trade_api_exc
    @abstractmethod
    def get_orders(self, *args, **kwargs) -> List:
        pass

    @staticmethod
    def _positions_to_dict(positions: List) -> Dict[QSymbol, Dict[PositionType, Dict]]:
        ret = {}
        for i in positions:
            if i["symbol"] not in ret.keys():
                ret[i["symbol"]] = {}
            if i["position_type"] not in ret[i["symbol"]].keys():
                ret[i["symbol"]][i["position_type"]] = {}
            ret[i["symbol"]][i["position_type"]] = i
        return ret

    @classmethod
    def get_near_trade_time(cls) -> Dict[str, Dict[str, pd.Timestamp]]:
        """获取前后最近的收盘时间

        Raises:
            RuntimeError: 获取交易日失败

        Returns:
            Dict[str, Dict[str, pd.Timestamp]]: key - last表示已经结束的交易日, next表示未结束的交易日, 包括当天，如果有夜盘，会把日盘和夜盘分成2段
        """
        now = pd.Timestamp.now(tz="Asia/Shanghai")
        trade_days = get_trade_days(
            cls.MARKET[0],
            (now - pd.Timedelta(days=20)).strftime("%Y-%m-%d"),
            (now + pd.Timedelta(days=20)).strftime("%Y-%m-%d"),
        )
        trade_days = [i.replace(hour=15) for i in trade_days]
        next_close_time_index = np.searchsorted(trade_days, now, "right")  # type: ignore
        ret = {
            "last": {
                "open": get_trade_day_open_time(
                    cls.MARKET[0], trade_days[next_close_time_index - 1].strftime("%Y-%m-%d")
                ),
                "close": trade_days[next_close_time_index - 1],
            },
            "next": {
                "open": get_trade_day_open_time(cls.MARKET[0], trade_days[next_close_time_index].strftime("%Y-%m-%d")),
                "close": trade_days[next_close_time_index],
            },
        }
        if ret["next"]["close"].date() != ret["next"]["open"].date():
            if now < (tmp := (ret["next"]["open"] + pd.Timedelta(days=1)).replace(hour=2, minute=30)):
                ret["next"]["close"] = tmp
            else:
                ret["next"]["open"] = ret["next"]["close"].replace(hour=9, minute=0)
        return ret

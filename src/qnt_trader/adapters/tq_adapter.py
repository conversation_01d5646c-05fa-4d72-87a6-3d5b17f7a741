import queue
import threading
import time
from typing import Dict, List

import pandas as pd

from qnt_research.api import is_within_trading_time
from qnt_utils.enums import Direction, OffSet, OrderStatus, PositionType, PriceType
from qnt_utils.label import QSymbol, Symbol

from ..exceptions import trade_api_exc
from .base import TradeAPIAdapter


class TQTradeAPIAdapter(TradeAPIAdapter):
    MARKET = ["DCE", "CZCE", "SHFE", "INE", "CFFEX", "GFEX"]

    def __init__(self, tq_account: str, tq_password: str, trade_broker: str, trade_account: str, trade_password: str):
        """初始化

        Args:
            tq_account (str): 天勤账号
            tq_password (str): 天勤密码
            trade_broker (str): 期商
            trade_account (str): 交易账号
            trade_password (str): 交易密码
        """
        super().__init__(trade_account, trade_password, False)
        self._tq_account = tq_account
        self._tq_password = tq_password
        self._trade_broker = trade_broker

        self._trl = threading.RLock()
        self._portfolio: dict = {}
        self._positions: list = []
        self._orders: list = []
        self._order_req_queue: queue.Queue = queue.Queue()
        self._order_ret_queue: queue.Queue = queue.Queue()
        threading.Thread(target=self._sub_thread, daemon=True).start()

    def _set_portfolio(self, portfolio):
        with self._trl:
            self._portfolio = {
                "asset": portfolio.balance,
                "debt": 0,
                "equity": portfolio.balance,
                "available_cash": portfolio.available,
                "frozen_cash": portfolio.frozen_margin + portfolio.frozen_commission + portfolio.frozen_premium,
                "market_value": portfolio.market_value,  # 期权市值
                "margin": portfolio.margin,  # 期货保证金占用
                "available_credit": 0,
                "interest_of_financed_funds": 0,
                "interest_of_financed_bonds": 0,
            }

    @property
    @trade_api_exc
    def portfolio(self) -> Dict:
        with self._trl:
            return self._portfolio.copy()

    def _set_positions(self, positions):
        with self._trl:
            self._positions = []
            for k, v in positions.items():
                if v.pos_long > 0:
                    self._positions.append(
                        {
                            "symbol": Symbol.qs_from_tq(k),
                            "amount": v.pos_long,
                            "available": v.volume_long - v.volume_long_frozen,
                            "frozen": v.volume_long_frozen,
                            "amount_today": v.pos_long_today,
                            "amount_his": v.pos_long_his,
                            "amount_today_available": v.volume_long_today - v.volume_long_frozen_today,
                            "amount_his_available": v.volume_long_his - v.volume_long_frozen_his,
                            "cost_basis": v.position_price_long,
                            "last_price": v.last_price,
                            "margin": v.margin_long,
                            "market_value": 0,
                            "profit": v.float_profit_long,
                            "profit_rate": v.float_profit_long / v.position_price_long,
                            "name": "",
                            "position_type": PositionType.LONG,
                        }
                    )
                if v.pos_short > 0:
                    self._positions.append(
                        {
                            "symbol": Symbol.qs_from_tq(k),
                            "amount": v.pos_short,
                            "available": v.volume_short - v.volume_short_frozen,
                            "frozen": v.volume_short_frozen,
                            "amount_today": v.pos_short_today,
                            "amount_his": v.pos_short_his,
                            "amount_today_available": v.volume_short_today - v.volume_short_frozen_today,
                            "amount_his_available": v.volume_short_his - v.volume_short_frozen_his,
                            "cost_basis": v.position_price_short,
                            "last_price": v.last_price,
                            "margin": v.margin_short,
                            "market_value": 0,
                            "profit": v.float_profit_short,
                            "profit_rate": v.float_profit_short / v.position_price_short,
                            "name": "",
                            "position_type": PositionType.SHORT,
                        }
                    )

    @property
    @trade_api_exc
    def positions(self) -> Dict:
        with self._trl:
            tmp = self._positions.copy()
        return self._positions_to_dict(tmp)

    @trade_api_exc
    def order(
        self,
        symbol: QSymbol | str,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: PriceType = PriceType.LIMIT,
    ) -> str:
        """因为tqsdk不支持多线程，所以通过子线程下单，此处使用队列来实现同步"""
        symbol_ = Symbol.qs_to_tq(symbol)
        direction_ = Direction[direction].name if isinstance(direction, str) else direction.name
        if direction_ not in ["BUY", "SELL"]:
            raise ValueError
        offset_ = OffSet[offset].name if isinstance(offset, str) else offset.name
        volume = int(amount)
        limit_price = price
        self._order_req_queue.put(("A", symbol_, direction_, offset_, volume, limit_price))
        insert_order = self._order_ret_queue.get()
        return "TQ_{}".format(insert_order.order_id)

    @trade_api_exc
    def cancel_order(self, api_order_id: str) -> None:
        self._order_req_queue.put(("D", api_order_id.replace("TQ_", "")))
        return

    def _set_orders(self, orders):
        def fnc(status, amount, amount_left):
            if status == "ALIVE":
                return OrderStatus.ALIVE
            else:
                if amount_left == 0:
                    return OrderStatus.FINISHED
                else:
                    return OrderStatus.CANCELLED

        with self._trl:
            self._orders = [
                {
                    "c_dt": pd.Timestamp.fromtimestamp(v["insert_date_time"] / 1e9, tz="Asia/Shanghai").strftime(
                        "%Y-%m-%d %H:%M:%S.%f"
                    ),
                    "m_dt": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "symbol": Symbol.qs_from_tq("{}.{}".format(v["exchange_id"], v["instrument_id"])),
                    "name": "",
                    "direction": {"BUY": Direction.BUY, "SELL": Direction.SELL}.get(v.direction),
                    "offset": {"OPEN": OffSet.OPEN, "CLOSE": OffSet.CLOSE, "CLOSETODAY": OffSet.CLOSETODAY}.get(
                        v.offset
                    ),
                    "amount": float(v.volume_orign),
                    "price": v.limit_price,
                    "status": fnc(v.status, v.volume_orign, v.volume_left),
                    "price_type": {"ANY": PriceType.MARKET, "LIMIT": PriceType.LIMIT}.get(v.price_type),
                    "trade_price": v.trade_price,
                    "trade_amount": float(v.volume_orign - v.volume_left),
                    "api_order_id": "TQ_{}".format(v.order_id),
                }
                for _, v in orders.items()
            ]

    @trade_api_exc
    def get_orders(self, *args, **kwargs) -> List:
        with self._trl:
            return self._orders.copy()

    def _sub_thread(self):
        try_times = 0
        while True:
            # 如果不在交易时间内则休眠
            if not is_within_trading_time("SHFE", pd.Timestamp.now(tz="Asia/Shanghai"), 600):
                time.sleep(60)
                continue

            # 初始化, 登录成功则重置尝试次数, 否则尝试次数加1, 如果尝试次数达到2次则抛出异常
            try:
                from tqsdk import TqAccount, TqApi, TqAuth

                # tq_api = TqApi(TqKq(), auth=TqAuth(self._tq_account, self._tq_password))
                tq_api = TqApi(
                    TqAccount(self._trade_broker, self._account, self._password),
                    auth=TqAuth(self._tq_account, self._tq_password),
                )

                try_times = 0
            except:
                try_times += 1
                if try_times == 2:
                    raise RuntimeError("TianQinAdapter: login failed")
                continue

            # 开始业务处理
            try:
                portfolio = tq_api.get_account()
                self._set_portfolio(portfolio)
                positions = tq_api.get_position()
                self._set_positions(positions)
                orders = tq_api.get_order()
                self._set_orders(orders)

                while True:
                    # 把队列里的订单请求先处理掉
                    while self._order_req_queue.qsize() > 0:
                        order_type, *args = self._order_req_queue.get()
                        if order_type == "A":
                            insert_order = tq_api.insert_order(
                                symbol=args[0], direction=args[1], offset=args[2], volume=args[3], limit_price=args[4]
                            )
                            self._order_ret_queue.put(insert_order)
                        elif order_type == "D":
                            tq_api.cancel_order(args[0])

                    tq_api.wait_update(deadline=time.time() + 0.01)
                    # 更新账户信息
                    if tq_api.is_changing(portfolio):
                        self._set_portfolio(portfolio)
                    # 更新持仓信息
                    if tq_api.is_changing(positions):
                        self._set_positions(positions)
                        # 这里不太明白，先放着
                        # for k, v in positions.items():
                        #     if tq_api.is_changing(
                        #         v, ["pos", "pos_long", "pos_long_his", "pos_long_today", "pos_short", "pos_short_his", "pos_short_today"]
                        #     ):
                        #         self._set_positions(positions)
                        #         break
                    # 更新订单信息
                    if tq_api.is_changing(orders):
                        self._set_orders(orders)
            except:
                pass
            finally:
                if not tq_api is None:
                    tq_api.close()
                    del tq_api

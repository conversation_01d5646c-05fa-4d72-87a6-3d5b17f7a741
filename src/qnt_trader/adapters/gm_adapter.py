import asyncio
import json
import uuid
from typing import Dict, List, Optional

from extern_api.api import get_stock_name
from qnt_trader.exceptions import trade_api_exc, trade_api_exc_async
from qnt_utils.config import get_config
from qnt_utils.enums import Direction, OffSet, OrderStatus, PositionType, PriceType
from qnt_utils.label import QSymbol

from .base import TradeAPIAdapter


class GMTradeAPIAdapter(TradeAPIAdapter):
    MARKET = ["SSE", "SZSE"]

    def __init__(self):
        import redis
        from redis.asyncio import Redis

        self._redis = Redis(host=get_config()["redis"]["host"], port=get_config()["redis"]["port"])
        super().__init__("gm", "password", False)
        self._order_status = {}

        if tmp := redis.Redis(host=get_config()["redis"]["host"], port=get_config()["redis"]["port"]).hgetall(
            "GM_ORDER_ID"
        ):
            tmp = {
                trader_order_id.decode("utf-8"): gm_order_id.decode("utf-8")
                for trader_order_id, gm_order_id in tmp.items()
            }
            for trader_order_id in tmp.keys():
                asyncio.create_task(self._coroutine_get_gm_order_id(trader_order_id))

    async def _coroutine_get_gm_order_id(self, trader_order_id):
        while True:
            order_status = await self._redis.get(trader_order_id)
            if order_status is not None:
                order_status = json.loads(order_status)
                order_status["direction"] = Direction[order_status["direction"]]
                order_status["offset"] = OffSet[order_status["offset"]]
                order_status["price_type"] = PriceType[order_status["price_type"]]
                order_status["status"] = OrderStatus[order_status["status"]]
                self._order_status[trader_order_id] = order_status
                if order_status["status"] in [OrderStatus.FINISHED, OrderStatus.CANCELLED, OrderStatus.SCRAPPED]:
                    break
            await asyncio.sleep(0.1)

    @property
    @trade_api_exc_async
    async def portfolio(self) -> Dict:
        res = await self._redis.get("GM_PORTFOLIO")
        return json.loads(res)

    @property
    @trade_api_exc_async
    async def positions(self) -> Dict:
        res = json.loads(await self._redis.get("GM_POSITIONS"))
        for i in res:
            i["name"] = get_stock_name(i["symbol"]) or ""
            i["position_type"] = PositionType.LONG
        return self._positions_to_dict(res)

    @trade_api_exc_async
    async def order(
        self,
        symbol: QSymbol | str,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: PriceType = PriceType.LIMIT,
    ) -> str:
        trader_order_id = f"GM_{str(uuid.uuid4())}"
        req = {
            "trader_order_id": trader_order_id,
            "symbol": symbol,
            "direction": direction if isinstance(direction, str) else direction.name,
            "offset": offset if isinstance(offset, str) else offset.name,
            "amount": amount,
            "price": price,
            "price_type": price_type.name,
        }
        await self._redis.lpush("GM_ORDER_REQ", json.dumps(req))
        asyncio.create_task(self._coroutine_get_gm_order_id(trader_order_id))
        return trader_order_id

    @trade_api_exc_async
    async def cancel_order(self, api_order_id: str) -> None:
        """撤单"""
        await self._redis.lpush("GM_CANCEL_ORDER_REQ", api_order_id)

    @trade_api_exc
    def get_orders(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List:
        """获取普通账号委托记录

        Args:
            start_date (Optional[str], optional): 开始日期. Defaults to None.
            end_date (Optional[str], optional): 结束日期. Defaults to None.

        Returns:
            List: 委托记录
        """
        return list(self._order_status.values())

import time
from functools import wraps
from typing import Callable, Dict

import pandas as pd


def _make_req_params(fnc: Callable) -> Callable:
    """装饰器, 在fnc生成的请求参数基础上做一定修改

    Args:
        fnc (Callable): 原始生成请求参数的函数

    Returns:
        Callable: 生成请求参数的函数
    """

    @wraps(fnc)
    def wrapper(*args, **kwargs):
        req_params = fnc(*args, **kwargs)
        req_params["verify"] = False
        req_params["headers"].pop("<PERSON>ie", None)
        req_params["headers"]["gw_reqtimestamp"] = str(int(time.time() * 1000))
        return req_params

    return wrapper


@_make_req_params
def get_identify_code(rand_number: str) -> Dict:
    res = {
        "url": "https://jywg.18.cn/Login/YZM?randNum={rand_number}",
        "headers": {
            "Accept": "image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Referer": "https://jywg.18.cn/Login?el=1&clear=1",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "image",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
        },
    }
    res["url"] = res["url"].format(rand_number=rand_number)
    res["data"] = {"randNum": rand_number}
    return res


@_make_req_params
def verify_user_info(identify_code: str, remote, type_) -> Dict:
    url = (
        "http://************:18888/api/verifyUserInfo?{identify_code}"
        if remote
        else "http://127.0.0.1:18888/api/verifyUserInfo?{identify_code}"
    )
    if type_ == 1:
        res = {
            "url": url,
            "headers": {
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Private-Network": "true",
                "Connection": "keep-alive",
                "Host": "127.0.0.1:18888",
                "Origin": "https://jywg.18.cn",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "cross-site",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            },
        }
    else:
        res = {
            "url": url,
            "headers": {
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded",
                "DNT": "1",
                "Host": "127.0.0.1:18888",
                "Origin": "https://jywg.18.cn",
                "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "cross-site",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            },
        }
    res["url"] = res["url"].format(identify_code=identify_code)
    res["data"] = {identify_code: ""}
    return res


@_make_req_params
def authentication1(account, password, rand_number, identify_code, sec_info):
    res = {
        "url": "https://jywg.18.cn/Login/Authentication?validatekey=",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "497",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Login?el=1&clear=1",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["data"] = {
        "validatekey": "",
        "userId": account,
        "password": password,
        "randNumber": rand_number,
        "identifyCode": identify_code,
        "duration": 1800,
        "authCode": "",
        "type": "Z",
        "secInfo": sec_info,
    }

    return res


@_make_req_params
def authentication2():
    res = {
        "url": "https://jywg.18.cn/Trade/Buy",
        "headers": {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Referer": "https://jywg.18.cn/Login?el=1&clear=1",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
        },
    }
    return res


@_make_req_params
def get_code_auto_complete(code):
    res = {
        "url": "https://hsmarketwg.eastmoney.com/api/SHSZQuery/GetCodeAutoComplete2?id={code}&count=10&callback=sData",
        "headers": {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "DNT": "1",
            "Host": "hsmarketwg.eastmoney.com",
            "Referer": "https://jywg.18.cn/",
            "sec-ch-ua": '"Not_A Brand";v="99", "Microsoft Edge";v="109", "Chromium";v="109"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "script",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "cross-site",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.78",
        },
    }
    res["url"] = res["url"].format(code=code)
    res["data"] = {
        "id": code,
        "count": 10,
        "callback": "sData",
    }
    return res


@_make_req_params
def get_all_need_trade_info(validatekey, code, name, price):
    res = {
        "url": "https://jywg.18.cn/Trade/GetAllNeedTradeInfo?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "109",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Trade/Buy",
            "sec-ch-ua": '"Not_A Brand";v="99", "Microsoft Edge";v="109", "Chromium";v="109"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.78",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "stockCode": code,
        "price": price,
        "tradeType": "B",
        "stockName": name,
        "gddm": "",
        "market": "HA",
        "jylb": "B",
    }
    return res


@_make_req_params
def get_stock_list(validatekey, dwc=""):
    res = {
        "url": "https://jywg.18.cn/Search/GetStockList?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "14",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Trade/Buy",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "qqhs": 1000, "dwc": dwc}
    return res


@_make_req_params
def query_asset_and_position(validatekey):
    res = {
        "url": "https://jywg.18.cn/Com/queryAssetAndPositionV1?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "13",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Search/Position",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "moneyType": "RMB"}
    return res


@_make_req_params
def get_orders_data(validatekey, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/Search/GetOrdersData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "12",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Search/Orders",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "qqhs": 20, "dwc": dwc}
    return res


@_make_req_params
def get_credit_orders_data(validatekey, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/MarginSearch/GetOrdersData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "12",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginSearch/Orders",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "qqhs": 20, "dwc": dwc}
    return res


@_make_req_params
def get_deal_data(validatekey, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/Search/GetDealData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "12",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Search/Deal",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "qqhs": 20, "dwc": dwc}
    return res


@_make_req_params
def get_credit_deal_data(validatekey, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/MarginSearch/GetDealData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "13",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginSearch/Deals",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "qqhs": 20, "dwc": dwc}
    return res


@_make_req_params
def get_his_orders_data(validatekey, st, et, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/Search/GetHisOrdersData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "40",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Search/HisOrders",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "validatekey": validatekey,
        "st": pd.Timestamp(st).strftime("%Y-%m-%d"),
        "et": pd.Timestamp(et).strftime("%Y-%m-%d"),
        "qqhs": 20,
        "dwc": dwc,
    }
    return res


@_make_req_params
def get_credit_his_orders_data(validatekey, st, et, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/MarginSearch/GetHisOrdersData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "40",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginSearch/HisOrders",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "validatekey": validatekey,
        "st": pd.Timestamp(st).strftime("%Y-%m-%d"),
        "et": pd.Timestamp(et).strftime("%Y-%m-%d"),
        "qqhs": 20,
        "dwc": dwc,
    }
    return res


@_make_req_params
def get_his_deal_data(validatekey, st, et, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/Search/GetHisDealData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "40",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Search/HisDeal",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "validatekey": validatekey,
        "st": pd.Timestamp(st).strftime("%Y-%m-%d"),
        "et": pd.Timestamp(et).strftime("%Y-%m-%d"),
        "qqhs": 20,
        "dwc": dwc,
    }
    return res


@_make_req_params
def get_credit_his_deal_data(validatekey, st, et, dwc: str = ""):
    res = {
        "url": "https://jywg.18.cn/MarginSearch/GetHisDealData?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "40",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginSearch/HisDeals",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "validatekey": validatekey,
        "st": pd.Timestamp(st).strftime("%Y-%m-%d"),
        "et": pd.Timestamp(et).strftime("%Y-%m-%d"),
        "qqhs": 20,
        "dwc": dwc,
    }
    return res


@_make_req_params
def submit_trade(
    validatekey: str,
    stock_code: str,
    price: float,
    amount: int,
    trade_type: int,
    name: str,
    market: str,
) -> Dict:
    """普通交易

    Args:
        validatekey (str): 授权
        stock_code (str): 证券代码, 6位
        price (float): 价格
        amount (int): 数量
        trade_type (int): 1 - 普通买入; 2 - 普通卖出
        name (str): 证券名称
        market (str): HA - 沪A; SA - 深A

    Returns:
        Dict: 请求参数
    """
    if not trade_type in [1, 2]:
        raise ValueError("Invalid trade type. 1 - 普通买入; 2 - 普通卖出")
    res = {
        "url": "https://jywg.18.cn/Trade/SubmitTradeV2?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "87",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Trade/Buy",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "validatekey": validatekey,
        "stockCode": stock_code,
        "price": price,
        "amount": amount,
        "tradeType": {1: "B", 2: "S"}.get(trade_type),
        "zqmc": name,
        "market": market,
    }
    return res


@_make_req_params
def submit_credit_trade(
    validatekey: str,
    stock_code: str,
    price: str,
    amount: str,
    trade_type: int,
    name: str,
    market: str,
) -> Dict:
    """信用交易

    Args:
        validatekey (str): 授权
        stock_code (str): 证券代码, 6位
        price (str): 价格
        amount (str): 数量
        trade_type (int): 1 - 担保品买入; 2 - 担保品卖出; 3 - 融资买入; 4 - 融券卖出
        name (str): 证券名称
        market (str): HA - 沪A; SA - 深A

    Returns:
        Dict: 请求参数
    """
    if trade_type not in [1, 2, 3, 4]:
        raise ValueError("Invalid trade type. 1 - 担保品买入; 2 - 担保品卖出; 3 - 融资买入; 4 - 融券卖出")
    res = {
        "url": "https://jywg.18.cn/MarginTrade/SubmitTradeV2?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "102",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginTrade/{}".format(
                {
                    1: "Buy",
                    2: "Sale",
                    3: "MarginBuy",
                    4: "FinanceSale",
                }.get(trade_type)
            ),
            "sec-ch-ua": '"Not_A Brand";v="99", "Microsoft Edge";v="109", "Chromium";v="109"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.78",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "validatekey": validatekey,
        "stockCode": stock_code,
        "stockName": name,
        "price": price,
        "amount": amount,
        "tradeType": {1: "B", 2: "S", 3: "B", 4: "S"}.get(trade_type),
        "xyjylx": {
            1: 6,
            2: 7,
            3: "a",
            4: "A",
        }.get(trade_type),
        "market": market,
    }
    return res


@_make_req_params
def revoke_orders(validatekey, wtbh):
    res = {
        "url": "https://jywg.18.cn/Trade/RevokeOrders?validatekey={validatekey}",
        "headers": {
            "Accept": "text/plain, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "22",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/Trade/Buy",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {
        "validatekey": validatekey,
        "revokes": wtbh,
    }
    return res


@_make_req_params
def revoke_credit_orders(validatekey, wtbh):
    res = {
        "url": "https://jywg.18.cn/MarginTrade/RevokeOrders?validatekey={validatekey}",
        "headers": {
            "Accept": "text/plain, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "23",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginTrade/Buy",
            "sec-ch-ua": '"Not_A Brand";v="99", "Microsoft Edge";v="109", "Chromium";v="109"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.78",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "revokes": wtbh}
    return res


@_make_req_params
def check_xy_pwd(validatekey):
    res = {
        "url": "https://jywg.18.cn/MarginSearch/CheckXYPwd?validatekey={validatekey}",
        "headers": {
            "Accept": "text/plain, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "0",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginSearch/MyAssets",
            "sec-ch-ua": '"Not_A Brand";v="99", "Microsoft Edge";v="109", "Chromium";v="109"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.78",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey}
    return res


@_make_req_params
def query_can_credit_stk(validatekey, dwc):
    res = {
        "url": "https://jywg.18.cn/MarginPersonal/QueryCanCreditStk?v={gw_reqtimestamp}&validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "56",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginPersonal/PersonalCanMarginStockQuery",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(
        validatekey=validatekey,
        gw_reqtimestamp=(gw_reqtimestamp := str(int(time.time() * 1000))),
    )
    res["data"] = {
        "validatekey": validatekey,
        "v": gw_reqtimestamp,
        "qqhs": 15,
        "dwc": dwc,
        "SortField": 0,
        "SortRule": 0,
        "keyWord": "",
        "HideZero": 0,
    }
    return res


@_make_req_params
def get_rzrq_assets(validatekey):
    res = {
        "url": "https://jywg.18.cn/MarginSearch/GetRzrqAssets?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "8",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginSearch/MarginAssets",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "hblx": "RMB"}
    return res


@_make_req_params
def query_collateral(validatekey, dwc=""):
    res = {
        "url": "https://jywg.18.cn/MarginSearch/QueryCollateral?validatekey={validatekey}",
        "headers": {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Content-Length": "12",
            "Content-Type": "application/x-www-form-urlencoded",
            "Cookie": "eastmoney_txzq_zjzh=NTQxMzAwMTIzNjM5fA%3D%3D; Yybdm=5413; Uid=0OC1i94vMvAbOUh4BN4pzA%3d%3d; Khmc=%e6%9d%a8%e5%b8%86; st_si=70346611744033; st_asi=delete; mobileimei=b7efe589-108e-4d05-9523-1b53b5bf61c3; Uuid=1f23678572a741a4a50d8cb2d6625aa8; st_pvi=62472625022084; st_sp=2023-02-12%2015%3A51%3A37; st_inirUrl=https%3A%2F%2Fjywg.18.cn%2FLogin; st_sn=5; st_psi=20230216172140650-11923323340385-9595325637",
            "DNT": "1",
            "gw_reqtimestamp": "1676540356096",
            "Host": "jywg.18.cn",
            "Origin": "https://jywg.18.cn",
            "Referer": "https://jywg.18.cn/MarginTrade/MarginBuy",
            "sec-ch-ua": '"Chromium";v="110", "Not A(Brand";v="24", "Microsoft Edge";v="110"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
            "X-Requested-With": "XMLHttpRequest",
        },
    }
    res["url"] = res["url"].format(validatekey=validatekey)
    res["data"] = {"validatekey": validatekey, "qqhs": 10, "dwc": dwc}
    return res

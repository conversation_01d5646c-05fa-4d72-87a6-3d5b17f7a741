import json
import pathlib
import random
import threading
import time
from functools import wraps
from typing import Any, Callable, Dict, List, Tuple, TypeVar

import ddddocr
import pandas as pd
import requests
from loguru import logger
from lxml import etree
from PIL import Image

import em_trade_api.http_req_generator as hrg
from qnt_research.api import is_within_trading_time

from .exceptions import (
    HttpApiError,
    LoginFailed,
    ResponseError,
    SecurityAmbiguous,
    SessionInvaluableError,
)

Self = TypeVar("Self", bound="EMSessionManager")


def _retry(fnc: Callable) -> Callable:
    """装饰器, 失败后重试"""

    @wraps(fnc)
    def wrapper(self, *args, **kwargs):
        with self._thread_lock:
            if not self.status:
                raise SessionInvaluableError
            for i in range(2):
                try:
                    return fnc(self, *args, **kwargs)
                except SecurityAmbiguous:
                    raise SecurityAmbiguous
                except Exception as e:
                    if i == 0:
                        logger.warning(f"操作执行失败(原因：{e}), 重试...")
                        self._open_session()
                        continue
                    else:
                        raise HttpApiError

    return wrapper


def _handle_dwc(hrg_fnc: Callable, qqhs: int) -> Callable:
    """返回能自动处理分布的查询接口

    Args:
        hrg_fnc (Callable): 请求生成函数
        qqhs (int): _description_

    Returns:
        Callable: 查询接口，如果有dwc参数，则递归调用
    """

    def fnc(self, **kwargs):
        res = json.loads(self._session.post(**hrg_fnc(self._validatekey, **kwargs)).text)["Data"]
        if len(res) < qqhs:
            return res
        else:
            (kwargs1 := kwargs.copy()).update(dwc=res[-1]["Dwc"])
            if kwargs1["dwc"] != "":
                res.extend(fnc(self, **kwargs1))
            return res

    return fnc


def _get_order_info(today_fnc: Callable, his_fnc: Callable) -> Callable:
    """生成汇总当日和历史的函数

    Args:
        today_fnc (Callable): 查询当日的函数
        his_fnc (Callable): 查询历史的函数

    Returns:
        Callable: 汇总当日和历史的函数
    """

    def fnc(self, st, et) -> Tuple[List, List]:
        today = pd.Timestamp.now(tz="Asia/Shanghai")
        st = min(today, pd.Timestamp(st, tz="Asia/Shanghai")).date()
        et = min(today, pd.Timestamp(et, tz="Asia/Shanghai")).date()
        if st > et:
            raise ValueError("参数有误")
        if et == today.date():
            if st == today.date():
                res1 = today_fnc(self)
                res2 = []
            else:
                res1 = today_fnc(self)
                res2 = his_fnc(self, st=st, et=et - pd.Timedelta(days=1))
        else:
            res1 = []
            res2 = his_fnc(self, st=st, et=et)
        return res1, res2

    return fnc


class SessionManager:
    def __init__(self, account, password) -> None:
        self._account = account
        self._password = password
        self._session: requests.Session
        self._sess_otime: pd.Timestamp
        """最近一次登录成功的时间"""
        self._thread_lock = threading.RLock()
        self._open_session()
        threading.Thread(target=self._keep_alive, daemon=True).start()

    def _open_session(self):
        """建立session

        Raises:
            HttpSessionBanned: 重试5次未成功, 会报"http接口被封禁了"
        """
        self._session = requests.session()
        requests.packages.urllib3.disable_warnings(  # type: ignore
            requests.packages.urllib3.exceptions.InsecureRequestWarning  # type: ignore
        )
        for _ in range(5):
            try:
                self._login()
                self._sess_otime = pd.Timestamp.now(tz="Asia/Shanghai")
                logger.success("EMTradeAPI Session建立成功")
                break
            except LoginFailed:
                logger.warning("EMTradeAPI Session建立失败, 重试...")
                continue
            except Exception:
                raise HttpApiError
        else:
            try:
                logger.log("WEBHOOK", "EMTradeAPI Session建立失败，已尝试多次")
            except Exception:
                pass
            raise HttpApiError

    def _login(self):
        pass

    def _keep_alive(self):
        """子线程, 因为session只能保持180秒，所以增加一个子线程来保持连接，每隔165会自动重连一次, 如果是非交易日则暂时不重连"""
        while True:
            with self._thread_lock:
                if (now := pd.Timestamp.now(tz="Asia/Shanghai")) - self._sess_otime > pd.Timedelta(
                    minutes=165
                ) and is_within_trading_time("SSE", now, 120):
                    self._open_session()
            time.sleep(60)

    @property
    def status(self):
        if self._sess_otime is not None and pd.Timestamp.now(tz="Asia/Shanghai") - self._sess_otime <= pd.Timedelta(
            minutes=178
        ):
            return True
        return False


class EMSessionManager(SessionManager):
    def __init__(self, account, password) -> None:
        self._security_info = {}
        self._try_num = 0
        self._rand_number: str
        self._identify_code: str  # 验证码，识别验证码图片之后获得
        self._sec_info: str  # 与本机东财控件交互获取secInfo
        self._validatekey: str  # 授权

        super().__init__(account=account, password=password)

    def _login(self):
        """登录

        Raises:
            LoginFailed: 登录失败
        """
        try:
            self._rand_number = str(random.random() - 0.*****************)
            self._identify_code = self._get_identify_code()
            self._sec_info = self._verify_user_info()
            self._validatekey = self._authentication()
            self._check_xy_pwd()
            self._try_num = 0
        except Exception as e:
            logger.warning(f"EMTradeAPI登录失败: {e}")
            self._try_num += 1
            raise LoginFailed(e)

    def _get_identify_code(self) -> str:
        """第一步，获取验证码图片并识别

        Raises:
            Exception: 获取验证码失败
            Exception: 识别验证码失败

        Returns:
            str: 验证码
        """
        try:
            response = self._session.get(**hrg.get_identify_code(self._rand_number))
        except Exception as e:
            raise Exception(f"第一步，获取验证码失败: {e}")
        try:
            if self._try_num >= 3:
                with open("dfyzm.png", "wb") as file:
                    file.write(response.content)
                image = Image.open("dfyzm.png")
                image.show()
                return input("请输入验证码: ")
            else:
                ocr = ddddocr.DdddOcr(
                    ocr=True,
                    show_ad=False,
                    import_onnx_path=str(
                        pathlib.Path(
                            pathlib.Path(__file__).parent,
                            "dependency",
                            "test_0.987012987012987_579_11000_2023-02-17-23-33-46.onnx",
                        )
                    ),
                    charsets_path=str(pathlib.Path(pathlib.Path(__file__).parent, "dependency", "charsets.json")),
                )
                res = ocr.classification(response.content)
                return res
        except Exception as e:
            raise Exception(f"第一步，验证码识别失败: {e}")

    def _verify_user_info(self):
        """第二步，携带验证码，与本机东财控件交互获取secInfo"""
        for i in range(2):
            # i == 0时，从远程获取secInfo，i == 1时，从本地获取secInfo
            try:
                self._session.options(**(tmp := hrg.verify_user_info(self._identify_code, i == 0, 1)))
                return json.loads(
                    self._session.get(**(tmp := hrg.verify_user_info(self._identify_code, i == 0, 2))).text
                )["userInfo"]
            except Exception:
                continue
        else:
            raise Exception("第二步，与东财控件交互获取secInfo失败")

    def _authentication(self):
        """第三步，登录, 获取授权"""
        try:
            response = self._session.post(
                **hrg.authentication1(
                    self._account, self._password, self._rand_number, self._identify_code, self._sec_info
                )
            )
            c = requests.cookies.RequestsCookieJar()  # type: ignore # 利用RequestsCookieJar获取
            c.set("cookie-name", "cookie-value")
            self._session.cookies.update(c)
            res = json.loads(response.text)
            if res["Status"] == -1:
                raise ResponseError
            response = self._session.get(**hrg.authentication2())
            html = etree.HTML(response.text)  # type: ignore
            return html.xpath('//input[@id="em_validatekey"]')[0].get("value")
        except ResponseError:
            raise Exception("第三步，获取授权失败，登录返回错误码")
        except Exception:
            raise Exception("第三步，获取授权失败")

    def _check_xy_pwd(self):
        """第四步，获取信用账户的授权"""
        try:
            return json.loads(self._session.post(**hrg.check_xy_pwd(self._validatekey)).text)
        except Exception:
            raise Exception("第四步，获取信用账户的授权失败")

    @_retry
    def _get_security_info(self, code: str) -> Tuple:
        """获取股票名称和市场

        Args:
            code (str): 股票代码

        Raises:
            SecurityAmbiguous: 当前股票不唯一

        Returns:
            Tuple: 股票名称, 市场
        """

        def fnc(s):
            s = s.replace("var sData = ", "")
            s = s.replace('"', "")
            s = s.split(";")
            s = [i.split(",") for i in s if not i == ""]
            return s

        if code not in self._security_info.keys():
            res = fnc(self._session.get(**hrg.get_code_auto_complete(code)).text)
            for i in res:
                self._security_info[i[0]] = (i[4], {"4": "B", "1": "HA", "2": "SA"}.get(i[5]))
            if len(res) > 1:
                raise SecurityAmbiguous
        return self._security_info[code]

    @_retry
    def _get_all_need_trade_info(self, code, price):
        sec_name, _ = self._get_security_info(code)
        return json.loads(
            self._session.post(**hrg.get_all_need_trade_info(self._validatekey, code, sec_name, price)).text
        )

    @_retry
    def get_stock_list(self):
        return json.loads(self._session.post(**hrg.get_stock_list(self._validatekey)).text)

    @_retry
    def query_asset_and_position(self) -> Dict:
        """向东财服务器查询资金和持仓

        Raises:
            SessionInvaluableError: session不可用
            SecurityAmbiguous: 当前股票不唯一
            HttpApiError: http api不可用

        Returns:
            Dict: 资金和持仓
        """
        return json.loads(self._session.post(**hrg.query_asset_and_position(self._validatekey)).text)["Data"][0]

    _get_orders_data: Callable[[Any], List] = _retry(_handle_dwc(hrg.get_orders_data, 20))
    """查询普通账户当日委托"""

    _get_his_orders_data: Callable[[Any], List] = _retry(_handle_dwc(hrg.get_his_orders_data, 20))
    """查询普通账户历史委托"""

    _get_deal_data: Callable[[Any, str, str], List] = _retry(_handle_dwc(hrg.get_deal_data, 20))
    """查询普通账户当日成交"""

    _get_his_deal_data: Callable[[Any, str, str], List] = _retry(_handle_dwc(hrg.get_his_deal_data, 20))
    """查询普通账户历史委托"""

    _get_credit_orders_data: Callable[[Any], List] = _retry(_handle_dwc(hrg.get_credit_orders_data, 20))
    """查询信用账户当日委托"""

    _get_credit_his_orders_data: Callable[[Any], List] = _retry(_handle_dwc(hrg.get_credit_his_orders_data, 20))
    """查询信用账户历史委托"""

    _get_credit_deal_data: Callable[[Any, str, str], List] = _retry(_handle_dwc(hrg.get_credit_deal_data, 20))
    """查询信用账户当日成交"""

    _get_credit_his_deal_data: Callable[[Any, str, str], List] = _retry(_handle_dwc(hrg.get_credit_his_deal_data, 20))
    """查询信用账户历史委托"""

    get_orders: Callable[[Any, str, str], Tuple] = _get_order_info(_get_orders_data, _get_his_orders_data)
    """查询普通账户委托, 汇总了当日和历史"""

    get_transactions: Callable[[Any, str, str], Tuple] = _get_order_info(_get_deal_data, _get_his_deal_data)
    """查询普通账户成交, 汇总了当日和历史"""

    get_credit_orders: Callable[[Any, str, str], Tuple] = _get_order_info(
        _get_credit_orders_data, _get_credit_his_orders_data
    )
    """查询信用账户委托, 汇总了当日和历史"""

    get_credit_transations: Callable[[Any, str, str], Tuple] = _get_order_info(
        _get_credit_deal_data, _get_credit_his_deal_data
    )
    """查询信用账户成交, 汇总了当日和历史"""

    @_retry
    def submit_trade(self, stock_code: str, price: float, amount: int, trade_type: int):
        """普通交易

        Args:
            stock_code (str): 证券代码, 6位
            price (float): 价格
            amount (int): 数量
            trade_type (int): 1 - 普通买入; 2 - 普通卖出

        Returns:
            Dict: 响应
        """
        sec_name, market = self._get_security_info(stock_code)
        ret = json.loads(
            self._session.post(
                **hrg.submit_trade(self._validatekey, stock_code, price, amount, trade_type, sec_name, market)
            ).text
        )
        if len(ret["Data"]) > 0:
            return ret["Data"][0]
        else:
            raise Exception(ret)

    @_retry
    def revoke_orders(self, wtbh):
        """普通账户撤单"""
        self._session.post(**hrg.revoke_orders(self._validatekey, wtbh)).text

    @_retry
    def submit_credit_trade(self, stock_code: str, price: float, amount: int, trade_type: int):
        """信用交易

        Args:
            stock_code (str): 证券代码, 6位
            price (float): 价格
            amount (int): 数量
            trade_type (int): 1 - 担保品买入; 2 - 担保品卖出; 3 - 融资买入; 4 - 融券卖出

        Returns:
            Dict: 响应
        """
        sec_name, market = self._get_security_info(stock_code)
        ret = json.loads(
            self._session.post(
                **hrg.submit_credit_trade(self._validatekey, stock_code, price, amount, trade_type, sec_name, market)
            ).text
        )
        if len(ret["Data"]) > 0:
            return ret["Data"][0]
        else:
            raise Exception(ret)

    @_retry
    def revoke_credit_orders(self, wtbh):
        """信用账户撤单"""
        self._session.post(**hrg.revoke_credit_orders(self._validatekey, wtbh)).text

    @_retry
    def query_can_credit_stk(self):
        """查询可融券"""
        res = []
        for i in range(1, 10):
            res.extend(
                tmp := json.loads(self._session.post(**hrg.query_can_credit_stk(self._validatekey, i)).text)["Data"]
            )
            if len(tmp) < 15:
                break
        return res

    @_retry
    def get_rzrq_assets(self):
        """获取信用账户portfolio"""
        return json.loads(self._session.post(**hrg.get_rzrq_assets(self._validatekey)).text)["Data"]

    query_collateral: Callable = _retry(_handle_dwc(hrg.query_collateral, 10))
    """查询信用账户持仓情况"""

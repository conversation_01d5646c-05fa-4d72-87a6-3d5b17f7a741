import pathlib
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd

from em_trade_api.session_manager import EMSessionManager


class FieldMapper:
    def __init__(self) -> None:
        self._order_fields = pd.read_csv(
            pathlib.Path(pathlib.Path(__file__).parent, "dependency", "order_fields.csv"), encoding="gbk"
        )
        self._order_fields = self._order_fields.loc[
            ~self._order_fields["字段名"].apply(lambda x: pd.isna(x) or "#" in x), :
        ]

        self.transaction_fields = pd.read_csv(
            pathlib.Path(pathlib.Path(__file__).parent, "dependency", "transaction_fields.csv"), encoding="gbk"
        )
        self.transaction_fields = self.transaction_fields.loc[
            ~self.transaction_fields["字段名"].apply(lambda x: pd.isna(x) or "#" in x), :
        ]

        self._portfolio_fields = pd.read_csv(
            pathlib.Path(pathlib.Path(__file__).parent, "dependency", "portfolio_fields.csv"), encoding="gbk"
        )
        self._portfolio_fields = self._portfolio_fields.loc[
            ~self._portfolio_fields["字段名"].apply(lambda x: pd.isna(x) or "#" in x), :
        ]

        self._positions_fields = pd.read_csv(
            pathlib.Path(pathlib.Path(__file__).parent, "dependency", "positions_fields.csv"), encoding="gbk"
        )
        self._positions_fields = self._positions_fields.loc[
            ~self._positions_fields["字段名"].apply(lambda x: pd.isna(x) or "#" in x), :
        ]

    def orders_map(self, type_, orders):
        """委托字段匹配"""
        if not orders:
            return []
        tmp = self._order_fields.loc[~self._order_fields[type_].isna(), ["字段名", "类型", type_]]
        fields = tmp[type_].tolist()
        orders = pd.DataFrame(orders)[fields]
        orders = orders.astype(dict(zip(fields, tmp["类型"])))
        orders.rename(columns=dict(zip(fields, tmp["字段名"])), inplace=True)
        orders = pd.concat([pd.DataFrame(columns=self._order_fields["字段名"]), orders], axis=0)
        return [i.to_dict() for _, i in orders.iterrows()]

    def transactions_map(self, type_, transactions):
        """成交字段匹配"""
        if not transactions:
            return []
        tmp = self.transaction_fields.loc[~self.transaction_fields[type_].isna(), ["字段名", "类型", type_]]
        fields = tmp[type_].tolist()
        transactions = pd.DataFrame(transactions)[fields]
        transactions = transactions.astype(dict(zip(fields, tmp["类型"])))
        transactions.rename(columns=dict(zip(fields, tmp["字段名"])), inplace=True)
        transactions = pd.concat([pd.DataFrame(columns=self.transaction_fields["字段名"]), transactions], axis=0)
        return [i.to_dict() for _, i in transactions.iterrows()]

    def portfolio_map(self, type_: str, portfolio1: Dict) -> Dict[str, Any]:
        if not portfolio1:
            return {}
        tmp = self._portfolio_fields.loc[~self._portfolio_fields[type_].isna(), ["字段名", "类型", type_]]
        fields = tmp[type_].tolist()
        portfolio: pd.DataFrame = pd.DataFrame(pd.Series(portfolio1)).T[fields]
        portfolio.replace("", np.nan, inplace=True)
        portfolio = portfolio.astype(dict(zip(fields, tmp["类型"])))
        portfolio.rename(columns=dict(zip(fields, tmp["字段名"])), inplace=True)
        return portfolio.iloc[0, :].to_dict()

    def positions_map(self, type_, positions1: List):
        if not positions1:
            return []
        tmp = self._positions_fields.loc[~self._positions_fields[type_].isna(), ["字段名", "类型", type_]]
        fields = tmp[type_].tolist()
        positions: pd.DataFrame = pd.DataFrame(positions1)[fields]
        positions.replace("", np.nan, inplace=True)
        positions = positions.astype(dict(zip(fields, tmp["类型"])))
        positions.rename(columns=dict(zip(fields, tmp["字段名"])), inplace=True)
        return [i.to_dict() for _, i in positions.iterrows()]


class Base:
    _session: EMSessionManager = None
    _field_mapper: FieldMapper = None

    def __init__(self, account, password) -> None:
        if Base._session is None:
            Base._session = EMSessionManager(account, password)
        if Base._field_mapper is None:
            Base._field_mapper = FieldMapper()

    def _calc_lots(self, code: str, price: float, money: float) -> int:
        """根据下单金额计算下单数量

        Args:
            code (str): 股票代码
            price (float): 下单价格
            money (float): 下单金额

        Returns:
            int: 下单数量
        """
        min_unit_cost = price * 200 if code[:2] == "68" else price * 100
        if min_unit_cost > money:
            return 0
        else:
            if code[:2] == "68":
                return int(money // price)
            else:
                return int(money / min_unit_cost) * 100


class EMTradeAPI(Base):
    def __init__(self, account, password) -> None:
        super().__init__(account, password)

    @property
    def portfolio(self) -> Dict:
        res = self._session.query_asset_and_position()
        res.pop("positions", None)
        return self._field_mapper.portfolio_map("普通", res)

    @property
    def positions(self) -> List[Dict]:
        res = self._session.query_asset_and_position()["positions"]
        return self._field_mapper.positions_map("普通", res)

    def order(
        self, stock_code: str, price: float, amount: Optional[int], money: Optional[float], trade_type: int
    ) -> Dict:
        """普通交易

        Args:
            stock_code (str): 证券代码, 6位
            price (float): 价格
            amount (Optional[int]): 数量
            money (Optional[float]): 金额, 以数量优先
            trade_type (int): 1 - 普通买入; 2 - 普通卖出

        Returns:
            Dict: 委托信息
        """
        if amount is None and money is None:
            raise ValueError("数量和金额不能同时为空")
        amount = amount if amount is not None else self._calc_lots(stock_code, price, money)
        res = self._session.submit_trade(stock_code, price, amount, trade_type)
        return {"合同序号": res["Htxh"], "委托编号": res["Wtbh"]}

    def cancel_order(self, wtbh):
        if not wtbh:
            raise ValueError("委托编号错误")
        self._session.revoke_orders("{}_{}".format(pd.Timestamp.now().strftime("%Y%m%d"), wtbh))

    def get_orders(self, st, et):
        res1, res2 = self._session.get_orders(st, et)
        res = self._field_mapper.orders_map("普通-当日委托", res1)
        res.extend(self._field_mapper.orders_map("普通-历史委托", res2))
        return res

    def get_transactions(self, st, et):
        res1, res2 = self._session.get_transactions(st, et)
        res = self._field_mapper.transactions_map("普通-当日成交", res1)
        res.extend(self._field_mapper.transactions_map("普通-历史成交", res2))
        return res


class EMCreditTradeAPI(Base):
    def __init__(self, account, password) -> None:
        super().__init__(account, password)

    @property
    def portfolio(self) -> Dict:
        res = self._session.get_rzrq_assets()
        return self._field_mapper.portfolio_map("信用", res)

    @property
    def positions(self) -> List[Dict]:
        res = self._session.query_collateral()
        return self._field_mapper.positions_map("信用", res)

    def order(
        self, stock_code: str, price: float, amount: Optional[int], money: Optional[float], trade_type: int
    ) -> Dict:
        """信用交易

        Args:
            stock_code (str): 证券代码, 6位
            price (float): 价格
            amount (Optional[int]): 数量
            money (Optional[float]): 金额, 以数量优先
            trade_type (int): 1 - 担保品买入; 2 - 担保品卖出; 3 - 融资买入; 4 - 融券卖出

        Returns:
            Dict: 委托信息
        """
        amount = amount if amount is not None else self._calc_lots(stock_code, price, money)
        res = self._session.submit_credit_trade(stock_code, price, amount, trade_type)
        return {"委托编号": res["Wtbh"]}

    def cancel_order(self, wtbh):
        if not wtbh:
            raise ValueError("委托编号错误")
        self._session.revoke_credit_orders("{}_{}".format(pd.Timestamp.now().strftime("%Y%m%d"), wtbh))

    def get_orders(self, st, et):
        res1, res2 = self._session.get_credit_orders(st, et)
        res = self._field_mapper.orders_map("信用-当日委托", res1)
        res.extend(self._field_mapper.orders_map("信用-历史委托", res2))
        return res

    def get_transactions(self, st, et):
        res1, res2 = self._session.get_credit_transations(st, et)
        res = self._field_mapper.transactions_map("信用-当日成交", res1)
        res.extend(self._field_mapper.transactions_map("信用-历史成交", res2))
        return res

    def get_can_credit_stk(self):
        res = self._session.query_can_credit_stk()
        return [
            {
                # "Enddate": "Enddate",
                "固定到期日": int(i["Expireddate"]),
                "交易市场": i["Market"],
                "可融券余额": float(i["Stkavl"]),
                "证券代码": i["Stkcode"],
                "证券名称": i["Stkname"],
                # "Type": "Type"
            }
            for i in res
        ]

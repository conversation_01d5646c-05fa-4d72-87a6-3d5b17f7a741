class LoginFailed(Exception):
    """登录失败"""

    def __init__(self, msg: str = ""):
        if msg:
            super().__init__(f"登录失败: {msg}")
        else:
            super().__init__("登录失败")


class HttpApiError(Exception):
    """Http接口发生错误"""

    def __init__(self):
        super().__init__("Http接口发生错误")


class NoResponse(Exception):
    """请求无正常响应"""

    def __init__(self):
        super().__init__("请求无正常响应")


class ResponseError(Exception):
    """请求返回错误码"""

    def __init__(self):
        super().__init__("请求返回错误码")


class SecurityAmbiguous(Exception):
    """不能确定唯一股票"""

    def __init__(self):
        super().__init__("不能确定唯一股票")


class VerifyUserInfoError(Exception):
    """与本机东财控件交互获取secInfo失败"""

    def __init__(self):
        super().__init__("与东财控件交互获取secInfo失败")


class SessionInvaluableError(Exception):
    """session不可用, 未初始化或者已过期"""

    def __init__(self):
        super().__init__("session不可用, 未初始化或者已过期")

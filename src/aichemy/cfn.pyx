import time

import cython
import numpy as np

@cython.boundscheck(False)
@cython.wraparound(False)
cdef double[:,:] c_calc_nearest_y_mean(double[:,:] key, double[:,:] value, double[:,:] query, double percent):
    cdef double[:,:] query_res = np.empty((query.shape[0], 1), dtype=np.float64)
    cdef int n_query = query.shape[0]
    cdef int n_key_value_pair = key.shape[0]
    cdef int n_features = key.shape[1]
    cdef size_t i, j, k
    cdef double[:] distance = key[:, 0].copy()
    cdef double tmp_sum, threshold
    cdef int tmp_count
    for i in range(n_query):
        for j in range(n_key_value_pair):
            tmp_sum = 0
            for k in range(n_features):
                tmp_sum += (key[j, k] - query[i, k]) ** 2
            distance[j] = tmp_sum ** 0.5
        threshold = np.percentile(distance, percent)
        tmp_sum = 0
        tmp_count = 0
        for j in range(n_key_value_pair):
            if distance[j] <= threshold:
                tmp_sum += value[j, 0]
                tmp_count += 1
        if tmp_count > 0:
            query_res[i, 0] = tmp_sum / tmp_count
        else:
            query_res[i, 0] = 0
    return query_res

@cython.boundscheck(False)
@cython.wraparound(False)
cdef double[:,:] c_calc_nearest_y_median(double[:,:] key, double[:,:] value, double[:,:] query, double percent):
    cdef double[:,:] query_res = np.empty((query.shape[0], 1), dtype=np.float64)
    cdef int n_query = query.shape[0]
    cdef int n_key_value_pair = key.shape[0]
    cdef int n_features = key.shape[1]
    cdef size_t i, j, k
    cdef double[:] distance = key[:, 0].copy()
    cdef double tmp_sum, threshold
    cdef int tmp_count
    cdef double[:] tmp_values
    for i in range(n_query):
        for j in range(n_key_value_pair):
            tmp_sum = 0
            for k in range(n_features):
                tmp_sum += (key[j, k] - query[i, k]) ** 2
            distance[j] = tmp_sum ** 0.5
        threshold = np.percentile(distance, percent)
        tmp_values = np.zeros(n_key_value_pair, dtype=np.float64)
        tmp_count = 0
        for j in range(n_key_value_pair):
            if distance[j] <= threshold:
                tmp_values[tmp_count] = value[j, 0]
                tmp_count += 1
        if tmp_count > 0:
            query_res[i, 0] = np.percentile(tmp_values[:tmp_count], 50)
        else:
            query_res[i, 0] = 0
    return query_res

def calc_nearest_y(key, value, query, percent, ty):
    if ty == 'mean':
        return np.array(c_calc_nearest_y_mean(key, value, query, percent))
    else:
        return np.array(c_calc_nearest_y_median(key, value, query, percent))

@cython.boundscheck(False)
@cython.wraparound(False)
cdef double[:,:] c_euclidean_distance_(double[:,:] key, double[:,:] query):
    cdef int n_query = query.shape[0]
    cdef int n_key_value_pair = key.shape[0]
    cdef int n_features = key.shape[1]
    cdef size_t i, j, k
    cdef double[:,:] distance = np.empty((n_query, n_key_value_pair), dtype=np.float64)
    cdef double tmp_sum 
    for i in range(n_query):
        for j in range(n_key_value_pair):
            tmp_sum = 0
            for k in range(n_features):
                tmp_sum += (key[j, k] - query[i, k]) ** 2
            distance[i, j] = tmp_sum ** 0.5
    return distance

def c_euclidean_distance(key, query):
    return np.array(c_euclidean_distance_(key, query))

@cython.boundscheck(False)
@cython.wraparound(False)
cdef double[:,:] c_quantile_mask_(double[:,:] distance, double percent):
    cdef int n_query = distance.shape[0]
    cdef int n_key_value_pair = distance.shape[1]
    cdef size_t i, j
    cdef double[:,:] ret = np.zeros((n_query, n_key_value_pair), dtype=np.float64)
    cdef double threshold 
    for i in range(n_query):
        threshold = np.percentile(distance[i, :], percent)
        for j in range(n_key_value_pair):
            if distance[i, j] < threshold:
                ret[i, j] = 1
    return ret

def c_quantile_mask(distance, percent):
    return np.array(c_quantile_mask_(distance, percent))

cdef double[:, :] c_filter_nan_sample_(double[:, :] array, long int num_step):
    cdef double[:, :] ret = np.ones((array.shape[0], array.shape[1]))
    cdef double[:, :] isnan = np.isnan(array).astype(np.float64)
    cdef long int i, j, k
    cdef double res    
    for j in range(0, array.shape[1]):
        res = 0
        for i in range(num_step - 1, array.shape[0]):
            if i < num_step - 1:
                continue
            elif i == num_step - 1:
                for k in range(num_step):
                    res = res + isnan[i - k, j]
            else:
                res = res - isnan[i - num_step, j] + isnan[i, j]
            ret[i, j] = res
    return ret

def c_filter_nan_sample(a, n):
    return ~np.array(c_filter_nan_sample_(a.astype(float),n)).astype(bool).flatten()
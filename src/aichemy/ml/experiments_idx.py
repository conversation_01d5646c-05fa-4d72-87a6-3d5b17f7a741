import math

import numpy as np
import torch
import torch.utils
from loguru import logger

from ..data_ops.pipeline import DataLocation
from ..factor_analyse.method import calc_ic, calc_rank_ic
from .exp_base import ExpBase
from .model.alpha_net import AlphaNetLinear, AlphaNetTimeMixer
from .model.angosformer import Angos<PERSON>
from .model.itransformer import Angos<PERSON> as iTransformer
from .model.timemixer import TimeMixer


class ExpTSPrd(ExpBase):
    def convert_x(self, x) -> torch.Tensor:
        return torch.tensor(x).float()

    def convert_y(self, y) -> torch.Tensor:
        return torch.tensor(y).float()

    def construct_batch_x(self, index: torch.Tensor, x_data_location: DataLocation) -> torch.Tensor:
        ret = x_data_location.locate(index)
        if "feature_length" in self.args:
            ret = ret[:, -self.args["feature_length"] :, :]
        if self.args.get("symbol_batch_norm", False):
            std = ret.std(dim=1, keepdim=True)
            std[std == 0] = 1e-8
            ret = (ret - ret.mean(dim=1, keepdim=True)) / std
        return ret

    def construct_batch_y(self, index: torch.Tensor, y_data_location: DataLocation) -> torch.Tensor:
        return y_data_location.locate(index)[:, [-1], :]

    def reverse_y(self, y: torch.Tensor) -> np.ndarray:
        return y[:, :, -1].squeeze(dim=1).numpy()

    def select_optimizer(self):
        return torch.optim.Adam(
            self.model.parameters(),
            lr=self.args.get("lr", 1e-4),
            betas=(self.args.get("beta1", 0.9), self.args.get("beta2", 0.999)),
            weight_decay=self.args.get("weight_decay", 1e-7),
        )

    def select_criterion(self):
        criterion = self.args.get("criterion", "mse")
        if criterion == "mse":
            return torch.nn.MSELoss()
        elif criterion == "mae":
            return torch.nn.L1Loss()
        else:
            raise NotImplementedError

    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        ic = calc_ic(y_hat[:, :, -1].flatten(), y[:, :, -1].flatten())
        rank_ic = calc_rank_ic(y_hat[:, :, -1].flatten(), y[:, :, -1].flatten())
        ic1 = []
        rank_ic1 = []
        n = math.ceil(y_hat.shape[0] / 1000)
        for i in range(n):
            ic1.append(calc_ic(y_hat[i::n, :, -1].flatten(), y[i::n, :, -1].flatten()))
            rank_ic1.append(calc_rank_ic(y_hat[i::n, :, -1].flatten(), y[i::n, :, -1].flatten()))
        ret = {flag: {"IC": ic, "RankIC": rank_ic, "IC1": np.mean(ic1), "RankIC1": np.mean(rank_ic1)}}
        logger.info(ret)
        return ret


class ExpAngosformer(ExpTSPrd):
    def build_model(self, input_size):
        net = Angosformer(
            input_size[-1],
            self.args.get("feature_length", input_size[1]),
            5,
            1,
            hidden_size=self.args.get("hidden_size", 64),
            ff_size=self.args.get("ff_size", 256),
            num_layers=self.args.get("num_layers", 4),
            heads=self.args.get("heads", 4),
            dropout_prob=self.args.get("dropout", 0.1),
            final_size=self.args.get("final_size", 16),
            scale=self.args.get("scale", 4),
        )
        return net


class ExpAngosformer1(ExpTSPrd):
    def build_model(self, input_size):
        net = Angosformer(
            input_size[-1],
            self.args.get("feature_length", input_size[1]),
            1,
            1,
            hidden_size=self.args.get("hidden_size", 64),
            ff_size=self.args.get("ff_size", 256),
            num_layers=self.args.get("num_layers", 4),
            heads=self.args.get("heads", 4),
            dropout_prob=self.args.get("dropout", 0.1),
            final_size=self.args.get("final_size", 16),
            scale=self.args.get("scale", 4),
        )
        return net


class ExpTimeMixer(ExpTSPrd):
    def build_model(self, input_size):
        net = TimeMixer(self.args.get("feature_length", input_size[1]), input_size[-1], 1, 5)
        return net


class ExpTimeMixer1(ExpTSPrd):
    def build_model(self, input_size):
        net = TimeMixer(
            self.args.get("feature_length", input_size[1]),
            input_size[-1],
            1,
            1,
            encoder_layers=self.args.get("encoder_layers", 4),
            sample_layers=self.args.get("sample_layers", 2),
        )
        return net


class ExpiTransformer(ExpTSPrd):
    def build_model(self, input_size):
        net = iTransformer(input_size[-1], self.args.get("feature_length", input_size[1]), 1, 1)
        return net


class ExpAlphaNetTimeMixer(ExpTSPrd):
    def build_model(self, input_size):
        net = AlphaNetTimeMixer(
            self.args.get("feature_length", input_size[1]),
            input_size[-1],
            1,
            1,
            encoder_layers=self.args.get("encoder_layers", 4),
            sample_layers=self.args.get("sample_layers", 2),
        )
        return net


class ExpAlphaNetLinear(ExpTSPrd):
    def build_model(self, input_size):
        net = AlphaNetLinear(
            self.args.get("feature_length", input_size[1]),
            input_size[-1],
            1,
            1,
            encoder_layers=self.args.get("encoder_layers", 4),
            sample_layers=self.args.get("sample_layers", 2),
        )
        return net


class ExpLinear(ExpTSPrd):
    def build_model(self, input_size):
        net = torch.nn.Linear(input_size[-1], 1)
        return net

    def construct_batch_x(self, index: torch.Tensor, x_data_location: DataLocation) -> torch.Tensor:
        return x_data_location.locate(index)[:, [-1], :]

import numpy as np
from ..cfn import calc_nearest_y, c_quantile_mask
from ..base import Model


def euclidean_distance(key, query):
    return np.sqrt(((np.expand_dims(key, 0) - np.expand_dims(query, 1)) ** 2).sum(axis=2))


def manhattan_distance(key, query):
    return np.abs(np.expand_dims(key, 0) - np.expand_dims(query, 1)).sum(axis=2)


def cosine_similarity(key, query):
    key = np.expand_dims(key, 0)
    query = np.expand_dims(query, 1)
    return (key * query).sum(axis=2) / (np.sqrt((key * key).sum(axis=2)) * np.sqrt((query * query).sum(axis=2)))


def gaussian_with_width(sigma):
    return lambda x: np.exp(-(x**2) / (2 * sigma**2))


def gaussian(x):
    return np.exp(-(x**2) / 2)


def constant(x):
    return 1


def softmax(x):
    t = np.exp(x)
    return t / t.sum(axis=1, keepdims=True)


def divide_sum(x):
    return x / x.sum(axis=1, keepdims=True)


def predict(query, key, value, percent, distance_fn, kernel_fn, weight_fn):
    distance = distance_fn(key, query)
    distance_mask = c_quantile_mask(distance, percent)
    weight = kernel_fn(distance)
    return np.dot(weight_fn(weight * distance_mask), value)


class NearestY(Model):
    def __init__(self, nearest_y_percent):
        self.nearest_y_percent = nearest_y_percent

        self.x_data = None
        self.y_data = None

    def fit(self, x_data, y_data):
        self.x_data = x_data
        self.y_data = y_data

    def predict(self, x, ty="mean"):
        if self.x_data is None or self.y_data is None:
            raise RuntimeError("Model has not been fitted")
        return calc_nearest_y(self.x_data, self.y_data, x, self.nearest_y_percent, ty)


class SplitDomain:
    def __init__(self, width, step, count_threshold, true_threshold=0.5):
        assert (100 - width) % step == 0
        self.width = width
        self.step = step
        self.count_threshold = count_threshold
        self.true_threshold = true_threshold

    def fit(self, x, y):
        a = np.arange(self.width, 100 + self.step, self.step)
        a = np.percentile(x, [a - self.width, a], axis=0)  # (2, n_percentile, n_features)

        def fnc(a, x, features_idx):
            for i in range(a.shape[1]):
                mask = (x[:, features_idx] >= a[0, i, features_idx]) & (x[:, features_idx] <= a[1, i, features_idx])
                if features_idx == x.shape[1] - 1:
                    yield mask, [i]
                else:
                    for tmp in fnc(a, x, features_idx + 1):
                        yield mask & tmp[0], [i] + tmp[1]

        result = np.zeros([a.shape[1]] * a.shape[2], dtype=np.float64)
        for mask, idx in fnc(a, x, 0):
            if np.sum(mask) >= self.count_threshold * x.shape[0]:
                result[idx] = y[..., 0][mask].mean()
            else:
                result[idx] = -np.inf

        if np.max(result) > self.true_threshold:
            self.valid = True
        else:
            self.valid = False
            return

        b = np.unravel_index(np.argmax(result), result.shape)
        self.bound = np.empty((a.shape[2], 2), dtype=np.float64)
        for i, j in enumerate(b):
            self.bound[i, 0] = a[0, j, i]
            self.bound[i, 1] = a[1, j, i]

    def predict(self, x):
        if not self.valid:
            return np.zeros((x.shape[0], 1), dtype=np.int64)

        mask = np.full(x.shape[0], True, dtype=np.bool_)
        for i in range(x.shape[1]):
            mask &= (x[:, i] >= self.bound[i, 0]) & (x[:, i] <= self.bound[i, 1])
        return mask.astype(np.int64).reshape(-1, 1)

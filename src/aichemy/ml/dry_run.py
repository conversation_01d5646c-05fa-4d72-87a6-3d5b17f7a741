from itertools import product
from typing import Iterable, Callable

import matplotlib.pyplot as plt
import pandas as pd
import torch
from IPython.display import clear_output, display
from tqdm import tqdm

from .exp_base import ExpBase


def determine_maximum_throughtput(
    exp_gen: Callable, num_threads: Iterable[int], batch_size_pow: Iterable[int], **kwargs
):
    """maximum batch size：超出后训练吞吐率不再增长。仅考虑在此之内的batch size\n
    critical batch size：在此之内，batch size和训练step保持perfect scaling\n
    在不运行任何实验的情况下，可以使用最大的batch size。"""
    ret = []
    kwargs["step_budget"] = kwargs.get("step_budget", 3000)
    for n, bs in tqdm(list(product(num_threads, batch_size_pow))):
        torch.set_num_threads(n)
        exp: ExpBase = exp_gen({"batch_size": 2**bs}).dry_run(**kwargs)
        try:
            exp.send(None)
        except StopIteration as e:
            throughput = e.value[0]
        ret.append(
            {
                "num_threads": n,
                "batch_size": str(2**bs),
                "throughput": throughput,
            }
        )
        if exp.device.type == "cuda":
            torch.cuda.empty_cache()
    pd.DataFrame(ret).set_index("batch_size").groupby("num_threads")["throughput"].plot(legend=True)
    return ret


def determine_batch_size(exp_gen: Callable, batch_size_pow: Iterable[int], **kwargs):
    """可以用不同batch size运行实验N个epoch，选择收敛速度最快的batch size"""
    ret = {}
    kwargs["step_budget"] = kwargs.get("step_budget", 3000)
    kwargs["vali_step"] = kwargs.get("vali_step", 500)
    for bs in tqdm(batch_size_pow):
        exp: ExpBase = exp_gen({"batch_size": 2**bs}).dry_run(**kwargs)
        try:
            exp.send(None)
        except StopIteration as e:
            loss = e.value[1]
        ret[str(bs)] = loss
        if exp.device.type == "cuda":
            torch.cuda.empty_cache()
    pd.DataFrame(ret).plot()
    return ret


def determine_epoch(exp_gen, lr, **kwargs):
    kwargs["step_budget"] = kwargs.get("step_budget", 0)
    kwargs["vali_step"] = kwargs.get("vali_step", 0)
    kwargs["vali_epoch"] = kwargs.get("vali_epoch", 1)
    gnt = {lr_: exp_gen({"lr": lr_}).dry_run(**kwargs) for lr_ in lr}
    while True:
        ret = {}
        for lr_, exp in gnt.items():
            ret[lr_] = exp.send(None)[1]
        ret = pd.DataFrame(ret)
        clear_output(wait=True)
        ret.plot()
        plt.show()
        display(ret)

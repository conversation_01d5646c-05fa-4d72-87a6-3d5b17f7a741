import inspect
import pathlib
import pickle
import time
from functools import wraps
from typing import Callable, Literal, Mapping, Optional, Tuple, Union

import numpy as np
import pandas as pd
import tomli_w
import torch
from loguru import logger
from optuna import TrialPruned
from torch import nn
from torch.optim.lr_scheduler import LinearLR, ReduceLROnPlateau
from torch.utils.data import DataLoader, Dataset, TensorDataset, random_split
from torch.utils.tensorboard.writer import SummaryWriter

from ..data_ops.pipeline import DataLocation
from ..utils import convert_seconds_to_hms

# torch.autograd.set_detect_anomaly(True)


class MetricsTracker:
    path: Optional[pathlib.Path]
    patience: Union[float, int]
    verbose: bool
    delta: float
    decide_metrics: Callable[[float, Mapping[str, float]], float]
    train_loss_series: pd.Series
    train_evl_series: pd.Series
    vali_loss_series: pd.Series
    vali_evl_series: pd.Series
    test_loss_series: pd.Series
    test_evl_series: pd.Series
    counter: int
    epoch: int
    lowest_vali_loss: float
    lowest_vali_loss_epoch: int
    best_vali_metrics: float
    best_vali_metrics_epoch: int

    def __init__(
        self,
        path: Union[str, pathlib.Path],
        patience: Union[float, int] = 7,
        verbose: bool = True,
        delta: float = 0,
        decide_metrics: Optional[Callable[[float, Mapping[str, float]], float]] = None,
    ):
        """EarlyStopping

        Args:
            path (Union[str, pathlib.Path]): model checkpoint的保存路径
            patience (Union[float, int], optional): 容忍的迭代次数. Defaults to 7.
            verbose (bool, optional): 是否输出日志. Defaults to True.
            delta (float, optional): 判断score是否提升的阈值. Defaults to 0.
            decide_metrics (Optional[Callable[[float, Mapping[str, float]], float]], optional): 决定模型效果的指标. Defaults to None.
        """
        self.path = pathlib.Path(path) if path else None
        self.patience = patience
        self.verbose = verbose
        self.delta = delta
        self.decide_metrics = decide_metrics or self._default_metrics

        self.train_loss_series = pd.Series({0: np.inf}, name="train_loss_series")
        self.train_evl_series = pd.Series(name="train_evl_series")
        self.vali_loss_series = pd.Series({0: np.inf}, name="vali_loss_series")
        self.vali_evl_series = pd.Series(name="vali_evl_series")
        self.test_loss_series = pd.Series({0: np.inf}, name="test_loss_series")
        self.test_evl_series = pd.Series(name="test_evl_series")

        self.counter = 0
        self.epoch = 0
        self.best_vali_metrics = -np.inf
        self.best_vali_metrics_epoch = 0
        self.lowest_vali_loss = np.inf
        self.lowest_vali_loss_epoch = 0

    @property
    def train_metrics(self) -> float:
        """返回最新一轮的训练指标"""
        if self.train_loss_series.empty:
            return np.nan
        return self.decide_metrics(self.train_loss_series.iloc[-1], self.train_evl_series.iloc[-1])

    @property
    def vali_metrics(self) -> float:
        """返回最新一轮的验证指标"""
        if self.vali_loss_series.empty:
            return np.nan
        return self.decide_metrics(self.vali_loss_series.iloc[-1], self.vali_evl_series.iloc[-1])

    @property
    def test_metrics(self) -> float:
        """返回最新一轮的测试指标"""
        if self.test_loss_series.empty:
            return np.nan
        return self.decide_metrics(self.test_loss_series.iloc[-1], self.test_evl_series.iloc[-1])

    @staticmethod
    def _default_metrics(loss: float, evl: Mapping[str, float]) -> float:
        matrics = -loss
        return matrics

    def is_early_stop(self) -> bool:
        if self.counter >= self.patience:
            return True
        return False

    def append(
        self, epoch: int, loss: float, evl: Mapping[str, float], flag: Literal["train", "vali", "test"] = "vali"
    ) -> None:
        """训练记录器

        Args:
            epoch (int): 当前轮次
            loss (float): 损失
            evl (Mapping[str, float]): 评价指标
            flag (Literal["train", "vali", "test"], optional): 训练阶段. Defaults to "vali".

        """
        if epoch != self.epoch + 1:
            raise RuntimeError(f"{self.__class__.__name__} - 当前epoch与之前epoch不匹配")
        else:
            self.epoch = epoch

        if flag == "vali":
            self.vali_loss_series[epoch] = loss
            self.vali_evl_series[epoch] = evl

            if loss < self.lowest_vali_loss - self.delta:
                if self.verbose:
                    logger.info(
                        f"Epoch {epoch}: Validation loss decreased ({self.lowest_vali_loss:.6f} --> {loss:.6f})."
                    )
                self.lowest_vali_loss = loss
                self.lowest_vali_loss_epoch = epoch
                self.counter = 0
            else:
                self.counter += 1
                if self.verbose:
                    logger.warning(
                        f"Epoch {epoch}: EarlyStopping counter: {self.counter} out of {self.patience:.0f}, \
The lowest vali loss is {self.lowest_vali_loss:.6f}"
                    )

            vali_metrics = self.vali_metrics
            if vali_metrics >= self.best_vali_metrics:
                if self.verbose:
                    logger.info(
                        f"Epoch {epoch}: Validation Metrics increased ({self.best_vali_metrics:.6f} --> {vali_metrics:.6f})."
                    )
                self.best_vali_metrics = vali_metrics
                self.best_vali_metrics_epoch = epoch

        elif flag == "train":
            self.train_loss_series[epoch] = loss
            self.train_evl_series[epoch] = evl

        elif flag == "test":
            self.test_loss_series[epoch] = loss
            self.test_evl_series[epoch] = evl

        self.save()

    def save(self):
        """保存策略是只有模型效果提升了才保存"""
        if not self.path:
            return
        with open(pathlib.Path(self.path, f"{self.__class__.__name__}.pkl"), "wb") as f:
            pickle.dump(self, f)
        pd.concat(
            [
                self.train_loss_series,
                self.train_evl_series,
                self.vali_loss_series,
                self.vali_evl_series,
                self.test_loss_series,
                self.test_evl_series,
            ],
            axis=1,
        ).to_json(
            path_or_buf=pathlib.Path(self.path, f"{self.__class__.__name__}.json"), orient="index", double_precision=10
        )

    # TODO
    @classmethod
    def load(cls, path: Union[str, pathlib.Path]) -> "MetricsTracker":
        with open(pathlib.Path(path, f"{cls.__name__}.pkl"), "rb") as f:
            tmp = pickle.load(f)
        tmp.counter = 0
        tmp.is_early_stop = False
        return tmp


def _load_toml(file):
    try:
        import tomllib as tomllib
    except ImportError:
        import tomli as tomllib

    with open(file, "rb") as f:
        args = tomllib.load(f)
    return args


def _wapper(fnc):
    sig = inspect.signature(fnc)

    @wraps(fnc)
    def wrapper(self, *args, **kwargs):
        try:
            if "enable_log" in sig.parameters:
                if not sig.bind(self, *args, **kwargs).arguments["enable_log"]:
                    logger.disable("aichemy.ml.exp_base")
            return fnc(self, *args, **kwargs)
        finally:
            if self.device.type == "cuda":
                torch.cuda.empty_cache()
            if "enable_log" in sig.parameters:
                if not sig.bind(self, *args, **kwargs).arguments["enable_log"]:
                    logger.enable("aichemy.ml.exp_base")

    return wrapper


class ExpBase:
    def __init__(self, path: Union[str, pathlib.Path], args: Optional[Union[Mapping, str, pathlib.Path]] = None):
        """Experiment基类

        Args:
            path (Union[str, pathlib.Path]): model checkpoint的保存路径
            args (Optional[Union[Mapping, str, pathlib.Path]], optional): 参数或参数文件路径. Defaults to None.
        """
        self.path = path
        if not (tmp := pathlib.Path(self.path, "checkpoints")).exists():
            tmp.mkdir(parents=True, exist_ok=True)

        self.args = {}
        self.update_args(args)
        self.device = self._acquire_device()

        self.model: nn.Module
        self.writer: SummaryWriter
        self.test_size: float
        self.input_size: Tuple
        """输入为1时的形状, 如(1, num_steps, num_features) or (1, num_features)"""

        self.data_load_mode: int
        self.train_loader: DataLoader
        self.vali_loader: DataLoader
        self.test_loader: Optional[DataLoader]
        self.data_x: DataLocation
        self.data_y: DataLocation
        self.matrics_tracker: MetricsTracker

    def update_args(self, args: Optional[Union[Mapping, str, pathlib.Path]] = None):
        """加载参数"""
        if args is None:
            args = _load_toml("model.toml")
        else:
            if isinstance(args, (str, pathlib.Path)):
                args = _load_toml(args)
            else:
                args = args
        self.args.update(args)
        if "device" in self.args:
            self.device = self._acquire_device()

    def export(self, path):
        torch.onnx.export(
            self.model,
            torch.randn(*self.input_size).to(self.device),
            path,
            verbose=True,
            input_names=["input"],
            dynamic_axes={
                "input": [0],  # 第0维是batch dimension
            },
        )

    def _acquire_device(self) -> torch.device:
        device = self.args.get("device", None)
        if device is not None:
            return torch.device(device)
        return torch.device("cuda" if torch.cuda.is_available() else "cpu")

    def _get_data_loader(self, flag: str, data_set: Dataset) -> DataLoader:
        batch_size = self.args["batch_size"]
        if flag == "train":
            shuffle_flag = True
            drop_last = False
        else:
            shuffle_flag = False
            drop_last = False
            # batch_size = 40960  # bsz=1 for evaluation
        return DataLoader(
            data_set,
            batch_size=batch_size,
            shuffle=shuffle_flag,
            num_workers=self.args.get("num_workers", 1),
            drop_last=drop_last,
        )

    def load_data(
        self,
        train_dataset: Optional[Dataset] = None,
        vali_dataset: Optional[Dataset] = None,
        test_dataset: Optional[Dataset] = None,
        **kwargs,
    ):
        """加载数据

        Args:
            train_dataset (Optional[Dataset], optional): 训练数据，None则从磁盘加载. Defaults to None.
            vali_dataset (Optional[Dataset], optional): 验证数据，None则从磁盘加载. Defaults to None.
            test_dataset (Optional[Dataset], optional): 测试数据，None则从磁盘加载. Defaults to None.
        """
        dataset_path = kwargs.get("dataset_path", self.path)
        if not hasattr(self, "test_size"):
            if "test_size" in kwargs:
                self.test_size = kwargs["test_size"]
            else:
                raise ValueError("test_size must be provided")

        if not hasattr(self, "data_load_mode"):
            if "data_load_mode" in kwargs:
                self.data_load_mode = kwargs["data_load_mode"]
            else:
                raise ValueError("data_load_mode must be provided")

        if train_dataset is None:  # 如果传入的training_dataset为None，则从磁盘加载
            train_dataset_: Dataset = torch.load(pathlib.Path(dataset_path, "train_dataset.pt"), weights_only=False)
        else:  # 如果传入的training_dataset不为None，且dump为True，则保存到磁盘
            train_dataset_: Dataset = train_dataset
        self.train_loader = self._get_data_loader("train", train_dataset_)

        if vali_dataset is None:
            vali_dataset_ = torch.load(pathlib.Path(dataset_path, "vali_dataset.pt"), weights_only=False)
        else:
            vali_dataset_ = vali_dataset
        self.vali_loader = self._get_data_loader("vali", vali_dataset_)

        if test_dataset is None:
            if (path := pathlib.Path(dataset_path, "test_dataset.pt")).exists():
                test_dataset_ = torch.load(path, weights_only=False)
                self.test_loader = self._get_data_loader("test", test_dataset_)
            else:
                self.test_loader = None
        else:
            self.test_loader = self._get_data_loader("test", test_dataset)

        x, *_ = train_dataset_[:]
        if self.data_load_mode == 1:
            self.input_size = x[[0]].size()
        else:
            if not hasattr(self, "data_x"):
                self.data_x = torch.load(pathlib.Path(dataset_path, "data_x.pt"), weights_only=False)
            if not hasattr(self, "data_y"):
                self.data_y = torch.load(pathlib.Path(dataset_path, "data_y.pt"), weights_only=False)
            self.input_size = (1, x.size()[-1], self.data_x.data.shape[-1])

    def load_model(self, map_location=None, **kwargs):
        """如果传入了model，则直接加载model；否则从path加载，先加载model.pt，如果没有model.pt，则从checkpoint.pth加载"""
        if kwargs.get("model", None) is not None:
            self.model = torch.load(kwargs["model"], weights_only=False).to(self.device)
            return self.model
        if (path := pathlib.Path(self.path, "model.pt")).exists():
            self.model = torch.load(path, weights_only=False).to(self.device)
            return self.model
        map_location = map_location if map_location is None else torch.device(map_location)
        checkpoint = torch.load(
            pathlib.Path(self.path, "checkpoint.pth"), map_location=map_location, weights_only=False
        )
        self.model = self.build_model(checkpoint.get("input_size", kwargs.get("input_size", None))).to(self.device)
        self.model.load_state_dict(checkpoint["model_state_dict"])
        return self.model

    def dry_run(self, step_budget: int, vali_step: int = 0, vali_epoch: int = 1, vali_dataset: str = "train"):
        """用于选择最优batch size

        Args:
            step_budget (int): 训练步数
            vali_step (int, optional): 每训练多少步验证一次. Defaults to 0.
            vali_epoch (int, optional): 每训练多少轮验证一次. Defaults to 1.
            vali_dataset (str, optional): 用于验证的数据集，train - 训练集，vali - 验证集. Defaults to "train".

        Returns:
            Tuple[float, List[float]]: 训练吞吐量，训练损失

        Yields:
            Tuple[None, List[float]]: 训练吞吐量，训练损失
        """
        # 初始化
        self.model = self.build_model(self.input_size).to(self.device)
        criterion = self.select_criterion()
        model_optim = self.select_optimizer()

        # 开始训练
        begin_time = time.time()
        all_examples_processed = 0
        finished_step = 0
        finished_epoch = 0
        train_loss_lst = []
        while True:
            self.model.train()
            for batch in self.train_loader:
                # 模型计算求loss
                if self.data_load_mode == 1:
                    batch_x, batch_y = batch
                    batch_x = batch_x.to(self.device)
                    batch_y = batch_y.to(self.device)
                elif self.data_load_mode == 2:
                    batch_index, *_ = batch
                    batch_x = self.construct_batch_x(batch_index, self.data_x).to(self.device)
                    batch_y = self.construct_batch_y(batch_index, self.data_y).to(self.device)
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)

                # 梯度求导，反向传播
                loss.backward()

                model_optim.step()
                model_optim.zero_grad()

                all_examples_processed += len(batch_y)
                finished_step += 1

                if step_budget > 0:
                    if bool(vali_step) and finished_step % vali_step == 0:
                        if vali_dataset == "train":
                            train_loss, _ = self.vali(self.train_loader, criterion, "train")
                            train_loss_lst.append(train_loss)
                        else:
                            train_loss, _ = self.vali(self.vali_loader, criterion, "vali")
                            train_loss_lst.append(train_loss)

                    if finished_step >= step_budget:
                        consumed_time = time.time() - begin_time
                        training_throughput = all_examples_processed / consumed_time  # 每秒训练样本数
                        return training_throughput, train_loss_lst

            finished_epoch += 1

            if step_budget == 0:
                if finished_epoch % vali_epoch == 0:
                    if vali_dataset == "train":
                        train_loss, _ = self.vali(self.train_loader, criterion, "train")
                        train_loss_lst.append(train_loss)
                    else:
                        train_loss, _ = self.vali(self.vali_loader, criterion, "vali")
                        train_loss_lst.append(train_loss)
                    yield None, train_loss_lst

    @_wapper
    def train(
        self,
        enable_log=True,
        progress_log: int = 5,
        tb_log_dir: Optional[str] = None,
        tb_comment="",
        resume=0,
        trial=None,
        decide_metrics: Optional[Callable] = None,
        need_vali_trainset: bool = False,
    ) -> nn.Module:
        """训练模型

        Args:
            enable_log (bool, optional): 是否输出日志. Defaults to True.
            progress_log (int, optional): 是否根据每轮训练的进度打印日志，0为不打印. Defaults to 5.
            tensorboard_comment (str, optional): tensorboard的comment. Defaults to "".
            resume (int, optional): 是否从历史模型继续训练，0为不继续训练. Defaults to 0.
            trial (_type_, optional): optuna的trial. Defaults to None.
            decide_metrics (Optional[Callable], optional): 决定模型效果的指标. Defaults to None.
            need_vali_trainset (bool, optional): 是否对训练集进行验证. Defaults to False.

        Raises:
            TrialPruned: optuna的TrialPruned

        Returns:
            nn.Module: 训练好的模型
        """
        self.writer = SummaryWriter(log_dir=tb_log_dir, comment=tb_comment)
        with open(pathlib.Path(self.path, "args.toml"), "wb") as f:
            tomli_w.dump(self.args, f)

        train_epochs = self.args["train_epochs"]
        accumulation_steps = self.args.get("accumulation_steps", 1)
        clip_grad_norm = self.args.get("clip_grad_norm", 0.0)
        is_clip_grad = clip_grad_norm > 0.0
        warm_up_step_p = self.args.get("warm_up_step", 0.0)
        is_warm_up = warm_up_step_p > 0.0

        # 初始化
        train_steps = len(self.train_loader)
        self.model = self.build_model(self.input_size).to(self.device)
        criterion = self.select_criterion()
        model_optim = self.select_optimizer()
        if is_warm_up:
            warm_up_step = int(train_steps * train_epochs * warm_up_step_p)
            warm_up_lr_scheduler = LinearLR(model_optim, start_factor=1 / warm_up_step, total_iters=warm_up_step - 1)
        scheduler2 = ReduceLROnPlateau(
            model_optim, factor=self.args["factor"], patience=self.args["patience"], min_lr=1e-6
        )
        # scheduler2 = ReduceLROnPlateau(model_optim, factor=0.5, patience=2, min_lr=1e-6)

        # 如果reload为True，则加载历史模型
        if bool(resume):
            self.matrics_tracker = MetricsTracker.load(self.path)
            checkpoint = torch.load(pathlib.Path(self.path, "checkpoint.pth"), weights_only=False)
            start_epoch = checkpoint["finished_epoch"]
            train_epochs = start_epoch + resume

            self.model.load_state_dict(checkpoint["model_state_dict"])
            model_optim.load_state_dict(checkpoint["optimizer_state_dict"])
            if is_warm_up:
                warm_up_lr_scheduler.load_state_dict(checkpoint["warm_up_lr_scheduler_state_dict"])
            scheduler2.load_state_dict(checkpoint["scheduler2_state_dict"])

        else:
            self.matrics_tracker = MetricsTracker(
                self.path,
                patience=self.args.get("early_stop_patience", np.inf),
                verbose=True,
                decide_metrics=decide_metrics or getattr(self, "decide_metrics", None),
            )
            checkpoint = {"finished_epoch": 0, "input_size": self.input_size}
            start_epoch = 0

        # 开始训练
        training_time_secs = 0
        begin_time = time.time()
        all_examples_processed = 0
        for epoch in range(start_epoch, train_epochs):
            acc_loss_sum, acc_loss_num, total_loss_num = 0, 0, 0
            train_loss = []
            epoch_begin_time = time.time()

            self.model.train()
            for i, batch in enumerate(self.train_loader):
                # 模型计算求loss
                if self.data_load_mode == 1:
                    batch_x, batch_y = batch
                    batch_x = batch_x.to(self.device)
                    batch_y = batch_y.to(self.device)
                elif self.data_load_mode == 2:
                    batch_index, *_ = batch
                    batch_x = self.construct_batch_x(batch_index, self.data_x).to(self.device)
                    batch_y = self.construct_batch_y(batch_index, self.data_y).to(self.device)
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)

                # 梯度累加
                batch_num = len(batch_y)
                acc_loss_sum += loss * batch_num
                acc_loss_num += batch_num
                total_loss_num += batch_num
                all_examples_processed += batch_num

                # 反向传播，梯度裁剪，更新参数
                if accumulation_steps == 1 or (i + 1) % accumulation_steps == 0 or (i + 1 == train_steps):
                    acc_loss_mean = acc_loss_sum / acc_loss_num
                    train_loss.append(acc_loss_sum.item())

                    # 梯度求导，反向传播
                    acc_loss_mean.backward()

                    # 梯度裁剪，更新参数
                    if is_clip_grad:
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), clip_grad_norm)
                    model_optim.step()
                    model_optim.zero_grad()

                    # lr scheduler step
                    if is_warm_up and warm_up_lr_scheduler.last_epoch < warm_up_step - 1:
                        lr1 = model_optim.param_groups[0]["lr"]
                        warm_up_lr_scheduler.step()
                        lr2 = model_optim.param_groups[0]["lr"]

                    acc_loss_sum, acc_loss_num = 0, 0

                # 打印当前进度
                if bool(progress_log) and int(i / train_steps * 100 / progress_log) != int(
                    (progress := (i + 1) / train_steps) * 100 / progress_log
                ):
                    this_epoch_consumed_time = time.time() - epoch_begin_time
                    training_throughput = all_examples_processed / (
                        this_epoch_consumed_time + training_time_secs
                    )  # 每秒训练样本数
                    left_time = this_epoch_consumed_time / (i + 1) * (train_steps - i - 1)
                    logger.info(
                        "\tepoch: {} / {}, progress: {: >3.0%} | loss: {:.6f} | training throughput: {:.2f}, this epoch remaining time: {}".format(
                            epoch + 1,
                            train_epochs,
                            progress,
                            np.sum(train_loss) / total_loss_num,
                            training_throughput,
                            convert_seconds_to_hms(left_time),
                        )
                    )

            training_time_secs += time.time() - epoch_begin_time

            # 每个epoch结束后进行模型评估
            train_loss = np.sum(train_loss) / total_loss_num
            if need_vali_trainset:
                train_loss, train_evl = self.vali(self.train_loader, criterion, "train")
            vali_loss, vali_evl = self.vali(self.vali_loader, criterion, "vali")
            if self.test_size > 0 and self.test_loader is not None:
                test_loss, test_evl = self.vali(self.test_loader, criterion, "test")

            # region: 每一个epoch结束记录日志和指标
            # 打印日志
            logger.success(
                "Epoch: {}, Steps: {}, elapsed time: {}, remaining time: {} | Train Loss: {:.6f} Vali Loss: {:.6f} {}".format(
                    epoch + 1,
                    train_steps,
                    convert_seconds_to_hms(time.time() - begin_time),
                    convert_seconds_to_hms((time.time() - epoch_begin_time) / (epoch + 1) * (train_epochs - epoch - 1)),
                    train_loss,
                    vali_loss,
                    ("Test Loss: {:.6f}".format(test_loss) if self.test_size > 0 else ""),
                )
            )

            # tensorboard输出
            # 先记录loss
            self.writer.add_scalar("train/loss", train_loss, epoch + 1)
            self.writer.add_scalar("vali/loss", vali_loss, epoch + 1)
            if self.test_size > 0:
                self.writer.add_scalar("test/loss", test_loss, epoch + 1)
            # 再记录其他指标
            for k in vali_evl["vali"].keys():
                self.writer.add_scalar("vali/{}".format(k), vali_evl["vali"][k], epoch + 1)
                if self.test_size > 0:
                    self.writer.add_scalar("test/{}".format(k), test_evl["test"][k], epoch + 1)
            # endregion

            # lr scheduler step
            if not (is_warm_up and warm_up_lr_scheduler.last_epoch < warm_up_step - 1):
                lr1 = model_optim.param_groups[0]["lr"]
                scheduler2.step(vali_loss)
                lr2 = model_optim.param_groups[0]["lr"]
                if lr1 != lr2:
                    logger.info("lr changed from {} to {}.".format(lr1, lr2))

            if need_vali_trainset:
                self.matrics_tracker.append(epoch + 1, train_loss, train_evl["train"], "train")
            if self.test_size > 0 and self.test_loader is not None:
                self.matrics_tracker.append(epoch + 1, test_loss, test_evl["test"], "test")
            # 根据验证集loss，判断模型是否在提升，如果提升就保存模型，否则判断是否early stop
            self.matrics_tracker.append(epoch + 1, vali_loss, vali_evl["vali"], "vali")

            # optuna记录
            if trial is not None:
                trial.report(self.matrics_tracker.vali_metrics, epoch)
                if trial.should_prune():
                    raise TrialPruned()

            checkpoint.update(
                {
                    "finished_epoch": epoch + 1,
                    "model_state_dict": self.model.state_dict(),
                    "optimizer_state_dict": model_optim.state_dict(),
                    "scheduler2_state_dict": scheduler2.state_dict(),
                    "loss": {"train": train_loss, "vali": vali_loss},
                    "evaluate": {"vali": vali_evl},
                }
            )
            if is_warm_up:
                checkpoint["warm_up_lr_scheduler_state_dict"] = warm_up_lr_scheduler.state_dict()
            if self.test_size > 0 and self.test_loader is not None:
                checkpoint["loss"]["test"] = test_loss
                checkpoint["evaluate"]["test"] = test_evl
            torch.save(checkpoint, pathlib.Path(self.path, "checkpoints", f"checkpoint_{epoch + 1}.pth"))
            torch.save(self.model, pathlib.Path(self.path, "checkpoints", f"model_{epoch + 1}.pt"))
            logger.success(f"epoch {epoch + 1}: model saved.")

            if self.matrics_tracker.is_early_stop():
                logger.warning("Early stopping")
                break

            logger.info("-" * 40)

        self.writer.close()

        checkpoint = torch.load(
            pathlib.Path(self.path, "checkpoints", f"checkpoint_{self.matrics_tracker.best_vali_metrics_epoch}.pth"),
            weights_only=False,
        )
        torch.save(checkpoint, pathlib.Path(self.path, "checkpoint.pth"))
        self.model.load_state_dict(checkpoint["model_state_dict"])
        torch.save(self.model, pathlib.Path(self.path, "model.pt"))

        logger.success(
            "{}\nFinally:  Train Loss: {:.6f} Vali Loss: {:.6f} {} | Vali Evl: {} {}".format(
                "=" * 40,
                checkpoint["loss"]["train"],
                checkpoint["loss"]["vali"],
                "Test Loss: {:.6f}".format(checkpoint["loss"]["test"]) if self.test_size > 0 else "",
                checkpoint["evaluate"]["vali"],
                "Test Evl: {}".format(checkpoint["evaluate"]["test"]) if self.test_size > 0 else "",
            )
        )

        return self.model

    def vali(
        self, vali_loader: DataLoader, criterion: nn.Module, flag: str = "vali"
    ) -> Tuple[float, Mapping[str, Mapping[str, float]]]:
        self.model.eval()
        total_loss, y_hat, y_true = [], [], []
        with torch.no_grad():
            for batch in vali_loader:
                if self.data_load_mode == 1:
                    batch_x, batch_y = batch
                    batch_x = batch_x.to(self.device)
                    batch_y = batch_y.to(self.device)
                elif self.data_load_mode == 2:
                    batch_index, *_ = batch
                    batch_x = self.construct_batch_x(batch_index, self.data_x).to(self.device)
                    batch_y = self.construct_batch_y(batch_index, self.data_y).to(self.device)
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                total_loss.append(loss.item() * len(batch_x))
                y_hat.append(outputs.detach().cpu().numpy())
                y_true.append(batch_y.detach().cpu().numpy())
            evl_res = self.evaluate(np.concatenate(y_hat, axis=0), np.concatenate(y_true, axis=0), flag)
        return np.sum(total_loss) / len(vali_loader.dataset), evl_res  # type: ignore

    @_wapper
    def predict(self, pred_x: np.ndarray, index: Optional[np.ndarray] = None):
        preds = []
        self.model.eval()
        with torch.no_grad():
            if index is None:
                x_dataloader = self._get_data_loader("pred", TensorDataset(self.convert_x(pred_x)))
                for (batch_x,) in x_dataloader:
                    batch_x = batch_x.to(self.device)
                    y_hat = self.model(batch_x)
                    preds.append(self.reverse_y(y_hat.detach().cpu()))
            else:
                index_dataloader = self._get_data_loader("pred", TensorDataset(torch.tensor(index)))
                x_data_location = DataLocation(self.convert_x(pred_x))
                for (batch_index,) in index_dataloader:
                    batch_x = self.construct_batch_x(batch_index, x_data_location).to(self.device)
                    y_hat = self.model(batch_x)
                    preds.append(self.reverse_y(y_hat.detach().cpu()))
        return np.concatenate(preds, axis=0)

    def _split_non_training_dataset(
        self, non_training_dataset: Dataset, test_size
    ) -> Tuple[Dataset, Optional[Dataset]]:
        self.test_size = test_size
        if self.test_size > 0:
            non_training_dataset_size = len(non_training_dataset)  # type: ignore
            vali_dataset, test_dataset = random_split(
                non_training_dataset,
                [
                    vali_size := int(non_training_dataset_size * (1 - self.test_size)),
                    non_training_dataset_size - vali_size,
                ],
            )
            return vali_dataset, test_dataset
        else:
            return non_training_dataset, None

    def construct_dataset(
        self,
        training_x: np.ndarray,
        non_training_x: np.ndarray,
        training_y: np.ndarray,
        non_training_y: np.ndarray,
        test_size: float = 0.5,
        dump: bool = False,
    ) -> Tuple[Dataset, Dataset, Optional[Dataset]]:
        """构造训练集和测试集

        Args:
            training_x (np.ndarray): 训练集x
            non_training_x (np.ndarray): 非训练集x
            training_y (np.ndarray): 训练集y
            non_training_y (np.ndarray): 非训练集y
            test_size (float, optional): 测试集比例. Defaults to 0.5.
            dump (bool, optional): 是否保存数据. Defaults to False.

        Returns:
            Tuple[Dataset, Dataset, Optional[Dataset]]: 返回训练集, 验证集, 测试集, 具体数据
        """
        self.data_load_mode = 1

        train_x_tensor = self.convert_x(training_x)
        test_x_tensor = self.convert_x(non_training_x)
        train_y_tensor = self.convert_y(training_y)
        test_y_tensor = self.convert_y(non_training_y)

        train_dataset = TensorDataset(train_x_tensor, train_y_tensor)
        vali_dataset, test_dataset = self._split_non_training_dataset(
            TensorDataset(test_x_tensor, test_y_tensor), test_size
        )
        if dump:
            torch.save(train_dataset, pathlib.Path(self.path, "train_dataset.pt"))
            torch.save(vali_dataset, pathlib.Path(self.path, "vali_dataset.pt"))
            if test_dataset is not None:
                torch.save(test_dataset, pathlib.Path(self.path, "test_dataset.pt"))
        return train_dataset, vali_dataset, test_dataset

    def construct_index_dataset(
        self,
        training_index: np.ndarray,
        non_training_index: np.ndarray,
        data_x: np.ndarray,
        data_y: np.ndarray,
        test_size: float = 0.5,
        dump: bool = False,
    ) -> Tuple[Dataset, Dataset, Optional[Dataset]]:
        """构造训练集和测试集的索引

        Args:
            training_index (np.ndarray): 训练集索引
            non_training_index (np.ndarray): 非训练集索引
            data_x (np.ndarray): 全部数据x, (n_samples, n_features)
            data_y (np.ndarray): 全部数据y, (n_samples, n_targets)
            test_size (float, optional): 测试集比例. Defaults to 0.5.
            dump (bool, optional): 是否保存数据. Defaults to False.

        Returns:
            Tuple[Dataset, Dataset]: 返回训练集, 验证集, 测试集，索引
        """
        self.data_load_mode = 2

        self.data_x = DataLocation(self.convert_x(data_x))
        self.data_y = DataLocation(self.convert_y(data_y))

        train_index_dataset = TensorDataset(torch.tensor(training_index))
        vali_index_dataset, test_index_dataset = self._split_non_training_dataset(
            TensorDataset(torch.tensor(non_training_index)), test_size
        )
        if dump:
            torch.save(train_index_dataset, pathlib.Path(self.path, "train_dataset.pt"))
            torch.save(vali_index_dataset, pathlib.Path(self.path, "vali_dataset.pt"))
            if test_index_dataset is not None:
                torch.save(test_index_dataset, pathlib.Path(self.path, "test_dataset.pt"))
            torch.save(self.data_x, pathlib.Path(self.path, "data_x.pt"))
            torch.save(self.data_y, pathlib.Path(self.path, "data_y.pt"))

        return train_index_dataset, vali_index_dataset, test_index_dataset

    def convert_x(self, x: np.ndarray) -> torch.Tensor:
        """把np.ndarray的x 转换为torch.Tensor, 在构建dataset时使用, 不是在train时构建batch使用"""
        raise NotImplementedError

    def convert_y(self, y) -> torch.Tensor:
        """把np.ndarray的y 转换为torch.Tensor, 在构建dataset时使用, 不是在train时构建batch使用"""
        raise NotImplementedError

    def construct_batch_x(self, index: torch.Tensor, x_data_location: DataLocation) -> torch.Tensor:
        """根据每一个batch的index，构建对应batch的x"""
        raise NotImplementedError

    def construct_batch_y(self, index: torch.Tensor, y_data_location: DataLocation) -> torch.Tensor:
        """根据每一个batch的index，构建对应batch的y"""
        raise NotImplementedError

    def build_model(self, input_size) -> nn.Module:
        """构建模型"""
        raise NotImplementedError

    def select_criterion(self) -> nn.Module:
        """选择损失函数"""
        raise NotImplementedError

    def select_optimizer(self) -> torch.optim.Optimizer:
        """选择优化器"""
        raise NotImplementedError

    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag="vali") -> Mapping[str, Mapping]:
        """评估模型"""
        raise NotImplementedError

    def reverse_y(self, y: torch.Tensor) -> np.ndarray:
        raise NotImplementedError

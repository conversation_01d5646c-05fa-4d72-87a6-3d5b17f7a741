from typing import Generator, Optional, Tuple, Union

import numpy as np
import torch
from sklearn.model_selection import KFold
from sklearn.preprocessing import StandardScaler
from torch.utils.data.dataloader import DataLoader
from torch.utils.data.dataset import TensorDataset

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


def tensorize(
    train_x, test_x, train_y, test_y
) -> Tuple[Union[torch.Tensor, Tuple], Union[torch.Tensor, Tuple], torch.Tensor, torch.Tensor]:
    """要求传入train_x, test_x, train_y, test_y，根据测试集进行标准化，最后张量化

    Returns:
        Tuple[Union[torch.Tensor, Tuple], Union[torch.Tensor, Tuple], torch.Tensor, torch.Tensor]: _description_
    """
    assert type(train_x) == type(test_x) and type(train_y) == type(test_y)
    if train_x is None:
        train_x, test_x = None, None
    elif isinstance(train_x, tuple):
        train_x, test_x, *_ = zip(*[tensorize(train_x[i], test_x[i], None, None) for i in range(len(train_x))])
    else:
        tmp_shape1 = train_x.shape
        train_x = train_x.reshape((-1, tmp_shape1[-1]))
        tmp_shape2 = test_x.shape
        test_x = test_x.reshape((-1, tmp_shape2[-1]))

        standard_scaler2 = StandardScaler()
        standard_scaler2.fit(train_x)
        train_x = standard_scaler2.transform(train_x)
        test_x = standard_scaler2.transform(test_x)

        train_x = torch.tensor(train_x.reshape(tmp_shape1)).float().to(device)
        test_x = torch.tensor(test_x.reshape(tmp_shape2)).float().to(device)

    if train_y is None:
        train_y, test_y = None, None
    else:
        train_y = torch.tensor(train_y).to(device)
        test_y = torch.tensor(test_y).to(device)
    return train_x, test_x, train_y, test_y


class Estimator:
    def __init__(self, net, loss, trainer, batch_size=4096, max_norm=0.3):
        self.net: torch.nn.Module = net
        self.loss: torch.nn.Module = loss
        self.trainer: torch.optim.Optimizer = trainer
        self.batch_size = batch_size
        self.max_norm = max_norm

        self.epoch = 0

    def _train_each_epoch(self, train_x: Tuple, train_y: torch.Tensor):
        """训练一个epoch"""
        for *x, y in DataLoader(TensorDataset(*train_x, train_y), batch_size=self.batch_size, shuffle=True):
            self.trainer.zero_grad()
            y_hat = self.net(x)
            l = self.loss(y_hat, y)
            l.backward()
            torch.nn.utils.clip_grad.clip_grad_norm_(self.net.parameters(), self.max_norm)
            self.trainer.step()

    def train(self):
        pass


class ClassifyEstimator(Estimator):
    def __init__(self, net, loss, trainer, batch_size=4096, max_norm=0.3):
        super().__init__(net, loss, trainer, batch_size, max_norm)
        self.precision = -np.inf
        self.loss_value = np.nan
        self.ratio = np.nan

    def __lt__(self, other):
        return self.precision < other.precision

    def train(
        self,
        num_epochs: int,
        train_x: Tuple,
        train_y: torch.Tensor,
        test_x: Optional[Tuple] = None,
        test_y: Optional[torch.Tensor] = None,
        rd=None,
        is_print=True,
    ):
        for _ in range(num_epochs):
            self.epoch += 1
            self._train_each_epoch(train_x, train_y)
            if not rd is None:
                rd.add_train(self.loss(self.net(train_x), train_y).item())
            if not rd is None and not test_x is None and not test_y is None:
                rd.add_test(self.loss(self.net(test_x), test_y).item())
            if is_print and (self.epoch) % 25 == 0:
                print(
                    "epoch {:>5} - train: loss {:.4f} presion {:.2f} ratio {:.2%}    test: loss {:.4f} presion {:.2f} ratio {:.2%}".format(
                        self.epoch,
                        self.loss(self.net(train_x), train_y).item(),
                        *self.evaluate(self.net(train_x), train_y),
                        self.loss(self.net(test_x), test_y).item(),
                        *self.evaluate(self.net(test_x), test_y) if not test_x is None and not test_y is None else (0, 0),
                    )
                )
        if not test_x is None and not test_y is None:
            precision, ratio = self.evaluate(self.net(test_x), test_y)
            self.precision = precision
            self.loss_value = self.loss(self.net(test_x), test_y)
            self.ratio = ratio

    @staticmethod
    def evaluate(y_hat: torch.Tensor, y: torch.Tensor) -> Tuple[float, float]:
        """estimator评估，以presicion为主要指标

        Args:
            y_hat (torch.Tensor): 预测值
            y (torch.Tensor): 真实值

        Returns:
            Tuple[float,float]: precision和判断为True的比例
        """
        y_hat = torch.argmax(y_hat.detach(), dim=1)
        return (y.detach()[(tmp := y_hat == 1)]).float().mean().item(), tmp.sum().float().item() / len(tmp)


class DenseNet(torch.nn.Module):
    def __init__(self, depth, num_input, num_output):
        def linear_block(num_input, num_output):
            return torch.nn.Sequential(
                torch.nn.BatchNorm1d(num_input),
                torch.nn.ReLU(),
                torch.nn.Linear(num_input, num_output),
            )

        super().__init__()
        layer = []
        for i in range(depth - 1):
            layer.append(linear_block(num_output * i + num_input, num_output))
        self.net1 = torch.nn.Sequential(*layer).to(device)
        self.net2 = torch.nn.Linear(num_output * (depth - 1) + num_input, num_output).to(device)

    def forward(self, X: torch.Tensor):
        for blk in self.net1:
            Y = blk(X)
            # 连接通道维度上每个块的输入和输出
            X = torch.cat((X, Y), dim=1)
        return self.net2(X)


def kfold(x: Tuple, y, n_splits: int = 5, shuffle: bool = True) -> Generator[list, None, None]:
    """生成器，KFold拆分数据集

    Args:
        n_splits (int, optional): 分割数量. Defaults to 5.
        shuffle (bool, optional): 是否随机. Defaults to True.

    Yields:
        Generator[list, None, None]: 用于训练的数据1, 用于测试的数据1...
    """
    kf = KFold(n_splits=n_splits, shuffle=shuffle)
    for train_index, test_index in kf.split(y):
        yield [
            tuple(i[train_index] for i in x),
            tuple(i[test_index] for i in x),
            y[train_index],
            y[test_index],
        ]


def cross_val(
    estimator: Estimator,
    n_splits: int,
    shuffle: bool,
    train_x: Tuple,
    train_y: torch.Tensor,
    num_epochs: int = 100,
    type_="precision",
) -> list:
    ret = []
    for train_x_, test_x_, train_y_, test_y_ in kfold(train_x, train_y, n_splits, shuffle):
        estimator.init()
        estimator.train(num_epochs, train_x_, train_y_, test_x_, test_y_, rd=None, is_print=False)
        if type_ == "precision":
            ret.append(estimator.precision)
        else:
            ret.append(estimator.loss_value)
    return ret

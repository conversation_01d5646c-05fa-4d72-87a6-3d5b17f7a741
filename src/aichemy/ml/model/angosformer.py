from torch import nn
import torch
import math

# add time stamp encoding
import os


class Attention(nn.Module):
    def __init__(self, hidden_size, heads, dropout_prob):
        super(Attention, self).__init__()
        self.hidden_size = hidden_size
        self.heads = heads
        self.head_dim = hidden_size // heads

        self.scale = 1 / math.sqrt(self.head_dim)

        self.fc_q = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_k = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_v = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_out = nn.Linear(heads * self.head_dim, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.attention_dropout = nn.Dropout(dropout_prob)
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x, cross_x=None, mask=None):
        batch_size, seq_len, _ = x.shape
        query = self.fc_q(x) * self.scale
        query = query.view(batch_size, seq_len, self.heads, self.head_dim).transpose(1, 2).contiguous()

        if cross_x is not None:
            key = self.fc_k(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
        else:
            key = self.fc_k(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()

        prob_shape = (batch_size * self.heads, -1, self.head_dim)
        query, key, value = query.view(*prob_shape), key.view(*prob_shape), value.view(*prob_shape)

        attention = query @ key.transpose(1, 2)

        if mask is not None:
            attention = attention.view(batch_size, self.heads, seq_len, seq_len)
            attention = attention + mask
            attention = attention.view(batch_size * self.heads, seq_len, seq_len)

        attention = torch.softmax(attention, dim=-1)
        attention = self.attention_dropout(attention)

        out = attention @ value
        out = out.view(batch_size, self.heads, seq_len, self.head_dim).transpose(1, 2).contiguous()
        out = out.view(batch_size, seq_len, self.hidden_size)

        out = self.fc_out(out)

        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class FeedForward(nn.Module):
    def __init__(self, hidden_size, ff_size, dropout_prob):
        super(FeedForward, self).__init__()

        self.fc1 = nn.Linear(hidden_size, ff_size, bias=False)
        self.fc2 = nn.Linear(ff_size, hidden_size, bias=False)

        self.norm = nn.LayerNorm(hidden_size)
        self.activation = nn.GELU()
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x):
        out = self.fc1(x)
        out = self.activation(out)
        out = self.dropout(out)
        out = self.fc2(out)
        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class EncoderLayer(nn.Module):
    def __init__(self, hidden_size, heads, ff_size, dropout_prob):
        super(EncoderLayer, self).__init__()
        self.attention = Attention(hidden_size, heads, dropout_prob)
        self.feed_forward = FeedForward(hidden_size, ff_size, dropout_prob)

    def forward(self, x):
        out = self.attention(x)
        out = self.feed_forward(out)
        return out


class DecoderLayer(nn.Module):
    def __init__(self, hidden_size, heads, ff_size, dropout_prob):
        super(DecoderLayer, self).__init__()
        self.attention = Attention(hidden_size, heads, dropout_prob)
        self.cross_attention = Attention(hidden_size, heads, dropout_prob)
        self.feed_forward = FeedForward(hidden_size, ff_size, dropout_prob)

    @staticmethod
    def build_mask(x):
        _, seq_length, _ = x.shape
        mask = torch.full((seq_length, seq_length), float("-inf"), dtype=x.dtype).tril(diagonal=-1).T
        return mask.to(x.device)

    def forward(self, x, cross_x):
        mask = self.build_mask(x)
        out = self.attention(x, mask=mask)
        out = self.cross_attention(out, cross_x)
        out = self.feed_forward(out)
        return out


class Embedding(nn.Module):
    def __init__(self, embed_size, embed_length, hidden_size, dropout_prob, cls=False):
        super(Embedding, self).__init__()
        if cls:
            self.input_embed = nn.Embedding(embed_size, hidden_size)  # for classification
        else:
            self.input_embed = nn.Linear(embed_size, hidden_size)
        self.position_embed = nn.Embedding(embed_length, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.dropout = nn.Dropout(dropout_prob)

    @staticmethod
    def build_positions(x):
        batch_size, seq_len = x.shape[0], x.shape[1]
        positions = torch.arange(0, seq_len, dtype=torch.long).expand(batch_size, -1)
        return positions.to(x.device)

    @staticmethod
    def normalize(x, scale_max=1, scale_min=-1):
        x_max, x_min = torch.max(x, dim=1, keepdim=True)[0], torch.min(x, dim=1, keepdim=True)[0]

        x_std = (x - x_min) / (x_max - x_min)
        return x_std * (scale_max - scale_min) + scale_min

    def forward(self, x):
        out = self.input_embed(x)
        position_out = self.position_embed(self.build_positions(x))

        out += position_out
        out = self.norm(out)
        out = self.dropout(out)
        return out


class Encoder(nn.Module):
    def __init__(self, feature_size, feature_length, hidden_size, ff_size, num_layers, heads, dropout_prob):
        super(Encoder, self).__init__()
        self.embedding = Embedding(feature_size, feature_length, hidden_size, dropout_prob)
        self.layers = nn.ModuleList(
            [EncoderLayer(hidden_size, heads, ff_size, dropout_prob) for _ in range(num_layers)]
        )

    def forward(self, x):
        out = self.embedding(x)
        for layer in self.layers:
            out = layer(out)

        return out


class Decoder(nn.Module):
    def __init__(self, label_size, label_length, hidden_size, ff_size, num_layers, heads, dropout_prob):
        super(Decoder, self).__init__()
        self.embedding = Embedding(label_size, label_length, hidden_size, dropout_prob)
        # self.embedding = Embedding(label_size, label_length, hidden_size, dropout_prob, cls=True)  # for classification
        self.layers = nn.ModuleList(
            [DecoderLayer(hidden_size, heads, ff_size, dropout_prob) for _ in range(num_layers)]
        )

    def forward(self, x, cross_x):
        out = self.embedding(x)
        for layer in self.layers:
            out = layer(out, cross_x)

        return out


class EncoderHead(nn.Module):
    def __init__(self, feature_length, label_size, hidden_size, final_size, scale):
        super(EncoderHead, self).__init__()

        self.layers = nn.ModuleList()
        while (scale_size := int(hidden_size / scale)) > max(scale, final_size):
            self.layers.append(nn.Linear(hidden_size, scale_size, bias=False))
            hidden_size = scale_size
        self.final_layer = nn.Linear(hidden_size, final_size, bias=False)
        self.activation = nn.GELU()

        self.head = nn.Linear(final_size, label_size)

        self.final_head = nn.Linear(feature_length, 1)

    def forward(self, x):
        out = x
        for layer in self.layers:
            out = layer(out)
            out = self.activation(out)
        out = self.final_layer(out)
        out = self.activation(out)
        out = self.head(out)
        out = self.final_head(out.transpose(-1, -2))
        out = out.transpose(-1, -2)
        return out


class DecoderHead(nn.Module):
    def __init__(self, label_size, hidden_size, final_size, scale):
        super(DecoderHead, self).__init__()

        self.layers = nn.ModuleList()
        while (scale_size := int(hidden_size / scale)) > max(scale, final_size):
            self.layers.append(nn.Linear(hidden_size, scale_size, bias=False))
            hidden_size = scale_size
        self.final_layer = nn.Linear(hidden_size, final_size, bias=False)
        self.activation = nn.GELU()

        self.head = nn.Linear(final_size, label_size)

    def forward(self, x):
        out = x
        for layer in self.layers:
            out = layer(out)
            out = self.activation(out)
        out = self.final_layer(out)
        out = self.activation(out)
        out = self.head(out)
        return out


class Angosformer(nn.Module):
    def __init__(
        self,
        feature_size,
        feature_length,
        label_size,
        label_length,
        hidden_size=64,
        ff_size=256,
        num_layers=4,
        heads=4,
        dropout_prob=0.1,
        final_size=16,
        scale=4,
    ):
        super(Angosformer, self).__init__()
        self.label_length = label_length
        self.encoder = Encoder(feature_size, feature_length, hidden_size, ff_size, num_layers, heads, dropout_prob)
        self.encoder_head = EncoderHead(feature_length, label_size, hidden_size, final_size, scale)

        self.decoder = Decoder(label_size, label_length, hidden_size, ff_size, num_layers, heads, dropout_prob)
        self.decoder_head = DecoderHead(label_size, hidden_size, final_size, scale)

    def forward(self, src, tgt=None):
        encoder_out = self.encoder(src)
        out = self.encoder_head(encoder_out)
        if tgt is not None:
            decoder_out = self.decoder(
                tgt[
                    :,
                    :-1,
                ],
                encoder_out,
            )
            out = torch.cat([out, self.decoder_head(decoder_out)], dim=1)
        return out

    def generate(self, src):
        encoder_out = self.encoder(src)
        tgt = self.encoder_head(encoder_out)
        while tgt.shape[1] < self.label_length:
            out = self.decoder(tgt, encoder_out)
            out = self.decoder_head(out)
            tgt = torch.cat([tgt, out[:, -1, :].unsqueeze(1)], dim=1)
        return tgt


if __name__ == "__main__":
    source = torch.randn((8, 16, 4))
    target = torch.randn((8, 8, 1))
    model = Angosformer(4, 16, 1, 8)
    model.eval()
    # result = model(source)  # pure encoder and need to set label_length to 1
    forward_result = model(source, target)  # encoder-decoder
    generation_result = model.generate(source)
    # result = model(source)  # pure encoder and need to set label_length to 1

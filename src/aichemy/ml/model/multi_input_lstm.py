import torch


class ExtractedLSTM(torch.nn.Module):
    def __init__(self, hidden_size):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = torch.nn.LSTM(1, hidden_size, batch_first=True)

    def forward(self, *inputs):
        extracted_inputs = []
        for input_ in inputs:
            # 重塑输入张量以批量处理所有特征
            batch_size, seq_len, num_features = input_.shape
            reshaped_input = input_.transpose(1, 2).reshape(batch_size * num_features, seq_len, 1)

            # 批量处理所有特征
            lstm_output, _ = self.lstm(reshaped_input)

            # 重塑回原始维度并求平均
            lstm_output = lstm_output.reshape(batch_size, num_features, seq_len, self.hidden_size)
            lstm_output = lstm_output.transpose(1, 2)
            extracted_input = lstm_output.mean(dim=-2)

            extracted_inputs.append(extracted_input)
        return extracted_inputs


class MILSTM(torch.nn.Module):
    def __init__(self, input_size, hidden_size, num_auxiliary_inputs):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_auxiliary_inputs = num_auxiliary_inputs

        self.w_f = torch.nn.Parameter(torch.randn(input_size, hidden_size))
        self.w_f_h = torch.nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.b_f = torch.nn.Parameter(torch.randn(1, hidden_size))

        self.w_o = torch.nn.Parameter(torch.randn(input_size, hidden_size))
        self.w_o_h = torch.nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.b_o = torch.nn.Parameter(torch.randn(1, hidden_size))

        self.w_i = torch.nn.Parameter(torch.randn(input_size, hidden_size))
        self.w_i_h = torch.nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.b_i = torch.nn.Parameter(torch.randn(1, hidden_size))

        self.w_c = torch.nn.Parameter(torch.randn(input_size, hidden_size))
        self.w_c_h = torch.nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.b_c = torch.nn.Parameter(torch.randn(1, hidden_size))

        self.w_c_auxiliary = torch.nn.ParameterList(
            [torch.nn.Parameter(torch.zeros(input_size, hidden_size)) for _ in range(num_auxiliary_inputs)]
        )
        self.w_c_h_auxiliary = torch.nn.ParameterList(
            [torch.nn.Parameter(torch.zeros(hidden_size, hidden_size)) for _ in range(num_auxiliary_inputs)]
        )
        self.b_c_auxiliary = torch.nn.ParameterList(
            [torch.nn.Parameter(torch.zeros(1, hidden_size)) for _ in range(num_auxiliary_inputs)]
        )

        self.w_i_auxiliary = torch.nn.ParameterList(
            [torch.nn.Parameter(torch.randn(input_size, hidden_size)) for _ in range(num_auxiliary_inputs)]
        )
        self.w_i_h_auxiliary = torch.nn.ParameterList(
            [torch.nn.Parameter(torch.randn(hidden_size, hidden_size)) for _ in range(num_auxiliary_inputs)]
        )
        self.b_i_auxiliary = torch.nn.ParameterList(
            [torch.nn.Parameter(torch.randn(1, hidden_size)) for _ in range(num_auxiliary_inputs)]
        )

        self.w_a = torch.nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.b_a = torch.nn.Parameter(torch.randn(1).squeeze())
        self.b_a_auxiliary = torch.nn.ParameterList(
            [torch.nn.Parameter(torch.randn(1).squeeze()) for _ in range(num_auxiliary_inputs)]
        )

    def forward(self, mainstream, *auxiliary_inputs):
        num_steps = mainstream.size(1)
        h = torch.zeros(mainstream.size(0), self.hidden_size, device=mainstream.device)
        c = torch.zeros(mainstream.size(0), self.hidden_size, device=mainstream.device)
        output = torch.empty(mainstream.size(0), num_steps, self.hidden_size, device=mainstream.device)
        for idx in range(num_steps):
            f = torch.sigmoid((mainstream[:, idx, :] @ self.w_f) + (h @ self.w_f_h) + self.b_f)
            o = torch.sigmoid((mainstream[:, idx, :] @ self.w_o) + (h @ self.w_o_h) + self.b_o)

            c_tilda = torch.tanh((mainstream[:, idx, :] @ self.w_c) + (h @ self.w_c_h) + self.b_c)
            c_tilda_auxiliary = [
                torch.tanh(
                    (auxiliary_input[:, idx, :] @ self.w_c_auxiliary[i])
                    + (h @ self.w_c_h_auxiliary[i])
                    + self.b_c_auxiliary[i]
                )
                for i, auxiliary_input in enumerate(auxiliary_inputs)
            ]

            i = torch.sigmoid((mainstream[:, idx, :] @ self.w_i) + (h @ self.w_i_h) + self.b_i)
            i_auxiliary = [
                torch.sigmoid(
                    (auxiliary_input[:, idx, :] @ self.w_i_auxiliary[i])
                    + (h @ self.w_i_h_auxiliary[i])
                    + self.b_i_auxiliary[i]
                )
                for i, auxiliary_input in enumerate(auxiliary_inputs)
            ]

            l = c_tilda * i
            l_auxiliary = [c_tilda_auxiliary[i] * i_auxiliary[i] for i in range(self.num_auxiliary_inputs)]

            u = torch.tanh(((l @ self.w_a) * c).sum(dim=1, keepdim=True) + self.b_a)  # (batch_size, 1)
            u_auxiliary = [
                torch.tanh(((l_auxiliary[i] @ self.w_a) * c).sum(dim=1, keepdim=True) + self.b_a_auxiliary[i])
                for i in range(self.num_auxiliary_inputs)
            ]
            u = torch.cat([u, *u_auxiliary], dim=1)  # (batch_size, 1 + num_auxiliary_inputs)
            alpha = torch.softmax(u, dim=1)  # (batch_size, 1 + num_auxiliary_inputs)
            upper_l = alpha[:, [0]] * l + sum(
                [alpha[:, [i + 1]] * l_auxiliary[i] for i in range(self.num_auxiliary_inputs)]
            )
            c = f * c + upper_l
            h = o * torch.tanh(c)
            output[:, idx, :] = h
        return output


class TemporalSelfAttention(torch.nn.Module):
    def __init__(self, input_size):
        super().__init__()
        self.input_size = input_size

        self.w_b = torch.nn.Parameter(torch.randn(input_size, input_size))
        self.b_b = torch.nn.Parameter(torch.randn(1, input_size))
        self.v_b = torch.nn.Parameter(torch.randn(input_size, 1))

    def forward(self, input_):
        beta = torch.empty(input_.size(0), input_.size(1), 1, device=input_.device)
        for idx in range(input_.size(1)):
            b = torch.tanh((input_[:, idx, :] @ self.w_b) + self.b_b)  # (batch_size, input_size)
            j = b @ self.v_b  # (batch_size, 1)
            beta[:, idx, :] = j
        return (input_ * beta).sum(dim=1)  # (batch_size, input_size)


class FCNet(torch.nn.Module):
    def __init__(self, input_size, output_size, scale):
        super().__init__()
        self.fc = torch.nn.Sequential()
        while input_size > max(scale, output_size):
            self.fc.append(torch.nn.Linear(input_size, input_size // scale))
            input_size = input_size // scale
            self.fc.append(torch.nn.ReLU())
        self.fc.append(torch.nn.Linear(input_size, output_size))

    def forward(self, input_):
        return self.fc(input_)


class MultiInputLSTM(torch.nn.Module):
    def __init__(self, hidden_size, num_auxiliary_inputs, output_size, scale=2):
        super().__init__()
        self.extracted_lstm = ExtractedLSTM(hidden_size)
        self.milstm = MILSTM(hidden_size, hidden_size, num_auxiliary_inputs)
        self.temporal_self_attention = TemporalSelfAttention(hidden_size)
        self.fc = FCNet(hidden_size, output_size, scale)

    def forward(self, mainstream, *auxiliary_inputs):
        mainstream_extracted, *auxiliary_extracted = self.extracted_lstm(mainstream, *auxiliary_inputs)
        milstm_output = self.milstm(mainstream_extracted, *auxiliary_extracted)
        temporal_self_attention_output = self.temporal_self_attention(milstm_output)
        fc_output = self.fc(temporal_self_attention_output)
        return fc_output.unsqueeze(1)


# if __name__ == "__main__":
#     # 生成随机训练数据
#     batch_size = 32
#     seq_length = 10
#     input_dim = 5
#     hidden_size = 64
#     num_auxiliary = 2
#     output_size = 3

#     # 主输入数据 (batch_size, seq_length, input_dim)
#     mainstream = torch.randn(batch_size, seq_length, input_dim).cuda()

#     # 辅助输入数据
#     aux1 = torch.randn(batch_size, seq_length, input_dim).cuda()
#     aux2 = torch.randn(batch_size, seq_length, input_dim).cuda()

#     # 创建模型
#     model = MultiInputLSTM(hidden_size=hidden_size, num_auxiliary_inputs=num_auxiliary, output_size=output_size).cuda()

#     # 生成随机标签
#     labels = torch.randn(batch_size, output_size).cuda()

#     # 定义损失函数和优化器
#     criterion = torch.nn.MSELoss()
#     optimizer = torch.optim.Adam(model.parameters())

#     # 训练循环
#     num_epochs = 10
#     for epoch in range(num_epochs):
#         # 前向传播
#         outputs = model(mainstream, aux1, aux2)
#         loss = criterion(outputs, labels)

#         # 反向传播和优化
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()

#         if (epoch + 1) % 2 == 0:
#             print(f"Epoch [{epoch + 1}/{num_epochs}], Loss: {loss.item():.4f}")

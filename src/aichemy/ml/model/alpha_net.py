import torch.nn.functional as F
from torch import nn

from .timemixer import TimeMixer


class AlphaNetTimeMixer(nn.Module):
    def __init__(
        self,
        feature_length,
        feature_size,
        label_length,
        label_size,
        hidden_size=32,
        ff_size=64,
        dropout=0.1,
        encoder_layers=4,
        sample_window=2,
        sample_layers=2,
        average_window=16,
    ):
        super().__init__()
        self.model1 = TimeMixer(
            feature_length - 5,
            feature_size - 1,
            1,
            feature_size,
            hidden_size,
            ff_size,
            dropout,
            encoder_layers,
            sample_window,
            sample_layers,
            average_window,
        )
        self.model2 = TimeMixer(
            feature_length,
            feature_size,
            1,
            1,
            hidden_size,
            ff_size,
            dropout,
            encoder_layers,
            sample_window,
            sample_layers,
            average_window,
        )

    def forward(self, x):
        x1 = x[:, :-5, 1:]
        x2 = x[:, 5:, [0]]
        y1 = self.model1(x1 - x2)
        y1 = F.softmax(y1, dim=-1)
        return self.model2(y1 * x)


class AlphaNetLinear(nn.Module):
    def __init__(
        self,
        feature_length,
        feature_size,
        label_length,
        label_size,
        hidden_size=32,
        ff_size=64,
        dropout=0.1,
        encoder_layers=4,
        sample_window=2,
        sample_layers=2,
        average_window=16,
    ):
        super().__init__()
        self.model1 = TimeMixer(
            feature_length - 5,
            feature_size,
            1,
            feature_size,
            hidden_size,
            ff_size,
            dropout,
            encoder_layers,
            sample_window,
            sample_layers,
            average_window,
        )
        # self.model2 = TimeMixer(
        #     feature_length,
        #     feature_size,
        #     1,
        #     1,
        #     hidden_size,
        #     ff_size,
        #     dropout,
        #     encoder_layers,
        #     sample_window,
        #     sample_layers,
        #     average_window,
        # )
        self.model2 = nn.Linear(feature_size, 1)

    def forward(self, x):
        x1 = x[:, :-5, :]
        x2 = x[:, 5:, [0]]
        y1 = self.model1(x1 - x2)
        y1 = F.softmax(y1, dim=-1)
        return self.model2(y1 * x[:, [-1], :])


if __name__ == "__main__":
    import torch

    model = TimeMixer(128, 8, 1, 1)
    input_id = torch.rand(8, 128, 8)
    ret = model(input_id)
    print(ret.shape)

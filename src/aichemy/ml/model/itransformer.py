from torch import nn
import torch
import math
# add time stamp encoding
import os


class Embedding(nn.Module):
    def __init__(self, feature_size, hidden_size, dropout=0.1):
        super().__init__()
        self.value_embed = nn.Linear(feature_size, hidden_size)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        out = x.transpose(1, 2)
        out = self.value_embed(out)
        out = self.dropout(out)
        return out


class Attention(nn.Module):
    def __init__(self, hidden_size, heads, dropout_prob):
        super(Attention, self).__init__()
        self.hidden_size = hidden_size
        self.heads = heads
        self.head_dim = hidden_size // heads

        self.scale = 1 / math.sqrt(self.head_dim)

        self.fc_q = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_k = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_v = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_out = nn.Linear(heads * self.head_dim, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.attention_dropout = nn.Dropout(dropout_prob)
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x, cross_x=None, mask=None):
        batch_size, seq_len, _ = x.shape
        query = self.fc_q(x) * self.scale
        query = query.view(batch_size, seq_len, self.heads, self.head_dim).transpose(1, 2).contiguous()

        if cross_x is not None:
            key = self.fc_k(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
        else:
            key = self.fc_k(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()

        prob_shape = (batch_size * self.heads, -1, self.head_dim)
        query, key, value = query.view(*prob_shape), key.view(*prob_shape), value.view(*prob_shape)

        attention = query @ key.transpose(1, 2)

        if mask is not None:
            attention = attention.view(batch_size, self.heads, seq_len, seq_len)
            attention = attention + mask
            attention = attention.view(batch_size * self.heads, seq_len, seq_len)

        attention = torch.softmax(attention, dim=-1)
        attention = self.attention_dropout(attention)

        out = attention @ value
        out = out.view(batch_size, self.heads, seq_len, self.head_dim).transpose(1, 2).contiguous()
        out = out.view(batch_size, seq_len, self.hidden_size)

        out = self.fc_out(out)

        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class FeedForward(nn.Module):
    def __init__(self, hidden_size, ff_size, dropout_prob):
        super(FeedForward, self).__init__()

        self.conv1 = nn.Conv1d(hidden_size, ff_size, kernel_size=1)
        self.conv2 = nn.Conv1d(ff_size, hidden_size, kernel_size=1)

        self.norm = nn.LayerNorm(hidden_size)
        self.activation = nn.GELU()
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x):
        out = x.transpose(-1, 1)
        out = self.conv1(out)
        out = self.activation(out)
        out = self.dropout(out)
        out = self.conv2(out)
        out = out.transpose(-1, 1)
        out = self.dropout(out)
        out = self.norm(out + x)
        return out


class EncoderLayer(nn.Module):
    def __init__(self, hidden_size, heads, ff_size, dropout_prob):
        super(EncoderLayer, self).__init__()
        self.attention = Attention(hidden_size, heads, dropout_prob)
        self.feed_forward = FeedForward(hidden_size, ff_size, dropout_prob)

    def forward(self, x):
        out = self.attention(x)
        out = self.feed_forward(out)
        return out


class Encoder(nn.Module):
    def __init__(self, hidden_size, ff_size, num_layers, heads, dropout_prob):
        super(Encoder, self).__init__()
        self.layers = nn.ModuleList(
            [EncoderLayer(hidden_size, heads, ff_size, dropout_prob) for _ in range(num_layers)]
        )
        self.norm = nn.LayerNorm(hidden_size)

    def forward(self, x):
        out = x
        for layer in self.layers:
            out = layer(out)
        out = self.norm(out)
        return out


class Angosformer(nn.Module):
    def __init__(self, feature_size, feature_length, label_size, label_length,
                 hidden_size=64, ff_size=256, num_layers=4, heads=4, dropout_prob=0.1):
        super(Angosformer, self).__init__()
        self.embedding = Embedding(feature_length, hidden_size, dropout_prob)
        self.encoder = Encoder(hidden_size, ff_size, num_layers, heads, dropout_prob)
        self.projection_layer = nn.Linear(hidden_size, label_length, bias=False)
        self.regression_layer = nn.Linear(feature_size, label_size, bias=False)

    def forward(self, x):
        out = self.embedding(x)
        out = self.encoder(out)
        out = self.projection_layer(out)
        out = out.transpose(1, 2)
        out = self.regression_layer(out)
        out = out.transpose(1, 2)
        return out


if __name__ == '__main__':
    model = Angosformer(4, 16, 1, 1)
    input_id = torch.randn((8, 16, 4))
    ret = model(input_id)
    print(ret.shape) # pure encoder and need to set label_length to 1
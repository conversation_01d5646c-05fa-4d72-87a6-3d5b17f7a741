import torch
from torch import nn

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


class Encoder(nn.Module):  # @save
    """The base encoder interface for the encoder--decoder architecture."""

    def __init__(self):
        super().__init__()

    # Later there can be additional arguments (e.g., length excluding padding)
    def forward(self, X, *args):
        raise NotImplementedError


class Decoder(nn.Module):  # @save
    """The base decoder interface for the encoder--decoder architecture."""

    def __init__(self):
        super().__init__()

    # Later there can be additional arguments (e.g., length excluding padding)
    def init_state(self, enc_all_outputs, *args):
        raise NotImplementedError

    def forward(self, X, state):
        raise NotImplementedError


class AttentionDecoder(Decoder):  # @save
    """The base attention-based decoder interface."""

    def __init__(self):
        super().__init__()

    @property
    def attention_weights(self):
        raise NotImplementedError


class EncoderDecoder(nn.Module):  # @save
    """The base class for the encoder--decoder architecture."""

    def __init__(self, encoder, decoder):
        super().__init__()
        self.encoder = encoder
        self.decoder = decoder

    def forward(self, enc_X, dec_X, *args):
        enc_all_outputs = self.encoder(enc_X, *args)
        dec_state = self.decoder.init_state(enc_all_outputs, *args)
        # Return decoder output only
        return self.decoder(dec_X, dec_state)[0]


def init_seq2seq(module):  # @save
    """Initialize weights for sequence-to-sequence learning."""
    if type(module) == nn.Linear:
        nn.init.xavier_uniform_(module.weight)
    if type(module) == nn.GRU:
        for param in module._flat_weights_names:
            if "weight" in param:
                nn.init.xavier_uniform_(module._parameters[param])


class PositionalEncoding(nn.Module):  # @save
    """Positional encoding."""

    def __init__(self, num_hiddens, dropout, max_len=1000):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        # Create a long enough P
        self.P = torch.zeros((1, max_len, num_hiddens))
        X = torch.arange(max_len, dtype=torch.float32).reshape(-1, 1) / torch.pow(
            10000, torch.arange(0, num_hiddens, 2, dtype=torch.float32) / num_hiddens
        )
        self.P[:, :, 0::2] = torch.sin(X)
        self.P[:, :, 1::2] = torch.cos(X)

    def forward(self, X):
        X = X + self.P[:, : X.shape[1], :].to(X.device)
        return self.dropout(X)


class Seq2SeqEncoder(Encoder):  # @save
    """The RNN encoder for sequence-to-sequence learning."""

    def __init__(self, input_size, num_hiddens, num_layers, dropout=0.5):
        super().__init__()
        self.rnn = nn.GRU(input_size, num_hiddens, num_layers, dropout=dropout).to(device)
        self.apply(init_seq2seq)

    def forward(self, X: torch.Tensor, *args):
        # X shape: (batch_size, num_steps, num_features)
        outputs, state = self.rnn(X.transpose(0, 1))
        # outputs shape: (num_steps, batch_size, num_hiddens)
        # state shape: (num_layers, batch_size, num_hiddens)
        return outputs, state


class Seq2SeqDecoder(Decoder):
    """The RNN decoder for sequence to sequence learning."""

    def __init__(self, input_size, num_hiddens, num_layers, label_length, dropout=0.5):
        super().__init__()
        self.rnn = nn.GRU(input_size + num_hiddens, num_hiddens, num_layers, dropout=dropout).to(device)
        self.dense = nn.LazyLinear(label_length).to(device)
        self.apply(init_seq2seq)

    def init_state(self, enc_all_outputs, *args):
        return enc_all_outputs

    def forward(self, x, state):
        # X shape: (batch_size, num_steps, num_features) -> (num_steps, batch_size, num_features)
        x = x.transpose(0, 1)
        # context shape: (batch_size, num_hiddens)
        enc_output, hidden_state = state
        context = enc_output[-1]
        # Broadcast context to (num_steps, batch_size, num_hiddens)
        context = context.repeat(x.shape[0], 1, 1)
        # Concat at the feature dimension, shape: (num_steps, batch_size, num_hiddens + num_features)
        x_and_context = torch.cat((x, context), -1)
        # outputs shape: (num_steps, batch_size, num_hiddens)
        # hidden_state shape: (num_layers, batch_size, num_hiddens)
        outputs, hidden_state = self.rnn(x_and_context, hidden_state)
        # outputs shape: (num_steps, batch_size, label_length)
        outputs = self.dense(outputs)[-1]
        return outputs


class AdditiveAttention(nn.Module):  # @save
    """Additive attention."""

    def __init__(self, num_hiddens, dropout, **kwargs):
        super(AdditiveAttention, self).__init__(**kwargs)
        self.W_k = nn.LazyLinear(num_hiddens, bias=False).to(device)
        self.W_q = nn.LazyLinear(num_hiddens, bias=False).to(device)
        self.w_v = nn.LazyLinear(1, bias=False).to(device)
        self.dropout = nn.Dropout(dropout).to(device)

    def forward(self, queries, keys, values):
        queries, keys = self.W_q(queries), self.W_k(keys)
        # After dimension expansion, shape of queries: (batch_size, no. of
        # queries, 1, num_hiddens) and shape of keys: (batch_size, 1, no. of
        # key-value pairs, num_hiddens). Sum them up with broadcasting
        # features shape: (batch_size, num_querys, num_k_v_pairs, num_hiddens)
        features = queries.unsqueeze(2) + keys.unsqueeze(1)
        features = torch.tanh(features)
        # There is only one output of self.w_v, so we remove the last
        # one-dimensional entry from the shape. Shape of scores: (batch_size, num_querys, num_k_v_pairs)
        scores = self.w_v(features).squeeze(-1)
        self.attention_weights = scores
        # Shape of output: (batch_size, num_querys, value dimension)
        return torch.bmm(self.dropout(self.attention_weights), values)


class Seq2SeqAttentionDecoder(AttentionDecoder):
    def __init__(self, input_size, num_hiddens, num_layers, label_length, dropout=0.5):
        super().__init__()
        self.attention = AdditiveAttention(num_hiddens, dropout)
        self.rnn = nn.GRU(input_size + num_hiddens, num_hiddens, num_layers, dropout=dropout).to(device)
        self.dense = nn.LazyLinear(label_length).to(device)
        self.apply(init_seq2seq)

    def init_state(self, enc_outputs):
        # Shape of outputs: (num_steps, batch_size, num_hiddens).
        # Shape of hidden_state: (num_layers, batch_size, num_hiddens)
        outputs, hidden_state = enc_outputs
        return outputs.permute(1, 0, 2), hidden_state

    def forward(self, X, state):
        # Shape of enc_outputs: (batch_size, num_steps, num_hiddens).
        # Shape of hidden_state: (num_layers, batch_size, num_hiddens)
        enc_outputs, hidden_state = state
        # Shape of the output X: (num_steps, batch_size, num_xdims)
        X = X.permute(1, 0, 2)
        outputs, self._attention_weights = [], []
        # Shape of x: (batch_size, num_xdims)
        for x in X:
            # Shape of query: (batch_size, 1, num_hiddens)
            query = torch.unsqueeze(hidden_state[-1], dim=1)
            # Shape of context: (batch_size, 1, num_hiddens)
            context = self.attention(query, enc_outputs, enc_outputs)
            # Concatenate on the feature dimension
            x = torch.cat((context, torch.unsqueeze(x, dim=1)), dim=-1)
            # Reshape x as (1, batch_size, num_xdims + num_hiddens)
            out, hidden_state = self.rnn(x.permute(1, 0, 2), hidden_state)
            outputs.append(out)
            self._attention_weights.append(self.attention.attention_weights)
        # After fully connected layer transformation, shape of outputs:
        # (num_steps, batch_size, vocab_size)
        outputs = self.dense(torch.cat(outputs, dim=0))[-1]
        return outputs

    @property
    def attention_weights(self):
        return self._attention_weights


class Seq2Seq(EncoderDecoder):
    def forward(self, enc_X):
        enc_all_outputs = self.encoder(enc_X)
        dec_state = self.decoder.init_state(enc_all_outputs)
        # Return decoder output only
        return self.decoder(enc_X, dec_state)


def modify_lr(trainer, lr):
    for param_group in trainer.param_groups:
        param_group["lr"] = lr  # 更新学习率

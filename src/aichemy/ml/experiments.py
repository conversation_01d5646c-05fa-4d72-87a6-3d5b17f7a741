import math

import numpy as np
import torch
import torch.utils
from loguru import logger
from torch import nn

from ..factor_analyse.method import calc_ic, calc_rank_ic
from .exp_base import ExpBase
from .model.angosformer import Angosformer
from .model.timemixer import TimeMixer


class ExpTSPrd(ExpBase):
    def convert_x(self, x) -> torch.Tensor:
        return torch.tensor(x).float()

    def convert_y(self, y) -> torch.Tensor:
        return torch.tensor(y)[:, [-1], :].float()

    def reverse_y(self, y: torch.Tensor) -> np.ndarray:
        return y[:, :, -1].squeeze(dim=1).numpy()

    def select_optimizer(self):
        return torch.optim.Adam(
            self.model.parameters(),
            lr=self.args.get("lr", 1e-4),
            betas=(self.args.get("beta1", 0.9), self.args.get("beta2", 0.999)),
            weight_decay=self.args.get("weight_decay", 1e-7),
        )

    def select_criterion(self):
        criterion = self.args.get("criterion", "mse")
        if criterion == "mse":
            return torch.nn.MSELoss()
        elif criterion == "mae":
            return torch.nn.L1Loss()
        else:
            raise NotImplementedError

    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        # ret = {flag: {"IC": 0.0}}
        ic = calc_ic(y_hat[:, :, -1].flatten(), y[:, :, -1].flatten())
        rank_ic = calc_rank_ic(y_hat[:, :, -1].flatten(), y[:, :, -1].flatten())
        ic1 = []
        rank_ic1 = []
        n = math.ceil(y_hat.shape[0] / 1000)
        for i in range(n):
            ic1.append(calc_ic(y_hat[i::n, :, -1].flatten(), y[i::n, :, -1].flatten()))
            rank_ic1.append(calc_rank_ic(y_hat[i::n, :, -1].flatten(), y[i::n, :, -1].flatten()))
        ret = {flag: {"IC": ic, "RankIC": rank_ic, "IC1": np.mean(ic1), "RankIC1": np.mean(rank_ic1)}}
        logger.info(ret)
        return ret


class ExpRNN(ExpBase):
    def convert_x(self, x) -> torch.Tensor:
        return torch.tensor(x).float()

    def convert_y(self, y) -> torch.Tensor:
        return torch.tensor(y).float()[:, -1, :]

    def build_model(self):
        input_size = self.input_size[-1]
        num_hiddens, num_layers, dropout = 256, 2, 0.3
        encoder2 = Seq2SeqEncoder(input_size, num_hiddens, num_layers, dropout)
        decoder2 = Seq2SeqAttentionDecoder(input_size, num_hiddens, num_layers, 1, dropout)
        net2 = Seq2Seq(encoder2, decoder2)
        return net2

    def select_optimizer(self):
        return torch.optim.Adam(
            self.model.parameters(), lr=self.args.get("lr", 1e-4), weight_decay=self.args.get("weight_decay", 1e-7)
        )

    def select_criterion(self):
        return torch.nn.MSELoss()

    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag="vali"):
        return_ = {flag: {"IC": _calc_ic(y_hat.flatten(), y.flatten())}}
        print(return_)
        return return_


class ExpAngosformer(ExpBase):
    def convert_x(self, x) -> torch.Tensor:
        return torch.tensor(x).float()

    def convert_y(self, y) -> torch.Tensor:
        return torch.tensor(y).float()[:, [-1], :]

    def reverse_y(self, y: torch.Tensor) -> np.ndarray:
        return y.squeeze(dim=1).numpy()

    def build_model(self, input_size):
        net2 = Angosformer(input_size[-1], input_size[1], 1, 1)
        return net2

    def select_optimizer(self):
        return torch.optim.Adam(
            self.model.parameters(), lr=self.args.get("lr", 1e-4), weight_decay=self.args.get("weight_decay", 1e-7)
        )

    def select_criterion(self):
        return torch.nn.MSELoss()

    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        ret = {flag: {"IC": _calc_ic(y_hat.flatten(), y.flatten())}}
        return ret


class ExpLinear(ExpBase):
    def convert_x(self, x) -> torch.Tensor:
        return torch.tensor(x).float()

    def convert_y(self, y) -> torch.Tensor:
        return torch.tensor(y).float()

    def reverse_y(self, y: torch.Tensor) -> np.ndarray:
        return y.numpy()

    def build_model(self):
        input_size = self.input_size[-1]
        net2 = nn.Linear(input_size, 1)
        return net2

    def select_optimizer(self):
        return torch.optim.Adam(
            self.model.parameters(), lr=self.args.get("lr", 1e-4), weight_decay=self.args.get("weight_decay", 1e-7)
        )

    def select_criterion(self):
        return torch.nn.MSELoss()

    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        ret = {flag: {"IC": _calc_ic(y_hat.flatten(), y.flatten())}}
        # print(flag, ret)
        return ret


class ExpTimeMixer(ExpTSPrd):
    def build_model(self, input_size):
        net = TimeMixer(
            self.args.get("feature_length", input_size[1]),
            input_size[-1],
            1,
            1,
            encoder_layers=self.args.get("encoder_layers", 4),
            sample_layers=self.args.get("sample_layers", 2),
        )
        return net

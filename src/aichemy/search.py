import multiprocessing
import pickle
import time
from itertools import combinations
from typing import Callable, Iterable, Sequence, Tuple, Mapping, MutableMapping

import pandas as pd

from .utils import convert_seconds_to_hms


def is_subset(a: Iterable, b: Iterable) -> bool:
    """判断a是否是b的子集，必须是a<b才会返回True

    Args:
        a (Sequence): 子集
        b (Sequence): 父集

    Returns:
        bool: 如果a是b的子集，返回True
    """
    return set(a) != set(b) and set(a).issubset(set(b))


class BeamSearch:
    """束搜索工具，用于找出最优特征集合

    Attributes:
        all_features (Sequence): 特征空间
        fnc_select_features (Callable[[pd.DataFrame], Sequence]): 特征选择的方法
        current_iter_times (int): 当前迭代次数
        next_features_iter (Sequence[Tuple]): 下一轮的特征
        selected_features_set (Sequence[Tuple]): 上一轮结束后选出来的特征集合
        result (Dict): 结果
        _features (Tuple): 最优特征集合

    Examples:
        >>> from aichemy.search import BeamSearch
        >>> def fnc_select_features(df):
        ...     return Sequence(df.loc[df['count']>200].sort_values("final_return", ascending=False).index)[:3]
        >>> def fnc_calc(features, *args):
        ...     return {
        ...         "final_return": 0.85,
        ...         "count": 30,
        ...     }
        >>> beam_search = BeamSearch(features, fnc_select_features)
        >>> beam_search(fnc_calc, args, max_iter_times=5, n_core=3, start_features=[])
    """

    def __init__(
        self,
        features: Sequence[str],
        fnc_select_features: Callable[[pd.DataFrame], Sequence[Tuple[str, ...]]],
        generator=None,
    ):
        """初奴化beam search tool

        Args:
            features (Sequence): 特征空间
            fnc_select_features (Callable[[pd.DataFrame], Sequence]): 特征选择的方法
        """
        self.all_features: Sequence[str] = features
        self.fnc_select_features: Callable[[pd.DataFrame], Sequence[Tuple[str, ...]]] = (
            fnc_select_features  # 特征筛选规则
        )
        self.generator = generator

        self.current_iter_times = 0
        self.next_features_iter: Sequence[Tuple[str, ...]] = []  # 下一轮的特征
        self.selected_features_set: Sequence[Tuple[str, ...]] = []  # 上一轮结束后选出来的特征集合

        self.result: MutableMapping[str, Mapping] = {}
        self._features: Tuple[str, ...] = ()

    def _generate_next_feature(self):
        """生成下一轮要搜索的特征集合

        Raises:
            RuntimeError: 上一轮迭代没有选出特征
        """
        if self.current_iter_times != 0 and len(self.selected_features_set) == 0:
            raise RuntimeError("上一轮迭代没有选出特征")

        self.current_iter_times += 1
        self.next_features_iter = []
        if self.selected_features_set:
            for j in self.selected_features_set:
                len_features = len(j) + 1
                for i in (
                    combinations(self.all_features, len_features)
                    if self.generator is None
                    else self.generator.send(len_features)
                ):
                    if is_subset(j, i):
                        self.next_features_iter.append(i)
        else:
            for i in combinations(self.all_features, 1) if self.generator is None else self.generator.send(1):
                self.next_features_iter.append(i)
        self.selected_features_set = []

    def __call__(
        self,
        fnc_calc: Callable[..., Mapping],
        args,
        max_iter_times: int = 5,
        n_core: int = 3,
        start_features: Sequence[Tuple[str, ...]] = [],
    ) -> Tuple[str, ...]:
        def fnc(res_: Sequence[Tuple[Tuple[str, ...], Mapping]]) -> Sequence[Tuple[Tuple[str, ...], Mapping]]:
            """对结果进行筛选，如果增加了特征，但是对最终结果没有帮助，就去掉"""
            tmp = []
            for j in res_:
                for i in self.result.keys():
                    tmp1 = self.result.get(i, None)
                    if is_subset(eval(i), j[0]) and j[1] == tmp1:
                        break
                else:
                    tmp.append(j)
            return tmp

        self.current_iter_times = 0
        self.next_features_iter = []
        self.selected_features_set = start_features

        while self.current_iter_times < max_iter_times:
            self._generate_next_feature()
            if not self.next_features_iter:
                break

            # 分为2部分，一是需要算的，二是已经算过的
            part1: Sequence[Tuple[str, ...]] = [i for i in self.next_features_iter if repr(i) not in self.result]
            part2: Sequence[Tuple[str, ...]] = [i for i in self.next_features_iter if repr(i) in self.result]

            # 计算
            st = time.time()
            if n_core == 1:
                res1: Sequence[Mapping] = [fnc_calc(features, *args) for features in part1]
            else:
                with multiprocessing.Pool(n_core) as p:
                    res1: Sequence[Mapping] = p.starmap(fnc_calc, ((features, *args) for features in part1))
            print(f"round {self.current_iter_times} take time: {convert_seconds_to_hms(time.time() - st)}")

            res: Sequence[Tuple[Tuple[str, ...], Mapping]] = list(zip(part1, res1))
            self.result.update(dict([(repr(i[0]), i[1]) for i in res]))
            res.extend([(i, self.result[repr(i)]) for i in part2])

            # 筛选，如果增加了特征，但是对最终结果没有帮助，就去掉
            res = fnc(res)
            if not res:
                break

            # 选出效果最好的特征集
            self.selected_features_set = [
                tuple(ii for ii in i if not pd.isna(ii)) for i in self.fnc_select_features(pd.DataFrame(dict(res)).T)
            ]
            # 如果一轮迭代没有选出特征，就退出
            if not self.selected_features_set:
                break
        res_: Sequence[Tuple[Tuple[str, ...], Mapping]] = [(eval(i), j) for i, j in self.result.items()]
        res_ = fnc(res_)
        self._features = tuple(i for i in self.fnc_select_features(pd.DataFrame(dict(res_)).T)[0] if not pd.isna(i))
        return self._features

    @property
    def features(self):
        """输出表现最好的特征"""
        return self._features

    def dump(self, filename):
        with open(filename, "wb") as f:
            pickle.dump((self._features, self.result), f)

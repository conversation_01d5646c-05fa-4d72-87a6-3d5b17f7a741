import pathlib
import time
from typing import Callable, Literal, Mapping, MutableMapping, Optional, Tuple, Union, overload

import numpy as np
import numpy.typing as npt
import pandas as pd
import torch
from loguru import logger
from tqdm import tqdm

from ..utils import ResourceLimiter
from .aligner import DFAligner
from .assembler import DataAssembler
from .scaler import ACScaler, scaling_method
from .value import FilterInvalidFeatures, handle_outlier


@overload
def get_training_set_label(
    trigger: pd.DataFrame, non_training_size: float = 0.25, shuffle: bool = True
) -> pd.DataFrame: ...
@overload
def get_training_set_label(
    trigger: np.ndarray, non_training_size: float = 0.25, shuffle: bool = True
) -> np.ndarray: ...
def get_training_set_label(
    trigger: Union[pd.DataFrame, np.ndarray], non_training_size: float = 0.25, shuffle: bool = True
) -> Union[pd.DataFrame, np.ndarray]:
    """在trigger和~trigger上分别切分出训练集, 训练集标记为1"""
    res = np.empty(trigger.shape, dtype=bool)
    if isinstance(trigger, pd.DataFrame):
        if shuffle:
            res[trigger.values] = np.random.binomial(1, 1 - non_training_size, res[trigger.values].size).astype(bool)
            res[~trigger.values] = np.random.binomial(1, 1 - non_training_size, res[~trigger.values].size).astype(bool)
        else:
            res.fill(False)
            res[np.arange(res.size).reshape(res.shape) < int(res.size * (1 - non_training_size))] = True
        return pd.DataFrame(res, index=trigger.index, columns=trigger.columns)
    else:
        if shuffle:
            res[trigger] = np.random.binomial(1, 1 - non_training_size, res[trigger].size).astype(bool)
            res[~trigger] = np.random.binomial(1, 1 - non_training_size, res[~trigger].size).astype(bool)
        else:
            res.fill(False)
            res[np.arange(res.size).reshape(res.shape) < int(res.size * (1 - non_training_size))] = True
        return res


def resource_limit(max_concurrent: int = 5, counter_file: str = "resource_counter.txt"):
    rl = ResourceLimiter(max_concurrent, counter_file)

    def f1(fnc):
        def f2(*args, **kwargs):
            while not rl.acquire():
                logger.info("等待资源释放...")
                time.sleep(5)
            try:
                return fnc(*args, **kwargs)
            finally:
                rl.release()

        return f2

    return f1


def _assemble_data(
    data_assembler: DataAssembler,
    data_: Union[MutableMapping[str, pd.DataFrame], MutableMapping[str, np.ndarray]],
    is_apply: bool,
    release_memory: bool,
    chunk_num: int = 1,
    func_train_or_pred: Optional[Callable] = None,
    func_non_train: Optional[Callable] = None,
) -> Tuple[
    npt.NDArray[np.float64],
    npt.NDArray[np.float64],
    npt.NDArray[np.float64],
    npt.NDArray[np.float64],
    npt.NDArray[np.bool_],
    npt.NDArray[np.bool_],
]:
    """对数据进行聚合

    Args:
        data_assembler (DataAssembler): 聚合数据的工具类
        data_ (Union[MutableMapping[str, pd.DataFrame], MutableMapping[str, np.ndarray]]): 需要聚合的数据
        chunk_num (int, optional): 切分的块数. Defaults to 1.
        func_train_or_pred (Optional[Callable], optional): 训练集/应用集筛选函数. Defaults to None.
        func_non_train (Optional[Callable], optional): 非训练集筛选函数. Defaults to None.

    Returns:
        Tuple[npt.NDArray[np.float64], npt.NDArray[np.float64], npt.NDArray[np.float64], npt.NDArray[np.float64], npt.NDArray[np.bool_], npt.NDArray[np.bool_]]:
         训练集/应用集X，非训练集X，训练集/应用集y，非训练集y，训练集/应用集的位置，非训练集的位置
    """
    if chunk_num == 1:
        data_x, data_y, data_s, data_m, _, custom_mask_lst = data_assembler.transform(
            data_, stack=True, is_apply=is_apply, release_memory=release_memory
        )

        custom_mask_train = np.full_like(data_s, True, dtype=bool)
        if func_train_or_pred is not None:
            custom_mask_train = func_train_or_pred(custom_mask_lst) & custom_mask_train

        custom_mask_non_train = np.full_like(data_s, True, dtype=bool)
        if func_non_train is not None:
            custom_mask_non_train = func_non_train(custom_mask_lst) & custom_mask_non_train

        a = data_s & data_m & custom_mask_train
        training_x = data_x[a]
        training_y = data_y[a]

        b = (~data_s) & data_m & custom_mask_non_train
        non_training_x = data_x[b]
        non_training_y = data_y[b]

        del data_x, data_y

        return training_x, non_training_x, training_y, non_training_y, a, b

    else:
        training_x, non_training_x, training_y, non_training_y, location_a, location_b = [], [], [], [], [], []
        for chunk_id in tqdm(range(chunk_num)):
            tmp_data_x, tmp_data_y, part_data_s, part_data_m, _, part_custom_mask_lst = data_assembler.transform(
                {k: v[:, chunk_id::chunk_num] for k, v in data_.items()},  # type: ignore
                stack=True,
                is_apply=is_apply,
            )

            part_custom_mask_train = np.full_like(part_data_s, True, dtype=bool)
            if func_train_or_pred is not None:
                part_custom_mask_train = func_train_or_pred(part_custom_mask_lst) & part_custom_mask_train

            part_custom_mask_non_train = np.full_like(part_data_s, True, dtype=bool)
            if func_non_train is not None:
                part_custom_mask_non_train = func_non_train(part_custom_mask_lst) & part_custom_mask_non_train

            a = part_data_s & part_data_m & part_custom_mask_train
            training_x.append(tmp_data_x[a])
            training_y.append(tmp_data_y[a])

            b = (~part_data_s) & part_data_m & part_custom_mask_non_train
            non_training_x.append(tmp_data_x[b])
            non_training_y.append(tmp_data_y[b])

            location_a.append(a)
            location_b.append(b)

            del tmp_data_x, tmp_data_y, part_data_s, part_data_m

        training_x = np.concatenate(training_x, axis=0)
        non_training_x = np.concatenate(non_training_x, axis=0)
        training_y = np.concatenate(training_y, axis=0)
        non_training_y = np.concatenate(non_training_y, axis=0)
        location_a = np.concatenate(location_a, axis=0)
        location_b = np.concatenate(location_b, axis=0)
        return training_x, non_training_x, training_y, non_training_y, location_a, location_b


def _assemble_idx_data(
    data_assembler: DataAssembler,
    data_: Union[MutableMapping[str, pd.DataFrame], MutableMapping[str, np.ndarray]],
    is_apply: bool,
    release_memory: bool,
    func_train_or_pred: Optional[Callable] = None,
    func_non_train: Optional[Callable] = None,
) -> Tuple[
    npt.NDArray[np.int64],
    npt.NDArray[np.int64],
    npt.NDArray[np.float64],
    npt.NDArray[np.float64],
    npt.NDArray[np.bool_],
    npt.NDArray[np.bool_],
]:
    """聚合数据与索引

    Args:
        data_assembler (DataAssembler): 聚合数据的工具类
        data_ (Mapping[str, pd.DataFrame  |  np.ndarray]): 需要聚合的数据
        func_train_or_pred (Optional[Callable], optional): 训练集/应用集筛选函数. Defaults to None.
        func_non_train (Optional[Callable], optional): 非训练集筛选函数. Defaults to None.

    Returns:
        Tuple[npt.NDArray[np.int64], npt.NDArray[np.int64], npt.NDArray[np.float64], npt.NDArray[np.float64], npt.NDArray[np.bool_], npt.NDArray[np.bool_]]:
         训练集/应用集索引，非训练集索引，全部X，全部y，训练集/应用集的位置，非训练集的位置
    """
    data_x, data_y, data_s, data_m, index, custom_mask_lst = data_assembler.transform(
        data_, stack=False, is_apply=is_apply, release_memory=release_memory
    )
    if index is None:
        raise RuntimeError("DataAssembler: index is None")

    custom_mask_train = np.full_like(data_s, True, dtype=np.bool_)
    if func_train_or_pred is not None:
        custom_mask_train = func_train_or_pred(custom_mask_lst) & custom_mask_train

    custom_mask_non_train = np.full_like(data_s, True, dtype=np.bool_)
    if func_non_train is not None:
        custom_mask_non_train = func_non_train(custom_mask_lst) & custom_mask_non_train

    training_location = data_s & data_m & custom_mask_train
    training_index = index[training_location].astype(np.int64)

    non_training_location = (~data_s) & data_m & custom_mask_non_train
    non_training_index = index[non_training_location].astype(np.int64)

    return training_index, non_training_index, data_x, data_y, training_location, non_training_location


def squeeze_idx_data(training_index, non_training_index, data_x, data_y):
    idx = np.concatenate([training_index, non_training_index], axis=0)
    idx = np.sort(np.unique(idx.flatten()))
    tmp = np.zeros(idx.max() + 1, dtype=int) - 1
    tmp[idx] = np.arange(idx.size)
    training_index_ = tmp[training_index.flatten()].reshape(training_index.shape)
    non_training_index_ = tmp[non_training_index.flatten()].reshape(non_training_index.shape)
    data_x_ = data_x[idx]
    data_y_ = data_y[idx]
    return training_index_, non_training_index_, data_x_, data_y_


class DataLocation:
    def __init__(self, data):
        self.data = data  # (n_samples, n_features)

    def locate(self, index):
        """返回(n_samples, n_steps, n_features)"""
        if isinstance(self.data, np.ndarray):
            tr_x = np.empty((*index.shape, self.data.shape[-1]), dtype=self.data.dtype)
        else:
            tr_x = torch.empty((*index.shape, self.data.shape[-1]), dtype=self.data.dtype)
        idx = index.flatten()
        for i in range(self.data.shape[-1]):
            tr_x[:, :, i] = self.data[:, i][idx].reshape(index.shape)  # type: ignore
        return tr_x


class Pipeline:
    project_path: pathlib.Path
    df_aligner: DFAligner
    full_y_hat: npt.NDArray[np.float64]

    def __init__(self, project_path: Union[str, pathlib.Path] = ""):
        self.project_path = pathlib.Path(project_path)
        self.df_aligner = DFAligner()

    def _init_y_hat(self, training_location, non_training_location):
        self.full_y_hat = np.full(self.df_aligner.shape, np.nan).reshape(-1, 1)
        self.full_y_hat[training_location] = 0
        self.full_y_hat[non_training_location] = 1

    def reverse_engineering(self, values, ty: Literal["apply", "train", "non_train"] = "apply"):
        full_y_hat = np.full_like(self.full_y_hat, np.nan)
        if ty == "train" or ty == "apply":
            full_y_hat[self.full_y_hat == 0] = values.flatten()
        elif ty == "non_train":
            full_y_hat[self.full_y_hat == 1] = values.flatten()
        return self.df_aligner.to_dataframe(full_y_hat)


class ResearchPipeline(Pipeline):
    """
    Examples:
        >>> from aichemy.data_ops import ResearchPipeline
        >>> from aichemy.database_manager import DatabaseManager
        >>> from tqdm import tqdm
        >>> quotation = DatabaseManager()
        >>> quotation.bind_db("quotation.h5")
        >>> factor = DatabaseManager()
        >>> factor.bind_db("factor.h5")
        >>> data = {}
        >>> for i in tqdm(factor.info):
        ...     data[f"[x]{i}"] = factor[i]
        >>> data["[x]quote_rate"] = quotation["quote_rate"]
        >>> data["[y]1"] = quotation["quote_rate"].shift(-3).rolling(3).apply(lambda x: x.cumsum().max())
        >>> data["[y]2"] = quotation["quote_rate"].shift(-3).rolling(3).apply(lambda x: x.cumsum().min())
        >>> industry_turnover_dev5 = (quotation["turnover"] / quotation["turnover"].rolling(5).mean() - 1).dropna(how="all", axis=1)
        >>> data["[trigger]"] = industry_turnover_dev5 > np.percentile(industry_turnover_dev5.dropna(), 0, axis=0, keepdims=True)
        >>> research_pipeline = ResearchPipeline("./v1", num_steps=20, dump=True)
        >>> training_x, non_training_x, training_y, non_training_y = research_pipeline.process_data(data)
    """

    cs_x_scaling_method: scaling_method
    cs_y_scaling_method: scaling_method
    cs_scaling_fillna: bool
    x_scaling_method: scaling_method
    y_scaling_method: scaling_method
    scaling_fillna: bool
    num_steps: Optional[int]
    non_training_size: float
    dump: bool
    drop_threshold_nan_ratio: float

    def __init__(
        self,
        project_path: Union[str, pathlib.Path] = "",
        cs_x_scaling_method: scaling_method = "none",
        cs_y_scaling_method: scaling_method = "none",
        cs_scaling_fillna: bool = True,
        x_scaling_method: scaling_method = "zscore",
        y_scaling_method: scaling_method = "zscore",
        scaling_fillna: bool = True,
        num_steps: Optional[int] = None,
        non_training_size=0.25,
        dump: bool = False,
        drop_threshold_nan_ratio: float = 1.0,
    ):
        super().__init__(project_path)
        self.cs_x_scaling_method = cs_x_scaling_method
        self.cs_y_scaling_method = cs_y_scaling_method
        self.cs_scaling_fillna = cs_scaling_fillna
        self.x_scaling_method = x_scaling_method
        self.y_scaling_method = y_scaling_method
        self.scaling_fillna = scaling_fillna
        self.num_steps = num_steps
        self.non_training_size = non_training_size
        self.dump = dump
        self.drop_threshold_nan_ratio = drop_threshold_nan_ratio

    # def reverse_engineering(self, values, ty):
    #     full_y_hat = self.full_y_hat.copy()
    #     if ty == "TRAIN":
    #         full_y_hat[self.full_y_hat == 1] = np.nan
    #         full_y_hat[self.full_y_hat == 0] = values.flatten()
    #     # TODO: 这里不是测试，而是验证集+测试集
    #     elif ty == "TEST":
    #         full_y_hat[self.full_y_hat == 0] = np.nan
    #         full_y_hat[self.full_y_hat == 1] = values.flatten()
    #     return self.df_aligner.to_dataframe(full_y_hat)

    def _step1_align_filter_csscaling(
        self, data: MutableMapping[str, pd.DataFrame], inplace: bool
    ) -> MutableMapping[str, pd.DataFrame]:
        """
        # 第一步
        1. 异常值处理，如inf
        2. 截面数据处理，如截面上做标准化、排序等操作
        3. 删掉全为NaN的factor
        4. 数据对齐
        """
        # 删掉全为NaN的factor
        # 数据对齐, 先执行对齐，因为要先处理trigger, 这里不删除全为空的列，要为标准化提供足够的数据
        data_ = self.df_aligner(data, mode="align", num_step=None, inplace=inplace)
        logger.success(f"{self.__class__.__name__}: 数据对齐(align)完成，当前shape：{self.df_aligner.shape}")

        filter_invalid_features = FilterInvalidFeatures(self.drop_threshold_nan_ratio)
        data_ = filter_invalid_features.fit_transform(data_, inplace=inplace)
        if self.dump:
            filter_invalid_features.dump(pathlib.Path(self.project_path, "filter_invalid_features.pkl"))
        if len(filter_invalid_features.dropout) > 0:
            logger.warning(
                "{}: 删掉全局有效值比例低于{:.2%}的factor, 删去{}".format(
                    self.__class__.__name__,
                    1 - self.drop_threshold_nan_ratio,
                    ",".join([f"{k}({v:.2%})" for k, v in filter_invalid_features.dropout.items()]),
                )
            )
        else:
            logger.success(
                f"{self.__class__.__name__}: 不存在全局有效值比例低于{1 - self.drop_threshold_nan_ratio}的特征"
            )

        # 截面数据处理
        cs_scaler = ACScaler(
            method_x=self.cs_x_scaling_method, method_y=self.cs_y_scaling_method, fillna=self.cs_scaling_fillna
        )
        data_ = cs_scaler.fit_transform(data_, inplace=inplace)
        if self.dump:
            self.project_path.mkdir(parents=True, exist_ok=True)
            cs_scaler.dump(pathlib.Path(self.project_path, "cs_scaler.pkl"))
        logger.success(f"{self.__class__.__name__}: {cs_scaler} 截面数据处理完成")

        return data_

    def _step3_scaling_align(
        self, data: MutableMapping[str, pd.DataFrame], need_reduce: bool, inplace: bool
    ) -> MutableMapping[str, pd.DataFrame]:
        """
        # 第三步
        1. 全局标准化
        2. 数据对齐，删除全为空的列和用不上的行
        """
        # 标准化
        tmp = list(data.keys())
        scaler = ACScaler(method_x=self.x_scaling_method, method_y=self.y_scaling_method, fillna=self.scaling_fillna)
        data_ = scaler.fit_transform(data, inplace=inplace)
        if self.dump:
            scaler.dump(pathlib.Path(self.project_path, "scaler.pkl"))
        if len(set(data_.keys()) - set(tmp)) > 0:
            logger.warning(
                f"{self.__class__.__name__}: {scaler} 标准化完成, 删去{','.join(set(data_.keys()) - set(tmp))}"
            )
        else:
            logger.success(f"{self.__class__.__name__}: {scaler} 标准化完成")

        # 异常值处理
        data_ = handle_outlier(data_, inplace=inplace)
        logger.success(f"{self.__class__.__name__}: 处理异常值完成")

        if need_reduce:
            # 数据对齐, 删除全为空的列
            data_ = self.df_aligner(data_, mode="reduce", num_step=self.num_steps, inplace=inplace)
            # data_ = {k: v.to_numpy() for k, v in data_.items()}
            logger.success(f"{self.__class__.__name__}: 数据对齐(reduce)完成，当前shape：{self.df_aligner.shape}")

        return data_

    def _pre_step4_assemblerfit(self, data: Mapping[str, pd.DataFrame]):
        data_assembler = DataAssembler(self.num_steps)
        data_assembler.fit(data)
        if self.dump:
            data_assembler.dump(pathlib.Path(self.project_path, "data_assembler.pkl"))
        return data_assembler

    def _step4_assemble(
        self,
        data: MutableMapping[str, pd.DataFrame],
        release_memory: bool,
        chunk_num: int,
        func_train: Optional[Callable],
        func_non_train: Optional[Callable],
        max_assemblers: int,
    ):
        # 数据聚合
        assemble_fnc = resource_limit(max_concurrent=max_assemblers)(_assemble_data)
        training_x, non_training_x, training_y, non_training_y, training_location, non_training_location = assemble_fnc(
            self._pre_step4_assemblerfit(data),
            data,
            is_apply=False,
            release_memory=release_memory,
            chunk_num=chunk_num,
            func_train_or_pred=func_train,
            func_non_train=func_non_train,
        )
        self._init_y_hat(training_location, non_training_location)
        return training_x, non_training_x, training_y, non_training_y

    def _step4_idx_assemble(
        self,
        data: MutableMapping[str, pd.DataFrame],
        release_memory: bool,
        func_train: Optional[Callable],
        func_non_train: Optional[Callable],
        max_assemblers: int,
    ):
        assemble_fnc = resource_limit(max_concurrent=max_assemblers)(_assemble_idx_data)
        # 数据聚合
        training_index, non_training_index, data_x, data_y, training_location, non_training_location = assemble_fnc(
            self._pre_step4_assemblerfit(data),
            data,
            is_apply=False,
            release_memory=release_memory,
            func_train_or_pred=func_train,
            func_non_train=func_non_train,
        )
        self._init_y_hat(training_location, non_training_location)
        return training_index, non_training_index, data_x, data_y

    # def cross_val(self, data, split_method, chunk_num=1, func_train=None, func_non_train=None):
    #     logger.disable("aichemy.data_ops.pipeline")
    #     data1 = self._step1_align_filter_csscaling(data)
    #     for train_index, _ in split_method.split(np.arange(data1["[trigger]"].size)):
    #         split = np.full(data1["[trigger]"].size, False, dtype=bool)
    #         split[train_index] = True
    #         data1["[split]"] = pd.DataFrame(
    #             split.reshape(data1["[trigger]"].shape),
    #             index=data1["[trigger]"].index,
    #             columns=data1["[trigger]"].columns,
    #         )
    #         data2 = self._step3_scaling_align(data1)
    #         yield self._step4_assemble(data2, chunk_num, func_train, func_non_train)
    #     logger.enable("aichemy.data_ops.pipeline")

    # def cross_val_idx(self, data, split_method, func_train=None, func_non_train=None):
    #     logger.disable("aichemy.data_ops.pipeline")
    #     data1 = self._step1_align_filter_csscaling(data)
    #     for train_index, _ in split_method.split(np.arange(data1["[trigger]"].size)):
    #         split = np.full(data1["[trigger]"].size, False, dtype=bool)
    #         split[train_index] = True
    #         data1["[split]"] = pd.DataFrame(
    #             split.reshape(data1["[trigger]"].shape),
    #             index=data1["[trigger]"].index,
    #             columns=data1["[trigger]"].columns,
    #         )
    #         data2 = self._step3_scaling_align(data1)
    #         yield self._step4_idx_assemble(data2, func_train, func_non_train)
    #     logger.enable("aichemy.data_ops.pipeline")

    def process_data(
        self,
        data: MutableMapping[str, pd.DataFrame],
        shuffle: bool = True,
        enable_log: bool = True,
        chunk_num: int = 1,
        func_train=None,
        func_non_train=None,
        need_reduce: bool = True,
        inplace: bool = False,
        release_memory: bool = False,
        max_assemblers: int = 5,
    ):
        """返回的是np.ndarray，分别为训练集x，非训练集x，训练集y，非训练集y"""
        if not enable_log:
            logger.disable("aichemy.data_ops.pipeline")

        data_ = self._step1_align_filter_csscaling(data, inplace=inplace)

        # step2: 切分训练集和测试集
        if "[split]" not in data_.keys():
            data_["[split]"] = get_training_set_label(data_["[trigger]"], self.non_training_size, shuffle=shuffle)
        logger.success(f"{self.__class__.__name__}: 切分训练集和测试集完成")

        data_ = self._step3_scaling_align(data_, need_reduce, inplace=inplace)

        training_x, non_training_x, training_y, non_training_y = self._step4_assemble(
            data_,
            release_memory=release_memory,
            chunk_num=chunk_num,
            func_train=func_train,
            func_non_train=func_non_train,
            max_assemblers=max_assemblers,
        )

        if not enable_log:
            logger.enable("aichemy.data_ops.pipeline")
        logger.success(
            "{}: 数据预处理完成, training_sample_size: {}, non_training_sample_size: {}, feature_length: {}, num_steps: {}, label_length: {}".format(
                self.__class__.__name__,
                training_x.shape[0],
                non_training_x.shape[0],
                training_x.shape[-1],
                self.num_steps,
                training_y.shape[-1],
            )
        )

        return training_x, non_training_x, training_y, non_training_y

    def process_idx_data(
        self,
        data: MutableMapping[str, pd.DataFrame],
        shuffle: bool = True,
        enable_log: bool = True,
        func_train=None,
        func_non_train=None,
        need_reduce: bool = True,
        inplace: bool = False,
        release_memory: bool = False,
        max_assemblers: int = 5,
    ):
        """返回的是np.ndarray，分别为训练集索引，非训练集索引，全部X，全部y"""
        if not enable_log:
            logger.disable("aichemy.data_ops.pipeline")

        data_ = self._step1_align_filter_csscaling(data, inplace=inplace)

        # step2: 切分训练集和测试集
        if "[split]" not in data_.keys():
            data_["[split]"] = get_training_set_label(data_["[trigger]"], self.non_training_size, shuffle=shuffle)
        logger.success(f"{self.__class__.__name__}: 切分训练集和测试集完成")

        data_ = self._step3_scaling_align(data_, need_reduce, inplace=inplace)

        training_index, non_training_index, data_x, data_y = self._step4_idx_assemble(
            data_,
            release_memory=release_memory,
            func_train=func_train,
            func_non_train=func_non_train,
            max_assemblers=max_assemblers,
        )

        if not enable_log:
            logger.enable("aichemy.data_ops.pipeline")
        logger.success(
            "{}: 数据预处理完成, training_sample_size: {}, non_training_sample_size: {}, feature_length: {}, num_steps: {}, label_length: {}".format(
                self.__class__.__name__,
                training_index.shape[0],
                non_training_index.shape[0],
                data_x.shape[-1],
                self.num_steps,
                data_y.shape[-1],
            )
        )

        return training_index, non_training_index, data_x, data_y


class ApplyPipeline(Pipeline):
    """
    Examples:
        >>> train_data = {k: data_ops.slice_dataset(v, t1, t2, "both") for k, v in data.items()}
        >>> apply_data = {k: data_ops.slice_dataset(v, t2 - pd.Timedelta("60D"), t3, "right") for k, v in data.items()}

        >>> research_pipeline = ResearchPipeline("./test", num_steps=20, dump=True)
        >>> training_x, non_training_x, training_y, non_training_y = research_pipeline.process_data(train_data)

        >>> apply_pipeline = ApplyPipeline("./test")
        >>> apply_x = torch.tensor(apply_pipeline.process_data(apply_data)).float()
    """

    def __init__(self, project_path: str):
        super().__init__(project_path)

    def _preprocess_data(self, data: MutableMapping[str, pd.DataFrame], inplace: bool):
        data_ = {k: v for k, v in data.items() if not k == "[split]"}

        # 删掉全为NaN的factor
        filter_invalid_features: FilterInvalidFeatures = FilterInvalidFeatures.load(
            pathlib.Path(self.project_path, "filter_invalid_features.pkl")
        )
        data_ = filter_invalid_features.transform(data_, inplace=inplace)
        if len(filter_invalid_features.dropout) > 0:
            logger.warning(
                "{}: 删掉全局有效值比例低于{:.2%}的factor, 删去{}".format(
                    self.__class__.__name__,
                    1 - filter_invalid_features.drop_threshold_nan_ratio,
                    ",".join(filter_invalid_features.dropout.keys()),
                )
            )
        else:
            logger.success(
                f"{self.__class__.__name__}: 不存在全局有效值比例低于{1 - filter_invalid_features.drop_threshold_nan_ratio}的特征"
            )

        # 数据对齐
        data_ = self.df_aligner(data_, mode="align", num_step=None, inplace=inplace)
        logger.success(f"{self.__class__.__name__}: 数据对齐完成，当前shape：{self.df_aligner.shape}")

        cs_scaler: ACScaler = ACScaler.load(pathlib.Path(self.project_path, "cs_scaler.pkl"))
        data_ = cs_scaler.transform(data_, inplace=inplace)
        logger.success(f"{self.__class__.__name__}: {cs_scaler} 截面数据处理完成")

        # 标准化
        tmp = list(data_.keys())
        scaler: ACScaler = ACScaler.load(pathlib.Path(self.project_path, "scaler.pkl"))
        data_ = scaler.transform(data_, inplace=inplace)
        if len(tmp := set(data_.keys()) - set(tmp)) > 0:
            logger.warning(f"{self.__class__.__name__}: {scaler} 标准化完成，删去{','.join(tmp)}")
        else:
            logger.success(f"{self.__class__.__name__}: {scaler} 标准化完成")

        # 异常值处理
        data_ = handle_outlier(data_, inplace=inplace)
        logger.success(f"{self.__class__.__name__}: 处理异常值完成")

        return data_

    def process_data(
        self,
        data: MutableMapping[str, pd.DataFrame],
        enable_log: bool = True,
        chunk_num: int = 1,
        func_pred=None,
        need_reduce: bool = True,
        inplace: bool = False,
        release_memory: bool = False,
        max_assemblers: int = 5,
    ) -> Tuple[npt.NDArray, npt.NDArray]:
        """返回的全是np.ndarray, 为pred_x"""
        if not enable_log:
            logger.disable("aichemy.data_ops.pipeline")

        data_ = self._preprocess_data(data, inplace=inplace)
        data_assembler: DataAssembler = DataAssembler.load(pathlib.Path(self.project_path, "data_assembler.pkl"))

        # 这里需要补一下[y]，因为训练的时候肯定是有[y]的，会被保存到DataAssembler中
        if "[y]" not in data_.keys():
            logger.warning(f"{self.__class__.__name__}: 原数据中不存在[y]，添加全为1的[y]")
            data_["[y]"] = pd.DataFrame(1, index=self.df_aligner.index, columns=self.df_aligner.columns)
        # else:
        #     logger.warning(f"{self.__class__.__name__}: 原数据中存在[y]，替换为全为1的[y]")

        if need_reduce:
            # 数据对齐, 删除全为空的列
            data_ = self.df_aligner(data_, mode="reduce", num_step=data_assembler.num_steps, inplace=inplace)
            # data_ = {k: v.to_numpy() for k, v in data_.items()}
            logger.success(f"{self.__class__.__name__}: 数据对齐(reduce)完成，当前shape：{self.df_aligner.shape}")

        # 数据聚合
        assemble_fnc = resource_limit(max_concurrent=max_assemblers)(_assemble_data)
        apl_x, _, true_y, _, training_location, non_training_location = assemble_fnc(
            data_assembler,
            data_,
            is_apply=True,
            release_memory=release_memory,
            chunk_num=chunk_num,
            func_train_or_pred=func_pred,
            func_non_train=None,
        )

        self._init_y_hat(training_location, non_training_location)

        if not enable_log:
            logger.enable("aichemy.data_ops.pipeline")
        logger.success(f"{self.__class__.__name__}: 数据预处理完成，数据量为{apl_x.shape}")

        return apl_x, true_y

    def process_idx_data(
        self,
        data: MutableMapping[str, pd.DataFrame],
        enable_log: bool = True,
        func_pred=None,
        need_reduce: bool = True,
        inplace: bool = False,
        release_memory: bool = False,
        max_assemblers: int = 5,
    ) -> Tuple[npt.NDArray, npt.NDArray, npt.NDArray]:
        """返回的是np.ndarray，分别为pred_index和data_x"""
        if not enable_log:
            logger.disable("aichemy.data_ops.pipeline")

        data_ = self._preprocess_data(data, inplace=inplace)
        data_assembler: DataAssembler = DataAssembler.load(pathlib.Path(self.project_path, "data_assembler.pkl"))

        # 这里需要补一下[y]，因为训练的时候肯定是有[y]的，会被保存到DataAssembler中
        if "[y]" not in data_.keys():
            logger.warning(f"{self.__class__.__name__}: 原数据中不存在[y]，添加全为1的[y]")
            data_["[y]"] = pd.DataFrame(1, index=self.df_aligner.index, columns=self.df_aligner.columns)
        # else:
        #     logger.warning(f"{self.__class__.__name__}: 原数据中存在[y]，替换为全为1的[y]")

        if need_reduce:
            # 数据对齐, 删除全为空的列
            data_ = self.df_aligner(data_, mode="reduce", num_step=data_assembler.num_steps, inplace=inplace)
            # data_ = {k: v.to_numpy() for k, v in data_.items()}
            logger.success(f"{self.__class__.__name__}: 数据对齐(reduce)完成，当前shape：{self.df_aligner.shape}")

        # 数据聚合
        assemble_fnc = resource_limit(max_concurrent=max_assemblers)(_assemble_idx_data)
        pred_index, _, data_x, data_y, training_location, non_training_location = assemble_fnc(
            data_assembler,
            data_,
            is_apply=True,
            release_memory=release_memory,
            func_train_or_pred=func_pred,
            func_non_train=None,
        )

        self._init_y_hat(training_location, non_training_location)

        if not enable_log:
            logger.enable("aichemy.data_ops.pipeline")
        logger.success(f"{self.__class__.__name__}: 数据预处理完成，数据量为{len(pred_index)}")

        return pred_index, data_x, data_y

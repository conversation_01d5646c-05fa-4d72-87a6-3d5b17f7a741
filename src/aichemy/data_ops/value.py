from typing import Mapping, MutableMapping, Sequence, Union

import numpy as np
import pandas as pd

from .base import ACTransformer


def handle_outlier(df_dict: MutableMapping[str, pd.DataFrame], inplace: bool) -> MutableMapping[str, pd.DataFrame]:
    """第一步，处理越界值，将inf和-inf替换为nan"""
    if inplace:
        for key in df_dict.keys():
            if key.startswith("[x]") or key.startswith("[y]"):
                df_dict[key].replace([np.inf, -np.inf], np.nan, inplace=True)
        return df_dict
    else:
        ret = {}
        for key in df_dict.keys():
            if key.startswith("[x]") or key.startswith("[y]"):
                ret[key] = df_dict[key].replace([np.inf, -np.inf], np.nan, inplace=False)
            else:
                ret[key] = df_dict[key].copy(deep=True)
        return ret


class FilterInvalidFeatures(ACTransformer):
    """第二步，把有效值比例低于阈值的特征剔除，无效值包括NaN和Inf。如果去掉无效值之后所有值都相同，也剔除"""

    drop_threshold_nan_ratio: float
    _keep_lst: Sequence[str]
    _drop_lst: Sequence[str]
    _valid_percentage: MutableMapping[str, float]

    def __init__(self, drop_threshold_nan_ratio: float = 1.0):
        # 初始化过滤阈值和存储列表
        self.drop_threshold_nan_ratio = drop_threshold_nan_ratio
        self._keep_lst = []  # 保留的特征列表，包括[y],[trigger],[mask],[x]
        self._drop_lst = []  # 删除的特征列表，仅包括[x]
        self._valid_percentage = {}  # 存储每个特征的有效值百分比

    def fit(self, df_dict: Mapping[str, Union[pd.DataFrame, np.ndarray]]) -> None:
        """计算每个特征的有效值百分比，并根据阈值筛选要保留的特征
        仅对[x]特征进行处理
        不考虑[trigger]

        Args:
            df_dict (Mapping[str, Union[pd.DataFrame, np.ndarray]]): data, 要求输入的shape一致
        """
        self._keep_lst = []
        for key, value in df_dict.items():
            if not key.startswith("[x]"):
                self._keep_lst.append(key)
                continue

            # 将DataFrame转换为numpy数组
            if isinstance(value, pd.DataFrame):
                value_arr = value.to_numpy()
            else:
                value_arr = value

            # 获取有效值（非NaN和非无穷大）
            valid_value = value_arr[np.isfinite(value_arr)]

            # 计算非NaN的百分比
            if valid_value.size == 0 or np.all(valid_value == valid_value.flat[0]):  # 空array 或 除去nan后全一样
                self._valid_percentage[key] = 0.0
            else:
                self._valid_percentage[key] = valid_value.size / value_arr.size

        # 归一化非NaN百分比
        tmp = pd.Series(self._valid_percentage)
        tmp /= tmp.max()

        # 根据阈值筛选要保留的特征
        self._keep_lst.extend(tmp.loc[tmp > (1 - self.drop_threshold_nan_ratio)].index.tolist())

        # 计算要删除的特征列表
        self._drop_lst = list(set(df_dict.keys()).difference(self._keep_lst))

    def transform(self, df_dict: MutableMapping[str, pd.DataFrame], inplace: bool) -> MutableMapping[str, pd.DataFrame]:
        """返回保留的特征"""
        if not inplace:
            res = {}
            for key, value in df_dict.items():
                if key in self._keep_lst:
                    res[key] = value.copy(deep=True)
            return res
        else:
            keys_to_drop = set(df_dict.keys()).difference(self._keep_lst)
            for key in keys_to_drop:
                df_dict.pop(key, None)
            return df_dict

    def fit_transform(
        self, df_dict: MutableMapping[str, pd.DataFrame], inplace: bool
    ) -> MutableMapping[str, pd.DataFrame]:
        self.fit(df_dict)
        return self.transform(df_dict, inplace)

    @property
    def dropout(self) -> Mapping[str, float]:
        """返回被删除的特征及其有效值百分比"""
        return {k: v for k, v in self._valid_percentage.items() if k in self._drop_lst}

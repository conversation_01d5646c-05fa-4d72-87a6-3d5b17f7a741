from typing import Literal, MutableMapping, Optional, Sequence, Union, overload

import numpy as np
import numpy.typing as npt
import pandas as pd

from ..utils import get_intersection


@overload
def convert_to_bool(x: pd.DataFrame) -> pd.DataFrame: ...
@overload
def convert_to_bool(x: np.ndarray) -> npt.NDArray[np.bool_]: ...
def convert_to_bool(
    x: Union[pd.DataFrame, np.ndarray],
) -> Union[pd.DataFrame, npt.NDArray[np.bool_]]:
    """将数据转换为bool类型，其中np.nan会被转换为False

    Args:
        x (Union[pd.DataFrame, np.ndarray]): mask

    Returns:
        Union[pd.DataFrame, npt.NDArray[np.bool_]]: bool类型
    """
    # 默认的np.nan会被转换为True, 所以需要额外处理
    return (~pd.isna(x)) & x.astype(bool)


@overload
def merge_masks(*masks: pd.DataFrame) -> pd.DataFrame: ...
@overload
def merge_masks(*masks: np.ndarray) -> np.ndarray: ...
def merge_masks(
    *masks: Union[pd.DataFrame, np.ndarray],
) -> Union[pd.DataFrame, np.ndarray]:
    """将多个mask合并

    Args:
        masks (Union[pd.DataFrame, np.ndarray]): mask

    Returns:
        Union[pd.DataFrame, np.ndarray]: 合并后的mask, 不会有NaN
    """
    if len(type_ := set([type(i) for i in masks])) != 1:
        raise ValueError("masks的类型必须相同")
    if list(type_)[0] == np.ndarray:
        if len(set([i.shape for i in masks])) != 1:
            raise ValueError("masks为np.ndarray时，shape必须相同")
    res = convert_to_bool(masks[0])
    for mask in masks[1:]:
        res = convert_to_bool(res & convert_to_bool(mask))
    return res


# TODO 时间轴没对齐时，需要用最近的数据来填充
def transform_time_axis(df: pd.DataFrame, index: pd.Index, delta=0) -> pd.DataFrame:
    """数据索引对齐，如果索引的当前时间没有数据，则以最近的有效数据来填充

    Args:
        df (pd.DataFrame): 原始数据
        index (pd.Index): 对齐到的索引

    Returns:
        pd.DataFrame: 对齐之后的数据，返回new object
    """
    if delta == 0:
        return df.reindex(index=index)
    index_ = np.sort(index)
    ret = df.reindex(index=df.index.union(index_)).sort_index()  # type: ignore
    ret.ffill(inplace=True)
    ret = ret.reindex(index=index_)
    return ret


class DFAligner:
    """用于对df的index和columns进行对齐，会保存index和columns"""

    _index: pd.Index
    _columns: pd.Index

    def __init__(self, index: Optional[Sequence] = None) -> None:
        """初始化，用于对df的index和columns进行对齐

        Args:
            index (Optional[Sequence], optional): index. 有可能出现要过滤某段时间的情况. Defaults to None.
        """
        self._index = pd.Index(index).sort_values() if index is not None else pd.Index([])

    def __call__(
        self,
        df_dict: MutableMapping[str, pd.DataFrame],
        mode: Literal["align", "reduce"],
        num_step: Optional[int],
        inplace: bool,
    ) -> MutableMapping[str, pd.DataFrame]:
        """首先确认index和columns，如果之前初始化对象时没有传入index，则优先使用y的index，再没有则使用x的index；
        返回的value是new object

        Args:
            df_dict (MutableMapping[str, pd.DataFrame]): key - feature name, value - pd.DataFrame, index为time, columns为symbol
            mode (Literal['align', 'reduce'], optional): 'align' - 对齐，'reduce' - 删去空列.
            num_step (Optional[int], optional): 步数.
            inplace (bool, optional): 是否在原数据上进行修改.

        Returns:
            MutableMapping[str, pd.DataFrame]: key - feature name
        """
        if mode == "align":
            # TODO
            # 1. num_step不为None的情况下，x是不是不需要和y同频率?
            # 2. 没有y的情况，不允许存在

            # 第一步，没有index的情况下，依次取y, trigger, x的index
            if self._index.empty:
                for tmp in ["[y]", "[trigger]", "[x]"]:
                    self._index = pd.Index(
                        get_intersection(*[df_dict[key].index for key in df_dict.keys() if key.startswith(tmp)])
                    ).sort_values()
                    if not self._index.empty:
                        break

            # 第二步，对于每个特征，要先去掉全为NaN的列得出columns，再把所有特征的columns进行并集，得出最终的columns
            columns = pd.Index([], dtype="str")
            for key in df_dict.keys():
                if not (key.startswith("[y]") or key.startswith("[x]")):
                    continue
                # 删除全为NaN的列
                columns_ = df_dict[key].dropna(how="all", axis=1).columns
                if columns.empty:
                    columns = columns_
                else:
                    # columns求并集
                    columns = columns.union(columns_)
            self._columns = columns.sort_values()

            # 第三步，对所有数据进行对齐
            res = {} if not inplace else df_dict
            for key in df_dict.keys():
                # 只对x进行时间轴变换，trigger和y需要自行保证时间轴准确
                if key.startswith("[x]"):
                    res[key] = transform_time_axis(df_dict[key], self._index).reindex(columns=self._columns)
                else:
                    res[key] = df_dict[key].reindex(index=self._index, columns=self._columns)
                    # trigger对齐后可能会出现NaN，所以进行转换，把NaN转换成False
                    if "[trigger]" in key or "[split]" in key or "[mask]" in key or "[valid]" in key:
                        res[key] = convert_to_bool(res[key])
            return res

        elif mode == "reduce":
            for key in df_dict.keys():
                # 删去不需要的行
                if num_step is not None:
                    tmp = pd.isna(df_dict[key]).all(axis=1)
                    tmp = (
                        tmp.rolling(num_step)
                        .apply(lambda x: x.all())
                        .shift(1 - num_step)
                        .replace(np.nan, 0)
                        .astype(bool)
                    )
                    index_ = df_dict[key].index[~tmp]
                    self._index = self._index.intersection(index_)

                # 删除全为NaN的列
                columns_ = df_dict[key].dropna(how="all", axis=1).columns
                # columns求交集
                self._columns = self._columns.intersection(columns_)

            self._index = self._index.sort_values()
            self._columns = self._columns.sort_values()

            res = {} if not inplace else df_dict
            for key in df_dict.keys():
                # 只对x进行时间轴变换，trigger和y需要自行保证时间轴准确
                res[key] = df_dict[key].reindex(index=self._index, columns=self._columns)
            return res

        else:
            raise ValueError(f"DFAligner: invalid mode: {mode}")

    def to_dataframe(self, arr: npt.NDArray[np.float64]) -> pd.DataFrame:
        return pd.DataFrame(arr.reshape(self.shape), index=self._index, columns=self._columns)

    @property
    def index(self):
        return self._index

    @property
    def columns(self):
        return self._columns

    @property
    def shape(self):
        return (self._index.size, self._columns.size)

import pickle
from typing import Callable, Mapping, Optional

import numpy as np
import pandas as pd
from loguru import logger

from ..base import Model


class ACTransformer(Model):
    def fit(self, *args, **kwargs): ...
    def transform(self, *args, **kwargs) -> Mapping[str, pd.DataFrame]: ...
    def fit_transform(self, *args, **kwargs) -> Mapping[str, pd.DataFrame]:
        self.fit(*args, **kwargs)
        return self.transform(*args, **kwargs)


def run_construct_dataset(
    fnc_construct_dataset: Callable, add_trigger_like_y: bool = True, **kwargs
) -> Mapping[str, pd.DataFrame]:
    """运行构造数据集的函数，并检查数据完整性

    Args:
        fnc_construct_dataset (Callable): 构造数据集的函数
        add_trigger_like_y (bool, optional): 如果数据集中没有触发器，是否添加触发器. Defaults to True.

    Raises:
        ValueError: 数据集中没有特征数据
        ValueError: 数据集中没有标签数据
        ValueError: 数据集中没有触发器数据
        ValueError: 数据集中没有触发器数据，且add_trigger_like_y为False

    Returns:
        Mapping[str, pd.DataFrame]: 构造好的数据集
    """
    # 调用构造数据集的函数
    data = fnc_construct_dataset(**kwargs)

    # 初始化标志变量
    x_isin = False
    y_isin = False
    trigger_isin = False
    tmp = pd.DataFrame()

    # 遍历数据字典的键
    for i in data.keys():
        # 检查是否存在特征数据
        if "[x]" in i and not x_isin:
            x_isin = True
        # 检查是否存在标签数据
        if "[y]" in i and not y_isin:
            if add_trigger_like_y:
                # 如果需要添加触发器，创建一个全为True的DataFrame
                tmp = pd.DataFrame(np.full(data[i].shape, True), index=data[i].index, columns=data[i].columns)
            y_isin = True
        # 检查是否存在触发器数据
        if i == "[trigger]" and not trigger_isin:
            trigger_isin = True
    else:
        # 检查数据完整性
        if not x_isin:
            raise ValueError("x is not in data")
        if not y_isin:
            raise ValueError("y is not in data")
        if not trigger_isin:
            if add_trigger_like_y:
                if tmp.empty:
                    raise ValueError("trigger is not in data and add trigger failed")
                else:
                    # 添加触发器数据
                    data["[trigger]"] = tmp
            else:
                raise ValueError("trigger is not in data")

    # 返回处理后的数据集
    return data


def check(timestamp, data):
    ret = []
    for k, v in data.items():
        if timestamp not in v.index or v.loc[timestamp].isna().all():
            logger.warning(f"{k} 在 {timestamp} 的数据缺失")
            ret.append(k)
    return ret


class RawData:
    def __init__(self):
        self.data = {}
        self._columns = pd.Index([])

    def register(self, symbol: str, data: pd.DataFrame):
        """注册数据

        Args:
            symbol (str): 股票代码
            data (pd.DataFrame): 数据
        """
        self.data[symbol] = data
        if self._columns.empty:
            self._columns = data.columns
        else:
            if (tmp := data.columns.difference(self._columns)).size > 0:
                logger.warning(f"{symbol} 有新的特征：{tmp}")
                self._columns = self._columns.union(data.columns)

    @property
    def columns(self):
        return self._columns.tolist()

    @property
    def symbols(self):
        return list(self.data.keys())

    def to_aichemy_data(self):
        data = {}
        for column in self._columns:
            data[column] = pd.concat([v[column].rename(k) for k, v in self.data.items()], axis=1)
        return AichemyData(data)


class AichemyData:
    def __init__(self, data):
        self.data = data

    def add_trigger(self, trigger: Optional[pd.DataFrame] = None):
        if trigger is not None:
            self.data["[trigger]"] = trigger
        else:
            a = pd.Index([])
            b = pd.Index([])
            for k, v in self.data.items():
                if k.startswith("[y]"):
                    a = a.union(v.index)
                    b = b.union(v.columns)
            self.data["[trigger]"] = pd.DataFrame(1, index=a, columns=b)
        return self

    def add_split(self, dt):
        a = pd.Index([])
        b = pd.Index([])
        for k, v in self.data.items():
            if k.startswith("[y]") or k.startswith("[x]"):
                a = a.union(v.index)
                b = b.union(v.columns)
        split = pd.DataFrame(0, index=a, columns=b)
        split.loc[split.index <= pd.Timestamp(dt).value, :] = 1
        self.data["[split]"] = split
        return self

    def dump(self, filename):
        with open(filename, "wb") as f:
            pickle.dump(self.data, f)

    @classmethod
    def load(cls, filename):
        with open(filename, "rb") as f:
            return cls(pickle.load(f))

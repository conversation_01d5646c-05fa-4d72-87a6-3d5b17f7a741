from functools import wraps
from typing import Mapping, MutableMapping, Optional, Sequence, Tuple, Union

import numba as nb
import numpy as np
import numpy.typing as npt
import pandas as pd

from .base import ACTransformer


def _to_array(data: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
    return data.values if isinstance(data, pd.DataFrame) else data


@nb.njit(nb.float64[:, :, :](nb.float64[:, :], nb.int64), cache=True)
def stack_float(array: npt.NDArray[np.float64], num_step: int) -> npt.NDArray[np.float64]:
    """把array按时序转成3维

    Args:
        array (npt.NDArray[np.float64]): 原始数组
        num_step (int): 时序上的长度

    Returns:
        npt.NDArray[np.float64]: 3维数据，shape为(num_step, *array.shape)
    """
    ret = np.full((num_step, *array.shape), np.nan, dtype=np.float64)
    ret[num_step - 1, :, :] = array
    for i in range(1, num_step):
        ret[num_step - 1 - i, i:, :] = array[:-i, :]
    return ret


@nb.njit(nb.float64[:, :, :](nb.float64[:, :], nb.int64, nb.boolean[:, :]), cache=True)
def stack_float_by_col(
    array: npt.NDArray[np.float64], num_step: int, valid_mask: npt.NDArray[np.bool_]
) -> npt.NDArray[np.float64]:
    shape0, shape1 = array.shape
    ret = np.full((num_step, shape0, shape1), np.nan, dtype=np.float64)  # (num_step, shape0, shape1)
    for i in range(shape1):
        mask = valid_mask[:, i]  # (shape0,)
        idx = np.full((shape0,), np.nan, dtype=np.float64)
        idx[mask] = np.arange(mask.sum())
        values = array[mask, i]  # (shape0,)
        target_arr = ret[:, :, i]  # (num_step, shape0)
        target_arr[num_step - 1, mask] = values
        for j in range(1, num_step):
            target_arr[num_step - 1 - j, idx >= j] = values[:-j]
    return ret


def filter_nan_sample(
    data: Union[Mapping[str, npt.NDArray[np.float64]], Mapping[str, pd.DataFrame]], num_steps: int, is_apply: bool
) -> npt.NDArray[np.bool_]:
    tmp1 = None
    if not is_apply:
        keys = [j for j in data.keys() if "[x]" in j or "[y]" in j]
    else:
        keys = [j for j in data.keys() if "[x]" in j]
    for i in keys:
        tmp = np.isfinite(_to_array(data[i]))
        if tmp1 is None:
            tmp1 = tmp
        else:
            tmp1 = tmp1 & tmp
    if tmp1 is None:
        raise ValueError("数据集中没有包含[x]和[y]的数据")
    tmp1 = tmp1.astype(np.float64)

    if "[valid]" in data.keys():
        ret = stack_float_by_col(tmp1, num_steps, _to_array(data["[valid]"]))
    else:
        ret = stack_float(tmp1, num_steps)
    np.nan_to_num(ret, copy=False, nan=0.0)
    ret = ret.astype(np.bool_).all(axis=0).flatten()
    return ret


def custom_sort_key(tag: str):
    @wraps(custom_sort_key)
    def wrapper(s):
        for i in range(100):
            # 优先级为0的字符串
            if s == f"{tag}{i}":
                return (i, s)
        # 其他字符串
        else:
            return (100, s)

    return wrapper


class DataAssembler(ACTransformer):
    """将features和target聚合成完整的数据集，能够输入模型"""

    num_steps: Optional[int]
    mask_nan: bool
    x_lst: Sequence[str]
    y_lst: Sequence[str]

    def __init__(self, num_steps: Optional[int] = None, mask_nan=True):
        """初始化

        Args:
            num_steps (Optional[int], optional): num_steps. Defaults to None，num_steps不为None时，返回3维ndarray.
            mask_nan (bool, optional): 是否过滤掉包含nan的样本. Defaults to True.
        """
        self.num_steps = num_steps
        self.mask_nan = mask_nan

        self.x_lst = []
        self.y_lst = []

    def fit(self, df_dict: Union[Mapping[str, np.ndarray], Mapping[str, pd.DataFrame]]):
        # 自定义排序函数

        self.x_lst = [key for key in df_dict.keys() if "[x]" in key]
        self.y_lst = [key for key in df_dict.keys() if "[y]" in key]
        self.x_lst = sorted(self.x_lst, key=custom_sort_key("[x]"))
        self.y_lst.sort()

        if len(self.x_lst) == 0:
            raise ValueError("x_lst is empty")
        if len(self.y_lst) == 0:
            raise ValueError("y_lst is empty")

    def transform(
        self,
        array_dict: Union[MutableMapping[str, np.ndarray], MutableMapping[str, pd.DataFrame]],
        stack: bool,
        is_apply: bool,
        release_memory: bool,
    ) -> Tuple[
        npt.NDArray[np.float64],
        npt.NDArray[np.float64],
        npt.NDArray[np.bool_],
        npt.NDArray[np.bool_],
        Optional[npt.NDArray[np.float64]],
        Sequence[npt.NDArray[np.bool_]],
    ]:
        """聚合数据

        Args:
            array_dict (Union[MutableMapping[str, np.ndarray], MutableMapping[str, pd.DataFrame]]): 原始数据
            stack (bool): 是否堆叠.
            is_apply (bool): 是否应用阶段，如果是，则过滤nan时不考虑y.
            release_memory (bool): 是否释放内存.

        Raises:
            RuntimeError: DataAssembler: x_lst or y_lst is empty
            RuntimeError: DataAssembler: 缺少数据
            ValueError: num_steps为None时，stack必须为True
            ValueError: 至多只能有3个mask，分别用于train, valid, test

        Returns:
            Tuple[npt.NDArray[np.float64],npt.NDArray[np.float64],npt.NDArray[np.bool_], npt.NDArray[np.bool_], Optional[npt.NDArray[np.float64]], Sequence[npt.NDArray[np.bool_]]]: x, y, split, mask, index, custom_mask_lst
        """
        if len(self.x_lst) == 0 or len(self.y_lst) == 0:
            raise RuntimeError("DataAssembler: x_lst or y_lst is empty")
        if len(tmp := set(self.x_lst).difference(array_dict.keys())) > 0:
            raise RuntimeError(f"DataAssembler: 缺少数据{', '.join(tmp)}")
        if len(tmp := set(self.y_lst).difference(array_dict.keys())) > 0:
            raise RuntimeError(f"DataAssembler: 缺少数据{', '.join(tmp)}")
        if self.num_steps is None and not stack:
            raise ValueError("num_steps为None时，stack必须为True")

        if self.num_steps is None:
            num_steps = 1
        else:
            num_steps = self.num_steps

        size = array_dict[self.x_lst[0]].size
        shape0, shape1 = array_dict[self.x_lst[0]].shape
        has_trigger = "[trigger]" in array_dict.keys()
        has_split = "[split]" in array_dict.keys()
        has_valid = "[valid]" in array_dict.keys()

        # 处理trigger  (n_samples, ), np.ndarray, dtype: bool
        finites = (
            filter_nan_sample(array_dict, num_steps, is_apply) if self.mask_nan else np.full(size, True, dtype=bool)
        )  # (n_samples, ), np.ndarray
        if has_trigger:
            trigger = (
                _to_array(array_dict.pop("[trigger]") if release_memory else array_dict["[trigger]"])
                .flatten()
                .astype(bool)
            )
        else:
            trigger = np.full(size, True, dtype=bool)
        mask: np.ndarray = trigger & finites

        # 处理split  (n_samples, ), np.ndarray, dtype: bool
        if has_split:
            split = (
                _to_array(array_dict.pop("[split]") if release_memory else array_dict["[split]"]).flatten().astype(bool)
            )
        else:
            split = np.full(size, True, dtype=bool)

        if has_valid:
            valid_mask = _to_array(array_dict.pop("[valid]") if release_memory else array_dict["[valid]"])

        if stack:
            # 处理x  (n_samples, n_steps, n_features), np.ndarray
            x = np.empty((size, num_steps, len(self.x_lst)), dtype=np.float64)
            for i, key in enumerate(self.x_lst):
                t_arr = _to_array(array_dict.pop(key) if release_memory else array_dict[key]).astype(np.float64)
                # t_arr: np.ndarray, (n_dts, n_symbols) --> (n_steps, n_dts, n_symbols)
                if has_valid:
                    t_arr = stack_float_by_col(t_arr, num_steps, valid_mask)
                else:
                    t_arr = stack_float(t_arr, num_steps)
                # t_arr: np.ndarray, (n_steps, n_dts, n_symbols) --> (n_samples, n_steps)
                t_arr = t_arr.reshape((t_arr.shape[0], -1)).T
                x[:, :, i] = t_arr

            # 处理y  (n_samples, n_steps, n_features), np.ndarray
            y = np.empty((size, num_steps, len(self.y_lst)), dtype=np.float64)
            for i, key in enumerate(self.y_lst):
                t_arr = _to_array(array_dict.pop(key) if release_memory else array_dict[key]).astype(np.float64)
                # t_arr: np.ndarray, (n_dts, n_symbols) --> (n_steps, n_dts, n_symbols)
                if has_valid:
                    t_arr = stack_float_by_col(t_arr, num_steps, valid_mask)
                else:
                    t_arr = stack_float(t_arr, num_steps)
                # t_arr: np.ndarray, (n_steps, n_dts, n_symbols) --> (n_samples, n_steps)
                t_arr = t_arr.reshape((t_arr.shape[0], -1)).T
                y[:, :, i] = t_arr

            index = None

            if self.num_steps is None:
                x = x.squeeze(1)
                y = y.squeeze(1)

        else:
            x = np.empty((size, len(self.x_lst)), dtype=np.float64)
            for i, key in enumerate(self.x_lst):
                t_arr = _to_array(array_dict.pop(key) if release_memory else array_dict[key])
                # t_arr: np.ndarray, (n_dts, n_symbols) --> (n_samples,)
                x[:, i] = t_arr.flat

            y = np.empty((size, len(self.y_lst)), dtype=np.float64)
            for i, key in enumerate(self.y_lst):
                t_arr = _to_array(array_dict.pop(key) if release_memory else array_dict[key])
                # t_arr: np.ndarray, (n_dts, n_symbols) --> (n_samples,)
                y[:, i] = t_arr.flat

            # t_arr: np.ndarray, (n_dts, n_symbols)
            t_arr = np.arange(size, dtype=np.float64).reshape((shape0, shape1))
            # t_arr: np.ndarray, (n_dts, n_symbols) --> (n_steps, n_dts, n_symbols)
            if has_valid:
                t_arr = stack_float_by_col(t_arr, num_steps, valid_mask)
            else:
                t_arr = stack_float(t_arr, num_steps)
            # t_arr: np.ndarray, (n_steps, n_dts, n_symbols) --> (n_samples, n_steps)
            t_arr = t_arr.reshape((t_arr.shape[0], -1)).T
            index = t_arr

        custom_mask_lst = sorted([i for i in array_dict.keys() if "[mask]" in i], key=custom_sort_key("[mask]"))
        custom_mask_lst = [
            _to_array(array_dict.pop(i) if release_memory else array_dict[i]).flatten().astype(bool)
            for i in custom_mask_lst
        ]
        if len(custom_mask_lst) > 3:
            raise ValueError("至多只能有3个mask，分别用于train, valid, test")

        return x, y, split, mask, index, custom_mask_lst

    def fit_transform(
        self,
        array_dict: Union[MutableMapping[str, np.ndarray], MutableMapping[str, pd.DataFrame]],
        stack: bool,
        is_apply: bool,
        release_memory: bool,
    ) -> Tuple[
        npt.NDArray[np.float64],
        npt.NDArray[np.float64],
        npt.NDArray[np.bool_],
        npt.NDArray[np.bool_],
        Optional[npt.NDArray[np.float64]],
        Sequence[npt.NDArray[np.bool_]],
    ]:
        self.fit(array_dict)
        return self.transform(array_dict, stack, is_apply, release_memory)

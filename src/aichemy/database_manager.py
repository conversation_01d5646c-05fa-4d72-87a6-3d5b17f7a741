import pathlib
from collections import defaultdict
from typing import Any, Dict, List

import numpy as np
import pandas as pd


class DatabaseManager(defaultdict):
    """用于管理本地HDF数据库

    Examples:
        >>> db = DatabaseManager()
        >>> db.bind_db("./factor.h5")
        >>> db.info
        ['factor_1', 'factor_2', 'factor_3']
        >>> db["factor_1"]
        Empty DataFrame
        Columns: []
        Index: []
        >>> db.update("factor_1", pd.DataFrame([[1, 2, 3], [4, 5, 6]]))

    """

    def __init__(self, re_calc_length: int = 0, save_length: int = 0, memory: bool = False):
        """用于管理本地数据库

        Args:
            re_calc_length (int, optional): _description_. Defaults to 0.
            save_length (int, optional): 落地时保存的数据长度. Defaults to 0.
            memory (bool, optional): 是否保存在内存中，否 - 每次都从磁盘加载. Defaults to False.
        """
        self.re_calc_length = re_calc_length
        self.save_length = save_length
        self.memory = memory

        self.localize = False
        self.path: pathlib.Path
        self.index: Dict[str, pd.Index] = {}
        self.min_index: Dict[str, int] = {}

    def bind_db(self, path: str):
        """绑定本地HDF数据库

        Args:
            path (str): 本地路径

        Examples:
            >>> db.bind_db("./factor.h5")
        """
        self.localize = True
        self.path = pathlib.Path(path)
        if not self.path.exists():
            with pd.HDFStore(self.path, "a") as store:
                pass

    def contain(self, key: str, index: Any) -> bool:
        """判断表中是否包含index

        Args:
            key (str): 表名
            index (Any): 索引

        Returns:
            bool: 表中是否包含index
        """
        return key in self.index.keys() and (index in self.index[key] or index < self.min_index[key])

    @property
    def info(self) -> List[str]:
        """获取本地数据库的所有表名

        Returns:
            List[str]: 表名列表
        """
        if self.localize:
            with pd.HDFStore(self.path, "r") as store:
                return [i[1:] for i in store.keys()]
        else:
            return list(self.keys())

    def update(self, key: str, data: pd.DataFrame):
        """更新数据

        Args:
            key (str): 表名
            data (pd.DataFrame): 更新的数据
        """
        # 先把cache concat，再把data里和cache的数据重复的数据去掉
        if not key in self.info:
            data_: pd.DataFrame = data
        else:
            data_: pd.DataFrame = self[key]
            data_ = data_.reindex(
                index=data_.index.union(data.index), columns=data_.columns.union(data.columns), fill_value=np.nan
            )
            data_.loc[data.index, data.columns] = data
        data_.sort_index(inplace=True)

        # index的排序和筛选
        if self.memory:
            if self.save_length != 0:
                self[key] = data_.iloc[-self.save_length :]
            else:
                self[key] = data_

        # 落地
        if self.localize:
            with pd.HDFStore(self.path, "a") as store:
                store[f"/{key}"] = data_

        # 每天要重算近self.re_calc_length天的数据, 如果为0则不重算
        if self.re_calc_length == 0:
            self.index[key] = data_.index.sort_values()
        else:
            self.index[key] = data_.index.sort_values()[: -self.re_calc_length]
        self.min_index[key] = self.index[key][0] if not self.index[key].empty else 0

    def __missing__(self, key: str) -> pd.DataFrame:
        """如果key不存在，则从磁盘加载"""
        if self.localize:
            with pd.HDFStore(self.path, "r") as store:
                store_key = f"/{key}"
                if store_key in store.keys():
                    data = store[store_key]
                    if self.memory:
                        self[key] = data
                    if self.re_calc_length == 0:
                        self.index[key] = data.index.sort_values()
                    else:
                        self.index[key] = data.index.sort_values()[: -self.re_calc_length]
                    self.min_index[key] = self.index[key][0] if not self.index[key].empty else 0
                    return data
                else:
                    super().__missing__(key)
        else:
            super().__missing__(key)

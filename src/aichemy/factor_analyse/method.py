import pathlib
from functools import partial, wraps
from typing import Any, Dict, List, Literal, Optional, Union

import matplotlib.gridspec as gridspec
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from scipy.stats import ttest_1samp

from ..utils import from_nstimestamp
from .utils import calc_return, cross_section_linear_regression, dropna

try:
    from IPython.display import HTML, display
except ImportError:
    pass


def align_df(fnc):
    @wraps(fnc)
    def wrapper(factor_df, close_df, *args, **kwds):
        start = max(factor_df.index.min(), close_df.index.min())
        end = min(factor_df.index.max(), close_df.index.max())
        f_idx: pd.Index = factor_df.index[(factor_df.index >= start) & (factor_df.index <= end)]
        c_idx: pd.Index = close_df.index[(close_df.index >= start) & (close_df.index <= end)]
        index: pd.Index = f_idx.union(c_idx).unique().sort_values(ascending=True)
        columns = factor_df.columns.intersection(close_df.columns)
        return fnc(
            factor_df.reindex(index=index, columns=columns),
            close_df.reindex(index=index, columns=columns),
            *args,
            **kwds,
        )

    return wrapper


def _Exc(x, w):
    return np.sum(w * x)


def _Cov(x, y, w):
    return np.sum(w * (x - _Exc(x, w)) * (y - _Exc(y, w)))


def _Var(x, w):
    return np.sum(w * x**2) - np.sum(w * x) ** 2


def _Pearson(x, y, w):
    return _Cov(x, y, w) / np.sqrt(_Var(x, w) * _Var(y, w))


def _weight(m, n=10):
    """返回的结果是权重向量，从小到大"""
    if not 0 < m <= 1:
        raise ValueError("m应该在0到1之间")
    w = np.array([m**i for i in range(n)])[::-1]
    return 1 / np.sum(w) * w


@dropna
def calc_rank_ic(factor_series: Union[pd.Series, np.ndarray], return_series: Union[pd.Series, np.ndarray]):
    if len(factor_series) > 1 and len(return_series) > 1:
        factor_series_ = factor_series if isinstance(factor_series, pd.Series) else pd.Series(factor_series)
        return_series_ = return_series if isinstance(return_series, pd.Series) else pd.Series(return_series)
        return np.corrcoef(factor_series_.rank(), return_series_.rank())[0, 1]
    return np.nan


@dropna
def calc_ic(factor_series: Union[pd.Series, np.ndarray], return_series: Union[pd.Series, np.ndarray]):
    if len(factor_series) > 1 and len(return_series) > 1:
        return np.corrcoef(factor_series, return_series)[0, 1]
    return np.nan


@dropna
def calc_weighted_ic(
    factor_series: Union[pd.Series, np.ndarray],
    return_series: Union[pd.Series, np.ndarray],
    direction: Literal["L", "S"] = "L",
    weight_decay=1.0,
    num_groups=10,
) -> float:
    if len(factor_series) > 1 and len(return_series) > 1:
        ic = calc_ic(factor_series, return_series)
        weight_series = _weight(weight_decay, n=num_groups)
        pcts = np.percentile(factor_series, np.arange(num_groups + 1) / num_groups * 100)
        weight_matrix = np.full((num_groups, len(factor_series)), np.nan)
        for i, (ub, lb) in enumerate(zip(pcts[:-1], pcts[1:])):
            if i == 0:
                if ub == lb:
                    idx = np.where(factor_series == ub)
                else:
                    idx = np.where((ub <= factor_series) & (factor_series <= lb))
            else:
                if ub == lb:
                    idx = np.where(factor_series == ub)
                else:
                    idx = np.where((ub < factor_series) & (factor_series <= lb))
            if direction == "L":
                if ic > 0:
                    weight_matrix[i, idx[0]] = weight_series[i] / len(idx[0])
                else:
                    weight_matrix[i, idx[0]] = weight_series[num_groups - 1 - i] / len(idx[0])
            else:
                if ic > 0:
                    weight_matrix[i, idx[0]] = weight_series[num_groups - 1 - i] / len(idx[0])
                else:
                    weight_matrix[i, idx[0]] = weight_series[i] / len(idx[0])
        weight = np.nanmean(weight_matrix, axis=0)
        weight = weight / np.sum(weight)
        return _Pearson(factor_series, return_series, weight)
    return np.nan


@dropna
def calc_weighted_ic1(
    factor_series: Union[pd.Series, np.ndarray],
    return_series: Union[pd.Series, np.ndarray],
    direction: Literal["L", "S"] = "L",
    weight_decay=1.0,
    num_groups=10,
) -> float:
    if len(factor_series) > 1 and len(return_series) > 1:
        weight_series = _weight(weight_decay, n=num_groups)
        pcts = np.percentile(return_series, np.arange(num_groups + 1) / num_groups * 100)
        weight_matrix = np.full((num_groups, len(return_series)), np.nan)
        for i, (ub, lb) in enumerate(zip(pcts[:-1], pcts[1:])):
            if i == 0:
                if ub == lb:
                    idx = np.where(return_series == ub)
                else:
                    idx = np.where((ub <= return_series) & (return_series <= lb))
            else:
                if ub == lb:
                    idx = np.where(return_series == ub)
                else:
                    idx = np.where((ub < return_series) & (return_series <= lb))

            if direction == "L":
                weight_matrix[i, idx[0]] = weight_series[i] / len(idx[0])
            else:
                weight_matrix[i, idx[0]] = weight_series[num_groups - 1 - i] / len(idx[0])

        weight = np.nanmean(weight_matrix, axis=0)
        weight = weight / np.sum(weight)
        return _Pearson(factor_series, return_series, weight)
    return np.nan


def stat_ic_series(ic_series):
    if len(ic_series) > 1:
        ret = {
            "ic_series": ic_series,
            "valid_count": len(ic_series),
            "ic_mean": (t1 := np.mean(ic_series)),
            "ic_std": (t2 := np.std(ic_series)),
            "ir": t1 / t2,
            "abs_ic_mean": (t3 := np.mean(np.abs(ic_series))),
            "abs_ic_std": (t4 := np.std(np.abs(ic_series))),
            "abs_ic_ir": t3 / t4,
            "positive_rate": np.sum(np.array(ic_series) > 0) / len(ic_series),
        }
    else:
        ret = {
            "ic_series": pd.Series(),
            "valid_count": 0,
            "ic_mean": np.nan,
            "ic_std": np.nan,
            "ir": np.nan,
            "abs_ic_mean": np.nan,
            "abs_ic_std": np.nan,
            "abs_ic_ir": np.nan,
            "positive_rate": np.nan,
        }
    return ret


def plot_ic(ic_stat, interval_length, savefig):
    display(HTML("""<span style="white-space: pre; font-size: 20px">IC分析</span>"""))
    figure, ax = plt.subplots(nrows=1, ncols=2, figsize=(17.5, 2.5))
    ax[0].bar(np.arange(len(ic_stat["ic_series"])), ic_stat["ic_series"])
    x1, x2, y1, y2 = *ax[0].get_xlim(), *ax[0].get_ylim()
    ax[0].axis([x1, x2 * 1.4, y1, y2])
    ax[0].text(
        x2,
        ax[0].get_ylim()[0],
        f"""
IC-mean: {ic_stat["ic_mean"]:.4f}
IC-std: {ic_stat["ic_std"]:.4f}
IC-IR: {ic_stat["ir"]:.4f}
IC-positive rate: {ic_stat["positive_rate"]:.2%}

ABS_IC-mean: {ic_stat["abs_ic_mean"]:.4f}
ABS_IC-std: {ic_stat["abs_ic_std"]:.4f}
ABS_IC-IR: {ic_stat["abs_ic_ir"]:.4f}
        """,
    )
    ax[0].set_title(f"IC Series(Fre: {interval_length})")

    ax[1].plot(ic_stat["ic_series"].fillna(0).cumsum().reset_index(drop=True))
    ax[1].set_title(f"IC Series Cumsum(Fre: {interval_length})")
    plt.show()
    if savefig is not None:
        pathlib.Path(savefig).parent.mkdir(parents=True, exist_ok=True)
        figure.savefig(savefig)


@align_df
def ic_analyse(
    factor_df: pd.DataFrame,
    close_or_return_df: pd.DataFrame,
    interval_length: int,
    lookback_period: int = 1,
    method: str = "IC",
    plot: bool = False,
    savefig: Optional[str] = None,
    mode: str = "close",
) -> Dict:
    """有效因子识别方法之一: 单因子IC分析

    Args:
        factor_df (pd.DataFrame): 因子df
        close_or_return_df (pd.DataFrame): 价格df或收益df
        interval_length (int): 调仓周期
        is_rank (bool, optional): 是否基于排名计算IC值. Defaults to True.
        method (str): IC计算方法，支持 "IC", "RankIC", "WeightedIC:L,0.9,10"
        lookback_period (int, optional): 因子df的lookback_period. Defaults to 1.
        plot (bool, optional): 是否绘制IC曲线. Defaults to False.
        savefig (str, optional): 图片保存路径. Defaults to None.
        mode (str, optional): close_or_return_df的类型，close - close_df，return - return_df. Defaults to "close".

    Raises:
        ValueError: 因子df和价格df的shape不一致

    Returns:
        Dict: IC分析结果, 包括: ic_series, valid_count, ic_mean, ic_std, ir, abs_ic_mean, abs_ic_std, abs_ic_ir, positive_rate

    Examples:
        >>> from aichemy.factor_analyse.analysis import ic_analyse
        >>> ic_analyse(factor_df, close_df, interval_length=5, is_rank=True)
    """
    if factor_df.shape != close_or_return_df.shape:
        raise ValueError(f"factor_df.shape {factor_df.shape} != close_df.shape {close_or_return_df.shape}")

    if mode == "close":
        return_df = calc_return(close_or_return_df, interval_length)
    else:
        return_df = close_or_return_df
    return_df = return_df.sub(return_df[~factor_df.isna()].mean(axis=1), axis=0)

    if method == "IC":
        call_method = calc_ic
    elif method == "RankIC":
        call_method = calc_rank_ic
    elif method.startswith("WeightedIC"):
        direction, weight_decay, num_groups = method.split(":")[-1].split(",")
        weight_decay = float(weight_decay)
        num_groups = int(num_groups)
        call_method = partial(calc_weighted_ic, direction=direction, weight_decay=weight_decay, num_groups=num_groups)
    ic_lst = []
    ic_index = []
    for i in range(lookback_period - 1, len(factor_df), 1):
        if not np.isnan(
            res := call_method(
                factor_df.iloc[i - lookback_period + 1 : i + 1].values.flatten(),
                return_df.iloc[i - lookback_period + 1 : i + 1].values.flatten(),
            )
        ):
            ic_lst.append(res)
            ic_index.append(factor_df.index[i])
    ret = stat_ic_series(pd.Series(ic_lst, index=ic_index))

    if plot:
        if savefig is None:
            plot_ic(ret, interval_length, None)
        else:
            # 分割文件路径和文件名
            name, extension = savefig.rsplit(".", 1)
            # 构建新的文件名
            new_filename = f"{name}_{method}.{extension}"
            plot_ic(ret, interval_length, new_filename)

    return ret


@dropna
def _calc_group_return(
    factor_series: pd.Series, return_series: pd.Series, num_groups: int = 3, is_rank: bool = False
) -> Dict[str, Any]:
    """计算分组收益

    Args:
        factor_series (pd.Series): 因子序列，index为symbol，value为因子值
        return_series (pd.Series): 收益序列，index为symbol，value为收益值
        num_groups (int, optional): 分组数量. Defaults to 3.
        is_rank (bool, optional): 收益是否基于排名. Defaults to False.

    Returns:
        dict: {"return": {1: 0.1, 2: 0.2}, "symbols": {1: ["a", "b"], 2: ["c", "d"]}}
    """
    ret = {"return": {i: 0 for i in range(num_groups)}, "symbols": {i: [] for i in range(num_groups)}}
    if not (len(factor_series) >= num_groups and len(return_series) >= num_groups):
        return ret

    # 收益调整，计算超额或计算排名等
    if is_rank:
        return_series = return_series.rank()
        return_series = (return_series - np.nanmean(return_series)) / np.nanstd(return_series)

    # 分组统计
    pcts = np.percentile(factor_series, np.arange(num_groups + 1) / num_groups * 100)
    for i, (ub, lb) in enumerate(zip(pcts[:-1], pcts[1:])):
        if i == 0:
            if ub == lb:
                idx = np.where(factor_series == ub)
            else:
                idx = np.where((ub <= factor_series) & (factor_series <= lb))
        else:
            if ub == lb:
                idx = np.where(factor_series == ub)
            else:
                idx = np.where((ub < factor_series) & (factor_series <= lb))
        ret["return"][i] = return_series.iloc[idx[0]].mean()
        ret["symbols"][i] = [i for i in factor_series.index[idx[0]]]
    return ret


def stat_gr_df(gr_df, cost, interval_length):
    def calc(ret_series, cost, interval_length, direction: Optional[Literal["L", "S"]] = None):
        if direction is None:
            direction = "L" if ret_series.sum() > 0 else "S"
        ret = ret_series.sum() / interval_length
        if direction == "L":
            ret_series = (ret_series - cost) / interval_length
        else:
            ret_series = (-ret_series - cost) / interval_length

        # 年化收益
        arr_simple_interest = np.mean(ret_series) * 250
        # 最终收益
        cum_ret_series = ret_series.fillna(0).cumsum()
        final = cum_ret_series.iloc[-1]
        # 最大回撤
        max_withdraw = (cum_ret_series.cummax() - cum_ret_series).max()
        return cum_ret_series, {
            "RET": ret,
            "FR": final,
            "ARR": arr_simple_interest,
            "MDD": max_withdraw,
            "D": direction,
        }

    def fnc(k):
        ret = calc(gr_df.iloc[:, k], 0.0, interval_length)[1]
        tmp = calc(gr_df.iloc[:, k], cost, interval_length)[1]
        for k, v in tmp.items():
            if k != "D" and k != "RET":
                ret[f"{k}(C: {cost})"] = v
        return ret

    num_groups = gr_df.shape[1]
    r_top_bottom = calc(gr_df.iloc[:, 0] - gr_df.iloc[:, -1], cost, interval_length)

    if r_top_bottom[1]["D"] == "L":
        r_long = calc(gr_df.iloc[:, 0], cost, interval_length, "L")
        r_short = calc(gr_df.iloc[:, -1], cost, interval_length, "S")
    else:
        r_long = calc(gr_df.iloc[:, -1], cost, interval_length, "L")
        r_short = calc(gr_df.iloc[:, 0], cost, interval_length, "S")

    ret = {
        "num_groups": num_groups,
        "group_return_df": gr_df,
        "group_return": {str(k): fnc(k) for k in range(num_groups)},
        "ls_portfolio": {"LS": r_top_bottom[1], "L": r_long[1], "S": r_short[1]},
    }
    return ret, r_top_bottom, r_long, r_short


@align_df
def group_return_analyse(
    factor_df: pd.DataFrame,
    close_or_return_df: pd.DataFrame,
    interval_length: int,
    lookback_period: int = 1,
    num_groups: int = 3,
    is_alpha: bool = True,
    is_rank: bool = False,
    cost: float = 0.0,
    plot: bool = False,
    savefig: Optional[str] = None,
    mode: str = "close",
) -> Dict:
    """有效因子识别方法之二: 分组收益分析

    Args:
        factor_df (pd.DataFrame): 因子df
        close_or_return_df (pd.DataFrame): 价格df或收益df
        interval_length (int): 调仓周期
        lookback_period (int, optional): 观察期. Defaults to 1.
        num_groups (int, optional): 分组数. Defaults to 4.
        is_alpha (bool, optional): 是否计算超额收益. Defaults to True.
        is_rank (bool, optional): 是否基于排名计算收益. Defaults to False.
        cost (float, optional): 交易成本. Defaults to 0.
        plot (bool, optional): 是否绘制分组收益曲线. Defaults to False.
        savefig (Optional[str], optional): 图片保存路径. Defaults to None.
        mode (str, optional): close_or_return_df的类型，close - close_df，return - return_df. Defaults to "close".

    Raises:
        ValueError: 因子df和价格df的shape不一致

    Returns:
        Dict: ...

    Examples:
        >>> from aichemy.factor_analyse.analysis import group_return_analyse
        >>> group_return_analyse(factor_df, close_or_return_df, interval_length=5, num_groups=4, plot=True, is_rank=True)
    """
    if factor_df.shape != close_or_return_df.shape:
        raise ValueError(f"factor_df.shape {factor_df.shape} != close_df.shape {close_or_return_df.shape}")

    # 计算分组收益序列
    if mode == "close":
        return_df = calc_return(close_or_return_df, interval_length)
    else:
        return_df = close_or_return_df
    # 如果需要计算超额收益，先挑选出因子值非空的，减去平均收益
    if is_alpha:
        return_df = return_df.sub(return_df[np.isfinite(factor_df)].mean(axis=1), axis=0)

    ret_lst = []
    if lookback_period > 1:
        turnover_rate = {
            i: pd.DataFrame(
                0.0,
                index=factor_df.index,
                columns=[f"{i1}_{i2}" for i1 in range(num_groups) for i2 in factor_df.columns],
            )
            for i in range(num_groups)
        }
    else:
        turnover_rate = {
            i: pd.DataFrame(0.0, index=factor_df.index, columns=factor_df.columns) for i in range(num_groups)
        }
    for i in range(lookback_period - 1, len(factor_df), 1):
        if lookback_period > 1:
            factor_sr = factor_df.iloc[i - lookback_period + 1 : i + 1].reset_index(drop=True).stack()
            factor_sr.index = factor_sr.index.map(lambda x: f"{x[0]}_{x[1]}")
            return_sr = return_df.iloc[i - lookback_period + 1 : i + 1].reset_index(drop=True).stack()
            return_sr.index = return_sr.index.map(lambda x: f"{x[0]}_{x[1]}")
            ret = _calc_group_return(factor_sr, return_sr, num_groups, is_rank=is_rank)
        else:
            ret = _calc_group_return(factor_df.iloc[i], return_df.iloc[i], num_groups, is_rank=is_rank)

        tmp: Dict[str, Any] = {"dt": from_nstimestamp(factor_df.index[i])}
        tmp.update(ret)
        ret_lst.append(tmp)
        for j in range(num_groups):
            if len(tmp["symbols"][j]) > 0:
                turnover_rate[j].loc[factor_df.index[i], tmp["symbols"][j]] = 1 / len(tmp["symbols"][j])

    for k in turnover_rate.keys():
        tmp = turnover_rate[k] - turnover_rate[k].shift(interval_length)
        turnover_rate[k] = tmp[tmp > 0].sum(axis=1).mean()

    group_return_df = pd.DataFrame({i["dt"]: i["return"] for i in ret_lst}).T

    ret, r_top_bottom, r_long, r_short = stat_gr_df(group_return_df, cost, interval_length)
    ret.update({"symbols": {i["dt"].strftime("%Y-%m-%d %H:%M"): i["symbols"] for i in ret_lst}})

    for i in range(num_groups):
        ret["group_return"][str(i)].update({"TR": turnover_rate[i]})

    if plot:
        display(HTML("""<span style="font-size: 20px">分组收益</span>"""))

        # 创建一个图形
        fig = plt.figure(figsize=(17, 15), constrained_layout=True)
        # 使用 gridspec 创建子图布局
        gs = gridspec.GridSpec(3, 1)  # 2 行 2 列

        # 绘制分组的累计收益曲线
        ax1 = fig.add_subplot(gs[0, 0])
        (group_return_df / interval_length).fillna(0).cumsum().plot(ax=ax1)
        ax1.set_title(f"Group {'Alpha ' if is_alpha else ''}Return(No Cost)")

        # 绘制多空组合的累计收益曲线
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.plot(r_top_bottom[0].index, r_top_bottom[0])
        ax2.plot(r_top_bottom[0].index, r_long[0], color="red", linestyle="--")
        ax2.plot(r_top_bottom[0].index, r_short[0], color="green", linestyle="--")

        # 绘制相关指标
        ls_ret = ret["ls_portfolio"]
        x1, x2, y1, y2 = *ax2.get_xlim(), *ax2.get_ylim()
        ax2.axis([x1, x2 + (x2 - x1) * 0.1, y1, y2])
        ax2.text(
            x2,
            ax2.get_ylim()[0],
            f"""L-ARR_0: {ls_ret["L"]["ARR"]:.2%}
L-MDD_0: {ls_ret["L"]["MDD"]:.2%}
----
S-ARR_1: {ls_ret["S"]["ARR"]:.2%}
S-MDD_1: {ls_ret["S"]["MDD"]:.2%}
----
ARR: {ls_ret["LS"]["ARR"]:.2%}
MDD: {ls_ret["LS"]["MDD"]:.2%}
            """,
        )
        ax2.set_title(f"Long-Short {'Alpha ' if is_alpha else ''}Return(Cost: {cost:.2%})")

        ax3 = fig.add_subplot(gs[2, 0])
        ax3.axis("off")
        df = pd.DataFrame(ret["group_return"]).T
        # 找到 C 列的最大值的索引
        max_index = df["RET"].astype("float").idxmax()
        min_index = df["RET"].astype("float").idxmin()
        for i in ["RET", "FR", "ARR", "MDD", "TR", f"FR(C: {cost})", f"ARR(C: {cost})", f"MDD(C: {cost})"]:
            df[i] = df[i].apply(lambda x: "{:.2%}".format(x))

        # 创建表格
        table = ax3.table(cellText=df.values, rowLabels=df.index, colLabels=df.columns, cellLoc="center", loc="center")
        # 遍历每一行，设置背景色
        for i in range(len(df)):
            if df.iloc[i]["D"] == "L":
                # 设置为浅绿色背景
                for j in range(len(df.columns)):
                    table[(i + 1, j)].set_facecolor("lightcoral")
            elif df.iloc[i]["D"] == "S":
                # 设置为浅红色背景
                for j in range(len(df.columns)):
                    table[(i + 1, j)].set_facecolor("lightgreen")

            # 设置 C 列最大值那一行的背景色为深红色
            if str(i) == max_index and df.iloc[i]["D"] == "L":
                for j in range(len(df.columns)):
                    table[(i + 1, j)].set_facecolor("darkred")
                    table[(i + 1, j)].set_text_props(color="white")  # 设置文本颜色为白色
            if str(i) == min_index and df.iloc[i]["D"] == "S":
                for j in range(len(df.columns)):
                    table[(i + 1, j)].set_facecolor("darkgreen")
                    table[(i + 1, j)].set_text_props(color="white")
        table.auto_set_font_size(False)
        table.set_fontsize(14)
        table.scale(1, 1.5)  # 调整表格大小
        plt.tight_layout()
        plt.show()

        if savefig is not None:
            pathlib.Path(savefig).parent.mkdir(parents=True, exist_ok=True)
            fig.savefig(savefig if not is_alpha else savefig.replace(".png", "_alpha.png"), bbox_inches="tight")

    return ret


def ttest(lst: Union[List, np.ndarray, pd.Series]) -> dict:
    """对回归系数t检验

    Args:
        lst (Union[List, np.ndarray, pd.Series]): 每一期单因子线性回归的系数

    Returns:
        dict: t检验结果, 包括abs_t_mean, abs_t>2, ttest_t, ttest_p
    """

    def convert_to_tvalue(lst):
        return lst / (np.std(lst) / np.sqrt(len(lst) - 1))

    lst = np.array([i for i in lst if not pd.isna(i)])
    if len(lst) <= 1:
        return {
            "t_mean": np.nan,
            "|t|>2 rate": np.nan,
            "ttest_t": np.nan,
            "ttest_p": np.nan,
            "abs_t_mean": np.nan,
            "abs_t>2": np.nan,
            "abs_ttest_t": np.nan,
            "abs_ttest_p": np.nan,
        }

    t = convert_to_tvalue(lst)
    abs_t = convert_to_tvalue(np.abs(lst))

    return dict(
        [
            ("t_mean", np.mean(t)),
            ("|t|>2 rate", (np.abs(t) > 2).mean() if len(t) > 0 else 0),
            *zip(["ttest_t", "ttest_p"], ttest_1samp(lst, 0)),
            ("abs_t_mean", np.mean(abs_t)),
            ("abs_t>2", (abs_t > 2).mean() if len(abs_t) > 0 else 0),
            *zip(["abs_ttest_t", "abs_ttest_p"], ttest_1samp(abs_t, 0)),
        ]
    )


# TODO:回归系数t检验支持lookback_period
@align_df
def ttest_analyse(
    factor_df: pd.DataFrame,
    close_df: pd.DataFrame,
    public_df_lst: List[pd.DataFrame],
    interval_length: int,
    fit_intercept: bool = True,
) -> dict:
    """有效因子识别方法之三: 单因子线性回归和t检验

    Args:
        factor_df (pd.DataFrame): 因子df
        close_df (pd.DataFrame): 价格df
        public_df_lst (List[pd.DataFrame]): 公共因子df, 主要是行业或市场的指数
        interval_length (int): 调仓周期
        fit_intercept (bool, optional): 是否拟合截距项, 如果public_df_lst中添加了市场因子，则可以不需要加截距项. Defaults to True.

    Raises:
        ValueError: 因子df和价格df的shape不一致

    Returns:
        dict: t检验结果, 包括abs_t_mean, abs_t>2, ttest_t, ttest_p
    """
    if factor_df.shape != close_df.shape:
        raise ValueError(f"factor_df.shape {factor_df.shape} != close_df.shape {close_df.shape}")

    return_df = (close_df.shift(-interval_length) - close_df) / close_df
    if pd.isna(return_df).all().all():
        return ttest([])

    # TODO 这里可能不太对，不一定都要算收益率
    public_df_ = []
    for i in public_df_lst:
        tmp = (i.shift(-interval_length) - i) / i
        if pd.isna(tmp).all().all():
            return ttest([])
        public_df_.append(tmp)

    if not pd.isna(factor_df).all().all():
        std = float(np.nanstd(factor_df))
        if std == 0 or pd.isna(std):
            return ttest([])
        else:
            factor_df_ = (factor_df - np.nanmean(factor_df)) / std
        if not pd.isna(factor_df_).all().all():
            # TODO 是否使用截距项
            ret = cross_section_linear_regression([factor_df_, *[i for i in public_df_]], return_df, fit_intercept)
            return ttest(ret.iloc[:, 0])
        else:
            return ttest([])
    else:
        return ttest([])

import functools
from typing import List, Sequence, Union

from typing import Literal
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression


def dropna(fnc):
    """dropna函数是一个装饰器，它包装另一个函数，并在调用包装函数之前从输入序列中删除任何具有缺失值的行。"""

    @functools.wraps(fnc)
    def wrap_fnc(factor, return_, *args, **kwargs):
        if isinstance(factor, (pd.DataFrame, pd.Series)) and isinstance(return_, pd.Series):
            index = factor.index.intersection(return_.index)
            factor1 = factor.loc[index]
            return1 = return_.loc[index]

            if len(factor1.shape) == 1:
                tmp = (pd.isna(factor1) == False) & (pd.isna(return1) == False)
            elif len(factor1.shape) == 2:
                tmp = (pd.isna(factor1).any(axis=1) == False) & (pd.isna(return1) == False)

            return fnc(factor1.loc[tmp], return1.loc[tmp], *args, **kwargs)

        elif isinstance(factor, np.ndarray) and isinstance(return_, np.ndarray):
            if len(factor) != len(return_):
                raise ValueError("输入数据长度不匹配")

            if len(factor.shape) == 1:
                tmp = (pd.isna(factor) == False) & (pd.isna(return_) == False)
            elif len(factor.shape) == 2:
                tmp = (pd.isna(factor).any(axis=1) == False) & (pd.isna(return_) == False)

            return fnc(factor[tmp], return_[tmp], *args, **kwargs)

        else:
            raise ValueError("输入数据类型不匹配")

    return wrap_fnc


def preprocessing(factor_df: pd.DataFrame):
    """因子按行预处理，先取排名，再标准化"""
    ret = factor_df.copy()
    for i, j in ret.iterrows():
        if np.isnan(j).all():
            ret.loc[i] = np.nan
        else:
            tmp = j.rank()
            tmp = (tmp - np.nanmean(tmp)) / np.nanstd(tmp)
            ret.loc[i] = tmp
    return ret


def calc_return(
    close_df: pd.DataFrame, n: int, direct: Literal["forward", "backward"] = "forward", keep_nan: bool = True
) -> pd.DataFrame:
    """计算收益

    Args:
        close_df (pd.DataFrame): 收盘价dataframe, index - time, columns - symbol
        n (int): 周期
        direct (Literal["forward", "backward"], optional): forward - 未来收益， backward - 历史收益. Defaults to "forward".
        keep_nan (bool, optional): 是否保留NaN，不保留则会把头部或尾部为NaN的行删除. Defaults to True.

    Returns:
        pd.DataFrame: 收益, index - time, columns - symbol
    """
    if direct == "forward":
        ret = (close_df.shift(-n) - close_df) / close_df
        if not keep_nan:
            ret = ret.iloc[:-n]

    elif direct == "backward":
        ret = (close_df - close_df.shift(n)) / close_df.shift(n)
        if not keep_nan:
            ret = ret.iloc[n:]

    else:
        raise ValueError("direct must be 'forward' or 'backward'")

    return ret


@dropna
def linear_regression(x, y, fit_intercept=True) -> Union[None, LinearRegression]:
    if len(x) <= 1 or len(y) <= 1:
        return None
    # linear_regression = Lasso(alpha=0.002, fit_intercept=fit_intercept)
    linear_regression = LinearRegression(fit_intercept=fit_intercept)
    linear_regression.fit(x, y)
    return linear_regression


def cross_section_linear_regression(x: List[pd.DataFrame], y: pd.DataFrame, fit_intercept: bool = True) -> pd.DataFrame:
    """横截面回归分析

    Args:
        x (List[pd.DataFrame]): X.
        y (pd.DataFrame): Y.
        fit_intercept (bool, optional): 是否拟合截距. Defaults to True

    Returns:
        pd.DataFrame: 前n-1个是因子回归系数，最后一个是截距
    """
    tmp = y.index
    for i in x:
        tmp = tmp.intersection(i.index)
    ret = np.full((len(tmp), len(x) + (1 if fit_intercept else 0)), np.nan)
    for i, j in enumerate(tmp):
        tmp1 = linear_regression(pd.concat([xx.loc[j] for xx in x], axis=1), y.loc[j], fit_intercept)
        if tmp1 is None:
            ret[i, :] = np.nan
        else:
            ret[i, :-1] = tmp1.coef_
            ret[i, -1] = tmp1.intercept_
    return pd.DataFrame(
        ret, index=tmp, columns=[f"factor{i}" for i in range(len(x))] + (["intercept"] if fit_intercept else [])
    )


def _orthogonalize_factor(y: pd.Series, x_list: List[pd.Series]) -> pd.Series:
    """正交化因子y，去除x_list中各因子的影响，同时处理缺失值。

    Args:
        y (pd.Series): 要正交化的因子。
        x_list (List[pd.Series]): 影响因子列表，每个元素为一个Series，包含与y相同索引的因子值。

    Returns:
        pd.Series: 正交化后的因子y。
    """
    if pd.isna(y).all():
        return pd.Series(np.nan, index=y.index)

    if len(x_list) == 0:
        return pd.Series(np.nan, index=y.index)

    # 合并所有Series为一个DataFrame
    data = pd.concat([y] + x_list, axis=1)

    # 处理缺失值：这里选择删除任何包含缺失值的行
    data.replace([np.inf, -np.inf], np.nan, inplace=True)
    data = data.dropna(axis=1, how="all").dropna(axis=0, how="any")

    if data.empty:
        return pd.Series(np.nan, index=y.index)

    # 重新获取处理缺失值后的y和x_list
    y_cleaned = data.iloc[:, 0].to_numpy()
    x_cleaned = data.iloc[:, 1:].to_numpy()

    # 添加常数项
    x_cleaned = np.hstack((np.ones((len(x_cleaned), 1)), x_cleaned))

    # 执行线性回归
    model = LinearRegression(fit_intercept=False)
    model.fit(x_cleaned, y_cleaned)

    # 计算残差，即正交化后的因子
    y_pred = model.predict(x_cleaned)
    residual = y_cleaned - y_pred

    # 将残差转换为Series，并重新索引到原始y的索引，缺失值位置为NaN
    residual_series = pd.Series(residual, index=data.iloc[:, 0].index)
    residual = residual_series.reindex(y.index, fill_value=np.nan)

    return residual


def orthogonalize_factor(y: pd.DataFrame, x_list: Sequence[pd.DataFrame]) -> pd.DataFrame:
    """正交化因子y，去除x_list中各因子的影响，同时处理缺失值。

    Args:
        y (pd.DataFrame): 要正交化的因子。
        x_list (Sequence[pd.DataFrame]): 影响因子列表，每个元素为一个DataFrame，包含与y相同索引的因子值。

    Returns:
        pd.DataFrame: 正交化后的因子y。
    """
    ret = []
    for i, j in y.iterrows():
        ret.append(_orthogonalize_factor(j, [k.loc[i] for k in x_list if i in k.index]).rename(i))
    return pd.concat(ret, axis=1).T

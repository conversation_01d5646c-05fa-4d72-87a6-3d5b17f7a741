import multiprocessing as mp
from typing import List

import numpy as np
import pandas as pd

from .analysis import ttest_analyse
from .utils import calc_return, cross_section_linear_regression


class MultiFactorModel:
    def __init__(self, factor_df_dict: dict[str, pd.DataFrame], close_df: pd.DataFrame, window: int, frequency: int) -> None:
        """初始化多因子模型

        Args:
            factor_df_dict (dict[str, pd.DataFrame]): 因子dict, key为因子名称, value为因子df
            close_df (pd.DataFrame): 收盘价df
            window (int): 每个截面上可以使用的历史数据长度，包括当前，用于筛选因子、预测未来收益等
            frequency (int): 调仓周期
        """
        from qnt_utils.toolset import from_nstimestamp

        self.original_factor_df_dict = factor_df_dict
        self.original_close_df = close_df

        self.window = window
        self.frequency = frequency

        # 每个截面上可以使用的历史数据
        self.end_date: int = 0
        self.factor_list: List[str] | str = []
        self.factor_df_dict: dict[str, pd.DataFrame]
        self.close_df: pd.DataFrame

        self._slice(from_nstimestamp(close_df.index[-1]))

    def _slice(self, end_date: str, factor_names: List[str] | str = "all"):
        """对数据进行切片，end_date为切片的截止日期"""
        from qnt_utils.toolset import to_nstimestamp

        tmp = to_nstimestamp(end_date)
        if tmp != self.end_date or factor_names != self.factor_list:
            self.end_date = tmp
            self.factor_list = factor_names
            self.close_df = self.original_close_df.loc[self.original_close_df.index <= self.end_date].iloc[-self.window :]
            if factor_names:
                self.factor_df_dict = {
                    i: self.original_factor_df_dict[i]
                    .loc[self.original_factor_df_dict[i].index <= self.end_date]
                    .iloc[-self.window :]
                    for i in factor_names
                }
            elif factor_names == "all":
                self.factor_df_dict = {
                    k: v.loc[v.index <= self.end_date].iloc[-self.window :] for k, v in self.original_factor_df_dict.items()
                }
            else:
                raise ValueError("factor_names should be list or 'all'")
            return True
        return False


class MultiFactorModelScore(MultiFactorModel):
    pass


class MultiFactorModelRegression(MultiFactorModel):
    def __init__(
        self,
        factor_df_dict: dict[str, pd.DataFrame],
        close_df: pd.DataFrame,
        public_df_lst: List[pd.DataFrame],
        window: int,
        frequency: int,
        fit_intercept: bool = True,
    ) -> None:
        self.original_public_df_lst = public_df_lst
        self.fit_intercept = fit_intercept
        # 每个截面上可以使用的历史数据
        self.public_df_lst: List[pd.DataFrame]

        super().__init__(factor_df_dict, close_df, window, frequency)

    def _slice(self, end_date: str, factor_names: List[str] | str = "all"):
        if super()._slice(end_date, factor_names):
            self.public_df_lst = [i.loc[i.index <= self.end_date].iloc[-self.window :] for i in self.original_public_df_lst]
            return True
        return False

    def select(self, end_date, num, n_cores=5):
        """执行因子检测，并选出最好的因子，后续用于多因子回归获得因子收益矩阵"""
        self._slice(end_date, [])
        with mp.Pool(n_cores) as p:
            res = p.starmap(
                ttest_analyse,
                [
                    (self.factor_df_dict[i], self.close_df, self.public_df_lst, self.frequency, self.fit_intercept)
                    for i in self.factor_df_dict.keys()
                ],
            )
            res = dict(zip(self.factor_df_dict.keys(), res))
        res = pd.DataFrame(res).T.dropna().sort_values("ttest_p")
        return list(res.index)[:num]

    def get_factor_return(self, end_date: str, factor_names: List[str] | str, predict_factor_return: callable) -> np.ndarray:
        """获取end_date因子收益，先计算T-1之前的因子收益矩阵，再根据因子收益矩阵预测T期的因子收益

        Args:
            end_date (str): T期
            factor_names (List[str] | str): 因子名称
            predict_factor_return (callable): 预测因子收益的函数

        Returns:
            np.ndarray: T期的因子收益
        """
        if isinstance(factor_names, list) and len(factor_names) == 0:
            raise ValueError("factor_names should not be empty")

        self._slice(end_date, factor_names)
        # 因子收益矩阵最后一行，是end_date当期的，所以应该剔除掉
        factor_return_matrix = cross_section_linear_regression(
            [*[self.factor_df_dict[i] for i in self.factor_df_dict.keys()], *self.public_df_lst],
            calc_return(self.close_df, self.frequency),
        ).to_numpy()[:-1]
        return predict_factor_return(factor_return_matrix)

    def get_factor_loading(self, end_date: str, factor_names: List[str] | str) -> pd.DataFrame:
        """获取end_date因子暴露, 即因子值

        Args:
            end_date (str): 日期
            factor_names (List[str] | str): 因子名称

        Returns:
            pd.DataFrame: 当天因子暴露拼接的df，列名为因子名称，index为标的代码
        """
        if isinstance(factor_names, list) and len(factor_names) == 0:
            return pd.DataFrame(index=self.close_df.columns)

        self._slice(end_date, factor_names)
        ret = pd.concat(
            [
                *[self.factor_df_dict[i].iloc[-1].rename(i) for i in self.factor_df_dict.keys()],
                *[j.iloc[-1].rename(f"pub_{i}") for i, j in enumerate(self.public_df_lst)],
            ],
            axis=1,
        )
        return ret

    def predict_return(self, end_date: str, factor_names: List[str] | str, predict_factor_return: callable) -> pd.Series:
        """预测未来收益"""
        if isinstance(factor_names, list) and len(factor_names) == 0:
            return pd.Series(np.nan, index=self.close_df.columns)

        a = self.get_factor_loading(end_date, factor_names)
        b = self.get_factor_return(end_date, factor_names, predict_factor_return)
        return pd.Series(np.dot(np.hstack([a.values, np.ones((len(a), 1))]), b), index=a.index)

import pathlib
from typing import Any, Dict, List, Optional, Union

import numpy as np
import pandas as pd
from sqlalchemy import BigInteger, Column, Float, Integer, String, and_, create_engine, select
from sqlalchemy.dialects.sqlite import insert as sqlite_insert
from sqlalchemy.engine import URL
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session
from sqlalchemy.sql.expression import null

from aichemy.utils import to_nstimestamp

Alpha = declarative_base()


class AlphaIC(Alpha):
    __tablename__ = "alpha_ic"
    timestamp = Column(BigInteger, primary_key=True)
    alpha = Column(String, primary_key=True)
    interval_length = Column(String, primary_key=True)
    lookback_period = Column(Integer, primary_key=True)
    stat = Column(String, primary_key=True)
    value = Column(Float)


class SQLHandlerOfAlphaIC:
    def __init__(self, database):
        url = URL.create(drivername="sqlite", database=str(pathlib.Path(database).absolute()))
        self._engine = create_engine(url)
        Alpha.metadata.create_all(self._engine)

    def insert(self, ins_tmp: Union[List[Dict], pd.DataFrame]):
        table1 = AlphaIC
        ins_tmp_0: pd.DataFrame = pd.DataFrame(ins_tmp) if not isinstance(ins_tmp, pd.DataFrame) else ins_tmp
        if ins_tmp_0.empty:
            return
        ins_tmp_0 = ins_tmp_0.copy().replace(np.nan, null())

        ins_tmp_1: list[dict[str, Any]] = [
            {k1: null() if v1 is None else v1 for k1, v1 in v.to_dict().items()} for _, v in ins_tmp_0.iterrows()
        ]
        with Session(self._engine) as session:
            for i in range(0, len(ins_tmp_1), 10000):
                insert_stmt = sqlite_insert(table1).values(ins_tmp_1[i : min(len(ins_tmp_1), i + 10000)])
                upsert_stmt = insert_stmt.on_conflict_do_update(
                    index_elements=table1.__table__.primary_key.columns.keys(),
                    set_={i: j for i, j in insert_stmt.excluded.items()},
                )
                session.execute(upsert_stmt)
                session.commit()

    def get(
        self,
        alpha: Optional[str],
        stat: str,
        interval_length: str,
        lookback_period: int,
        start_time: str,
        end_time: str,
    ):
        st = to_nstimestamp(start_time)
        et = to_nstimestamp(end_time)

        cond = and_(
            AlphaIC.timestamp >= st,
            AlphaIC.timestamp <= et,
            AlphaIC.stat == stat,
            AlphaIC.interval_length == interval_length,
            AlphaIC.lookback_period == lookback_period,
        )
        if alpha is not None:
            cond = and_(cond, AlphaIC.alpha == alpha)
        stmt = select(AlphaIC).where(cond)
        with self._engine.begin() as conn:
            res = pd.read_sql(
                stmt,
                con=conn,
                dtype={
                    "timestamp": np.int64,
                    "alpha": str,
                    "interval_length": str,
                    "lookback_period": np.int64,
                    "stat": str,
                    "value": np.float64,
                },
            ).sort_values("timestamp")

        return res

import threading
from abc import ABC, abstractmethod
from collections import deque
from enum import Enum
from typing import Any, <PERSON>, Tuple, Union

import numpy as np

from qnt_utils.fields import Bar, TickSnapshot
from qnt_utils.label import DataLabel, parse_data_label
from qnt_utils.toolset import to_nstimestamp


class Event(Enum):
    DATA = 1
    ORDER = 2
    INSTRUCTION = 3
    TICK = 4
    END = 999


class DataObserver(ABC):
    def __init__(self) -> None:
        self.__upstream: List[DataSubject] = []

    @abstractmethod
    def on_data(self, msg: Bar | TickSnapshot, **kwargs):
        pass

    def be_attached(self, obs: "DataSubject"):
        self.__upstream.append(obs)

    def release(self):
        for i in self.__upstream:
            i.detach(self)


class OrderObserver(ABC):
    def __init__(self) -> None:
        self.__upstream: List[OrderSubject] = []

    @abstractmethod
    def on_order(self, msg, **kwargs):
        pass

    def be_attached(self, obs: "OrderSubject"):
        self.__upstream.append(obs)

    def release(self):
        for i in self.__upstream:
            i.detach(self)


class InstructionObserver(ABC):
    def __init__(self) -> None:
        self.__upstream: List[InstructionSubject] = []

    @abstractmethod
    def on_instruction(self, msg, **kwargs):
        pass

    def be_attached(self, obs: "InstructionSubject"):
        self.__upstream.append(obs)

    def release(self):
        for i in self.__upstream:
            i.detach(self)


class TickObserver(ABC):
    def __init__(self) -> None:
        self.__upstream: List[TickSubject] = []

    @abstractmethod
    def on_tick(self, msg, **kwargs):
        pass

    def be_attached(self, obs: "TickSubject"):
        self.__upstream.append(obs)

    def release(self):
        for i in self.__upstream:
            i.detach(self)


class DataSubject(ABC):
    def __init__(self):
        self.__observers: List[DataObserver] = []
        self.__priority_obs = {0: [], 1: [], -1: []}

    def attach(self, obs: DataObserver, priority: int = 1):  # 绑定
        """DataSubject连接DataObserver

        Args:
            obs (DataObserver): DataObserver
            priority (int, optional): 0 = 最高优先级, -1 = 最低优先级. Defaults to 1.
        """
        if not isinstance(obs, DataObserver):
            raise TypeError("obs must be DataObserver")
        if obs not in self.__priority_obs[priority]:
            self.__priority_obs[priority].append(obs)
        self.__modify_observers()

    def detach(self, obs: DataObserver):  # 解绑
        for i in [0, 1, -1]:
            if obs in self.__priority_obs[i]:
                self.__priority_obs[i].remove(obs)
        self.__modify_observers()

    def __modify_observers(self):
        self.__observers = []
        for i in [0, 1, -1]:
            for j in self.__priority_obs[i]:
                if j in self.__observers:
                    raise RuntimeError("duplicate observer")
                self.__observers.append(j)

    def notify(self, msg: Any, **kwargs):  # 推送
        for obs in self.__observers:
            obs.on_data(msg, **kwargs)


class OrderSubject(ABC):
    def __init__(self):
        self.__observers: List[OrderObserver] = []
        self.__priority_obs = {0: [], 1: [], -1: []}

    def attach(self, obs: OrderObserver, priority: int = 1):  # 绑定
        """OrderSubject连接OrderObserver

        Args:
            obs (OrderObserver): OrderObserver
            priority (int, optional): 0 = 最高优先级, -1 = 最低优先级. Defaults to 1.
        """
        if not isinstance(obs, OrderObserver):
            raise TypeError("obs must be OrderObserver")
        if obs not in self.__priority_obs[priority]:
            self.__priority_obs[priority].append(obs)
        self.__modify_observers()

    def detach(self, obs: OrderObserver):  # 解绑
        for i in [0, 1, -1]:
            if obs in self.__priority_obs[i]:
                self.__priority_obs[i].remove(obs)
        self.__modify_observers()

    def __modify_observers(self):
        self.__observers = []
        for i in [0, 1, -1]:
            for j in self.__priority_obs[i]:
                if j in self.__observers:
                    raise RuntimeError("duplicate observer")
                self.__observers.append(j)

    def notify(self, msg: Any, **kwargs):  # 推送
        for obs in self.__observers:
            obs.on_order(msg, **kwargs)


class InstructionSubject(ABC):
    def __init__(self):
        self.__observers: List[InstructionObserver] = []
        self.__priority_obs = {0: [], 1: [], -1: []}

    def attach(self, obs: InstructionObserver, priority: int = 1):  # 绑定
        """InstructionSubject连接InstructionObserver

        Args:
            obs (InstructionObserver): InstructionObserver
            priority (int, optional): 0 = 最高优先级, -1 = 最低优先级. Defaults to 1.
        """
        if not isinstance(obs, InstructionObserver):
            raise TypeError("obs must be InstructionObserver")
        if obs not in self.__priority_obs[priority]:
            self.__priority_obs[priority].append(obs)
        self.__modify_observers()

    def detach(self, obs: InstructionObserver):  # 解绑
        for i in [0, 1, -1]:
            if obs in self.__priority_obs[i]:
                self.__priority_obs[i].remove(obs)
        self.__modify_observers()

    def __modify_observers(self):
        self.__observers = []
        for i in [0, 1, -1]:
            for j in self.__priority_obs[i]:
                if j in self.__observers:
                    raise RuntimeError("duplicate observer")
                self.__observers.append(j)

    def notify(self, msg: Any, **kwargs):  # 推送
        for obs in self.__observers:
            obs.on_instruction(msg, **kwargs)


class TickSubject(ABC):
    def __init__(self):
        self.__observers: List[TickObserver] = []
        self.__priority_obs = {0: [], 1: [], -1: []}

    def attach(self, obs: TickObserver, priority: int = 1):  # 绑定
        """TickSubject连接TickObserver

        Args:
            obs (TickObserver): TickObserver
            priority (int, optional): 0 = 最高优先级, -1 = 最低优先级. Defaults to 1.
        """
        if not isinstance(obs, TickObserver):
            raise TypeError("obs must be TickObserver")
        if obs not in self.__priority_obs[priority]:
            self.__priority_obs[priority].append(obs)
        self.__modify_observers()

    def detach(self, obs: TickObserver):  # 解绑
        for i in [0, 1, -1]:
            if obs in self.__priority_obs[i]:
                self.__priority_obs[i].remove(obs)
        self.__modify_observers()

    def __modify_observers(self):
        self.__observers = []
        for i in [0, 1, -1]:
            for j in self.__priority_obs[i]:
                if j in self.__observers:
                    raise RuntimeError("duplicate observer")
                self.__observers.append(j)

    def notify(self, msg: Any, **kwargs):  # 推送
        for obs in self.__observers:
            obs.on_tick(msg, **kwargs)


class AbstractEventDriveEngine(DataSubject, OrderSubject, InstructionSubject, TickSubject):
    def __init__(self):
        DataSubject.__init__(self)
        OrderSubject.__init__(self)
        InstructionSubject.__init__(self)
        TickSubject.__init__(self)

        self.event_dq = deque()
        self._tl = threading.RLock()

    def attach(
        self,
        obs: Union[DataObserver, OrderObserver, InstructionObserver, TickObserver],
        priority: int = 1,
        pub_content: list[str] = ["DATA", "ORDER", "INSTRUCTION", "TICK"],
    ):
        """Subject连接Observer

        Args:
            obs (Union[DataObserver, OrderObserver, InstructionObserver, TickObserver]): Observer
            priority (int, optional): 0 - 最高优先级, -1 - 最低优先级. Defaults to 1.
            pub_content (list[str], optional): 发布内容 DATA - 数据，ORDER - 订单，INSTRUCTION - 指令，TICK - 时间. Defaults to ["DATA", "ORDER", "INSTRUCTION", "TICK"].
        """
        if isinstance(obs, DataObserver) and "DATA" in pub_content:
            DataSubject.attach(self, obs, priority)
        if isinstance(obs, OrderObserver) and "ORDER" in pub_content:
            OrderSubject.attach(self, obs, priority)
        if isinstance(obs, InstructionObserver) and "INSTRUCTION" in pub_content:
            InstructionSubject.attach(self, obs, priority)
        if isinstance(obs, TickObserver) and "TICK" in pub_content:
            TickSubject.attach(self, obs, priority)

    def detach(self, obs: Union[DataObserver, OrderObserver, InstructionObserver, TickObserver]):
        if isinstance(obs, DataObserver):
            DataSubject.detach(self, obs)
        if isinstance(obs, OrderObserver):
            OrderSubject.detach(self, obs)
        if isinstance(obs, InstructionObserver):
            InstructionSubject.detach(self, obs)
        if isinstance(obs, TickObserver):
            TickSubject.detach(self, obs)

    def extend_data(self, data_lst: list):
        with self._tl:
            self.event_dq.extend([(Event.DATA, i) for i in data_lst])
            if len(self.event_dq) == len(data_lst):
                return

            prioritization = []
            order_id = 0
            for i, j in self.event_dq:
                if i == Event.DATA:
                    prioritization.append(to_nstimestamp("{} {:0>10.3f}".format(j[1]["date"], j[1]["time"] / 1000)))
                elif i == Event.ORDER or i == Event.TICK:
                    prioritization.append(order_id)
                    order_id += 1
                elif i == Event.INSTRUCTION:
                    prioritization.append(-np.inf)
                elif i == Event.END:
                    prioritization.append(np.inf)

            self.event_dq, tmp_lst = deque(), list(self.event_dq)
            for i in np.argsort(prioritization):
                self.event_dq.append(tmp_lst[i])

    def on_data(self, data: Tuple[DataLabel, TickSnapshot | Bar]):
        """data放在最后面，按顺序处理. 实时数据才会通过这个方法进入到队列中, 所以接收实时数据时不会有Event.END"""
        with self._tl:
            self.event_dq.append((Event.DATA, data))

    def on_tick(self, tick):
        """tick放在最后面"""
        with self._tl:
            self._insert((Event.TICK, tick))

    def _insert(self, event):
        for i, (j, _) in enumerate(self.event_dq):
            if j == Event.DATA or j == Event.END:
                self.event_dq.insert(i, event)
                break
        else:
            self.event_dq.append(event)

    def on_order(self, order):
        with self._tl:
            self._insert((Event.ORDER, order))

    def on_instruction(self, instruction):
        """instruction放在最前面"""
        with self._tl:
            self.event_dq.appendleft((Event.INSTRUCTION, instruction))

    def add_end_signal(self):
        with self._tl:
            if self.event_dq[-1][0] != Event.END:
                self.event_dq.append((Event.END, None))

    def __iter__(self):
        return self

    def __next__(self):
        with self._tl:
            event_type, msg = self.event_dq.popleft()
        if event_type == Event.DATA:
            label, data = msg
            DataSubject.notify(self, data, basic_data=parse_data_label(label)[1])
        elif event_type == Event.ORDER:
            OrderSubject.notify(self, msg)
        elif event_type == Event.INSTRUCTION:
            InstructionSubject.notify(self, msg)
        elif event_type == Event.TICK:
            TickSubject.notify(self, msg)
        elif event_type == Event.END:
            raise StopIteration
        return event_type, msg

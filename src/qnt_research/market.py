import datetime as dt

import datetype
import numpy as np
import pandas as pd

from qnt_utils.ctoolset import generate_datetime
from qnt_utils.enums import BasicData, FinancialAsset
from qnt_utils.label import QSymbol
from qnt_utils.toolset import get_financial_asset

from .api import get_contract_info
from .entity import DCMarketSnapshot, DCUnderlyingInfo
from .observer_pattern import DataObserver

gl_trade_parameters: dict[QSymbol, DCUnderlyingInfo] = {}


def init_trade_params(symbol: QSymbol, trade_params: dict = {}) -> DCUnderlyingInfo:
    """初始化交易参数

    Args:
        symbol (QSymbol): 标的
        trade_params (Dict, optional): {
            "slippage": 1,  # 滑点为slippage跳
            "min_transaction_fee": 5,   #单笔最低手续费
            "margin": 1,    # 保证金率
            "transaction_cost": 2.5 / 10000,    # 费率
        }. Defaults to {}.
    """
    if (financial_asset := get_financial_asset(symbol)) == FinancialAsset.STOCK:
        t_min_lots, t_lots_change = (200, 1) if symbol[:2] == "68" else (100, 100)
        return DCUnderlyingInfo(
            slippage=trade_params.get("slippage", 1),
            # TODO:股票支持最低5元手续费
            min_transaction_fee=trade_params.get("min_transaction_fee", 0),
            margin=trade_params.get("margin", 1),
            transaction_type=1,
            transaction_cost=trade_params.get("transaction_cost", 2.5 / 10000),
            min_price=0.01,
            multiplier=1,
            min_lots=t_min_lots,
            lots_change=t_lots_change,
        )
    elif financial_asset == FinancialAsset.FUND:
        return DCUnderlyingInfo(
            slippage=trade_params.get("slippage", 1),
            min_transaction_fee=trade_params.get("min_transaction_fee", 0),
            margin=trade_params.get("margin", 1),
            transaction_type=1,
            transaction_cost=trade_params.get("transaction_cost", 2.5 / 10000),
            min_price=0.001,
            multiplier=1,
            min_lots=100,
            lots_change=100,
        )
    elif financial_asset == FinancialAsset.FUTURES:
        contract_info = get_contract_info(symbol)
        if contract_info:
            t_transaction_cost = trade_params.get(
                "transaction_cost",
                contract_info["fee_per_lot"]
                if not pd.isna(contract_info["fee_per_lot"])
                else contract_info["fee_per_turnover"],
            )
            t_transaction_type = 1 if t_transaction_cost < 1 else 2  # 1为按金额*费率, 2为手数*每手费用
            t_min_price = contract_info["minimum_price_change"]
            t_multiplier = contract_info["contract_multiplier"]
            t_min_lots, t_lots_change = 1, 1
        else:
            t_transaction_cost = 1.5 / 10000
            t_transaction_type = 1  # 1为按金额*费率, 2为手数*每手费用
            t_min_price = 1
            t_multiplier = 0
            t_min_lots, t_lots_change = 1, 1
        return DCUnderlyingInfo(
            slippage=trade_params.get("slippage", 1),
            min_transaction_fee=trade_params.get("min_transaction_fee", 0),
            margin=trade_params.get("margin", 0.1),
            transaction_cost=t_transaction_cost,
            transaction_type=t_transaction_type,
            min_price=t_min_price,
            multiplier=t_multiplier,
            min_lots=t_min_lots,
            lots_change=t_lots_change,
        )
    else:
        raise NotImplementedError(f"暂不支持交易品种: {symbol}")


class MarketSnapshot(DataObserver):
    def __init__(self):
        self._snapshot: dict[QSymbol, DCMarketSnapshot] = {}
        self._current_date_time: datetype.NaiveDateTime = dt.datetime.min  # type: ignore

    def clear(self):
        self.__init__()

    def on_data(self, msg, **kwargs):
        def update_market_snapshot(symbol: QSymbol, date_time: datetype.NaiveDateTime, last_price: float, **kwargs):
            """根据最新价更新, 在每次周期结束策略逻辑执行前运行"""
            # TODO: 目前只更新价格, 后续可考虑成交量
            # TODO: 如果有初始持仓，先用初始持仓的价格先更新
            if symbol not in self._snapshot.keys():
                self._snapshot[symbol] = DCMarketSnapshot(date_time, last_price, **kwargs)
            else:
                if date_time > self._snapshot[symbol].date_time:
                    self._snapshot[symbol].date_time = date_time
                    self._snapshot[symbol].last_price = (
                        last_price if not np.isnan(last_price) else self._snapshot[symbol].last_price
                    )
                    for k, v in kwargs.items():
                        setattr(self._snapshot[symbol], k, v)

        date_time: datetype.NaiveDateTime = generate_datetime(msg["date"], msg["time"])
        if date_time > self._current_date_time:
            self._current_date_time = date_time

        if kwargs["basic_data"] == BasicData.MINUTE:
            update_market_snapshot(msg["code"], date_time, msg["close"])
        elif kwargs["basic_data"] == BasicData.SNAPSHOT:
            update_market_snapshot(
                msg["code"],
                date_time,
                msg["new_price"],
                **{
                    "underlying_name": msg["codename"],
                    "bidorder_price": msg["bidorder_price"],
                    "bidorder_volume": msg["bidorder_volume"],
                    "askorder_price": msg["askorder_price"],
                    "askorder_volume": msg["askorder_volume"],
                },
            )

    @property
    def current_date_time(self) -> datetype.NaiveDateTime:
        return self._current_date_time

    @property
    def snapshot(self) -> dict[QSymbol, DCMarketSnapshot]:
        return self._snapshot

    def __getitem__(self, key):
        return self._snapshot[key]

    def keys(self):
        return self._snapshot.keys()


gl_market_snapshot = MarketSnapshot()

import datetime
import gc
import json
import multiprocessing
import time
import traceback
from functools import wraps
from typing import Type

import numpy as np
import pandas as pd
import tqdm
from qnt_utils.enums import StrategyMode

from .communicator.instructor import HTTPInstructorForEDA, InstructorForEDA
from .data_manager import gl_original_data
from .market import gl_market_snapshot
from .observer_pattern import AbstractEventDriveEngine
from .strategy.base import CustomStrategy
from .timer import Timer
from .trader.analyse import analyse
from .trader.trader import Trader
from .virtual_exchange import gl_virtual_exchange


def split_list(lst: list, min_num_sublists: int, max_num_len: int):
    """多进程进行参数优化时, 进行调度分配

    Args:
        lst (list): _description_
        min_num_sublists (int): 最小组数
        max_num_len (int): 每组最大长度
    """

    def fnc1(lst, n):
        """
        Splits a list into N sublists as evenly as possible.
        Args:
        lst (list): The list to be split.
        n (int): The number of sublists to create.
        Returns:
        A list of N sublists.
        """
        size = len(lst) // n  # calculate the size of each sublist
        remainder = len(lst) % n  # calculate the remainder
        result = []
        index = 0
        for i in range(n):
            if index >= len(lst):
                break
            if remainder > 0:  # add the remainder to the first few sublists
                sublist = lst[index : index + size + 1]
                index += size + 1
                remainder -= 1
            else:
                sublist = lst[index : index + size]
                index += size
            result.append(sublist)
        return result

    if len(lst) <= min_num_sublists:
        return [[i] for i in lst]
    else:
        if isinstance(lst[0], dict) and "symbol" in lst[0].keys():
            symbol_dict = {}
            for i in lst:
                if not i["symbol"] in symbol_dict:
                    symbol_dict[i["symbol"]] = []
                symbol_dict[i["symbol"]].append(i)
            ret = []
            for _, i in symbol_dict.items():
                ret += [i[j : j + max_num_len] for j in range(0, len(i), max_num_len)]
            while len(ret) < min_num_sublists:
                tmp = [len(i) for i in ret]
                ret = [ret[i] for i in np.argsort(tmp)[::-1]]
                tmp, ret = ret, []
                for i in range(0, len(tmp)):
                    if i < min_num_sublists - len(tmp):
                        ret += fnc1(tmp[i], 2)
                    else:
                        ret.append(tmp[i])
            return ret
        else:
            result = fnc1(lst, min_num_sublists)
            ret = []
            for i in result:
                ret += [i[j : j + max_num_len] for j in range(0, len(i), max_num_len)]
            return ret


def _fnc(
    start_dt: str, end_dt: str, init_money: float, strategy_type: Type, params_: list[dict]
) -> dict[str, dict[str, float]]:
    gl_event_drive_engine.set_env(StrategyMode.BACKTEST, start_dt, end_dt)
    for param in params_:
        strategy = strategy_type(is_quiet=True, **param)
        strategy.register_trader(Trader(init_money, gl_virtual_exchange))
        gl_event_drive_engine.register_strategy(strategy)
        del strategy
    ret = dict(
        zip(
            [", ".join(["{}: {}".format(k, v) for k, v in i.items()]) for i in params_], gl_event_drive_engine.run(True)
        )
    )
    gc.collect()
    return ret


def hyperparameter_tuning(
    n_cores: int, start_dt: str, end_dt: str, init_money: float, strategy_type: Type, params: list[dict]
):
    print("总运算次数为：{}".format(len(params)))
    with multiprocessing.Pool(n_cores) as pool:
        tmp = pool.starmap(
            _fnc, [(start_dt, end_dt, init_money, strategy_type, i) for i in split_list(params, n_cores, 3)]
        )
    res = {}
    for i in tmp:
        res.update(i)
    return pd.DataFrame(res).T


def _fnc1(
    start_dt: str, end_dt: str, init_money: float, strategy_type: Type, params_: list[dict]
) -> dict[str, dict[str, float]]:
    """单次回测

    Args:
        start_dt (str): 开始时间
        end_dt (str): 结束时间
        init_money (float): 初始资金
        strategy_type (Type): 策略
        params_ (list[dict]): 参数组列表

    Returns:
        dict[str, dict[str, float]]: key=str(参数组), value=回测结果
    """
    gl_event_drive_engine.set_env(StrategyMode.BACKTEST, start_dt, end_dt)
    for param in params_:
        strategy = strategy_type(is_quiet=True, **param)
        strategy.register_trader(Trader(init_money, gl_virtual_exchange))
        gl_event_drive_engine.register_strategy(strategy)
        del strategy
    ret = dict(zip([json.dumps(i) for i in params_], gl_event_drive_engine.run(True)))
    gc.collect()
    return ret


def hyperparameter_tuning1(
    n_cores: int, start_dt: str, end_dt: str, init_money: float, strategy_type: Type, params: list[dict]
):
    print("总运算次数为：{}".format(len(params)))

    pool = multiprocessing.Pool(n_cores)
    tasks = []
    params_lst = split_list(params, n_cores, 3)
    progress_bar = tqdm.tqdm(total=len(params_lst))
    for i in params_lst:
        tasks.append(
            pool.apply_async(
                _fnc1, args=(start_dt, end_dt, init_money, strategy_type, i), callback=lambda x: progress_bar.update()
            )
        )
    pool.close()
    pool.join()
    progress_bar.close()

    res = {}
    for i in [j.get() for j in tasks]:
        res.update(i)
    res1 = {}  # {symbol: {str(参数组): 回测结果}}
    for k, v in res.items():
        k1 = json.loads(k)
        k11 = json.dumps(k1.pop("symbol"))
        if not k11 in res1.keys():
            res1[k11] = {}
        res1[k11][json.dumps(k1)] = v["年化单利"] / v["最大损益回撤"] * init_money
    res2 = pd.DataFrame(res1)  # index - str(参数组), column - symbol, value - 回测结果
    # 对每列获取值的排名
    for column in res2.columns:
        res2[column] = np.argsort(np.argsort(res2[column]))
    res3: pd.Series = res2.sum(axis=1)  # index - str(参数组), value - 排名汇总
    return res3.index[np.argmax(res3)]


def timing(fnc):
    @wraps(fnc)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        res = fnc(*args, **kwargs)
        end_time = time.time()
        time_cost = end_time - start_time
        hours = time_cost // 3600
        mins = (time_cost % 3600) // 60
        secs = (time_cost % 3600) % 60
        print(
            "%s      Take time: %d hours %d mins %d s"
            % (datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), hours, mins, secs)
        )
        return res

    return wrapper


class EventDriveEngine(AbstractEventDriveEngine):
    def __init__(self):
        super().__init__()
        self._mode: StrategyMode
        self._trader_lst: list[Trader] = []
        self._strategy_lst: list[CustomStrategy] = []
        self._scheduler: Timer | None = None

    def set_env(self, mode: StrategyMode, start_dt: str | None = None, end_dt: str | None = None):
        """设置环境

        Args:
            mode (StrategyMode): 回测或实盘
            start_dt (str | None, optional): 历史数据起始时间. Defaults to None.
            end_dt (str | None, optional): 历史数据截止时间. Defaults to None.
        """
        self._mode = mode
        if self._mode == StrategyMode.BACKTEST:
            gl_original_data.set_time_interval(start_dt, end_dt=end_dt, sub_quotation=False)
            self.attach(gl_virtual_exchange, pub_content=["DATA"])
        else:
            gl_original_data.set_time_interval(start_dt, end_dt=end_dt, sub_quotation=True)

    def register_instructor(self, instructor: InstructorForEDA):
        instructor.register_event_engine(self)

    def wrap_job(self, fnc):
        @wraps(fnc)
        def wrapper(*args, **kwargs):
            ret = fnc(*args, **kwargs)
            self.on_tick(ret)
            return ret

        return wrapper

    def register_scheduler(self, scheduler: Timer):
        """注册Schedule"""
        if not self._mode == StrategyMode.BACKTEST:
            self._scheduler = scheduler
        else:
            raise NotImplementedError("不能在回测模式下注册Scheduler")

    def _register_trader(self, *traders: Trader):
        """注册Trader, 回测运行结束时会对每个Trader进行分析"""
        for trader in traders:
            if trader in self._trader_lst:
                continue
            self._trader_lst.append(trader)
            trader.trade_api.register_event_engine(self)
            self.attach(trader, pub_content=["DATA", "ORDER"])

    def register_strategy(self, *strategys: CustomStrategy):
        """注册策略, 同时会把每个策略绑定的Trader注册到EventDriveEngine中"""
        for strategy in strategys:
            if strategy in self._strategy_lst:
                continue
            self._strategy_lst.append(strategy)
            # 策略不需要ORDER，ORDER从Trader过来
            self.attach(strategy, -1, ["DATA", "INSTRUCTION", "TICK"])
            self._register_trader(*strategy.get_traders())

    @timing
    def run(self, brief_analyse: bool = False) -> list:
        """运行

        Args:
            brief_analyse (bool, optional): 是否简单分析. Defaults to False.

        Raises:
            StopIteration: 数据停止或实时数据中断
            RuntimeError: _description_

        Returns:
            list: 注册的每个trader的绩效分析结果
        """

        for strategy in self._strategy_lst:
            strategy.init()
        if not self._scheduler is None:
            self._scheduler.run()
        while True:
            try:
                next(self)
            except IndexError:
                time.sleep(0.01)
            except StopIteration:
                ret = [
                    analyse(*[v for k, v in trader_info.items()], brief_analyse=brief_analyse)
                    for i in self._trader_lst
                    if (trader_info := i.get_info()) is not None
                ]

                self.clear()
                return ret
            except:
                self.clear()
                traceback.print_exc()
                raise RuntimeError

    def clear(self):
        gl_virtual_exchange.clear()
        gl_market_snapshot.clear()
        gl_original_data.clear()

        for i in self._trader_lst:
            self.detach(i)
        self._trader_lst = []

        for i in self._strategy_lst:
            self.detach(i)
        self._strategy_lst = []


gl_event_drive_engine = EventDriveEngine()
gl_original_data.register_event_engine(gl_event_drive_engine)
gl_event_drive_engine.attach(gl_market_snapshot, 0, pub_content=["DATA"])

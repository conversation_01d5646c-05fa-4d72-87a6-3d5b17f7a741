import datetime as dt
from typing import Dict

import datetype

from qnt_utils.enums import BasicData, Direction, OffSet, StatusCode
from qnt_utils.label import QSymbol
from qnt_utils.logger import Logger

from ..data_manager import BarSeriesAggregator, SeriesAggregator
from ..data_manager.original_data import gl_original_data
from ..market import gl_market_snapshot, gl_trade_parameters, init_trade_params
from ..observer_pattern import DataObserver, InstructionObserver, OrderObserver, TickObserver
from ..trader.trader import Trader
from ..trader.utils import calc_amount


class BaseStrategy(DataObserver, OrderObserver, InstructionObserver, TickObserver):
    """策略基类"""

    def __init__(self):
        """策略初始化"""
        self.trader: Trader

        self.data: Dict[QSymbol, SeriesAggregator] = {}
        """存数据序列, key为代码"""

        self._subscribe_lst: list[tuple[QSymbol, BasicData]] = []
        """订阅的数据"""

        self.logger = Logger(self.__class__.__name__, "INFO")

    @property
    def date_time(self) -> datetype.NaiveDateTime:
        return gl_market_snapshot.current_date_time

    def _set_parameters(self, parameters):
        """策略参数设为默认值, 在对象初始化的时候执行"""
        for key, value in parameters.items():
            setattr(self, key, value)

    def register_trader(self, trader: Trader):
        """注册Trader

        Args:
            trader (Trader): _description_
        """
        self.trader = trader
        trader.attach(self)

    def subscribe(self, symbol: QSymbol, size: int, merge_num: int, basic_data: BasicData) -> SeriesAggregator | None:
        """策略需要订阅的数据

        Args:
            symbol (QSymbol): 订阅代码
            size (int): 序列数据需要保存的长度, 如果订阅的是分钟数据且size>1, 则把数据放入BarSeriesAggregator
            merge_num (int): N个数据合成一个数据
            basic_data (BasicData): 基础数据

        Returns:
            SeriesAggregator | None: 如果是需要保存的序列数据,返回一个SeriesAggregator, 否则返回None
        """
        self._subscribe_lst.append((symbol, basic_data))
        his = gl_original_data.subscribe(symbol, basic_data)

        if size >= 1 and basic_data == BasicData.MINUTE:
            self.data[symbol] = BarSeriesAggregator(symbol, size, merge_num, basic_data)
            for i in his:
                self.data[symbol].on_data(i, basic_data)
        return self.data.get(symbol, None)

    def on_data(self, data: dict, **kwargs):
        if (code := data["code"], kwargs["basic_data"]) not in self._subscribe_lst:
            return
        if kwargs["basic_data"] == BasicData.MINUTE:
            if code in self.data.keys():
                self.data[code].on_data(data, kwargs["basic_data"])
                if (tmp := self.data[code]).new_data and tmp.is_inited[0]:
                    self.on_bar(tmp)
        elif kwargs["basic_data"] == BasicData.SNAPSHOT:
            self.on_snapshot(data)

    @staticmethod
    def _order(direction, offset):
        """闭包函数, 用于创建下单函数"""

        def fnc(self, symbol, amount, money=None):
            # 如果不基于历史信号, 则判断最新数据时间和当前时间是否相差30秒以上, 如果是则返回失败
            if (
                not self._is_based_on_his_signals
                and abs(gl_market_snapshot[symbol].date_time - dt.datetime.now()).total_seconds() >= 30
            ):
                return StatusCode.FAILED, None

            # 下单价格为当前最新价+滑点
            tp = gl_trade_parameters.setdefault(symbol, init_trade_params(symbol))
            if direction in [Direction.BUY, Direction.CREDIT_BUY]:
                price = gl_market_snapshot[symbol].last_price + tp.slippage * tp.min_price
            else:
                price = gl_market_snapshot[symbol].last_price - tp.slippage * tp.min_price
            if amount is None:
                amount = calc_amount(symbol, offset, money, price)

            if symbol not in self.data.keys() or all(self.data[symbol].is_inited):
                status_code, order_id = self.trader.order(
                    symbol,
                    direction=direction,
                    offset=offset,
                    amount=amount,
                    price=price,
                )
                if not self._is_quiet:
                    if status_code == StatusCode.SUCCESS:
                        self.logger.info(
                            "\t{dt} \t{direction:<15} {offset:<15} {symbol} \tprice: {price:.4f} \tamount: {amount:.0f}".format(
                                dt=self.date_time.strftime("%Y-%m-%d %H:%M:%S"),
                                direction=direction.name,
                                offset=offset.name,
                                symbol=symbol,
                                price=price,
                                amount=amount,
                            )
                        )
                    else:
                        self.logger.info(
                            "\t{dt} \t{direction:<15} {offset:<15} {symbol} \tprice: {price:.4f} \tamount: {amount:.0f} is failed, ACCOUNT LIMIT".format(
                                dt=self.date_time.strftime("%Y-%m-%d %H:%M:%S"),
                                direction=direction.name,
                                offset=offset.name,
                                symbol=symbol,
                                price=price,
                                amount=amount,
                            )
                        )
                return status_code, order_id
            else:
                if not self._is_quiet:
                    self.logger.info(
                        "\t{dt} \t{direction:<15} {offset:<15} {symbol} \tprice: {price:.4f} \tamount: {amount:.0f} failed, NOT INITIATED".format(
                            dt=self.date_time.strftime("%Y-%m-%d %H:%M:%S"),
                            direction=direction.name,
                            offset=offset.name,
                            symbol=symbol,
                            price=price,
                            amount=amount,
                        )
                    )
                return StatusCode.FAILED, None

        return fnc

    buy_open = _order(Direction.BUY, OffSet.OPEN)
    buy_close = _order(Direction.BUY, OffSet.CLOSE)
    sell_open = _order(Direction.SELL, OffSet.OPEN)
    sell_close = _order(Direction.SELL, OffSet.CLOSE)

    def get_traders(self):
        return [self.trader]

    def on_bar(self, data: SeriesAggregator):
        """策略逻辑"""
        raise NotImplementedError

    def on_snapshot(self, data):
        """处理快照"""
        raise NotImplementedError


class CustomStrategy(BaseStrategy):
    """自定义策略, 需要自定义init, on_bar, on_order等方法"""

    def __init__(self, is_quiet=False, is_based_on_his_signals=True, **kwargs):
        """策略初始化

        Args:
            is_quiet (bool, optional): True则不输出日志. Defaults to False.
            is_based_on_his_signals (bool, optional): False则策略运行不基于历史信号, 如果与当前时间相差过大不会开仓. Defaults to True.
        """
        self.parameters = kwargs
        self._is_quiet = is_quiet
        self._is_based_on_his_signals = is_based_on_his_signals
        self._set_parameters(kwargs)

        super().__init__()

    def init(self):
        """需要应用层定义, 主要用于创建ASeries和MSeries, 会在EventEngine.run中调用"""
        pass

    def on_order(self, msg, **kwargs):
        """处理委托回报"""
        pass

    def on_instruction(self, msg, **kwargs):
        """处理指令"""
        pass

    def on_snapshot(self, data):
        """处理快照"""
        pass

    def on_bar(self, data: SeriesAggregator):
        """策略逻辑"""
        pass

    def on_tick(self, msg, **kwargs):
        """定时器"""
        pass


class SyncStrategy(CustomStrategy):
    """同步型策略, 一般用于期货, 因为可能期货的计算依赖加权合约, 而交易是在具体合约上。"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.slave_trader: Trader | None
        self.trade_symbol_map: dict[QSymbol, QSymbol]
        self._trade_symbol_lst: list[QSymbol] = []

    def on_order(self, msg, **kwargs):
        self._sync()

    def on_data(self, data: dict, **kwargs):
        if data is None:
            return
        if (code := data["code"], kwargs["basic_data"]) not in self._subscribe_lst:
            return

        if code in self._trade_symbol_lst:
            self._sync()
        if code in self.data.keys():
            self.data[code].on_data(data, **kwargs)
            if (tmp := self.data[code]).new_data and tmp.is_inited[0]:
                self.on_bar(tmp)

    def register_trader(
        self, master: Trader, slave: None | Trader = None, trade_symbol_map: dict[QSymbol, QSymbol] = {}
    ) -> None:
        super().register_trader(master)
        self.slave_trader = slave
        if slave is not None:
            slave.attach(self)
        self.trade_symbol_map = trade_symbol_map
        for _, trade_symbol in self.trade_symbol_map.items():
            self.subscribe(trade_symbol, 1, 1, BasicData.SNAPSHOT)
            self._trade_symbol_lst.append(trade_symbol)

    def _sync(self):
        """同步master trader和slave trader的数据"""
        if self.slave_trader is None:
            return

        positions0 = self.trader.positions[1]
        portfolio0 = self.trader.portfolio[1]
        positions1 = self.slave_trader.positions[1]
        portfolio1 = self.slave_trader.portfolio[1]
        frozen = portfolio1.frozen_cash

        for calc_symbol, trade_symbol in self.trade_symbol_map.items():
            if not (calc_symbol in gl_market_snapshot.keys() and trade_symbol in gl_market_snapshot.keys()):
                continue
            if gl_market_snapshot[calc_symbol].date_time > gl_market_snapshot[trade_symbol].date_time:
                continue
            # print(dt.datetime.now(), 1111)
            # 多仓补齐
            l1 = 0 if calc_symbol not in positions0.keys() else positions0[calc_symbol]["LONG"].amount
            l2 = 0 if trade_symbol not in positions1.keys() else positions1[trade_symbol]["LONG"].amount
            f = 0 if trade_symbol not in positions1.keys() else positions1[trade_symbol]["LONG"].frozen
            tp_c = gl_trade_parameters.setdefault(calc_symbol, init_trade_params(calc_symbol))
            tp_t = gl_trade_parameters.setdefault(trade_symbol, init_trade_params(trade_symbol))
            if f == 0 and frozen == 0:
                if l1 > l2:
                    self.slave_trader.order(
                        trade_symbol,
                        "BUY",
                        "OPEN",
                        l1 - l2,
                        gl_market_snapshot[trade_symbol].last_price + tp_c.slippage * tp_t.min_price,
                    )
                elif l1 < l2:
                    self.slave_trader.order(
                        trade_symbol,
                        "SELL",
                        "CLOSE",
                        min(l2, l2 - l1),
                        gl_market_snapshot[trade_symbol].last_price - tp_c.slippage * tp_t.min_price,
                    )
            # 空仓补齐
            s1 = 0 if calc_symbol not in positions0.keys() else positions0[calc_symbol]["SHORT"].amount
            s2 = 0 if trade_symbol not in positions1.keys() else positions1[trade_symbol]["SHORT"].amount
            f = 0 if trade_symbol not in positions1.keys() else positions1[trade_symbol]["SHORT"].frozen
            if f == 0 and frozen == 0:
                if s1 > s2:
                    self.slave_trader.order(
                        trade_symbol,
                        "SELL",
                        "OPEN",
                        s1 - s2,
                        gl_market_snapshot[trade_symbol].last_price - tp_c.slippage * tp_t.min_price,
                    )
                elif s1 < s2:
                    self.slave_trader.order(
                        trade_symbol,
                        "BUY",
                        "CLOSE",
                        min(s2, s2 - s1),
                        gl_market_snapshot[trade_symbol].last_price + tp_c.slippage * tp_t.min_price,
                    )

    def get_traders(self):
        return super().get_traders() + ([] if self.slave_trader is None else [self.slave_trader])

import re
from itertools import chain
from typing import List, Union

import numpy as np

from ..data_manager.series import IndicatorSeries
from ..utils.indicator_parse import *
from .base import BaseStrategy


class IndicatorStrategy(BaseStrategy):
    _QUOTE_DATA_SERIES_PAIR = {
        "C": "close",
        "CLOSE": "close",
        "H": "high",
        "HIGH": "high",
        "L": "low",
        "LOW": "low",
        "O": "open",
        "OPEN": "open",
        "V": "volume",
        "VOL": "volume",
        "OPI": "open_interest",
    }
    _TRADE_DATA_SERIES = [
        "BK", "BKVOL", "BKPRICE", "BARSBK", "BKHIGH", "SK", "SKVOL", "SKPRICE", "BARSSK", "SKLOW", "BP", "BARSBP", "SP", "BARSSP", "SETTLE"
    ]

    def __init__(self, symbol, code, basic_data, merge_num, size):

        super().__init__()
        self._symbol = symbol
        self._code = code
        self._series: List = []

        self._edit_code()
        self.subscribe(symbol, size, merge_num, basic_data)
        for i in chain(self._series, self._QUOTE_DATA_SERIES_PAIR.keys(), self._TRADE_DATA_SERIES):
            self.data[self._symbol].create_series(i, data_type="float", is_ind=True)

    def BK(self, amount: np.uint):
        if amount > 0:
            self.buy_open(self._symbol, amount)

    def SK(self, amount: np.uint):
        if amount > 0:
            self.sell_open(self._symbol, amount)

    def BP(self, amount: Union[np.uint, IndicatorSeries]):
        if isinstance(amount, IndicatorSeries):
            t_amount = amount.value
        else:
            t_amount = amount
        if t_amount > 0:
            self.buy_close(self._symbol, t_amount)

    def SP(self, amount: Union[np.uint, IndicatorSeries]):
        if isinstance(amount, IndicatorSeries):
            t_amount = amount.value
        else:
            t_amount = amount
        if t_amount > 0:
            self.sell_close(self._symbol, t_amount)

    def _edit_code(self):

        def get_indicator_name(code):
            res = set()
            for i in code.split("\n"):
                tmp = i.split(":=")
                if len(tmp) >= 2:
                    res.add(re.sub("[ \\\t]*", "", tmp[0]))
            return list(res)

        def trade_op_modify(code):
            begin = [not (i.isspace()) for i in code].index(True)
            res = code[:begin] + "if " + code[begin:]
            res = res.replace(",BK(", ": self.BK(")
            res = res.replace(",SK(", ": self.SK(")
            res = res.replace(",BP(", ": self.BP(")
            res = res.replace(",SP(", ": self.SP(")
            res = res.replace(",BPK(", ": self.BPK(")
            res = res.replace(",SPK(", ": self.SPK(")
            return res

        cp_code = self._code
        # 去掉无用行, 比如注释
        #TODO /*......*/的注释目前需要手动去除
        cp_code = re.sub("[ \\t]*#.*\\n", "", cp_code)
        cp_code = re.sub("[ \\t]*//.*\\n", "", cp_code)
        cp_code = re.sub("[ \\t]*BEGIN[ ]*\\n", "", cp_code)
        cp_code = re.sub("[ \\t]*END[ ]*\\n", "", cp_code)
        cp_code = re.sub("VARIABLE.*\\n", "", cp_code)
        cp_code = re.sub("[\\n]+", "\\n", cp_code)

        self._series = get_indicator_name(cp_code)

        res = []
        # 对接行情数据
        for i, j in self._QUOTE_DATA_SERIES_PAIR.items():
            res.append("self.data[self._symbol].%s.update(self.data[self._symbol].%s[-1])" % (i, j))
            res.append("%s = self.data[self._symbol].%s" % (i, i))
        # 对接交易数据
        for i in self._TRADE_DATA_SERIES:
            res.append("%s = self.data[self._symbol].%s" % (i, i))
        res.append("""
SETTLE.update(SUM(VOL*C, self.data[self._symbol].n_trade_day[-1])/SUM(VOL, self.data[self._symbol].n_trade_day[-1]))
if self._symbol in self.trader.positions.keys():
    BKVOL.update(self.trader.positions[self._symbol]["LONG"]["amount"])
else:
    BKVOL.update(0)
if BKVOL[-1]>BKVOL[-2]:
    BK.modify(1)
elif BKVOL[-1]<BKVOL[-2]:
    SP.modify(1)
BK.update(0)
SP.update(0)
if BK[-2]==1:
    BKPRICE.update(CLOSE[-2])
    BARSBK.update(1)
    BKHIGH.update(HIGH[-1])
else:
    BKPRICE.update(BKPRICE[-1])
    BARSBK.update(BARSBK[-1]+1)
    BKHIGH.update(max(BKHIGH[-1],HIGH[-1]))
if SP[-2]==1:
    BARSSP.update(1)
else:
    BARSSP.update(BARSSP[-1]+1)

if self._symbol in self.trader.positions.keys():
    SKVOL.update(self.trader.positions[self._symbol]["SHORT"]["amount"])
else:
    SKVOL.update(0)
if SKVOL[-1]>SKVOL[-2]:
    SK.modify(1)
elif SKVOL[-1]<SKVOL[-2]:
    BP.modify(1)
SK.update(0)
BP.update(0)
if SK[-2]==1:
    SKPRICE.update(CLOSE[-2])
    BARSSK.update(1)
    SKLOW.update(LOW[-1])
else:
    SKPRICE.update(SKPRICE[-1])
    BARSSK.update(BARSSK[-1]+1)
    SKLOW.update(min(SKLOW[-1],LOW[-1]))
if BP[-2]==1:
    BARSBP.update(1)
else:
    BARSBP.update(BARSBP[-1]+1)
""")

        for i in self._series:
            res.append("%s = self.data[self._symbol].%s" % (i, i))

        # IF...THEN处理
        for i in re.findall("IF .* THEN\\n", cp_code):
            cp_code = cp_code.replace(i, "if " + i[3:-6] + ":;\n")

        # 一句多行合并
        tmp = cp_code[1:].replace("\n", " ")
        # 分行
        tmp = re.sub(";[ ]*", "\n", tmp)

        # 交易处理
        trade_op = ["BK", "SK", "BP", "SP"]
        for j in chain(*[re.findall(r".*,%s(.*)" % i, self._code) for i in trade_op]):
            tmp = tmp.replace(j, trade_op_modify(j))

        tmp = tmp.split("\n")
        for i in tmp:
            i_ = i.replace("&&", " and ")
            i_ = i_.replace("||", " or ")
            i_ = i_.replace("<>", "!=")
            i_ = i_.replace("\t", "    ")
            if ":=" in i_:
                i_ = i_.replace(":=", ".update(")
                i_ += ")"
                res.append(i_)
            else:
                res.append(i_)
        self._code = "\n".join(res)

    def calc(self, data):
        exec(self._code)

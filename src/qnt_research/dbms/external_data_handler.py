import datetime
import json
import pathlib
import socket
import traceback
from functools import wraps
from multiprocessing.dummy import Pool
from typing import List

import numpy as np
import pandas as pd
import requests
from ashare_api import ashare_api
from loguru import logger
from qnt_utils.enums import BasicData, FinancialAsset
from qnt_utils.fields import BAR_FIELDS
from qnt_utils.label import Symbol, generate_data_label
from qnt_utils.toolset import decrypt, to_nstimestamp

from .hdr import HistoricalDataReq

MINDGO_GET_PRICE_DAY_FIELDS = [
    "open",
    "high",
    "low",
    "close",
    "factor",
    "volume",
    "turnover",
    "turnover_rate",
    "is_paused",
    "high_limit",
    "low_limit",
    "avg_price",
    "prev_close",
    "quote_rate",
    "amp_rate",
    "is_st",
]
MINDGO_GET_PRICE_MINUTE_FIELDS = [
    "open",
    "high",
    "low",
    "close",
    "factor",
    "volume",
    "turnover",
    "turnover_rate",
    "is_paused",
]
MINDGO_GET_PRICE_FUTURE_DAY_FIELDS = [
    "open",
    "high",
    "low",
    "close",
    "high_limit",
    "low_limit",
    "settle",
    "volume",
    "turnover",
    "change",
    "quote_rate",
    "amp_rate",
    "open_interest",
    "pos_change",
]
MINDGO_GET_PRICE_FUTURE_MINUTE_FIELDS = ["open", "high", "low", "close", "volume", "turnover", "open_interest"]
MINDGO_RENAME_COLUMNS = {"high_limit": "uplimit_price", "prev_close": "pre_price", "low_limit": "downlimit_price"}

TB_MINUTE_FIELDS = ["//时间", "开盘价", "最高价", "最低价", "收盘价", "成交量", "持仓量"]
TB_RENAME_COLUMNS = {
    "开盘价": "open",
    "最高价": "high",
    "最低价": "low",
    "收盘价": "close",
    "成交量": "volume",
    "持仓量": "open_interest",
}

TQ_MINUTE_FIELDS = ["datetime", "open", "high", "low", "close", "volume", "open_oi", "close_oi"]
TQ_RENAME_COLUMNS = {"close_oi": "open_interest"}

IFIND_MINUTE_FIELDS = ["open", "high", "low", "close", "volume", "turnover", "open_interest"]

ASHARE_FIELDS = ["open", "high", "low", "close", "volume"]


def astype(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        df: pd.DataFrame = func(*args, **kwargs)
        return df.astype(BAR_FIELDS)

    return wrapper


@astype
def mindgo_formatter(symbol, data, basic_data) -> pd.DataFrame:
    result = pd.DataFrame(columns=list(BAR_FIELDS.keys()))
    if data is not None and len(data) > 0:
        data.rename(columns=MINDGO_RENAME_COLUMNS, inplace=True)
        # result = pd.concat([result, data], axis=0)
        result = data.reindex(columns=result.columns)
        result["code"] = symbol
        result["date"] = result.index.map(lambda x: int(x.strftime("%Y%m%d")))
        if basic_data == BasicData.MINUTE:
            result["time"] = result.index.map(lambda x: int(x.strftime("%H%M%S000")))
            result.index = result.index.map(lambda x: to_nstimestamp(x.strftime("%Y%m%d %H%M")))
        else:
            result["time"] = 150000000
            result.index = result.index.map(lambda x: to_nstimestamp(x.strftime("%Y%m%d 1500")))
        result.sort_index(ascending=True, inplace=True)
    return result


@astype
def tb_formatter(symbol, data, basic_data) -> pd.DataFrame:
    result = pd.DataFrame(columns=list(BAR_FIELDS.keys()))
    if data is not None and len(data) > 0:
        data.rename(columns=TB_RENAME_COLUMNS, inplace=True)
        data["//时间"] = data["//时间"].apply(lambda x: int(to_nstimestamp(x) + 60 * 1e9))
        data["date"] = data["//时间"].apply(
            lambda x: int(pd.Timestamp.fromtimestamp(x / 1e9, tz="Asia/Shanghai").strftime("%Y%m%d"))
        )
        data["time"] = data["//时间"].apply(
            lambda x: int(pd.Timestamp.fromtimestamp(x / 1e9, tz="Asia/Shanghai").strftime("%H%M%S000"))
        )
        data["code"] = symbol
        data["is_st"] = False
        data["is_paused"] = False
        data.set_index(keys=["//时间"], drop=True, inplace=True, append=False)
        data.index.name = None
        result = data.reindex(columns=result.columns)
        # result = pd.concat([result, data], axis=0)
        result.sort_index(ascending=True, inplace=True)
    return result


@astype
def tq_formatter(symbol, data, basic_data) -> pd.DataFrame:
    basic_data_ = BasicData[basic_data] if isinstance(basic_data, str) else basic_data
    result = pd.DataFrame(columns=list(BAR_FIELDS.keys()))
    if data is not None and len(data) > 0:
        data = data.loc[data["id"] >= 0, :].copy()
        data.rename(columns=TQ_RENAME_COLUMNS, inplace=True)
        data["datetime"] = data["datetime"].apply(
            lambda x: int(x + (60 if basic_data_ == BasicData.MINUTE else 15 * 3600) * 1e9)
        )
        data["tmp"] = data["datetime"] / 1e9
        data["tmp"] = data["tmp"].apply(
            lambda x: datetime.datetime.fromtimestamp(x, tz=datetime.timezone(datetime.timedelta(hours=8)))
        )
        data["date"] = data["tmp"].apply(lambda x: x.year * 10000 + x.month * 100 + x.day)
        data["time"] = data["tmp"].apply(lambda x: x.hour * 10000000 + x.minute * 100000 + x.second * 1000)
        data["code"] = symbol
        data["is_st"] = False
        data["is_paused"] = False
        data.set_index(keys=["datetime"], drop=True, inplace=True, append=False)
        data.index.name = None
        data = data[list(set(data.columns) & BAR_FIELDS.keys())]
        # result = pd.concat([result, data], axis=0)
        result = data.reindex(columns=result.columns)
        result.sort_index(ascending=True, inplace=True)
    return result


@astype
def ifind_formatter(symbol, data, basic_data) -> pd.DataFrame:
    result = pd.DataFrame(columns=list(BAR_FIELDS.keys()))
    if data is not None and len(data) > 0:
        data["date"] = list(map(lambda x: x.year * 10000 + x.month * 100 + x.day, data.index))
        data["time"] = list(
            map(
                lambda x: x.hour * 10000000 + x.minute * 100000 + x.second * 1000,
                data.index,
            )
        )
        data.index = list(map(lambda x: int(x.timestamp() * 1e9), data.index))
        data["code"] = symbol
        data["is_st"] = False
        data["is_paused"] = False
        data = data[list(set(data.columns) & BAR_FIELDS.keys())]
        # result = pd.concat([result, data], axis=0)
        result = data.reindex(columns=result.columns)
        result.sort_index(ascending=True, inplace=True)
    return result


@astype
def ashare_formatter(symbol, data, basic_data) -> pd.DataFrame:
    result = pd.DataFrame(columns=list(BAR_FIELDS.keys()))
    if data is not None and len(data) > 0:
        data["date"] = data.index.map(lambda x: int(x.strftime("%Y%m%d")))
        data["time"] = data.index.map(lambda x: int(x.strftime("%H%M%S000")))
        data["code"] = symbol
        data.index = data.index.map(lambda x: to_nstimestamp(x.strftime("%Y%m%d %H%M")))
        data.index.name = None
        # result = pd.concat([result, data], axis=0)
        result = data.reindex(columns=result.columns)
        result.sort_index(ascending=True, inplace=True)
    return result


class IFindApi:
    def __init__(self, refreshtoken):
        getAccessTokenUrl = "https://quantapi.51ifind.com/api/v1/get_access_token"
        getAccessTokenHeader = {"Content-Type": "application/json", "refresh_token": refreshtoken}
        getAccessTokenResponse = requests.post(url=getAccessTokenUrl, headers=getAccessTokenHeader)
        self._accessToken = json.loads(getAccessTokenResponse.content)["data"]["access_token"]

    def high_frequency(self, symbol, st, et):
        try:
            res = requests.post(
                url="https://quantapi.51ifind.com/api/v1/high_frequency",
                headers={"Content-Type": "application/json", "access_token": self._accessToken},
                json={
                    "codes": symbol,
                    "indicators": "open,high,low,close,volume,amount,openInterest",
                    "starttime": pd.Timestamp(st).strftime("%Y-%m-%d %H:%M:%S"),
                    "endtime": pd.Timestamp(et).strftime("%Y-%m-%d %H:%M:%S"),
                },
            )
            ret = pd.DataFrame((tmp := json.loads(res.content)["tables"][0])["table"], index=tmp["time"])
            ret.rename(columns={"amount": "turnover", "openInterest": "open_interest"}, inplace=True)
            ret.index = pd.to_datetime(ret.index).tz_localize("Asia/Shanghai")
            ret["volume1"] = ret["volume"].shift(1)
            ret["turnover1"] = ret["turnover"].shift(1)
            ret["tmp"] = pd.Series(ret.index, index=ret.index).shift(1)
            ret["volume"] = np.where(
                ret["tmp"].apply(
                    lambda x: not pd.isna(x) and (x.time() == datetime.time(9) or x.time() == datetime.time(21))
                ),
                ret["volume"] + ret["volume1"],
                ret["volume"],
            )
            ret["turnover"] = np.where(
                ret["tmp"].apply(
                    lambda x: not pd.isna(x) and (x.time() == datetime.time(9) or x.time() == datetime.time(21))
                ),
                ret["turnover"] + ret["turnover1"],
                ret["turnover"],
            )
            ret.drop(["volume1", "turnover1", "tmp"], axis=1, inplace=True)
            ret = ret.loc[
                list(filter(lambda x: x.time() != datetime.time(9) and x.time() != datetime.time(21), ret.index)), :
            ]
            return ret
        except:
            return None


class ExternalDataHandler:
    def __init__(self, config):
        self._config = config

        if "ifind" in self._config and self._config["ifind"]["available"]:
            self._ifind_handler = IFindApi(self._config["ifind"]["refresh_token"])
        else:
            self._ifind_handler = None

    def get_data(self, hdr: HistoricalDataReq) -> List[pd.DataFrame]:
        """从源数据库提取数据, 遍历配置的所有数据库"""
        if not self._config:
            return []
        data = []
        p = Pool(4)
        hostname = socket.gethostname()
        if hdr.asset_type in [FinancialAsset.STOCK, FinancialAsset.FUND, FinancialAsset.BOND]:
            if (
                "mindgo" in self._config
                and self._config["mindgo"]["available"]
                and ("jupyter" in hostname or self._config["mindgo"]["is_local"])
            ):
                data.append(
                    p.apply_async(func=self._get_data_from_mindgo, args=(hdr, self._config["mindgo"]["is_local"]))
                )
            if "ifind" in self._config and self._config["ifind"]["available"]:
                data.append(p.apply_async(func=self._get_data_from_ifind, args=(hdr,)))
            if "ashare" in self._config and self._config["ashare"]["available"]:
                data.append(p.apply_async(func=self._get_data_from_ashare, args=(hdr,)))
        elif hdr.asset_type == FinancialAsset.FUTURES:
            if "tb" in self._config and self._config["tb"]["available"]:
                data.append(p.apply_async(func=self._get_data_from_tb, args=(hdr,)))
            if "tq" in self._config and self._config["tq"]["available"]:
                data.append(p.apply_async(func=self._get_data_from_tq, args=(hdr,)))
            if (
                "mindgo" in self._config
                and self._config["mindgo"]["available"]
                and ("jupyter" in hostname or self._config["mindgo"]["is_local"])
            ):
                data.append(
                    p.apply_async(func=self._get_data_from_mindgo, args=(hdr, self._config["mindgo"]["is_local"]))
                )
            if "ifind" in self._config and self._config["ifind"]["available"]:
                data.append(p.apply_async(func=self._get_data_from_ifind, args=(hdr,)))
        p.close()
        p.join()
        return [i.get() for i in data]

    def _get_data_from_mindgo(self, hdr: HistoricalDataReq, is_local=False) -> pd.DataFrame:
        """从MindGo数据库中提取历史数据"""
        assert hdr.basic_data == BasicData.MINUTE, "supermind数据仅支持日级和分钟"
        tmp_symbol = Symbol.qs_to_mindgo(hdr.symbol)
        data = None
        try:
            if is_local:
                data_label = generate_data_label(hdr.symbol, hdr.basic_data)
                with pd.HDFStore(self._config["mindgo"]["path"], "r") as store:
                    try:
                        data = store.get(data_label)
                    except KeyError:
                        logger.warning("can not find {} in local database of mindgo data".format(data_label))
            else:
                from mindgo_api import get_price, get_price_future  # type: ignore

                if hdr.asset_type == FinancialAsset.STOCK:
                    if hdr.basic_data == BasicData.MINUTE:
                        data = get_price(
                            tmp_symbol,
                            hdr.start_time,
                            hdr.end_time,
                            "1m",
                            MINDGO_GET_PRICE_MINUTE_FIELDS,
                            skip_paused=True,
                            fq="pre",
                            bar_count=0,
                            is_panel=False,
                        )
                    elif hdr.basic_data == BasicData.DAY:
                        data = get_price(
                            tmp_symbol,
                            hdr.start_time,
                            hdr.end_time,
                            "1d",
                            MINDGO_GET_PRICE_DAY_FIELDS,
                            skip_paused=True,
                            fq="pre",
                            bar_count=0,
                            is_panel=False,
                        )
                elif hdr.asset_type == FinancialAsset.FUTURES:
                    if hdr.basic_data == BasicData.MINUTE:
                        data = get_price_future(
                            tmp_symbol,
                            hdr.start_time,
                            hdr.end_time,
                            "1m",
                            MINDGO_GET_PRICE_FUTURE_MINUTE_FIELDS,
                            skip_paused=False,
                            fq=None,
                            bar_count=0,
                            is_panel=False,
                        )
                    elif hdr.basic_data == BasicData.DAY:
                        data = get_price_future(
                            tmp_symbol,
                            hdr.start_time,
                            hdr.end_time,
                            "1d",
                            MINDGO_GET_PRICE_FUTURE_DAY_FIELDS,
                            skip_paused=False,
                            fq=None,
                            bar_count=0,
                            is_panel=False,
                        )
        except:
            traceback.print_exc()
        finally:
            return mindgo_formatter(hdr.symbol, data, hdr.basic_data)

    def _get_data_from_tb(self, hdr: HistoricalDataReq) -> pd.DataFrame:
        assert hdr.basic_data == BasicData.MINUTE, "TB数据仅支持分钟"
        tmp_symbol = Symbol.qs_to_tb(hdr.symbol)
        data = None
        path = pathlib.Path(self._config["tb"]["path"], tmp_symbol + ".csv")
        try:
            if path.exists():
                data = pd.read_csv(path, encoding="gbk", header=0, usecols=TB_MINUTE_FIELDS)
        except:
            traceback.print_exc()
        finally:
            return tb_formatter(hdr.symbol, data, hdr.basic_data)

    def _get_data_from_tq(self, hdr: HistoricalDataReq) -> pd.DataFrame:
        assert hdr.basic_data in [BasicData.DAY, BasicData.MINUTE], "该周期不支持"

        from tqsdk import TqApi, TqAuth, TqTimeoutError

        data = None
        symbol = Symbol.qs_to_tq(hdr.symbol)
        if symbol is not None:
            for _ in range(3):
                try:
                    api = TqApi(auth=TqAuth(self._config["tq"]["account"], decrypt(self._config["tq"]["password"])))
                except:
                    continue
                else:
                    try:
                        data = api.get_kline_serial(
                            symbol=symbol,
                            duration_seconds=60 if hdr.basic_data == BasicData.MINUTE else 86400,
                            data_length=8000,
                        )
                        api.close()
                        break
                    except TqTimeoutError:
                        api.close()
                        print("time out")
                    except:
                        import traceback

                        traceback.print_exc()
                        api.close()
        return tq_formatter(hdr.symbol, data, hdr.basic_data)

    def _get_data_from_ifind(self, hdr: HistoricalDataReq) -> pd.DataFrame:
        assert hdr.basic_data in [BasicData.MINUTE], "该周期不支持"
        assert self._ifind_handler is not None, "iFindApi不可用"

        data = None
        try:
            data = self._ifind_handler.high_frequency(Symbol.qs_to_ifind(hdr.symbol), hdr.start_time, hdr.end_time)
        except:
            logger.warning("get ifind data failed!")
        finally:
            return ifind_formatter(hdr.symbol, data, hdr.basic_data)

    def _get_data_from_ashare(self, hdr: HistoricalDataReq) -> pd.DataFrame:
        assert hdr.basic_data in [BasicData.MINUTE], "该周期不支持"
        symbol = Symbol.qs_to_as(hdr.symbol)
        data = None
        try:
            data = ashare_api.get_price(symbol, count=320, frequency="1m")
        except:
            logger.warning("get ashare data failed!")
        finally:
            return ashare_formatter(hdr.symbol, data, hdr.basic_data)

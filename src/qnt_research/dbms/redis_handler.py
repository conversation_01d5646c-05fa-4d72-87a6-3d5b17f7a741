import json
from typing import Dict, List, Optional

import pandas as pd
import redis


class RedisHandler:
    _redis_pool = redis.ConnectionPool(host="127.0.0.1", port=6379, decode_responses=True)

    def __init__(self):
        self._redis = redis.Redis(connection_pool=self._redis_pool)

    def push_positions(self, strategy_name, positions):
        self._redis.set(
            "positions_%s" % strategy_name,
            json.dumps({"date_time": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"), "data": positions}),
        )

    def get_positions(self, strategy_name: str = "") -> Dict:
        """获取对应策略的持仓情况

        Args:
            strategy_name (str, optional): 策略名称. Defaults to "".

        Returns:
            Dict: {策略名: 持仓}
        """
        if strategy_name:
            if self._redis.exists("positions_%s" % strategy_name):
                return {strategy_name: json.loads(self._redis.get("positions_%s" % strategy_name))}
            else:
                return {}
        else:
            return {k.replace("positions_", ""): json.loads(self._redis.get(k)) for k in self._redis.keys("positions*")}

    def push_portfolio(self, strategy_name, portfolio):
        self._redis.set(
            "portfolio_%s" % strategy_name,
            json.dumps({"date_time": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"), "data": portfolio}),
        )

    def get_portfolio(self, strategy_name: str = None) -> Dict:
        """获取对应策略的账户情况

        Args:
            strategy_name (str, optional): 策略名称. Defaults to None.

        Returns:
            Dict: {策略名: 账户情况}
        """
        if strategy_name:
            if self._redis.exists("portfolio_%s" % strategy_name):
                return {strategy_name: json.loads(self._redis.get("portfolio_%s" % strategy_name))}
            else:
                return {}
        else:
            return {k.replace("portfolio_", ""): json.loads(self._redis.get(k)) for k in self._redis.keys("portfolio*")}

    def push_tradelog(self, tradelog: Dict):
        self._redis.set("tradelog_%s" % tradelog["order_id"], json.dumps(tradelog))

    def get_tradelog(self, order_ids: Optional[List] = None) -> Dict:
        """获取多个order_id对应的成交情况

        Returns:
            Dict: {order_id: 委托单信息}
        """
        if not order_ids is None:
            res = [json.loads(j) for j in [self._redis.get("tradelog_%s" % i) for i in set(order_ids)] if not j is None]
            return {j["order_id"]: j for j in res}
        else:
            return {k.replace("tradelog_", ""): json.loads(self._redis.get(k)) for k in self._redis.keys("tradelog*")}

    def delete_tradelog(self, order_ids: Optional[List] = None):
        """删除多个order_id对应的成交情况"""
        if not order_ids is None:
            for i in order_ids:
                if self._redis.exists("tradelog_%s" % i):
                    self._redis.delete(i)
        else:
            keys = list(self._redis.keys("tradelog*"))
            keys and self._redis.delete(*keys)

from typing import List, Union

from qnt_utils.enums import BasicData
from qnt_utils.label import QSymbol
from qnt_utils.toolset import get_financial_asset


class HistoricalDataReq:
    def __init__(
        self,
        symbol: QSymbol,
        basic_data: Union[str, BasicData],
        start_time: Union[str,None],
        end_time: Union[str,None],
        fields: List = [],
        bar_count: int = 0,
    ):
        self._symbol = symbol
        self._basic_data = BasicData[basic_data] if isinstance(basic_data, str) else basic_data
        self._start_time = start_time
        self._end_time = end_time
        self._fields = fields
        self._asset_type = get_financial_asset(symbol)
        self._bar_count = bar_count

    @property
    def symbol(self):
        return self._symbol

    @property
    def basic_data(self):
        return self._basic_data

    @property
    def start_time(self):
        return self._start_time

    @property
    def end_time(self):
        return self._end_time

    @property
    def fields(self):
        return self._fields

    @property
    def asset_type(self):
        return self._asset_type

    @property
    def bar_count(self):
        return self._bar_count

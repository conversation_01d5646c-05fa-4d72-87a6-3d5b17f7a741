import re
import time
import uuid
from collections.abc import MutableMapping
from functools import lru_cache
from itertools import chain
from typing import Dict, List, Literal, Optional, Union

import datetype
import numpy as np
import pandas as pd
from sqlalchemy import Column, Join, PrimaryKeyConstraint, Table, UniqueConstraint, and_, case, literal, or_
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import select
from sqlalchemy.sql.expression import null
from sqlalchemy.types import BigInteger, Boolean, Date, Integer, Numeric, SmallInteger, String, Text, Time

from qnt_utils.enums import Exchange, FinancialAsset
from qnt_utils.fields import *
from qnt_utils.label import QSymbol, Symbol
from qnt_utils.toolset import from_nstimestamp, to_nstimestamp

from .base import QTable, SQLHandler


class SQLHandlerOfBasicInfo(SQLHandler):
    _tabs: MutableMapping[str, Table]
    _exc_fut: Join
    _exc_fut_con: Join

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs, database="quant")

        self._tabs = {}
        self._initialize_tabs()
        self._meta.create_all(self._engine)

        self._exc_fut = self.tabs["futures_info"].join(
            self.tabs["exchange_info"], self.tabs["exchange_info"].c.qcode == self.tabs["futures_info"].c.exchange_qcode
        )
        self._exc_fut_con = (
            self.tabs["qnt004"]
            .join(self.tabs["futures_info"], self.tabs["futures_info"].c.qcode == self.tabs["qnt004"].c.parent_qcode)
            .join(
                self.tabs["exchange_info"],
                self.tabs["exchange_info"].c.qcode == self.tabs["futures_info"].c.exchange_qcode,
            )
        )

    def _initialize_tabs(self):
        self._tabs["exchange_info"] = Table(
            "exchange_info",
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("qcode", UUID(as_uuid=False), nullable=False, default=lambda x: str(uuid.uuid1())),
            Column("exchange", Text, primary_key=True, nullable=False),
            Column("exchange_name", Text),  # 交易所名称
            Column("tz", Text),  # 交易所英文简称
            Column("country", Text),  # 所属国家
            extend_existing=True,
        )
        self._tabs["futures_info"] = Table(
            "futures_info",
            self._meta,
            Column("ctime", BigInteger, default=time.time, nullable=False),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time, nullable=False),
            Column("qcode", UUID(as_uuid=False), nullable=False, default=lambda x: str(uuid.uuid1())),
            Column("futures_code", Text, primary_key=True, nullable=False),  # 品种代码
            Column("futures_name", Text, nullable=False),  # 品种名称
            Column("futures_type", Text, nullable=False),  # 品种类型
            Column("trade_unit_num", Numeric(20, 6), nullable=False),  # 交易单位数量
            Column("trade_unit", Text, nullable=False),  # 交易单位
            Column("price_unit", Text, nullable=False),  # 报价单位
            Column("contract_multiplier", Numeric(20, 6), nullable=False),  # 合约乘数
            Column("exchange_qcode", UUID(as_uuid=False), primary_key=True, nullable=False),  # 交易所qcode
            Column("listing_date", Date, nullable=False),  # 上市日期, 左闭
            extend_existing=True,
        )
        self._tabs["futures_dynamic_info"] = Table(
            "futures_dynamic_info",
            self._meta,
            Column("ctime", BigInteger, default=time.time, nullable=False),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time, nullable=False),
            Column("qcode", UUID(as_uuid=False), primary_key=True, nullable=False),
            Column("minimum_price_change", Numeric(20, 6), nullable=False),  # 最小变动价位
            Column("minimum_margin", Numeric(20, 6)),  # 最低保证金, %
            Column("fee_per_lot", Numeric(20, 6)),  # 交易手续费每手费用, 元
            Column("fee_per_turnover", Numeric(20, 6)),  # 交易手续费成交金额比例
            Column("is_delisted", Boolean, nullable=False),  # 是否退市
            Column("delisting_date", Date),  # 退市日期, 右开
            Column("effective_date", Date, primary_key=True, nullable=False),  # 生效日期，当日有效
            extend_existing=True,
        )
        self._tabs["trade_time"] = Table(
            "trade_time",
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("futures_qcode", UUID(as_uuid=False), nullable=False),  # 期货品种qcode
            Column("begin", Time, nullable=False),
            Column("end", Time, nullable=False),
            Column("is_night", Boolean),
            Column("start_date", Date, nullable=False),
            Column("end_date", Date),
            Column("order_number", SmallInteger),
            Column("is_valid", Boolean),
            Column("change", Text),
            Column("remarks", Text),
            UniqueConstraint("futures_qcode", "order_number", "start_date"),
            extend_existing=True,
        )
        self._tabs["qnt004"] = QTable(
            "qnt004",  # 标的信息
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("qcode", UUID(as_uuid=False), default=lambda x: str(uuid.uuid1())),
            Column("parent_qcode", UUID(as_uuid=False)),  # 期货品种qcode,
            Column("exchange_qcode", UUID(as_uuid=False), nullable=False, primary_key=True),  # 交易所qcode
            Column("q001v_qnt004", String(16), nullable=False, primary_key=True),  # 证券代码
            Column("q002s_qnt004", SmallInteger),  # 证券类型, 同FinancialAsset.value
            Column("q003d_qnt004", Date),  # 上市日期, 左闭，这一天可交易
            Column("q004d_qnt004", Date),  # 退市日期, 右开，这一天不可交易
            Column("q005d_qnt004", Date),  # 最后交易日
            Column("q006i_qnt004", Integer),  # 交割月份, 期货期权才有值
            Column("q007b_qnt004", Boolean),  # 是否退市
            Column("ths_zqid", String(32)),  # 同花顺内部唯一id
            extend_existing=True,
            mapper={
                "q001v_qnt004": "code",
                "q002s_qnt004": "financial_asset",
                "q003d_qnt004": "listing_date",
                "q004d_qnt004": "delisting_date",
                "q005d_qnt004": "last_trade_date",
                "q006i_qnt004": "delivery_month",
                "q007b_qnt004": "is_delisted",
            },
        )
        self._tabs["qnt005"] = Table(
            "qnt005",  # 交易日历
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("timestamp", BigInteger, primary_key=True, nullable=False),  # 时间戳
            Column("q001d_qnt005", Date, nullable=False),  # 日期
            Column("exchange_qcode", UUID(as_uuid=False), primary_key=True, nullable=False),  # 交易所qcode
            Column("q002b_qnt005", Boolean),  # 是否开市
            Column("q003v_qnt005", String(1000)),  # 非周末闭市原因
            Column("q004v_qnt005", String(200)),  # 特殊情况说明
            extend_existing=True,
        )
        self._tabs["qnt006"] = Table(
            "qnt006",  # 指数成分股变动表
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("qcode1_qnt006", UUID(as_uuid=False), primary_key=True, nullable=False),  # 标的qcode
            Column("qcode2_qnt006", UUID(as_uuid=False), primary_key=True, nullable=False),  # 指数qcode
            Column(
                "q001d_qnt006", Date, primary_key=True, nullable=False
            ),  # 纳入日期，左闭右开，当日的主力已经换过来了
            Column("q002v_qnt006", String(20)),  # 纳入原因类型
            Column("q003v_qnt006", String(200)),  # 特殊情况说明
            Column("q004d_qnt006", Date),  # 剔除日期，左闭右开，当日上一个主力合约已经被剔除了
            Column(
                "q005s_qnt006", SmallInteger, primary_key=True, nullable=False
            ),  # 采用哪个厂商的换月标准：0 - 同花顺标准, 1 - 成交量和持仓量都大时，次日切换主力，2 - 快期标准
            extend_existing=True,
        )
        self._tabs["qnt007"] = Table(
            "qnt007",  # 工作日表
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("q001d_qnt007", Date, primary_key=True, nullable=False),  # 日期
            Column("q002t_qnt007", Text, primary_key=True, nullable=False),  # 国家
            Column("q003b_qnt007", Boolean, nullable=False),  # 是否工作日
            Column("q004t_qnt007", Text, nullable=True),  # 节假日公休说明
            Column("q005t_qnt007", Text, nullable=True),  # 其他公休日说明
            extend_existing=True,
        )
        self._tabs["qnt008"] = Table(
            "qnt008",  # 复权因子表
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("timestamp", BigInteger, primary_key=True, nullable=False),  # 复权时点
            Column("qcode_qnt008", UUID(as_uuid=False), primary_key=True, nullable=False),  # 标的qcode
            Column("q001n_qnt008", Numeric(20, 6), nullable=False),  # 复权因子的值
            Column(
                "q002s_qnt008", SmallInteger, nullable=False, primary_key=True
            ),  # 采用哪个厂商的换月标准：0 - 同花顺标准, 1 - 成交量和持仓量都大时，次日切换主力，2 - 快期标准
            Column("q003s_qnt008", SmallInteger, nullable=False, primary_key=True),  # 1 - 等差复权, 2 - 等比复权
            extend_existing=True,
        )

    @property
    def tabs(self):
        return self._tabs

    def to_sql(self, table: str, data: Union[pd.DataFrame, List[Dict]]) -> None:
        tab = self._tabs[table]
        for i in tab.constraints:
            if isinstance(i, PrimaryKeyConstraint):
                uniques = [j.name for j in i.columns]
                break
        else:
            uniques = []
        if isinstance(data, List):
            self.batch_upsert(tab, data, uniques)
        else:
            self.batch_upsert(tab, [i.to_dict() for _, i in data.copy().replace(np.nan, null()).iterrows()], uniques)  # type:ignore

    def get_qcode_series(self) -> dict[str, pd.Series]:
        """获取所有qcode

        Returns:
            dict[str, pd.Series]:
                key - exchange, value - index为exchange
                key - futures, value - index为futures_code+exchange
                key - symbol, value - index为 合约代码+exchange
        """

        res = {}
        with self._engine.connect() as conn:
            sql1 = select(*[v for k, v in self.tabs["exchange_info"].c.items() if k in ["qcode", "exchange"]])
            tmp1 = pd.read_sql(sql=sql1, con=conn)
            sql2 = select(
                self.tabs["exchange_info"].c.exchange,
                self.tabs["futures_info"].c.qcode,
                self.tabs["futures_info"].c.futures_code,
            ).select_from(self._exc_fut)
            tmp2 = pd.read_sql(sql=sql2, con=conn)
            sql3 = select(
                self.tabs["qnt004"].c.q001v_qnt004, self.tabs["qnt004"].c.qcode, self.tabs["exchange_info"].c.exchange
            ).select_from(self._exc_fut_con)
            tmp3 = pd.read_sql(sql=sql3, con=conn)
        res["exchange"] = tmp1.set_index("exchange")["qcode"]
        res["futures"] = tmp2.set_index(["futures_code", "exchange"])["qcode"]
        res["symbol"] = tmp3.set_index(["q001v_qnt004", "exchange"])["qcode"]
        return res

    def get_workday(
        self, country: str, start: str | None, end: str | None, ty: Literal["all", "workday", "nonworkday", "holiday"]
    ) -> List[datetype.AwareDateTime]:
        """获取工作日安排

        Args:
            country (str): 国家
            start (str | None): 开始日期
            end (str | None): 结束日期
            ty (Literal[&quot;all&quot;, &quot;workday&quot;, &quot;nonworkday&quot;, &quot;holiday&quot;]): 类型，工作日、非工作日、节假日、所有

        Returns:
            List[datetype.AwareDateTime]: 日期列表
        """
        sql = (
            select(self.tabs["qnt007"].c.q001d_qnt007, self.tabs["exchange_info"].c.tz)
            .select_from(
                self.tabs["qnt007"].join(
                    self.tabs["exchange_info"],
                    self.tabs["qnt007"].c.q002t_qnt007 == self.tabs["exchange_info"].c.country,
                )
            )
            .where(
                and_(
                    self.tabs["qnt007"].c.q002t_qnt007 == country,
                    self.tabs["qnt007"].c.q001d_qnt007.between(
                        start if start is not None else pd.Timestamp.min, end if end is not None else pd.Timestamp.max
                    ),
                )
            )
            .group_by(self.tabs["qnt007"].c.q001d_qnt007, self.tabs["exchange_info"].c.tz)
        )
        if ty == "workday":
            sql = sql.where(self.tabs["qnt007"].c.q003b_qnt007 == True)
        elif ty == "nonworkday":
            sql = sql.where(self.tabs["qnt007"].c.q003b_qnt007 == False)
        elif ty == "holiday":
            sql = sql.where(or_(self.tabs["qnt007"].c.q004t_qnt007 != "", self._tabs["qnt007"].c.q005t_qnt007 != ""))
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        return res.apply(lambda x: pd.Timestamp(x["q001d_qnt007"], tz=x["tz"]), axis=1).sort_values().tolist()

    def get_trade_days(
        self, exchange: Union[str, Exchange], start_dt: str | None, end_dt: str, count: int = 0
    ) -> List[datetype.AwareDateTime]:
        """获取交易日

        Args:
            exchange (Union[str, Exchange]): 交易所
            start_dt (str | None): 开始日期. 与count同时存在时，忽略count
            end_dt (str): 结束日期
            count (int): 天数. Defaults to 0.

        Returns:
            List[datetype.AwareDateTime]: 交易日
        """
        exchange = exchange if isinstance(exchange, str) else exchange.name
        sql = select(self.tabs["qnt005"].c.timestamp, self.tabs["exchange_info"].c.tz).select_from(
            self.tabs["qnt005"].join(
                self.tabs["exchange_info"], self.tabs["qnt005"].c.exchange_qcode == self.tabs["exchange_info"].c.qcode
            )
        )
        if not start_dt is None:
            sql = sql.where(
                and_(
                    to_nstimestamp(start_dt) <= self.tabs["qnt005"].c.timestamp,
                    self.tabs["qnt005"].c.timestamp <= to_nstimestamp(end_dt),
                    self.tabs["qnt005"].c.q002b_qnt005 == True,
                    self.tabs["exchange_info"].c.exchange == exchange,
                )
            ).order_by(self.tabs["qnt005"].c.timestamp)
        else:
            sql = (
                sql.where(
                    and_(
                        self.tabs["qnt005"].c.timestamp <= to_nstimestamp(end_dt),
                        self.tabs["qnt005"].c.q002b_qnt005 == True,
                        self.tabs["exchange_info"].c.exchange == exchange,
                    )
                )
                .order_by(self.tabs["qnt005"].c.timestamp.desc())
                .limit(count)
            )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql, conn).sort_values("timestamp")
        return (
            res.apply(lambda x: from_nstimestamp(x["timestamp"], tz=x["tz"]), axis=1).tolist() if not res.empty else []
        )

    def get_all_symbols(
        self, exchange: Union[str, Exchange], ty: Union[str, FinancialAsset], start_dt: str, end_dt: str
    ) -> pd.Series:
        """获取指定交易日的代码列表

        Args:
            exchange (Union[str, Exchange]): 交易所
            ty (str): 类型
            start_dt (str): 开始日期
            end_dt (str): 结束日期

        Returns:
            pd.Series: 代码列表
        """
        exchange = exchange if isinstance(exchange, str) else exchange.name
        ty = FinancialAsset[ty].value if isinstance(ty, str) else ty.value  # type: ignore

        jt = (
            self.tabs["qnt005"]
            .join(
                self.tabs["exchange_info"],
                self.tabs["qnt005"].c.exchange_qcode == self.tabs["exchange_info"].c.qcode,
            )
            .join(
                self.tabs["qnt004"],
                and_(
                    self.tabs["qnt005"].c.q001d_qnt005 >= self.tabs["qnt004"].c.q003d_qnt004,
                    or_(
                        self.tabs["qnt005"].c.q001d_qnt005 < self.tabs["qnt004"].c.q004d_qnt004,
                        self.tabs["qnt004"].c.q004d_qnt004.is_(None),
                    ),
                    self.tabs["qnt005"].c.exchange_qcode == self.tabs["qnt004"].c.exchange_qcode,
                ),
            )
        )
        sql = (
            select(
                self.tabs["qnt005"].c.timestamp,
                self.tabs["qnt004"].c.q001v_qnt004,
                self.tabs["exchange_info"].c.exchange,
                self.tabs["exchange_info"].c.tz,
            )
            .select_from(jt)
            .where(
                and_(
                    self.tabs["exchange_info"].c.exchange == exchange,
                    self.tabs["qnt004"].c.q002s_qnt004 == ty,
                    self.tabs["qnt005"].c.timestamp >= to_nstimestamp(start_dt),
                    self.tabs["qnt005"].c.timestamp <= to_nstimestamp(end_dt),
                    self.tabs["qnt005"].c.q002b_qnt005 == True,
                )
            )
            .order_by(
                self.tabs["qnt005"].c.timestamp, self.tabs["qnt004"].c.parent_qcode, self.tabs["qnt004"].c.q001v_qnt004
            )
        )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        if res.empty:
            return pd.Series(dtype=str)
        else:
            res["symbol"] = res["q001v_qnt004"] + "." + res["exchange"]
            res = res.groupby(["timestamp", "tz"]).apply(lambda x: x["symbol"].tolist())
            res.index = res.index.map(lambda x: from_nstimestamp(x[0], x[1]))
            return res

    def get_quotation_tables(self) -> pd.Series:
        sql = select(
            self.tabs["qnt004"].c.q001v_qnt004,
            self.tabs["exchange_info"].c.exchange,
            self.tabs["qnt004"].c.qcode,
        ).select_from(self._exc_fut_con)
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        if res.empty:
            return pd.Series()
        res["symbol"] = res["q001v_qnt004"] + "." + res["exchange"]
        res["qcode"] = res["qcode"].apply(lambda x: "q" + x.replace("-", ""))
        res.set_index("symbol", inplace=True)
        res.index.name = None
        return res["qcode"]

    def get_contract_info(self, symbol: QSymbol | str, date: str) -> Optional[Dict]:
        """查询基本信息

        Args:
            symbol (QSymbol): symbol

        Returns:
            Optional[Dict]: 基本信息
        """
        date = pd.Timestamp(date).normalize()
        ss = Symbol.get_pure_symbol(symbol)
        ex = Symbol.get_exchange(symbol)
        if "8888" in ss or "9999" in ss:
            ss = re.sub("8888|9999", "", ss)
            sql = select(
                self.tabs["futures_info"].c.futures_code,
                self.tabs["futures_info"].c.futures_name,
                self.tabs["futures_info"].c.futures_type,
                self.tabs["futures_info"].c.trade_unit_num,
                self.tabs["futures_info"].c.trade_unit,
                self.tabs["futures_info"].c.price_unit,
                self.tabs["futures_info"].c.contract_multiplier,
                self.tabs["futures_info"].c.listing_date,
                self.tabs["futures_dynamic_info"].c.delisting_date,
                self.tabs["futures_dynamic_info"].c.minimum_price_change,
                self.tabs["futures_dynamic_info"].c.minimum_margin,
                self.tabs["futures_dynamic_info"].c.fee_per_lot,
                self.tabs["futures_dynamic_info"].c.fee_per_turnover,
                self.tabs["futures_dynamic_info"].c.effective_date,
                self.tabs["exchange_info"].c.exchange,
                self.tabs["exchange_info"].c.exchange_name,
                self.tabs["exchange_info"].c.tz,
                self.tabs["exchange_info"].c.country,
                case(
                    (self.tabs["futures_dynamic_info"].c.delisting_date.is_(None), literal(False, type_=Boolean)),
                    (self.tabs["futures_dynamic_info"].c.delisting_date <= date, literal(True, type_=Boolean)),
                    (self.tabs["futures_dynamic_info"].c.delisting_date > date, literal(False, type_=Boolean)),
                ).label("is_delisted"),
            )
            sql = sql.select_from(
                self.tabs["futures_dynamic_info"].join(
                    self._exc_fut, self.tabs["futures_dynamic_info"].c.qcode == self.tabs["futures_info"].c.qcode
                )
            )
            sql = sql.where(
                and_(
                    self.tabs["futures_info"].c.futures_code == ss,
                    self.tabs["exchange_info"].c.exchange == ex.name,
                    self.tabs["futures_dynamic_info"].c.effective_date <= date,
                )
            )
        else:
            sql = select(
                self.tabs["futures_info"].c.futures_code,
                self.tabs["futures_info"].c.futures_name,
                self.tabs["futures_info"].c.futures_type,
                self.tabs["futures_info"].c.trade_unit_num,
                self.tabs["futures_info"].c.trade_unit,
                self.tabs["futures_info"].c.price_unit,
                self.tabs["futures_info"].c.contract_multiplier,
                self.tabs["futures_dynamic_info"].c.minimum_price_change,
                self.tabs["futures_dynamic_info"].c.minimum_margin,
                self.tabs["futures_dynamic_info"].c.fee_per_lot,
                self.tabs["futures_dynamic_info"].c.fee_per_turnover,
                self.tabs["futures_dynamic_info"].c.effective_date,
                self.tabs["qnt004"].c.q001v_qnt004.label("code"),
                self.tabs["qnt004"].c.q002s_qnt004.label("financial_asset"),
                self.tabs["qnt004"].c.q003d_qnt004.label("listing_date"),
                self.tabs["qnt004"].c.q004d_qnt004.label("delisting_date"),
                self.tabs["qnt004"].c.q005d_qnt004.label("last_trade_date"),
                self.tabs["qnt004"].c.q006i_qnt004.label("delivery_month"),
                self.tabs["exchange_info"].c.exchange,
                self.tabs["exchange_info"].c.exchange_name,
                self.tabs["exchange_info"].c.tz,
                self.tabs["exchange_info"].c.country,
                case(
                    (self.tabs["qnt004"].c.q004d_qnt004.is_(None), literal(False, type_=Boolean)),
                    (self.tabs["qnt004"].c.q004d_qnt004 <= date, literal(True, type_=Boolean)),
                    (self.tabs["qnt004"].c.q004d_qnt004 > date, literal(False, type_=Boolean)),
                ).label("is_delisted"),
            )
            sql = sql.select_from(
                self.tabs["futures_dynamic_info"].join(
                    self._exc_fut_con, self.tabs["futures_dynamic_info"].c.qcode == self.tabs["futures_info"].c.qcode
                )
            )
            sql = sql.where(
                and_(
                    self.tabs["qnt004"].c.q001v_qnt004 == ss,
                    self.tabs["exchange_info"].c.exchange == ex.name,
                    self.tabs["futures_dynamic_info"].c.effective_date <= date,
                )
            )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn).sort_values("effective_date", ascending=True)
        if res.empty:
            return None
        res = res.iloc[-1].to_dict()
        res.pop("effective_date")
        join_table2 = self.tabs["trade_time"].join(
            self._exc_fut,
            self.tabs["trade_time"].c.futures_qcode == self.tabs["futures_info"].c.qcode,
        )
        sql1 = (
            select(
                self.tabs["trade_time"].c.begin,
                self.tabs["trade_time"].c.end,
                self.tabs["trade_time"].c.is_night,
                self.tabs["trade_time"].c.is_valid,
                self.tabs["trade_time"].c.end_date,
            )
            .select_from(join_table2)
            .where(
                and_(
                    self.tabs["futures_info"].c.futures_code == res["futures_code"],
                    self.tabs["exchange_info"].c.exchange == res["exchange"],
                )
            )
        )
        res["day"] = []
        res["night"] = []
        with self._engine.connect() as conn:
            res1 = pd.read_sql(sql=sql1, con=conn)
        if not res1["is_valid"].any():
            res1 = res1.loc[res1["end_date"] == res1["end_date"].max(), :]
        else:
            res1 = res1.loc[res1["is_valid"], :]
        for _, i in res1.iterrows():
            if not i["is_night"]:
                res["day"].append((i["begin"], i["end"]))
            else:
                res["night"].append((i["begin"], i["end"]))
        return res

    def get_contract_list(
        self,
        exchange: Optional[str] = None,
        futcodes: List = [],
        start_m: int = 0,
        end_m: int = 999999,
        is_delisted: Optional[bool] = None,
    ) -> List:
        """获取指定品种，交割月份在指定时间范围内的所有合约, 不包含主力和指数合约

        Args:
            exchange (Optional[str], optional): 交易所. Defaults to None.
            futcodes (List, optional): 期货代码. Defaults to [].
            start_m (int, optional): 起始月份. Defaults to 0.
            end_m (int, optional): 结束月份. Defaults to 999999.
            expired (Optional[bool], optional): 合约是否已过期. Defaults to None.

        Returns:
            List: 合约列表
        """
        sql = (
            select(self.tabs["qnt004"].c.q001v_qnt004, self.tabs["exchange_info"].c.exchange)
            .select_from(self._exc_fut_con)
            .where(
                and_(
                    self.tabs["qnt004"].c.q006i_qnt004 >= start_m,
                    self.tabs["qnt004"].c.q006i_qnt004 <= end_m,
                    self.tabs["qnt004"].c.q007b_qnt004.in_([True, False] if is_delisted is None else [is_delisted]),
                )
            )
        )
        if exchange is not None:
            sql = sql.where(self.tabs["exchange_info"].c.exchange == exchange)
            if futcodes:
                sql = sql.where(self.tabs["futures_info"].c.futures_code.in_(futcodes))
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        res = res.sort_values("q001v_qnt004")
        return (res["q001v_qnt004"] + "." + res["exchange"]).tolist()

    def get_all_futures(self, exchange: Optional[Union[str, List]] = None):
        sql = select(
            *(
                [
                    i
                    for i in chain(self._tabs["futures_info"].c, self._tabs["exchange_info"].c)
                    if i.name not in ["ctime", "mtime", "qcode", "exchange_qcode", "tz"]
                ]
            )
        )
        sql = sql.select_from(
            self._tabs["futures_info"].join(
                self._tabs["exchange_info"],
                self._tabs["futures_info"].c.exchange_qcode == self._tabs["exchange_info"].c.qcode,
            )
        )
        if exchange is not None:
            exchange = [exchange] if isinstance(exchange, str) else exchange
            sql = sql.where(self._tabs["exchange_info"].c.exchange.in_(exchange))
        with self._engine.connect() as conn:
            ret = pd.read_sql(sql=sql, con=conn)
        return ret

    # TODO:
    # def get_index_constituents(
    #     self, index: Union[List, str], start_dt: Union[str, pd.Timestamp], end_dt: Union[str, pd.Timestamp]
    # ) -> Dict:
    #     """获取指定日期对应的成分股

    #     Args:
    #         index (Union[List, str]): 指数
    #         start_dt (Union[str, pd.Timestamp]): 开始日期
    #         end_dt (Union[str, pd.Timestamp]): 结束日期

    #     Returns:
    #         Dict: 成分股
    #     """
    #     start_dt_ = to_nstimestamp(start_dt if isinstance(start_dt, str) else start_dt.strftime("%Y-%m-%d"))
    #     end_dt_ = to_nstimestamp(end_dt if isinstance(end_dt, str) else end_dt.strftime("%Y-%m-%d"))
    #     index = [index] if isinstance(index, str) else index
    #     sql = (
    #         select(self._tab_index_constituent, self._tab_exchange_info.c.tz)
    #         .select_from(
    #             self._tab_index_constituent.join(
    #                 self._tab_contract_info, self._tab_index_constituent.c.t_index == self._tab_contract_info.c.symbol
    #             ).join(
    #                 self._tab_exchange_info, self._tab_contract_info.c.exchange == self._tab_exchange_info.c.exchange
    #             )
    #         )
    #         .where(
    #             and_(
    #                 self._tab_index_constituent.c.t_index.in_(index),
    #                 self._tab_index_constituent.c.timestamp >= start_dt_,
    #                 self._tab_index_constituent.c.timestamp <= end_dt_,
    #             )
    #         )
    #         .order_by(self._tab_index_constituent.c.timestamp)
    #     )
    #     with self._engine.connect() as conn:
    #         res = pd.read_sql(sql=sql, con=conn)
    #     res1 = pd.DataFrame()
    #     if res.empty:
    #         return res1
    #     else:
    #         res["timestamp"] = res.apply(lambda x: from_nstimestamp(x["timestamp"], tz=x["tz"]), axis=1)
    #         res.set_index("timestamp", drop=True, append=False, inplace=True)
    #         for v1, v2 in res.groupby("t_index"):
    #             res1[v1] = v2["symbol_list"].apply(lambda x: x.split(","))
    #         res1.index.name = None
    #         return res1

    def get_fut_contracts(
        self, exchange: Union[str, Exchange], futures_code: str, start_dt: str, end_dt: str
    ) -> pd.Series:
        """获取指定品种的合约代码列表，不包含连续和加权指数合约

        Args:
            exchange (Union[str, Exchange]): 交易所
            futures_code (str): 期货品种代码
            start_dt (str): 开始日期
            end_dt (str): 结束日期

        Returns:
            pd.Series: 代码列表
        """
        exchange = exchange if isinstance(exchange, str) else exchange.name

        jt = self.tabs["qnt004"].join(
            self.tabs["exchange_info"], self.tabs["qnt004"].c.exchange_qcode == self.tabs["exchange_info"].c.qcode
        )
        jt = jt.join(self.tabs["futures_info"], self.tabs["qnt004"].c.parent_qcode == self.tabs["futures_info"].c.qcode)
        jt = self.tabs["qnt005"].join(
            jt,
            and_(
                self.tabs["qnt005"].c.q001d_qnt005 >= self.tabs["qnt004"].c.q003d_qnt004,
                or_(
                    self.tabs["qnt005"].c.q001d_qnt005 < self.tabs["qnt004"].c.q004d_qnt004,
                    self.tabs["qnt004"].c.q004d_qnt004.is_(None),
                ),
                self.tabs["qnt005"].c.exchange_qcode == self.tabs["qnt004"].c.exchange_qcode,
            ),
        )
        sql = (
            select(
                self.tabs["qnt005"].c.timestamp,
                self.tabs["qnt004"].c.q001v_qnt004,
                self.tabs["exchange_info"].c.exchange,
                self.tabs["exchange_info"].c.tz,
            )
            .select_from(jt)
            .where(
                and_(
                    self.tabs["exchange_info"].c.exchange == exchange,
                    self.tabs["qnt005"].c.timestamp >= to_nstimestamp(start_dt),
                    self.tabs["qnt005"].c.timestamp <= to_nstimestamp(end_dt),
                    self.tabs["qnt005"].c.q002b_qnt005 == True,
                    self.tabs["qnt004"].c.q002s_qnt004 == 4,
                    self.tabs["futures_info"].c.futures_code == futures_code,
                )
            )
            .order_by(self.tabs["qnt005"].c.timestamp, self.tabs["qnt004"].c.q001v_qnt004)
        )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        if res.empty:
            return pd.Series(dtype=str)
        else:
            res["symbol"] = res["q001v_qnt004"] + "." + res["exchange"]
            res = res.groupby(["timestamp", "tz"]).apply(lambda x: x["symbol"].tolist())
            res.index = res.index.map(lambda x: from_nstimestamp(x[0], x[1]))
            return res

    def get_fut_mcontract(
        self, exchange: Union[str, Exchange], futures_code: str, start_dt: str, end_dt: str, standard: int = 2
    ) -> pd.Series:
        """获取指定日期对应的主力合约

        Args:
            exchange (Union[str, Exchange]): 交易所
            futures_code (str): 期货品种代码
            start_dt (str): 开始日期
            end_dt (str): 结束日期
            standard (int): 换月标准

        Returns:
            pd.Series: 代码列表
        """
        exchange = exchange if isinstance(exchange, str) else exchange.name

        tmp_t = self.tabs["qnt004"].alias("tmp_t")
        jt = self.tabs["qnt006"].join(tmp_t, self.tabs["qnt006"].c.qcode1_qnt006 == tmp_t.c.qcode)
        jt = jt.join(self.tabs["qnt004"], self.tabs["qnt006"].c.qcode2_qnt006 == self.tabs["qnt004"].c.qcode)
        jt = jt.join(self.tabs["exchange_info"], tmp_t.c.exchange_qcode == self.tabs["exchange_info"].c.qcode)
        jt = jt.join(self.tabs["futures_info"], tmp_t.c.parent_qcode == self.tabs["futures_info"].c.qcode)
        jt = self.tabs["qnt005"].join(
            jt,
            and_(
                self.tabs["qnt005"].c.q001d_qnt005 >= self.tabs["qnt006"].c.q001d_qnt006,
                or_(
                    self.tabs["qnt005"].c.q001d_qnt005 < self.tabs["qnt006"].c.q004d_qnt006,
                    self.tabs["qnt006"].c.q004d_qnt006.is_(None),
                ),
                self.tabs["qnt005"].c.exchange_qcode == tmp_t.c.exchange_qcode,
            ),
        )
        sql = (
            select(
                self.tabs["qnt005"].c.timestamp,
                tmp_t.c.q001v_qnt004,
                self.tabs["exchange_info"].c.exchange,
                self.tabs["exchange_info"].c.tz,
            )
            .select_from(jt)
            .where(
                and_(
                    self.tabs["exchange_info"].c.exchange == exchange,
                    self.tabs["qnt005"].c.timestamp >= to_nstimestamp(start_dt),
                    self.tabs["qnt005"].c.timestamp <= to_nstimestamp(end_dt),
                    self.tabs["qnt005"].c.q002b_qnt005 == True,
                    self.tabs["qnt006"].c.q005s_qnt006 == standard,
                    self.tabs["futures_info"].c.futures_code == futures_code,
                )
            )
            .order_by(self.tabs["qnt005"].c.timestamp, tmp_t.c.q001v_qnt004)
        )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        if res.empty:
            return pd.Series(dtype=str)
        else:
            res["symbol"] = res["q001v_qnt004"] + "." + res["exchange"]
            res = res.groupby(["timestamp", "tz"]).apply(lambda x: x["symbol"].iloc[0])
            res.index = res.index.map(lambda x: from_nstimestamp(x[0], x[1]))
            return res

    @lru_cache(maxsize=1024)
    def get_fq_factor(self, symbol: QSymbol, standard: int = 2, method: int = 2) -> pd.Series:
        """获取指定合约的复权因子

        Args:
            symbol (QSymbol): 合约
            standard (int): 换月标准, 0 - 同花顺标准, 1 - 成交量和持仓量都大时，次日切换主力，2 - 快期标准, 目前仅支持快期
            method (int): 复权方法, 1 - 等差复权, 2 - 等比复权，目前仅支持等比

        Returns:
            pd.Series: 复权因子
        """
        ss = Symbol.get_pure_symbol(symbol)
        ex = Symbol.get_exchange(symbol).name

        link_table = self.tabs["qnt008"].join(
            self._exc_fut_con, self.tabs["qnt008"].c.qcode_qnt008 == self.tabs["qnt004"].c.qcode
        )
        sql = (
            select(self.tabs["qnt008"].c.timestamp, self.tabs["qnt008"].c.q001n_qnt008)
            .select_from(link_table)
            .where(
                self.tabs["qnt004"].c.q001v_qnt004 == ss,
                self.tabs["exchange_info"].c.exchange == ex,
                self.tabs["qnt008"].c.q002s_qnt008 == standard,
                self.tabs["qnt008"].c.q003s_qnt008 == method,
            )
        )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        if res.empty:
            return pd.Series(dtype=float)
        else:
            return res.set_index("timestamp")["q001n_qnt008"]

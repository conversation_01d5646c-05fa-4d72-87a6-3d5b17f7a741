from collections.abc import Mapping
from typing import List, Optional, overload

import numpy as np
import pandas as pd
from sqlalchemy import Column, Integer, Table, and_
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import select
from sqlalchemy.sql.expression import null
from sqlalchemy.types import <PERSON><PERSON><PERSON><PERSON>, Boolean, Numeric

from qnt_utils.enums import BasicData
from qnt_utils.fields import BAR_FIELDS
from qnt_utils.label import QSymbol
from qnt_utils.toolset import to_nstimestamp

from ..hdr import HistoricalDataReq
from .base import SQLHandler
from .basic_info import SQLHandlerOfBasicInfo


class SQLHandlerOfQuotation(SQLHandler):
    def __init__(self, drivername="postgresql+psycopg2", username=None, password=None, host="localhost", port=5432):
        super().__init__(drivername, username, password, host, port, "quant")
        s = SQLHandlerOfBasicInfo(drivername, username, password, host, port)
        tmp = s.get_qcode_series()["symbol"].to_dict()
        self._symbol_qcode_mapping: Mapping[str, str] = {".".join(k): v for k, v in tmp.items()}
        del s

        self._tables: Mapping[str, Table] = {
            i: Table(
                j,
                self._meta,
                Column("timestamp", BigInteger, primary_key=True),  # 纳秒时间戳
                Column("qcode", UUID(as_uuid=False), primary_key=True),
                Column("date", Integer),
                Column("time", Integer),
                Column("open", Numeric(20, 6)),
                Column("high", Numeric(20, 6)),
                Column("low", Numeric(20, 6)),
                Column("close", Numeric(20, 6)),
                Column("factor", Numeric(18, 4)),
                Column("volume", BigInteger),
                Column("turnover", Numeric(18, 4)),
                Column("turnover_rate", Numeric(18, 4)),
                Column("is_paused", Boolean),
                Column("uplimit_price", Numeric(20, 6)),
                Column("downlimit_price", Numeric(20, 6)),
                Column("avg_price", Numeric(20, 6)),
                Column("pre_price", Numeric(20, 6)),
                Column("quote_rate", Numeric(18, 4)),
                Column("amp_rate", Numeric(18, 4)),
                Column("is_st", Boolean),
                Column("settle", Numeric(20, 6)),
                Column("pre_settle", Numeric(20, 6)),
                Column("change", Numeric(20, 6)),
                Column("open_interest", BigInteger),
                Column("pos_change", BigInteger),
                Column("pre_open_interest", BigInteger),
                extend_existing=True,
            )
            for i, j in [("DAY", "chn_futures_daily_quotation"), ("MINUTE", "chn_futures_minute_quotation")]
        }
        self._meta.create_all(self._engine)

    @overload
    def read_sql(self, hdr: HistoricalDataReq) -> pd.DataFrame: ...

    @overload
    def read_sql(self, hdr: None, **kwargs) -> pd.DataFrame: ...

    @overload
    def read_sql(self, hdr: list[HistoricalDataReq]) -> dict[str | QSymbol, pd.DataFrame]: ...

    def read_sql(
        self, hdr: Optional[List[HistoricalDataReq] | HistoricalDataReq], **kwargs
    ) -> pd.DataFrame | dict[str | QSymbol, pd.DataFrame]:
        if hdr is None:
            hdrs = [HistoricalDataReq(**kwargs)]
        elif isinstance(hdr, HistoricalDataReq):
            hdrs = [hdr]
        else:
            hdrs = hdr
        res = {}
        with self._engine.connect() as conn:
            for hdr_ in hdrs:
                if hdr_.start_time is None and hdr_.end_time is None:
                    raise ValueError("start_time和end_time不可都为None")
                try:
                    # 基础sql
                    table = self._tables[hdr_.basic_data.name]
                    sql = select(*([table.c[i] for i in hdr_.fields if i != "code"] + [table.c.timestamp])).where(
                        table.c.qcode == self._symbol_qcode_mapping[hdr_.symbol]
                    )

                    # 根据时间筛选
                    if hdr_.start_time is not None and hdr_.end_time is not None:
                        st = to_nstimestamp(hdr_.start_time)
                        et = to_nstimestamp(hdr_.end_time)
                        sql = sql.where(and_(table.c.timestamp >= st, table.c.timestamp <= et))
                    elif hdr_.start_time is None and hdr_.end_time is not None:
                        et = to_nstimestamp(hdr_.end_time)
                        sql = (
                            sql.where(table.c.timestamp <= et).order_by(table.c.timestamp.desc()).limit(hdr_.bar_count)
                        )
                    elif hdr_.start_time is not None and hdr_.end_time is None:
                        st = to_nstimestamp(hdr_.start_time)
                        sql = sql.where(table.c.timestamp >= st).order_by(table.c.timestamp).limit(hdr_.bar_count)
                    df = pd.read_sql(sql=sql, con=conn)

                    # 对数据处理一：补充code
                    if "code" in hdr_.fields:
                        df["code"] = hdr_.symbol

                    # 对数据处理二：设置timestamp为index
                    df.set_index("timestamp", drop=True, append=False, inplace=True)
                    df.index.name = None

                    # 对数据处理三：排序
                    df.sort_index(ascending=True, inplace=True)

                    # 对数据处理四：
                    if hdr_.basic_data in [BasicData.DAY, BasicData.MINUTE]:
                        fields_type = {i: j for i, j in BAR_FIELDS.items() if i in df.columns}
                        df = df.astype(fields_type)
                        # df = df.astype(fields_type, errors="ignore")

                    res[hdr_.symbol] = df
                except Exception:
                    res[hdr_.symbol] = pd.DataFrame(columns=hdr_.fields)
        if hdr is None or isinstance(hdr, HistoricalDataReq):
            return res[hdrs[0].symbol]
        else:
            return res

    def to_sql(self, symbol: QSymbol | str, basic_data_: BasicData, df: pd.DataFrame):
        basic_data = BasicData[basic_data_] if isinstance(basic_data_, str) else basic_data_
        if basic_data == BasicData.SNAPSHOT:
            pass
        elif basic_data == BasicData.ORDER:
            pass
        elif basic_data == BasicData.TRANS:
            pass
        elif basic_data == BasicData.QUEUE:
            pass
        elif basic_data == BasicData.SUPER_STOCK:
            pass
        else:
            if symbol not in self._symbol_qcode_mapping:
                return
            table = self._tables[basic_data.name]
            ins_tmp = []
            df = df.copy().replace(np.nan, null())  # type: ignore
            for row_name, row_value in df.iterrows():
                ins_tmp.append(
                    dict(
                        timestamp=row_name,
                        qcode=self._symbol_qcode_mapping[symbol],
                        **row_value.drop("code", errors="ignore"),
                    )
                )
                if len(ins_tmp) > 30000:
                    self.batch_upsert(table, ins_tmp, ["timestamp", "qcode"])
                    ins_tmp = []
            else:
                self.batch_upsert(table, ins_tmp, ["timestamp", "qcode"])

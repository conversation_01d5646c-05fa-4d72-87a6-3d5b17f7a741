import pathlib
from typing import Dict, List

import pandas as pd
from sqlalchemy import Engine, MetaData, Table, create_engine
from sqlalchemy.dialects.postgresql import insert as pg_insert
from sqlalchemy.dialects.sqlite import insert as sqlite_insert
from sqlalchemy.engine import URL
from sqlalchemy.sql import select, text

from qnt_utils.fields import *


class QTable(Table):
    def __new__(cls, *args, **kw):
        return super().__new__(cls, *args, **{k: v for k, v in kw.items() if not k == "mapper"})

    def __init__(self, *args, **kw) -> None:
        super().__init__(*args, **{k: v for k, v in kw.items() if not k == "mapper"})
        self._mapper = kw.get("mapper", {})

    def __getitem__(self, k):
        if k in self._mapper.keys():
            return self.c[k].label(self._mapper[k])
        else:
            return self.c[k]


class SQLHandler:
    _engine: Engine
    _meta: MetaData

    def __init__(
        self, drivername="postgresql+psycopg2", username=None, password=None, host="localhost", port=5432, database=""
    ):
        assert drivername in ("postgresql+psycopg2", "sqlite")
        if drivername == "sqlite":
            url = URL.create(drivername=drivername, database=str(pathlib.Path(database).absolute()))
        else:
            url = URL.create(
                drivername=drivername,
                username=username,
                password=password,
                host=host,
                port=port,
                database=database,
            )
        self._engine = create_engine(url, pool_size=5, max_overflow=10, pool_pre_ping=True)
        self._meta = MetaData()

    def connect(self):
        return self._engine.connect()

    def execute(self, sql, con=None):
        """直接执行sql"""
        try:
            if con is None:
                with self._engine.connect() as conn:
                    res = conn.execute(text(sql) if isinstance(sql, str) else sql)
            else:
                res = con.execute(text(sql) if isinstance(sql, str) else sql)
            return res.fetchall()
        except Exception as e:
            print(e)
            return None

    def batch_upsert(self, table: Table, ins_tmp: List[Dict], index_elements: List[str]):
        """批量更新数据表，若唯一索引不存在则插入，若唯一索引存在则更新除qcode和ctime以外的所有字段

        Args:
            table (Table): 表
            ins_tmp (List[Dict]): 数据list
            index_elements (List[str]): 唯一索引
        """
        if not ins_tmp:
            return
        with self._engine.connect() as conn:
            if "sqlite" in self._engine.url.drivername:
                for i in range(0, len(ins_tmp), 999):
                    insert_stmt = sqlite_insert(table).values(ins_tmp[i : min(len(ins_tmp), i + 999)])
                    upsert_stmt = insert_stmt.on_conflict_do_update(
                        index_elements=index_elements,
                        set_={i: j for i, j in insert_stmt.excluded.items() if not i in ["qcode", "ctime"]},
                    )
                    conn.execute(upsert_stmt)
                    conn.commit()
            else:
                for i in range(0, len(ins_tmp), 100000):
                    insert_stmt = pg_insert(table).values(ins_tmp[i : min(len(ins_tmp), i + 100000)])
                    upsert_stmt = insert_stmt.on_conflict_do_update(
                        index_elements=index_elements,
                        set_={i: j for i, j in insert_stmt.excluded.items() if not i in ["qcode", "ctime"]},
                    )
                    conn.execute(upsert_stmt)
                    conn.commit()

    def insert(self, table: Table, ins: Dict):
        """插入数据

        Args:
            table (Table): 表
            ins (Dict): 数据
        """
        if "sqlite" in self._engine.url.drivername:
            insert_stmt = sqlite_insert(table).values(**ins)
        else:
            insert_stmt = pg_insert(table).values(**ins)
        with self._engine.connect() as conn:
            conn.execute(insert_stmt)
            conn.commit()

    def upsert(self, table: Table, ins: Dict, index_elements: List[str]):
        """更新数据表，若唯一索引不存在则插入，若唯一索引存在则更新除qcode和ctime以外的所有字段

        Args:
            table (Table): 表
            ins (Dict): 数据
            index_elements (List[str]): 唯一索引
        """
        if "sqlite" in self._engine.url.drivername:
            insert_stmt = sqlite_insert(table).values(**ins)
        else:
            insert_stmt = pg_insert(table).values(**ins)
        upsert_stmt = insert_stmt.on_conflict_do_update(
            index_elements=index_elements,
            set_={i: j for i, j in insert_stmt.excluded.items() if not i in ["qcode", "ctime"]},
        )
        with self._engine.connect() as conn:
            conn.execute(upsert_stmt)
            conn.commit()

    def get_qcode(self, table: Table, key: Dict) -> str:
        """根据key获取匹配的qcode

        Args:
            table (Table): _表
            key (Dict): key

        Raises:
            ValueError: 无匹配key
            ValueError: 有多个匹配key

        Returns:
            str: qcode
        """
        sql = select(table)
        for k, v in key.items():
            sql = sql.where(table.c[k] == v)
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)["qcode"]
        if res.empty:
            raise ValueError("无匹配key")
        elif len(res) > 1:
            raise ValueError("有多个匹配key")
        else:
            return res[0]

    def get_values_by_qcode(self, table: Table, qcodes: List) -> pd.DataFrame:
        """根据qcode获取表记录

        Args:
            table (Table): 表
            qcodes (List): qcode list

        Returns:
            pd.DataFrame: 记录
        """
        sql = select(table).where(table.c.qcode.in_(qcodes))
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        return res

    def modify_values_by_qcode(self, table: Table, qcode: str, values: Dict):
        """根据qcode修改表记录

        Args:
            table (Table): 表
            qcode (str): qcode
            values (Dict): 需要更新的值

        Raises:
            ValueError: ctime, mtime和qcode不可修改
            ValueError: 修改失败, 无匹配qcode
            ValueError: 有多个匹配qcode
        """
        if "ctime" in values.keys() or "qcode" in values.keys() or "mtime" in values.keys():
            raise ValueError("ctime, mtime和qcode不可修改")
        res = self.get_values_by_qcode(table, [qcode])
        if res.empty:
            raise ValueError("修改失败, 无匹配qcode")
        elif len(res) > 1:
            raise ValueError("有多个匹配qcode")
        else:
            res = res.iloc[0, :].to_dict()
        for i in ["ctime", "mtime", "qcode"]:
            res.pop(i)
        res.update(values)
        sql = table.update().where(table.c.qcode == qcode).values(**res)
        with self._engine.connect() as conn:
            conn.execute(sql)
            conn.commit()

from typing import Dict, List, Optional, Union, Any

import numpy as np
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.dialects.postgresql import insert as pg_insert
from sqlalchemy.engine import URL
from sqlalchemy.orm import DeclarativeBase, Session
from sqlalchemy.sql.expression import null


class SQLHandlerOrm:
    def __init__(
        self, drivername="postgresql+psycopg2", username=None, password=None, host="localhost", port=5432, database=None
    ):
        url = URL.create(drivername=drivername, username=username, password=password, host=host, port=port, database=database)
        self._engine = create_engine(url)
        self._tabs = {}

    @property
    def tabs(self):
        return self._tabs

    def upsert(
        self,
        table: Union[str, DeclarativeBase],
        ins_tmp: Union[List[Dict], pd.DataFrame],
        index_elements: Optional[List[str]] = None,
    ):
        table1 = self._tabs[table] if isinstance(table, str) else table
        ins_tmp_0: pd.DataFrame = pd.DataFrame(ins_tmp) if not isinstance(ins_tmp, pd.DataFrame) else ins_tmp
        if ins_tmp_0.empty:
            return
        ins_tmp_0 = ins_tmp_0[[i for i in table1.columns if not i in ["ctime", "mtime"]]].copy().replace(np.nan, null())

        ins_tmp_1: list[dict[str, Any]] = [
            {k1: null() if v1 is None else v1 for k1, v1 in v.to_dict().items()} for _, v in ins_tmp_0.iterrows()
        ]
        index_elements = table1.primary_keys if index_elements is None else index_elements
        with Session(self._engine) as session:
            for i in range(0, len(ins_tmp_1), 100000):
                insert_stmt = pg_insert(table1).values(ins_tmp_1[i : min(len(ins_tmp_1), i + 100000)])
                upsert_stmt = insert_stmt.on_conflict_do_update(
                    index_elements=index_elements,
                    set_={i: j for i, j in insert_stmt.excluded.items() if not i in ["ctime"]},
                )
                try:
                    session.execute(upsert_stmt)
                    session.commit()
                except Exception as e:
                    with open("exception.txt", "w") as f:
                        f.write(str(e))
                        f.write("\n")
                        f.write(str(ins_tmp_1[i : min(len(ins_tmp_1), i + 100000)]))
                    raise

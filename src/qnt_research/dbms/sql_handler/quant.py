import time
from typing import List

import pandas as pd
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, UniqueConstraint, and_, or_
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import select
from sqlalchemy.types import <PERSON>I<PERSON>ger, <PERSON>olean, Date, Integer, Numeric, SmallInteger, String, Text

from qnt_utils.enums import Exchange
from qnt_utils.fields import *

from .base import QTable
from .basic_info import SQLHandlerOfBasicInfo


class SQLHandlerOfQuant(SQLHandlerOfBasicInfo):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _initialize_tabs(self):
        super()._initialize_tabs()
        self._tabs["qnt001"] = QTable(
            "qnt001",  # 注册仓单
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("timestamp", BigInteger, nullable=False),
            Column("futures_qcode", UUID(as_uuid=False)),
            Column("q001v_qnt001", String(100)),  # 仓库名称
            Column("q002d_qnt001", Date),  # 日期
            Column("q003v_qnt001", String(30)),  # 单位
            Column("q004n_qnt001", Numeric(20, 4)),  # 库存量
            Column("q005n_qnt001", Numeric(20, 4)),  # 库存增减量
            Column("q006n_qnt001", Numeric(20, 4)),  # 注册仓单
            Column("q007n_qnt001", Numeric(20, 4)),  # 注册仓单增减量
            Column("q008n_qnt001", Numeric(20, 4)),  # 可用库容量
            Column("q009n_qnt001", Numeric(20, 4)),  # 可用库容量增减量
            UniqueConstraint("futures_qcode", "timestamp", "q001v_qnt001"),
            extend_existing=True,
            mapper={
                "q001v_qnt001": "warehouse_name",
                "q002d_qnt001": "date",
                "q003v_qnt001": "unit",
                "q004n_qnt001": "storage",
                "q005n_qnt001": "storage_change",
                "q006n_qnt001": "receipt",
                "q007n_qnt001": "receipt_change",
                "q008n_qnt001": "available_capacity",
                "q009n_qnt001": "available_capacity_change",
            },
        )
        self._tabs["qnt002"] = QTable(
            "qnt002",  # 持仓成交排名
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("timestamp", BigInteger, nullable=False),
            Column("contract_qcode", UUID(as_uuid=False)),
            Column("q001d_qnt002", Date),  # 交易日
            Column("q002t_qnt002", Text),  # 排名类型, 取值为成交量排名, 持仓排名等
            Column("q003s_qnt002", SmallInteger),  # 排名
            Column("q004t_qnt002", Text),  # 期商
            Column("q005n_qnt002", Numeric(20, 6)),  # 成交量或持仓量
            Column("q006n_qnt002", Numeric(20, 6)),  # 成交量或持仓量较昨日变动
            UniqueConstraint("contract_qcode", "timestamp", "q002t_qnt002", "q003s_qnt002"),
            extend_existing=True,
            mapper={
                "q001d_qnt002": "date",
                "q002t_qnt002": "ranking_type",
                "q003s_qnt002": "ranking",
                "q004t_qnt002": "broker",
                "q005n_qnt002": "amount",
                "q006n_qnt002": "change",
            },
        )
        self._tabs["qnt003"] = QTable(
            "qnt003",  # 现货价格数据
            self._meta,
            Column("ctime", BigInteger, default=time.time),
            Column("mtime", BigInteger, default=time.time, onupdate=time.time),
            Column("timestamp", BigInteger, nullable=False),
            Column("futures_qcode", UUID(as_uuid=False)),
            Column("q001d_qnt003", Date),  # 交易日
            Column("q002i_qnt003", Integer),  # 序号
            Column("q003v_qnt003", String(128)),  # 指标名称
            Column("q004b_qnt003", Boolean),  # 是否为期货合约对应的现货
            Column("q005n_qnt003", Numeric(24, 4)),  # 现货原价格
            Column("q006v_qnt003", String(100)),  # 现货原单位
            Column("q007n_qnt003", Numeric(24, 4)),  # 现货折算比例, 现货原价*折算比例=可与期货对比的现货价格
            Column("q008n_qnt003", Numeric(24, 4)),  # 折算后的现货价格
            Column("q009v_qnt003", String(100)),  # 折算后的现货单位
            Column("q010v_qnt003", String(512)),  # 数据来源
            Column("q011v_qnt003", String(4000)),  # 备注
            UniqueConstraint("futures_qcode", "timestamp", "q002i_qnt003"),
            extend_existing=True,
            mapper={"q001d_qnt003": "date", "q002i_qnt003": "No.", "q003v_qnt003": "name", "q008n_qnt003": "price"},
        )

    def get_ranking(
        self, ranking_type: str, exchange: Exchange, futcodes_or_concodes: List[str], start_dt: str, end_dt: str
    ) -> pd.DataFrame:
        """获取成交/持仓排名

        Args:
            ranking_type (str): 成交量排名, 持买单量排名, 持卖单量排名
            exchange (Exchange): 交易所
            futcodes_or_concodes (List[str]): futures_codes or contract_codes
            start_dt (str): 开始日期
            end_dt (str): 结束日期

        Returns:
            pd.DataFrame: 排名情况
        """
        futcodes = [i for i in futcodes_or_concodes if len(i) <= 4]
        concodes = [i for i in futcodes_or_concodes if len(i) > 4]
        table = self._tabs["qnt002"].join(
            self._exc_fut_con, self._tabs["qnt002"].c.contract_qcode == self._tabs["qnt004"].c.qcode
        )
        sql = select(
            self._tabs["qnt002"]["q001d_qnt002"],
            self._tabs["exchange_info"].c.exchage,
            self._tabs["futures_info"].c.futures_code,
            self._tabs["qnt004"]["q001v_qnt004"],
            self._tabs["qnt002"]["q002t_qnt002"],
            self._tabs["qnt002"]["q003s_qnt002"],
            self._tabs["qnt002"]["q004t_qnt002"],
            self._tabs["qnt002"]["q005n_qnt002"],
            self._tabs["qnt002"]["q006n_qnt002"],
        ).select_from(table)
        sql = sql.where(
            and_(
                self._tabs["qnt002"].c.q001d_qnt002 >= start_dt,
                self._tabs["qnt002"].c.q001d_qnt002 <= end_dt,
                self._tabs["qnt002"].c.q002t_qnt002 == ranking_type,
                self._tabs["exchange_info"].c.exchange == exchange,
                or_(self._tabs["futures_info"].c.futures_code.in_(futcodes), self._tabs["qnt004"].c.q001v_qnt004.in_(concodes)),
            )
        ).order_by(
            self._tabs["qnt002"].c.q001d_qnt002,
            self._tabs["exchange_info"].c.exchage,
            self._tabs["futures_info"].c.futures_code,
            self._tabs["qnt004"]["q001v_qnt004"],
        )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        return res

    def get_spot_price(self, exchange: Exchange, futcodes: List[str], start_dt: str, end_dt: str) -> pd.DataFrame:
        table = self._tabs["qnt003"].join(
            self._exc_fut, self._tabs["qnt003"].c.futures_qcode == self._tabs["futures_info"].c.qcode
        )
        sql = select(
            self._tabs["qnt003"]["q001d_qnt003"],
            self._tabs["exchange_info"].c.exchange,
            self._tabs["futures_info"].c.futures_code,
            self._tabs["qnt003"]["q002i_qnt003"],
            self._tabs["qnt003"]["q003v_qnt003"],
            self._tabs["qnt003"]["q008n_qnt003"],
        ).select_from(table)
        sql = sql.where(
            and_(
                self._tabs["qnt003"].c.q001d_qnt003 >= start_dt,
                self._tabs["qnt003"].c.q001d_qnt003 <= end_dt,
                self._tabs["exchange_info"].c.exchange == exchange,
                self._tabs["futures_info"].c.futures_code.in_(futcodes),
            )
        ).order_by(
            self._tabs["qnt003"].c.q001d_qnt003,
            self._tabs["exchange_info"].c.exchange,
            self._tabs["futures_info"].c.futures_code,
        )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        return res

    def get_warehouse_receipt(self, exchange: Exchange, futcodes: List[str], start_dt: str, end_dt: str) -> pd.DataFrame:
        table = self._tabs["qnt001"].join(
            self._exc_fut, self._tabs["qnt001"].c.futures_qcode == self._tabs["futures_info"].c.qcode
        )
        sql = select(
            self._tabs["qnt001"]["q002d_qnt001"],
            self._tabs["exchange_info"].c.exchange,
            self._tabs["futures_info"].c.futures_code,
            self._tabs["qnt001"]["q001v_qnt001"],
            self._tabs["qnt001"]["q003v_qnt001"],
            self._tabs["qnt001"]["q004n_qnt001"],
            self._tabs["qnt001"]["q005n_qnt001"],
            self._tabs["qnt001"]["q006n_qnt001"],
            self._tabs["qnt001"]["q007n_qnt001"],
            self._tabs["qnt001"]["q008n_qnt001"],
            self._tabs["qnt001"]["q009n_qnt001"],
        ).select_from(table)
        sql = sql.where(
            and_(
                self._tabs["qnt001"].c.q002d_qnt001 >= start_dt,
                self._tabs["qnt001"].c.q002d_qnt001 <= end_dt,
                self._tabs["exchange_info"].c.exchange == exchange,
                self._tabs["futures_info"].c.futures_code.in_(futcodes),
            )
        ).order_by(
            self._tabs["qnt001"].c.q002d_qnt001,
            self._tabs["exchange_info"].c.exchange,
            self._tabs["futures_info"].c.futures_code,
        )
        with self._engine.connect() as conn:
            res = pd.read_sql(sql=sql, con=conn)
        return res

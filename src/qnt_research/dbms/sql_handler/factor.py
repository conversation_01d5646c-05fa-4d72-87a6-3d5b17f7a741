import time

import pandas as pd
from qnt_utils.toolset import to_nstimestamp
from sqlalchemy import and_, select
from sqlalchemy.orm import DeclarativeBase, mapped_column
from sqlalchemy.types import UUI<PERSON>, BigInteger, Double, String

from .basic_info import SQLHandlerOfBasicInfo
from .orm_base import SQLHandlerOrm


class _Base(DeclarativeBase):
    @classmethod
    @property
    def columns(cls):
        """返回所有列名"""
        return cls.__table__.c.keys()

    @classmethod
    @property
    def primary_keys(cls):
        """返回主键"""
        return cls.__table__.primary_key.columns.keys()


class Alpha191(_Base):
    __tablename__ = "alpha191"
    ctime = mapped_column(BigInteger, default=time.time, nullable=False)
    mtime = mapped_column(BigInteger, default=time.time, onupdate=time.time, nullable=False)
    symbol_qcode = mapped_column(UUID(as_uuid=False), nullable=False, primary_key=True)
    timestamp = mapped_column(BigInteger, nullable=False, primary_key=True)
    alpha_001 = mapped_column(Double, nullable=True)
    alpha_002 = mapped_column(Double, nullable=True)
    alpha_003 = mapped_column(Double, nullable=True)
    alpha_004 = mapped_column(Double, nullable=True)
    alpha_005 = mapped_column(Double, nullable=True)
    alpha_006 = mapped_column(Double, nullable=True)
    alpha_007 = mapped_column(Double, nullable=True)
    alpha_008 = mapped_column(Double, nullable=True)
    alpha_009 = mapped_column(Double, nullable=True)
    alpha_010 = mapped_column(Double, nullable=True)
    alpha_011 = mapped_column(Double, nullable=True)
    alpha_012 = mapped_column(Double, nullable=True)
    alpha_013 = mapped_column(Double, nullable=True)
    alpha_014 = mapped_column(Double, nullable=True)
    alpha_015 = mapped_column(Double, nullable=True)
    alpha_016 = mapped_column(Double, nullable=True)
    alpha_017 = mapped_column(Double, nullable=True)
    alpha_018 = mapped_column(Double, nullable=True)
    alpha_019 = mapped_column(Double, nullable=True)
    alpha_020 = mapped_column(Double, nullable=True)
    alpha_021 = mapped_column(Double, nullable=True)
    alpha_022 = mapped_column(Double, nullable=True)
    alpha_023 = mapped_column(Double, nullable=True)
    alpha_024 = mapped_column(Double, nullable=True)
    alpha_025 = mapped_column(Double, nullable=True)
    alpha_026 = mapped_column(Double, nullable=True)
    alpha_027 = mapped_column(Double, nullable=True)
    alpha_028 = mapped_column(Double, nullable=True)
    alpha_029 = mapped_column(Double, nullable=True)
    alpha_030 = mapped_column(Double, nullable=True)
    alpha_031 = mapped_column(Double, nullable=True)
    alpha_032 = mapped_column(Double, nullable=True)
    alpha_033 = mapped_column(Double, nullable=True)
    alpha_034 = mapped_column(Double, nullable=True)
    alpha_035 = mapped_column(Double, nullable=True)
    alpha_036 = mapped_column(Double, nullable=True)
    alpha_037 = mapped_column(Double, nullable=True)
    alpha_038 = mapped_column(Double, nullable=True)
    alpha_039 = mapped_column(Double, nullable=True)
    alpha_040 = mapped_column(Double, nullable=True)
    alpha_041 = mapped_column(Double, nullable=True)
    alpha_042 = mapped_column(Double, nullable=True)
    alpha_043 = mapped_column(Double, nullable=True)
    alpha_044 = mapped_column(Double, nullable=True)
    alpha_045 = mapped_column(Double, nullable=True)
    alpha_046 = mapped_column(Double, nullable=True)
    alpha_047 = mapped_column(Double, nullable=True)
    alpha_048 = mapped_column(Double, nullable=True)
    alpha_049 = mapped_column(Double, nullable=True)
    alpha_050 = mapped_column(Double, nullable=True)
    alpha_051 = mapped_column(Double, nullable=True)
    alpha_052 = mapped_column(Double, nullable=True)
    alpha_053 = mapped_column(Double, nullable=True)
    alpha_054 = mapped_column(Double, nullable=True)
    alpha_055 = mapped_column(Double, nullable=True)
    alpha_056 = mapped_column(Double, nullable=True)
    alpha_057 = mapped_column(Double, nullable=True)
    alpha_058 = mapped_column(Double, nullable=True)
    alpha_059 = mapped_column(Double, nullable=True)
    alpha_060 = mapped_column(Double, nullable=True)
    alpha_061 = mapped_column(Double, nullable=True)
    alpha_062 = mapped_column(Double, nullable=True)
    alpha_063 = mapped_column(Double, nullable=True)
    alpha_064 = mapped_column(Double, nullable=True)
    alpha_065 = mapped_column(Double, nullable=True)
    alpha_066 = mapped_column(Double, nullable=True)
    alpha_067 = mapped_column(Double, nullable=True)
    alpha_068 = mapped_column(Double, nullable=True)
    alpha_069 = mapped_column(Double, nullable=True)
    alpha_070 = mapped_column(Double, nullable=True)
    alpha_071 = mapped_column(Double, nullable=True)
    alpha_072 = mapped_column(Double, nullable=True)
    alpha_073 = mapped_column(Double, nullable=True)
    alpha_074 = mapped_column(Double, nullable=True)
    alpha_075 = mapped_column(Double, nullable=True)
    alpha_076 = mapped_column(Double, nullable=True)
    alpha_077 = mapped_column(Double, nullable=True)
    alpha_078 = mapped_column(Double, nullable=True)
    alpha_079 = mapped_column(Double, nullable=True)
    alpha_080 = mapped_column(Double, nullable=True)
    alpha_081 = mapped_column(Double, nullable=True)
    alpha_082 = mapped_column(Double, nullable=True)
    alpha_083 = mapped_column(Double, nullable=True)
    alpha_084 = mapped_column(Double, nullable=True)
    alpha_085 = mapped_column(Double, nullable=True)
    alpha_086 = mapped_column(Double, nullable=True)
    alpha_087 = mapped_column(Double, nullable=True)
    alpha_088 = mapped_column(Double, nullable=True)
    alpha_089 = mapped_column(Double, nullable=True)
    alpha_090 = mapped_column(Double, nullable=True)
    alpha_091 = mapped_column(Double, nullable=True)
    alpha_092 = mapped_column(Double, nullable=True)
    alpha_093 = mapped_column(Double, nullable=True)
    alpha_094 = mapped_column(Double, nullable=True)
    alpha_095 = mapped_column(Double, nullable=True)
    alpha_096 = mapped_column(Double, nullable=True)
    alpha_097 = mapped_column(Double, nullable=True)
    alpha_098 = mapped_column(Double, nullable=True)
    alpha_099 = mapped_column(Double, nullable=True)
    alpha_100 = mapped_column(Double, nullable=True)
    alpha_101 = mapped_column(Double, nullable=True)
    alpha_102 = mapped_column(Double, nullable=True)
    alpha_103 = mapped_column(Double, nullable=True)
    alpha_104 = mapped_column(Double, nullable=True)
    alpha_105 = mapped_column(Double, nullable=True)
    alpha_106 = mapped_column(Double, nullable=True)
    alpha_107 = mapped_column(Double, nullable=True)
    alpha_108 = mapped_column(Double, nullable=True)
    alpha_109 = mapped_column(Double, nullable=True)
    alpha_110 = mapped_column(Double, nullable=True)
    alpha_111 = mapped_column(Double, nullable=True)
    alpha_112 = mapped_column(Double, nullable=True)
    alpha_113 = mapped_column(Double, nullable=True)
    alpha_114 = mapped_column(Double, nullable=True)
    alpha_115 = mapped_column(Double, nullable=True)
    alpha_116 = mapped_column(Double, nullable=True)
    alpha_117 = mapped_column(Double, nullable=True)
    alpha_118 = mapped_column(Double, nullable=True)
    alpha_119 = mapped_column(Double, nullable=True)
    alpha_120 = mapped_column(Double, nullable=True)
    alpha_121 = mapped_column(Double, nullable=True)
    alpha_122 = mapped_column(Double, nullable=True)
    alpha_123 = mapped_column(Double, nullable=True)
    alpha_124 = mapped_column(Double, nullable=True)
    alpha_125 = mapped_column(Double, nullable=True)
    alpha_126 = mapped_column(Double, nullable=True)
    alpha_127 = mapped_column(Double, nullable=True)
    alpha_128 = mapped_column(Double, nullable=True)
    alpha_129 = mapped_column(Double, nullable=True)
    alpha_130 = mapped_column(Double, nullable=True)
    alpha_131 = mapped_column(Double, nullable=True)
    alpha_132 = mapped_column(Double, nullable=True)
    alpha_133 = mapped_column(Double, nullable=True)
    alpha_134 = mapped_column(Double, nullable=True)
    alpha_135 = mapped_column(Double, nullable=True)
    alpha_136 = mapped_column(Double, nullable=True)
    alpha_137 = mapped_column(Double, nullable=True)
    alpha_138 = mapped_column(Double, nullable=True)
    alpha_139 = mapped_column(Double, nullable=True)
    alpha_140 = mapped_column(Double, nullable=True)
    alpha_141 = mapped_column(Double, nullable=True)
    alpha_142 = mapped_column(Double, nullable=True)
    alpha_143 = mapped_column(Double, nullable=True)
    alpha_144 = mapped_column(Double, nullable=True)
    alpha_145 = mapped_column(Double, nullable=True)
    alpha_146 = mapped_column(Double, nullable=True)
    alpha_147 = mapped_column(Double, nullable=True)
    alpha_148 = mapped_column(Double, nullable=True)
    alpha_149 = mapped_column(Double, nullable=True)
    alpha_150 = mapped_column(Double, nullable=True)
    alpha_151 = mapped_column(Double, nullable=True)
    alpha_152 = mapped_column(Double, nullable=True)
    alpha_153 = mapped_column(Double, nullable=True)
    alpha_154 = mapped_column(Double, nullable=True)
    alpha_155 = mapped_column(Double, nullable=True)
    alpha_156 = mapped_column(Double, nullable=True)
    alpha_157 = mapped_column(Double, nullable=True)
    alpha_158 = mapped_column(Double, nullable=True)
    alpha_159 = mapped_column(Double, nullable=True)
    alpha_160 = mapped_column(Double, nullable=True)
    alpha_161 = mapped_column(Double, nullable=True)
    alpha_162 = mapped_column(Double, nullable=True)
    alpha_163 = mapped_column(Double, nullable=True)
    alpha_164 = mapped_column(Double, nullable=True)
    alpha_165 = mapped_column(Double, nullable=True)
    alpha_166 = mapped_column(Double, nullable=True)
    alpha_167 = mapped_column(Double, nullable=True)
    alpha_168 = mapped_column(Double, nullable=True)
    alpha_169 = mapped_column(Double, nullable=True)
    alpha_170 = mapped_column(Double, nullable=True)
    alpha_171 = mapped_column(Double, nullable=True)
    alpha_172 = mapped_column(Double, nullable=True)
    alpha_173 = mapped_column(Double, nullable=True)
    alpha_174 = mapped_column(Double, nullable=True)
    alpha_175 = mapped_column(Double, nullable=True)
    alpha_176 = mapped_column(Double, nullable=True)
    alpha_177 = mapped_column(Double, nullable=True)
    alpha_178 = mapped_column(Double, nullable=True)
    alpha_179 = mapped_column(Double, nullable=True)
    alpha_180 = mapped_column(Double, nullable=True)
    alpha_181 = mapped_column(Double, nullable=True)
    alpha_182 = mapped_column(Double, nullable=True)
    alpha_183 = mapped_column(Double, nullable=True)
    alpha_184 = mapped_column(Double, nullable=True)
    alpha_185 = mapped_column(Double, nullable=True)
    alpha_186 = mapped_column(Double, nullable=True)
    alpha_187 = mapped_column(Double, nullable=True)
    alpha_188 = mapped_column(Double, nullable=True)
    alpha_189 = mapped_column(Double, nullable=True)
    alpha_190 = mapped_column(Double, nullable=True)
    alpha_191 = mapped_column(Double, nullable=True)


class FT001(_Base):
    __tablename__ = "ft001"
    ctime = mapped_column(BigInteger, default=time.time, nullable=False)
    mtime = mapped_column(BigInteger, default=time.time, onupdate=time.time, nullable=False)
    symbol_qcode = mapped_column(UUID(as_uuid=False), nullable=False, primary_key=True)
    timestamp = mapped_column(BigInteger, nullable=False, primary_key=True)
    name = mapped_column(String(16), nullable=False, primary_key=True)
    value = mapped_column(Double, nullable=True)


class SQLHandlerOrmOfFactor(SQLHandlerOrm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs, database="factor")
        _Base.metadata.create_all(self._engine)
        self._tabs["alpha191"] = Alpha191
        self._tabs["ft001"] = FT001

        sql_handler_of_basic_info = SQLHandlerOfBasicInfo(*args, **kwargs)
        symbol_qcodes = sql_handler_of_basic_info.get_qcode_series()["symbol"]
        self._symbol_qcodes = dict(zip(symbol_qcodes.values, list(map(lambda x: ".".join(x), symbol_qcodes.index))))

    def get_factors(
        self, symbol_list: str | list[str], table: str, factors: str | list[str], start_date: str, end_date: str
    ) -> dict:
        st = to_nstimestamp(f"{start_date} 0000")
        et = to_nstimestamp(f"{end_date} 235959")
        if table == "alpha191":
            if factors == "all":
                columns = [i for i in self._tabs[table].__table__.c.keys() if i not in ["ctime", "mtime"]]
                sql = select(*[self._tabs[table].__table__.c[i] for i in columns]).where(
                    self._tabs[table].timestamp.between(st, et)
                )
            else:
                factors_ = [factors] if isinstance(factors, str) else factors
                columns = ["timestamp", "symbol_qcode"] + factors_
                sql = select(*[self._tabs[table].__table__.c[i] for i in columns]).where(
                    self._tabs[table].timestamp.between(st, et)
                )

            # 数据读取
            dtype = dict([(i, "float") for i in columns if i not in ["ctime", "mtime", "timestamp", "symbol_qcode"]])
            dtype.update({"timestamp": "int", "symbol_qcode": "str"})
            with self._engine.begin() as conn:
                res = pd.read_sql(sql, con=conn, dtype=dtype).sort_index()

            # 过滤symbol
            res["symbol"] = res["symbol_qcode"].apply(lambda x: self._symbol_qcodes[x])
            if not symbol_list == "all":
                symbol_list_ = [symbol_list] if isinstance(symbol_list, str) else symbol_list
                res = res.loc[res["symbol"].isin(symbol_list_)]

            # 转换
            res = res.drop("symbol_qcode", axis=1)
            res = res.set_index(["timestamp", "symbol"]).unstack("symbol")
            res.index.name = None
            res.columns.names = None, None
            return {i: res[i] for i in res.columns.levels[0]}
        else:
            columns = ["symbol_qcode", "timestamp", "name", "value"]
            if factors == "all":
                sql = select(*[self._tabs[table].__table__.c[i] for i in columns]).where(
                    self._tabs[table].timestamp.between(st, et)
                )
            else:
                factors_ = [factors] if isinstance(factors, str) else factors
                sql = select(*[self._tabs[table].__table__.c[i] for i in columns]).where(
                    and_(self._tabs[table].timestamp.between(st, et), self._tabs[table].in_(columns))
                )

            # 数据读取
            dtype = {"timestamp": "int", "symbol_qcode": "str", "name": "str", "value": "float"}
            with self._engine.begin() as conn:
                res = pd.read_sql(sql, con=conn, dtype=dtype).sort_index()

            # 过滤symbol
            res["symbol"] = res["symbol_qcode"].apply(lambda x: self._symbol_qcodes[x])
            if not symbol_list == "all":
                symbol_list_ = [symbol_list] if isinstance(symbol_list, str) else symbol_list
                res = res.loc[res["symbol"].isin(symbol_list_)]

            # 转换
            res = res.drop("symbol_qcode", axis=1)
            res = res.set_index(["timestamp", "name", "symbol"])["value"].unstack(["name", "symbol"])
            res.index.name = None
            res.columns.names = None, None
            return {i: res[i] for i in res.columns.levels[0]}

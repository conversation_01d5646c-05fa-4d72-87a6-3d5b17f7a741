import json
import threading
import time
from functools import partial
from http import HTTPStatus
from http.server import BaseHTTPRequestHandler, HTTPServer

import pika

from qnt_utils.config import get_config
from qnt_utils.enums import CommunicatorMessageType

from ..observer_pattern import AbstractEventDriveEngine
from .base import CommunicatorBase


class InstructorForEDA(CommunicatorBase):
    """用于接收指令，并驱动事件引擎"""

    def __init__(self) -> None:
        self._event_engine: AbstractEventDriveEngine

        super().__init__()

    def _run(self):
        pass

    def register_event_engine(self, event_engine):
        self._event_engine = event_engine
        threading.Thread(target=self._run, daemon=True).start()


class _HttpRequestHandler(BaseHTTPRequestHandler):
    def __init__(self, request, client_address, server, fnc) -> None:
        self._fnc = fnc
        super().__init__(request, client_address, server)

    def do_GET(self):
        self._fnc(json.loads(self.headers["data"]))
        self.send_response(HTTPStatus.OK)
        self.end_headers()


class HTTPInstructorForEDA(InstructorForEDA):
    """用于接收指令并驱动事件引擎，通过http通信"""

    def __init__(self, port: int = 8000):
        """初始化http服务用于接收指令

        Args:
            port (int, optional): http服务监听端口. Defaults to 8000.
        """
        super().__init__()
        self._port = port

    def _run(self):
        httpd = HTTPServer(("0.0.0.0", self._port), partial(_HttpRequestHandler, fnc=self._event_engine.on_instruction))
        httpd.serve_forever()


class RabbitMQInstructorForEDA(InstructorForEDA):
    """用于接收指令并驱动事件引擎，通过rabbitmq通信"""

    def __init__(self, host: str = "", port: int = 0):
        """连接rabbitmq，接收指令

        Args:
            host (str, optional): rabbitmq连接地址. Defaults to "".
            port (int, optional): rabbitmq连接端口. Defaults to 0.
        """
        super().__init__()
        self._host = host or (config := get_config())["rabbitmq"]["host"]
        self._port = port or config["rabbitmq"]["port"]

    def _run(self):
        def callback(ch, method, properties, body):
            msg = json.loads(body)
            msg["type"] = CommunicatorMessageType(msg["type"])
            self._event_engine.on_instruction(msg)

        while True:
            try:
                connection = pika.BlockingConnection(pika.ConnectionParameters(host=self._host, port=self._port))
                channel = connection.channel()

                channel.exchange_declare(exchange="INSTRUCTION-DIRECT", exchange_type="direct")
                result = channel.queue_declare(queue="", exclusive=True, auto_delete=True)
                queue_name = result.method.queue
                channel.queue_bind(exchange="INSTRUCTION-DIRECT", queue=queue_name, routing_key=self.communicator_id)
                channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=True)

                channel.exchange_declare(exchange="INSTRUCTION-FANOUT", exchange_type="fanout")
                result = channel.queue_declare(queue="", exclusive=True, auto_delete=True)
                queue_name = result.method.queue
                channel.queue_bind(exchange="INSTRUCTION-FANOUT", queue=queue_name)
                channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=True)

                channel.start_consuming()
            except:
                time.sleep(5)
                continue


class ExternalRabbitMQInstructor:
    """用于发送指令，通过rabbitmq进行通信"""

    def __init__(self, host: str = "", port: int = 0):
        """连接rabbitmq，发送指令

        Args:
            host (str, optional): rabbitmq连接地址. Defaults to "".
            port (int, optional): rabbitmq连接端口. Defaults to 0.
        """
        self._host = host or (config := get_config())["rabbitmq"]["host"]
        self._port = port or config["rabbitmq"]["port"]
        self.connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=self._host,
                port=self._port,
                connection_attempts=3,
                retry_delay=5,
                heartbeat=0,
            )
        )
        self.channel = self.connection.channel()
        self.channel.exchange_declare(exchange="INSTRUCTION-DIRECT", exchange_type="direct")
        self.channel.exchange_declare(exchange="INSTRUCTION-FANOUT", exchange_type="fanout")

    def publish(self, data, type_: CommunicatorMessageType, strategy_id: int, routing_key="*"):
        msg = {"type": type_.value, "timestamp": int(time.time() * 1e9), "strategy": strategy_id, "data": data}
        if routing_key == "*":
            self.channel.basic_publish(exchange="INSTRUCTION-FANOUT", routing_key="", body=json.dumps(msg))
        else:
            self.channel.basic_publish(exchange="INSTRUCTION-DIRECT", routing_key=routing_key, body=json.dumps(msg))

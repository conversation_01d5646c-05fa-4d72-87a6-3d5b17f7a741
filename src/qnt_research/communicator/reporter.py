import json
import time

import pika

from qnt_utils.config import get_config
from qnt_utils.enums import CommunicatorMessageType

from ..observer_pattern import AbstractEventDriveEngine
from .base import CommunicatorBase


class RabbitMQReporter(CommunicatorBase):
    def __init__(self, host: str = "", port: int = 0):
        """初始化

        Args:
            host (str, optional): 连接地址. Defaults to "".
            port (int, optional): 连接端口. Defaults to 0.
        """
        super().__init__()

        self._host = host or (config := get_config())["rabbitmq"]["host"]
        self._port = port or config["rabbitmq"]["port"]
        self._event_engine: AbstractEventDriveEngine
        self._connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=self._host,
                port=self._port,
                connection_attempts=3,
                retry_delay=5,
                heartbeat=0,
            )
        )
        self._channel = self._connection.channel()
        self._channel.exchange_declare(exchange="REPORT", exchange_type="fanout")

    def publish(self, data: dict, type_: CommunicatorMessageType) -> None:
        """发布消息，消息内容如{ "type": CommunicatorMessageType.value, "timestamp": ***************, "from": communicator_id, "hostname": "hostname", "data": {...}, }

        Args:
            data (dict): 消息内容，放在data字段中
            type_ (CommunicatorMessageType): 消息类型
        """
        msg = {
            "type": type_.value,
            "timestamp": time.time_ns(),
            "from": self.communicator_id,
            "hostname": self.hostname,
            "data": data,
        }
        self._channel.basic_publish(exchange="REPORT", routing_key="", body=json.dumps(msg))

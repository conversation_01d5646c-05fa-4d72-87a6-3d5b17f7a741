import json
import socket

from qnt_utils.config import get_config
from qnt_utils.zk_handler import ZKHandler


class CommunicatorBase:
    zk = None
    communicator_id = ""
    hostname = socket.gethostname()

    def __init__(self):
        if CommunicatorBase.zk is None:
            CommunicatorBase.zk = ZKHandler(
                "/communicators", f"{get_config()['zookeeper']['host']}:{get_config()['zookeeper']['port']}"
            )
            CommunicatorBase.communicator_id = CommunicatorBase.zk.client_id

    def write(self, **kwargs):
        kwargs["communicator_id"] = self.communicator_id
        kwargs["hostname"] = self.hostname
        self.zk.write(json.dumps(kwargs))

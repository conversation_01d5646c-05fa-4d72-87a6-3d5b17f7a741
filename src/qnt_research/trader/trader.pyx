from typing import Dict, Optional, Tuple, Union

import numpy as np
import pandas as pd
import datetime as dt
from matplotlib import pyplot as plt
from qnt_utils.ctoolset import generate_datetime
from qnt_utils.enums import BasicData, Direction, OffSet, OrderStatus, OrderType, PositionType, PriceType, StatusCode
from qnt_utils.label import QSymbol
from sklearn.linear_model import LinearRegression

from ..entity import DCPortfolio, DCPosition
from ..observer_pattern import DataObserver, OrderObserver, OrderSubject
from ..trade_api import TradeAPI
from ..virtual_exchange import VirtualExchange
from .order import COrder
from .utils import calc_cash_change

from .cash cimport Cash
from .position cimport Position
from .order cimport MOrder

cdef class CEquityManager:
    """管理账户里的权益部分, 比如持仓、现金"""
    cdef public Cash cash
    cdef public object positions
    cdef object _last_price, _last_morders
    cdef object _date_time, _time_series, _equity_series, _cash_series
    cdef object _positions_snapshot, _portfolio_snapshot

    def __init__(self, init_money: float):
        """初始化权益管理对象

        Args:
            init_money (float): 初始资金
        """
        self.cash = Cash(init_money)
        self.positions: Dict[QSymbol, Dict[PositionType, Position]] = {}

        # on_order 更新这些
        self._last_price: Dict[QSymbol, float] = {}     # 保存最新价格
        self._last_morders: Dict[str, Dict] = {}        # 保存母单

        # on_data 更新这些
        self._date_time: Optional[dt.datetime] = None
        self._time_series = []
        self._equity_series: list[float] = []
        self._cash_series: list[float] = []

        # snapshot, positions和portfolio的缓存, 用于策略中查询加速
        self._positions_snapshot: dict[QSymbol, dict[str, DCPosition]] = {}
        self._portfolio_snapshot: DCPortfolio = DCPortfolio(init_money, 0, init_money, init_money, 0, 0, 0, 0, 0, 0)

    def register_order(self, order):
        """关联MOrder, 未关联的后面收到状态不处理"""
        self._c_register_order(order)
    
    cdef void _c_register_order(self, object order):
        if isinstance(order, MOrder):
            if not order.get_order_id() in self._last_morders.keys():
                self._last_morders[order.get_order_id()] = {}
                self.on_order(order.get_info())

    cdef void _update(self, object msg, object kwargs):
        """更新账户状态"""
        if kwargs["basic_data"] == BasicData.MINUTE:
            self._last_price[msg["code"]] = msg["close"]
        elif kwargs["basic_data"] == BasicData.SNAPSHOT:
            self._last_price[msg["code"]] = msg["new_price"]

        if msg["code"] in self.positions.keys():
            for key, value in self.positions[msg["code"]].items():
                self.cash.cashflow(value.update_last_price(self._last_price.get(msg["code"], np.nan)))

    cdef void _capital_clear(self):
        """保存账户截面数据"""
        tmp = self._portfolio_snapshot
        if len(self._time_series) > 0 and self._date_time == self._time_series[-1]:
            self._equity_series[-1] = tmp.equity
            self._cash_series[-1] = tmp.available_cash + tmp.frozen_cash
        else:
            self._time_series.append(self._date_time)
            self._equity_series.append(tmp.equity)
            self._cash_series.append(tmp.available_cash + tmp.frozen_cash)

    def on_data(self, msg, **kwargs):
        if msg is None:
            self._capital_clear()
            return

        dt = generate_datetime(msg["date"], msg["time"])
        if self._date_time is None:
            self._date_time = dt
            self._update(msg, kwargs)
        elif self._date_time == dt:
            self._update(msg, kwargs)
        elif self._date_time < dt:
            self._capital_clear()
            self._date_time = dt
            self._update(msg, kwargs)
        self._get_portfolio()
        self._get_positions()

    def on_order(self, msg, **kwargs):
        """只有接收到MOrder才执行"""
        if msg.get("order_type", None) != OrderType.MORDER:
            return
        if msg["order_id"] not in self._last_morders.keys():
            return

        for _, v in self.positions[msg["symbol"]].items():
            self.cash.cashflow(v.update_last_price(msg["trade_price"]))

        self.cash.handle_order(msg, self._last_morders.get(msg["order_id"], None))
        
        if (msg["direction"].is_buy() and msg["offset"].is_open()) or (
            msg["direction"].is_sell() and msg["offset"].is_close()
        ):
            # 当买开或卖平时，处理多仓
            self.positions[msg["symbol"]][PositionType.LONG].handle_order(msg, self._last_morders.get(msg["order_id"], None))
        else:
            # 当卖开或买平时，处理空仓
            self.positions[msg["symbol"]][PositionType.SHORT].handle_order(msg, self._last_morders.get(msg["order_id"], None))
        self._last_morders[msg["order_id"]].update(**msg)

        for _, v in self.positions[msg["symbol"]].items():
            self.cash.cashflow(v.update_last_price(self._last_price.get(msg["symbol"], np.nan)))
        self._get_portfolio()
        self._get_positions()


    def get_positions(self):
        return (StatusCode.SUCCESS, self._positions_snapshot)

    def get_portfolio(self):
        return (StatusCode.SUCCESS, self._portfolio_snapshot)

    cdef void _get_positions(self):
        self._positions_snapshot = {
            k1: {
                "LONG": v1[PositionType.LONG].info,
                "SHORT": v1[PositionType.SHORT].info,
            }
            for k1, v1 in self.positions.items()
        }

    cdef void _get_portfolio(self):
        cdef double t1, t2, t3, t4

        t1 = self.cash.get_equity()
        t2 = self.cash.get_frozen_cash()
        t3 = self.cash.get_available_cash()
        t4 = sum([i4.equity for i1, i2 in self.positions.items() for i3, i4 in i2.items()])

        self._portfolio_snapshot.asset = t1 + t4
        self._portfolio_snapshot.debt = 0
        self._portfolio_snapshot.equity = t1 + t4
        self._portfolio_snapshot.available_cash = t3
        self._portfolio_snapshot.frozen_cash = t2
        self._portfolio_snapshot.market_value = sum([i4.market_value for i1, i2 in self.positions.items() for i3, i4 in i2.items()])
        self._portfolio_snapshot.margin = sum([i4.margin for i1, i2 in self.positions.items() for i3, i4 in i2.items()])
        self._portfolio_snapshot.available_credit = 0
        self._portfolio_snapshot.interest_of_financed_funds = 0
        self._portfolio_snapshot.interest_of_financed_bonds = 0

    def get_init_money(self):
        return self.cash.get_init_money()

    def get_info(self):
        # 未发生交易的情况
        if len(self._last_morders) == 0:
            print("\n时间区间内无交易!\n")
            return None

        # 先把时间list和权益list转成数组，后面转成Series可以更快一些
        llll = len(self._time_series)
        arr_time = np.empty((llll,), dtype=dt.datetime)
        arr_equity = np.empty((llll,), dtype=np.float64)
        arr_cash = np.empty((llll,), dtype=np.float64)
        arr_x = np.empty((llll,), dtype=np.int64)
        cdef i
        for i in range(llll):
            arr_time[i] = self._time_series[i]
            arr_equity[i] = self._equity_series[i]
            arr_cash[i] = self._cash_series[i]
            arr_x[i] = i

        result1 = pd.concat(
            [
                pd.Series(arr_equity).rename("equity"), 
                pd.Series(arr_cash).rename("cash"), 
            ], 
            axis=1
        )
        result1.index = arr_time
        result1["date"] = list(map(lambda x: x.strftime("%Y-%m-%d"), arr_time))

        tmp_trade_log = pd.DataFrame(self._last_morders.copy()).T
        tmp_trade_log = tmp_trade_log.drop(["order_id", "api_order_id", "order_type"], axis=1).reset_index(drop=True)
        new_order = ["c_dt", "m_dt"] + list(set(tmp_trade_log.columns).difference(["c_dt", "m_dt"]))
        tmp_trade_log = tmp_trade_log.reindex(columns=new_order)
        return {
            "init_equity": self.cash.get_init_money(), 
            "equity_series": result1, 
            "trade_log": tmp_trade_log,
        }

class EquityManager(DataObserver, OrderObserver):
    def __init__(self, init_money: float):
        self._cem = CEquityManager(init_money)
    
    def register_order(self, order):
        self._cem.register_order(order)

    def on_data(self, msg, **kwargs):
        self._cem.on_data(msg, **kwargs)

    def on_order(self, msg, **kwargs):
        self._cem.on_order(msg, **kwargs)

    @property
    def positions(self):
        return self._cem.get_positions()

    @property
    def portfolio(self):
        return self._cem.get_portfolio()

    @property
    def init_money(self):
        return self._cem.get_init_money()

    def get_info(self):
        return self._cem.get_info()

class OrderManager(OrderSubject, OrderObserver):
    """订单管理对象，用于处理下单、订单查询、订单状态更新等"""

    def __init__(self, trade_api_handler: Union[VirtualExchange, TradeAPI]):
        """初始化订单管理对象

        Args:
            trade_api_handler (Union[VirtualExchange, TradeAPI]): 交易接口，可以传入VirtualExchange或者TradeAPI
        """
        OrderSubject.__init__(self)

        self._trade_api_handler: Union[VirtualExchange, TradeAPI] = trade_api_handler
        """交易接口对象"""
        self._morders: Dict[str, MOrder] = {}
        """key为order_id, value为MOrder对象"""
        self._corders: Dict[str, COrder] = {}
        """key为子单id, value为子单状态dict"""

    def order(self, symbol, direction, offset, amount, price, price_type: PriceType = PriceType.LIMIT) -> Tuple[StatusCode, Optional[str]]:
        """
        1. 创建母单, 同步返回
        2. 母单创建成功之后, 不一定已经实际下单了. 实盘的时候子单是异步下单, 回测的时候是同步下单
        3. 需要等委托状态回来, 才能确定子单是否下单成功了
        """
        if amount == 0:
            return StatusCode.AMOUNT_IS_ZERO, None
        morder = MOrder(self, symbol, direction, offset, amount, price, price_type)
        self.register_order(morder)
        # TODO: morder的执行在实盘时可以是异步的, 在回测里的同步的
        morder.execute()
        return StatusCode.SUCCESS, morder.get_order_id()

    def cancel_order(self, order_id) -> Tuple[StatusCode, bool]:
        if not self._morders[order_id].is_dead():
            self._morders[order_id].cancel()
            return StatusCode.SUCCESS, True
        else:
            return StatusCode.CANCEL_ORDER_FAILED, False

    def register_order(self, order):
        """关联MOrder和COrder"""
        if isinstance(order, MOrder):
            if not order.get_order_id() in self._morders.keys():
                self._morders[order.get_order_id()] = order
            else:
                raise RuntimeError("MOrder.order_id重复")
        else:
            if not order["api_order_id"] in self._corders.keys():
                self._corders[order["api_order_id"]] = order
                self._morders[order["order_id"]].update()
                self.on_order(self._morders[order["order_id"]].get_info())
            else:
                raise RuntimeError("COrder.api_order_id重复")

    def on_order(self, msg, **kwargs):
        """只有接收到COrder才执行, 更新完MOrder后notify"""
        if msg.get("order_type", None) == OrderType.MORDER:
            return
        if not msg["api_order_id"] in self._corders.keys():
            return
        corder = self._corders[msg["api_order_id"]]
        if not corder["status"] in [
            OrderStatus.ALIVE,
            OrderStatus.ORDER_TBC,
            OrderStatus.CANCEL_TBC,
        ]:
            return

        corder.update(**msg)
        self._morders[corder["order_id"]].update()
        self.notify(self._morders[corder["order_id"]].get_info())

    def get_orders(self, start_dt: str | pd.Timestamp, end_dt: str | pd.Timestamp):
        start_dt_ = pd.Timestamp(start_dt).replace(hour=0, minute=0, second=0)
        end_dt_ = pd.Timestamp(end_dt).replace(hour=23, minute=59, second=59)
        ret = []
        for _, v in self._morders.items():
            v1 = v.get_info().copy()
            if start_dt_ <= v1["c_dt"] <= end_dt_:
                v1.pop("order_type")
                v1.pop("api_order_id")
                ret.append(v1)
        return ret

    @property
    def trade_api(self):
        return self._trade_api_handler


class Trader(EquityManager, OrderManager):
    def __init__(self, init_money: float, trade_api_handler: Union[VirtualExchange, TradeAPI], is_verify: bool=True):
        EquityManager.__init__(self, init_money)
        OrderManager.__init__(self, trade_api_handler)
        self.attach(self, 0)
        self._is_verify = is_verify

    def on_order(self, msg, **kwargs):
        if msg.get("order_type", None) == OrderType.MORDER:
            EquityManager.on_order(self, msg, **kwargs)
        else:
            OrderManager.on_order(self, msg, **kwargs)

    def register_order(self, order):
        EquityManager.register_order(self, order)
        OrderManager.register_order(self, order)

    def order(
        self,
        symbol: QSymbol | str,
        direction: Direction | str,
        offset: OffSet | str,
        amount: float,
        price: Optional[float] = None,
        price_type: PriceType | str = PriceType.LIMIT,
    ) -> Tuple[StatusCode, Optional[str]]:
        symbol_ = QSymbol(symbol)
        direction_ = direction if isinstance(direction, Direction) else Direction[direction]
        offset_ = offset if isinstance(offset, OffSet) else OffSet[offset]
        price_type_ = price_type if isinstance(price_type, PriceType) else PriceType[price_type]

        if not symbol_ in self._cem.positions.keys():
            self._cem.positions[symbol_] = {
                PositionType.LONG: Position(symbol_, PositionType.LONG),
                PositionType.SHORT: Position(symbol_, PositionType.SHORT),
            }

        if amount == 0:
            return StatusCode.AMOUNT_IS_ZERO, None

        a1 = True
        a2 = True

        # 验资验券
        if self._is_verify:
            if (direction_.is_buy() and offset_.is_open()) or (direction_.is_sell() and offset_.is_close()):
                a1 = self._cem.positions[symbol_][PositionType.LONG].verify_security(offset_, amount)
            else:
                a1 = self._cem.positions[symbol_][PositionType.SHORT].verify_security(offset_, amount)
            a2 = self._cem.cash.verify_capital(symbol_, offset_, amount, price)

        if a1 and a2:
            return OrderManager.order(self, symbol_, direction_, offset_, amount, price, price_type_)
        elif not a1:
            return StatusCode.SEC_NOT_ENOUGH, None
        else:
            return StatusCode.CASH_NOT_ENOUGH, None

from qnt_utils.enums import OffSet, OrderStatus

from .utils import calc_cash_change, calc_transaction


cdef class Cash:

    def __init__(self, double init_money):
        """资金管理对象

        Args:
            init_money (float): 初始资金
        """
        self._available = init_money
        self._init = init_money
        self._frozen = 0

    cdef double get_available_cash(self):
        """可用资金"""
        return self._available

    cdef double get_frozen_cash(self):
        """冻结资金"""
        return self._frozen

    cdef double get_equity(self):
        """资金权益"""
        return self._frozen + self._available

    cdef double get_init_money(self):
        return self._init

    cpdef bint verify_capital(self, object symbol, object offset, double amount, double price):
        cdef double frozen
        if offset == OffSet.OPEN:
            frozen = abs(sum(calc_cash_change(symbol, offset, price, amount)))
            if frozen <= self._available:
                return 1
            else:
                return 0
        else:
            return 1

    cdef void handle_order(self, object new_order, object last_order):
        cdef double trade_amount, trade_price, cash1, cash2, cash3

        trade_amount, trade_price = calc_transaction(new_order, last_order)
        if new_order["offset"] == OffSet.OPEN:
            if last_order == {}:
                # 冻结order_amount
                cash1 = abs(sum(calc_cash_change(new_order["symbol"], new_order["offset"], new_order["price"], new_order["amount"])))
                self._available -= cash1
                self._frozen += cash1
            # 解冻trade_amount
            cash2 = abs(sum(calc_cash_change(new_order["symbol"], new_order["offset"], new_order["price"], trade_amount)))
            self._available += cash2
            self._frozen -= cash2
            # 开仓占用资金, 扣掉手续费
            cash3 = abs(sum(calc_cash_change(new_order["symbol"], new_order["offset"], trade_price, trade_amount)))
            self._available -= cash3
            # 订单结束, 解冻未使用的资金
            if new_order["status"] in [OrderStatus.CANCELLED, OrderStatus.FINISHED, OrderStatus.SCRAPPED]:
                cash5 = abs(sum(calc_cash_change(new_order["symbol"], new_order["offset"], new_order["price"], new_order["amount"] - trade_amount)))
                self._available += cash5
                self._frozen -= cash5
        else:
            # 平仓获得现金, 扣掉手续费
            cash4 = abs(sum(calc_cash_change(new_order["symbol"], new_order["offset"], trade_price, trade_amount)))
            self._available += cash4

    cdef void cashflow(self, double money):
        """由杠杆类资产变现的收益

        Args:
            money (float): 资金流
        """
        self._available += money

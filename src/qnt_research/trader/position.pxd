cdef class Position:
    """用于管理仓位, 分多和空2个方向"""

    cdef object _symbol, _position_type, _financial_asset, _dcposition
    cdef double _available, _frozen, _cost_basis, _last_price, _pre_price, _margin, _multiplier

    cpdef double update_last_price(self, double price)
    cpdef bint verify_security(self, object offset, double amount)
    cpdef void handle_order(self, object new_order, object last_order)
    
    cdef double _calc_delta_market_value(self)
    cdef double _calc_delta_margin(self)
    cdef double _delta_cash(self)
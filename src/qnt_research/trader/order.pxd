cdef class MOrder:
    """母单, 在OMS中生成"""
    cdef object _om_handler, _symbol, _direction, _offset, _price_type, _account, _order_id, _c_dt, _m_dt, _status
    cdef double _amount, _price, _trade_amount, _trade_price, _placed_amount
    cdef bint _is_credit
    cdef list _child_orders

    cpdef bint is_dead(self)
    cpdef object get_order_id(self)
    cpdef object get_info(self)
    cdef void add_corder(self, object child_order)
    cpdef void execute(self)
    cpdef void cancel(self)
    cpdef void update(self)
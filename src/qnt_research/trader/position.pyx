import numpy as np
import pandas as pd
from ..entity import DCPosition

from qnt_utils.enums import FinancialAsset, OffSet, OrderStatus, PositionType
from qnt_utils.label import QSymbol
from qnt_utils.toolset import get_financial_asset

from ..market import gl_trade_parameters, gl_market_snapshot, init_trade_params
from .utils import calc_transaction


cdef class Position:
    """用于管理仓位, 分多和空2个方向"""

    def __init__(self, symbol: QSymbol, position_type: PositionType):
        self._symbol = symbol
        self._position_type = position_type
        self._financial_asset = get_financial_asset(symbol)

        self._available: float = 0
        self._frozen: float = 0
        self._cost_basis: float = 0

        self._last_price: float = np.nan
        self._pre_price: float = np.nan

        self._dcposition: DCPosition = DCPosition(
            symbol=self._symbol,
            amount=self._available + self._frozen,
            available=self._available,
            frozen=self._frozen,
            amount_today=0,
            amount_his=0,
            amount_today_available=0,
            amount_his_available=0,
            cost_basis=self._cost_basis,
            last_price=self._last_price,
            margin=self.margin,
            market_value=self.market_value,
            profit=0,
            profit_rate=0,
            name="",
            position_type=self._position_type,
        )

        tp = gl_trade_parameters.setdefault(symbol, init_trade_params(symbol))
        self._margin: float = tp.margin
        self._multiplier: float = tp.multiplier

    @property
    def info(self):
        self._dcposition.amount = self._available + self._frozen
        self._dcposition.available = self._available
        self._dcposition.frozen = self._frozen
        self._dcposition.cost_basis = self._cost_basis
        self._dcposition.last_price = self._last_price
        self._dcposition.margin = self.margin
        self._dcposition.market_value = self.market_value
        return self._dcposition

    cpdef double update_last_price(self, double price):
        """"更新持仓的最新价，返回现金的变动"""
        if np.isnan(price):
            return 0.0

        self._pre_price = self._last_price
        self._last_price = price

        if self._available + self._frozen == 0.0 or np.isnan(self._pre_price):
            return 0.0
        return self._delta_cash()

    cpdef bint verify_security(self, object offset, double amount):
        """验券"""
        if offset == OffSet.OPEN:
            return 1
        else:
            if self._available >= amount:
                return 1
            else:
                return 0

    cpdef void handle_order(self, object new_order, object last_order):
        cdef double trade_amount, trade_price, tmp1

        trade_amount, trade_price = calc_transaction(new_order, last_order)
        if not new_order["offset"] == OffSet.OPEN:
            if last_order == {}:
                # 冻结order_amount
                self._available -= new_order["amount"]
                self._frozen += new_order["amount"]
            if trade_amount > 0:
                # 解冻trade_amount
                # 平仓扣掉trade_amount
                # 调整cost_basis
                if (self._available + self._frozen) <= trade_amount:
                    self._cost_basis = 0
                else:
                    self._cost_basis = (self._cost_basis * (self._available + self._frozen) - trade_amount * trade_price) / ((self._available + self._frozen) - trade_amount)
                self._frozen -= trade_amount
                # 订单结束, 解冻未使用的资金
                if new_order["status"] in [OrderStatus.CANCELLED, OrderStatus.FINISHED, OrderStatus.SCRAPPED]:
                    tmp1 = new_order["amount"] - trade_amount
                    self._available += (tmp1)
                    self._frozen -= tmp1
        else:
            if trade_amount > 0:
                # 开仓加上trade_amount
                # 调整cost_basis
                self._cost_basis = (self._cost_basis * (self._available + self._frozen) + trade_amount * trade_price) / ((self._available + self._frozen) + trade_amount)
                self._available += trade_amount

    @property
    def symbol(self):
        return self._symbol

    @property
    def position_type(self):
        return self._position_type

    @property
    def financial_asset(self):
        return self._financial_asset

    @property
    def available_amount(self):
        """资产的可用数量"""
        return self._available

    @property
    def frozen_amount(self):
        """资产的冻结数量"""
        return self._frozen

    @property
    def amount(self):
        """持仓数量, 包括冻结和可用, 不包括开仓中的数量"""
        return self._available + self._frozen

    @property
    def avg_cost_basis(self):
        """持仓成本"""
        return self._cost_basis

    @property
    def equity(self):
        """返回资产类的权益"""
        if self.amount == 0.0:
            return 0.0
        return self._last_price * self.amount * self._multiplier * self._margin

    @property
    def margin(self):
        if self._financial_asset in [FinancialAsset.FUTURES, FinancialAsset.OPTION]:
            return self.equity
        else:
            return 0

    @property
    def market_value(self):
        if self._financial_asset in [FinancialAsset.STOCK, FinancialAsset.FUND, FinancialAsset.BOND]:
            return self.equity
        else:
            return 0

    cdef double _calc_delta_market_value(self):
        """计算持仓合约价值或股票市值"""
        return (self._last_price - self._pre_price) * (self._available + self._frozen) * self._multiplier

    cdef double _calc_delta_margin(self):
        """保证金占用的变化"""
        return self._calc_delta_market_value() * self._margin

    cdef double _delta_cash(self):
        """价格更新之后计算现金的变化"""
        cdef double delta_margin = self._calc_delta_margin()
        cdef double profit

        if self.position_type == PositionType.LONG:
            profit = self._calc_delta_market_value()
        else:
            profit = -self._calc_delta_market_value()
        return profit - delta_margin

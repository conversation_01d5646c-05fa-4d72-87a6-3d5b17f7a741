import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from qnt_utils.enums import Direction, OffSet
from sklearn.linear_model import LinearRegression

from ..market import gl_trade_parameters
from .utils import calc_cash_change


def calc_interest(init: float, final: float, num_days: int) -> tuple[float, float]:
    """计算年化收益率

    Args:
        init (float): 初始权益
        final (float): 最终权益
        num_days (int): 交易天数

    Returns:
        tuple[float,float]: 年化单利，年化复利
    """
    return (final / init - 1) / num_days * 250, (final / init) ** (250 / num_days) - 1


def analyse(init_equity: float, equity_df: pd.DataFrame, trade_log_df: pd.DataFrame, brief_analyse: bool = False):
    """分析回测结果

    Args:
        init_equity (float): 初始权益
        equity_df (pd.DataFrame): index为datetime, column为date, equity, cash
        trade_log_df (pd.DataFrame): 交易记录
        brief_analyse (bool, optional): 是否简要分析. Defaults to False.
    """
    # 计算收益率, 包括年化单利、年化复利
    arr_simple_interest, arr_compound_interest = calc_interest(
        init_equity, equity_df["equity"].iloc[-1], len(equity_df["date"].unique())
    )

    trade_log_df = trade_log_df.copy()
    # 计算手续费
    trade_log_df["transaction_fee"] = trade_log_df.apply(
        lambda x: calc_cash_change(x["symbol"], x["offset"], x["trade_price"], x["trade_amount"])[1], axis=1
    )
    total_transaction_fee = trade_log_df["transaction_fee"].sum()

    # 计算滑点
    trade_log_df["slippage_cost"] = trade_log_df.apply(
        lambda x: -gl_trade_parameters[x["symbol"]].slippage
        * gl_trade_parameters[x["symbol"]].min_price
        * gl_trade_parameters[x["symbol"]].multiplier
        * x["trade_amount"],
        axis=1,
    )
    total_slippage_cost = trade_log_df["slippage_cost"].sum()

    # region 计算胜率、交易次数、总盈亏
    trade_log_df["delta_holding"] = trade_log_df.apply(
        lambda x: x["trade_amount"] if x["direction"] in [Direction.BUY, Direction.CREDIT_BUY] else -x["trade_amount"],
        axis=1,
    )
    trade_log_df["value"] = trade_log_df.apply(
        lambda x: -x["trade_price"] * x["delta_holding"] * gl_trade_parameters[x["symbol"]].multiplier
        + x["transaction_fee"],
        axis=1,
    )
    trade_log_df["position_type"] = trade_log_df.apply(
        lambda x: (
            "LONG"
            if (
                (x["direction"] in [Direction.BUY, Direction.CREDIT_BUY] and x["offset"] == OffSet.OPEN)
                or (x["direction"] in [Direction.SELL, Direction.CREDIT_SELL] and x["offset"] != OffSet.OPEN)
            )
            else "SHORT"
        ),
        axis=1,
    )
    trade_log_df["hold"] = np.nan
    trade_log_df["profit"] = np.nan

    for k, v in trade_log_df.groupby(["symbol", "position_type"]):
        trade_log_df.loc[v.index, "hold"] = v["delta_holding"].cumsum()
        profit = 0
        for i, _ in v.iterrows():
            profit += trade_log_df.at[i, "value"]
            if trade_log_df.at[i, "hold"] == 0:
                trade_log_df.at[i, "profit"] = profit
                profit = 0
    ret = []
    for k, v in trade_log_df.groupby(["symbol", "position_type"]):
        ret.append(
            {
                "symbol": k[0],
                "position_type": k[1],
                "total_profit": v["profit"].sum(),
                "number_of_trade": (t1 := v["profit"].count()),
                "number_of_win": (t2 := v.loc[v["profit"] > 0, "profit"].count()),
                "winning_percentage": t2 / (t1 or np.nan),
            }
        )
    ret = pd.DataFrame(ret)
    ret1 = ret.groupby("position_type").sum()
    total_profit_1 = ret["total_profit"].sum()
    number_of_trade_1 = ret["number_of_trade"].sum()
    winning_percentage_1 = ret["number_of_win"].sum() / (number_of_trade_1 or np.nan)

    total_profit_2 = ret1["total_profit"].get("LONG", 0)  # type: ignore
    number_of_trade_2 = ret1["number_of_trade"].get("LONG", 0)  # type: ignore
    winning_percentage_2 = ret1["number_of_win"].get("LONG", 0) / (number_of_trade_2 or np.nan)  # type: ignore

    total_profit_3 = ret1["total_profit"].get("SHORT", 0)  # type: ignore
    number_of_trade_3 = ret1["number_of_trade"].get("SHORT", 0)  # type: ignore
    winning_percentage_3 = ret1["number_of_win"].get("SHORT", 0) / (number_of_trade_3 or np.nan)  # type: ignore
    # endregion

    # 计算最大回撤
    max_withdraw1 = equity_df["equity"].cummax() - equity_df["equity"]
    index1 = int(np.argmax(max_withdraw1))
    index2 = int(np.argmax(equity_df["equity"].iloc[: index1 + 1]))
    max_withdraw1 = max_withdraw1.max()
    tmp = trade_log_df["profit"].cumsum()
    max_withdraw2 = (tmp.cummax() - tmp).max()

    # 计算日胜率
    daily_equity = equity_df["equity"].loc[equity_df["date"] != equity_df["date"].shift(-1)]
    daily_earn = daily_equity - daily_equity.shift(1)
    daily_win_rate = (daily_earn > 0).sum() / (daily_earn != 0).sum()

    if not brief_analyse:
        # 绘图

        _, ax = plt.subplots(nrows=2, ncols=1, figsize=(17.5, 5), sharex=True)
        ax1, ax2 = ax[0], ax[1]

        # 1. 画权益曲线
        ax1.plot(equity_df.index, equity_df["equity"])
        ax1.fill_between(
            equity_df.index,
            equity_df["equity"],
            equity_df["equity"].min(),
            where=[(True if index2 <= i <= index1 else False) for i in range(len(equity_df))],
            color="blue",
            alpha=0.2,
        )
        ax1.set_title("Account Equity")
        ax1.set_ylabel("equity", loc="top")

        # 2. 画资金利用率曲线
        fund_utilization_rate = (100 - equity_df["cash"] / equity_df["equity"] * 100).to_numpy()
        ax2.plot(equity_df.index, fund_utilization_rate)
        ax2.fill_between(
            equity_df.index, fund_utilization_rate, 0, where=list(fund_utilization_rate > 0), color="gray", alpha=0.2
        )
        ax2.set_title("Fund Utilization Rate")
        ax2.set_xlabel("datetime", loc="right")
        ax2.set_ylabel("fund utilization rate", loc="top")

        plt.show(block=True)

        # 计算夏普
        r_s = (daily_equity - daily_equity.shift(1)) / init_equity
        sharpe = r_s.mean() / r_s.std() * np.sqrt(252)

        # 计算kama
        kama = (arr_simple_interest / (max_withdraw2 / init_equity)) if max_withdraw2 > 0 else 0

        # 计算score
        reg_x = np.arange(len(equity_df)).reshape((-1, 1))
        reg_y = equity_df["equity"].to_numpy().reshape((-1, 1))
        reg = LinearRegression().fit(reg_x, reg_y)
        score = reg.score(reg_x, reg_y)

        print(
            f"""
最终权益：{equity_df["equity"].iloc[-1]:,.2f},  年化单利：{arr_simple_interest:.2%},  年化复利：{arr_compound_interest:.2%}

最大权益回撤：{max_withdraw1:,.2f},  最大损益回撤：{max_withdraw2:,.2f}

score: {score:.4f},  夏普比率：{sharpe:.2f},  卡玛比率：{kama:.2f}

手续费：{total_transaction_fee:,.2f},  滑点成本：{total_slippage_cost:,.2f}

总盈利：{total_profit_1:,.2f},  多：{total_profit_2:,.2f},  空：{total_profit_3:,.2f}

总次数：{number_of_trade_1:.0f},  多：{number_of_trade_2:.0f},  空：{number_of_trade_3:.0f}

日胜率：{daily_win_rate:.2%},  胜率：{winning_percentage_1:.2%},  多：{winning_percentage_2:.2%},  空：{winning_percentage_3:.2%}
"""
        )
    try:
        from IPython.display import display

        display(ret.set_index(["symbol", "position_type"], drop=True, append=False))
    except:
        print(ret.set_index(["symbol", "position_type"], drop=True, append=False))

    return {
        "overview": {
            "final equity": equity_df["equity"].iloc[-1],
            "annualized simple interest": arr_simple_interest,
            "annualized compound interest": arr_compound_interest,
            "maximum drawdown": max_withdraw1,
            "maximum drawdown with no position": max_withdraw2,
            "number of trades": number_of_trade_1,
            "win rate": winning_percentage_1,
            "daily win rate": daily_win_rate,
        },
        "trade_log": trade_log_df,
    }

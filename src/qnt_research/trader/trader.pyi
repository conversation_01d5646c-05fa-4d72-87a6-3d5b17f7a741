from typing import Optional, Tuple, Union

import pandas as pd

from qnt_utils.enums import Direction, OffSet, PriceType, StatusCode
from qnt_utils.label import QSymbol

from ..entity import DCPortfolio, DCPosition
from ..observer_pattern import DataObserver, OrderObserver, OrderSubject
from ..trade_api import TradeAPI
from ..virtual_exchange import VirtualExchange

class EquityManager(DataObserver, OrderObserver):
    def __init__(self, init_money: float): ...
    def register_order(self, order): ...
    def on_data(self, msg, **kwargs): ...
    def on_order(self, msg, **kwargs): ...
    @property
    def positions(self) -> tuple[StatusCode, dict[QSymbol, dict[str, DCPosition]]]: ...
    @property
    def portfolio(self) -> tuple[StatusCode, DCPortfolio]: ...
    @property
    def init_money(self) -> float: ...
    def get_info(self) -> dict: ...

class OrderManager(OrderSubject, OrderObserver):
    def __init__(self, trade_api_handler: Union[VirtualExchange, TradeAPI]): ...
    def order(
        self, symbol, direction, offset, amount, price, price_type: PriceType = PriceType.LIMIT
    ) -> Tuple[StatusCode, Optional[str]]: ...
    def cancel_order(self, order_id) -> Tuple[StatusCode, bool]: ...
    def register_order(self, order): ...
    def on_order(self, msg, **kwargs): ...
    def get_orders(self, start_dt: str | pd.Timestamp, end_dt: str | pd.Timestamp): ...
    @property
    def trade_api(self): ...

class Trader(EquityManager, OrderManager):
    def __init__(self, init_money: float, trade_api_handler: Union[VirtualExchange, TradeAPI], is_verify: bool = True): ...
    def on_order(self, msg, **kwargs): ...
    def register_order(self, order): ...
    def order(
        self,
        symbol: QSymbol | str,
        direction: Direction | str,
        offset: OffSet | str,
        amount: float,
        price: Optional[float] = None,
        price_type: PriceType | str = PriceType.LIMIT,
    ) -> Tuple[StatusCode, Optional[str]]: ...

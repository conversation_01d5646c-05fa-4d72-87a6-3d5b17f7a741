import datetype
import uuid
from typing import Any, Dict, NewType

import pandas as pd

from qnt_utils.enums import OrderStatus, OrderType, PriceType, StatusCode

from ..market import gl_market_snapshot
from ..virtual_exchange import VirtualExchange

COrder = NewType("COrder", Dict)
"""子单"""


cdef class MOrder:
    """母单, 在OMS中生成"""

    def __init__(self, om_handler, symbol, direction, offset, amount, price, price_type: PriceType = PriceType.LIMIT):
        self._om_handler = om_handler
        self._symbol = symbol
        self._direction = direction
        self._offset = offset
        self._amount = amount
        self._price = price
        self._price_type = price_type

        tmp = self._om_handler.trade_api.account if not isinstance(self._om_handler.trade_api, VirtualExchange) else ("", False)
        self._account = tmp[0]
        self._is_credit = tmp[1]
        self._order_id = str(uuid.uuid1())
        if symbol in gl_market_snapshot.keys():
            self._c_dt: datetype.NaiveDateTime = gl_market_snapshot[symbol].date_time
        else:
            self._c_dt: datetype.NaiveDateTime = gl_market_snapshot.current_date_time
        self._m_dt: datetype.NaiveDateTime = self._c_dt
        self._child_orders: list[COrder] = []
        self._status = OrderStatus.ORDER_TBC
        self._trade_amount = 0
        self._placed_amount = 0
        self._trade_price = 0

    cpdef bint is_dead(self):
        return self._status in [OrderStatus.FINISHED, OrderStatus.SCRAPPED, OrderStatus.CANCELLED]

    cpdef object get_order_id(self):
        return self._order_id

    cpdef object get_info(self):
        return {
            "symbol": self._symbol,
            "direction": self._direction,
            "offset": self._offset,
            "amount": self._amount,
            "price": self._price,
            "price_type": self._price_type,
            "c_dt": self._c_dt,
            "m_dt": self._m_dt,
            "status": self._status,
            "trade_amount": self._trade_amount,
            "trade_price": self._trade_price,
            "order_id": self._order_id,
            "account": self._account,
            "is_credit": self._is_credit,
            "api_order_id": "",
            "order_type": OrderType.MORDER,  # M-母单, C-子单
        }

    cdef void add_corder(self, object child_order):
        self._child_orders.append(child_order)

    cpdef void execute(self):
        """执行订单"""
        if self._status == OrderStatus.ORDER_TBC:
            self._status = OrderStatus.ALIVE
            status, res = self._om_handler.trade_api.order(
                symbol=self._symbol,
                direction=self._direction,
                offset=self._offset,
                amount=self._amount,
                price=self._price,
                price_type=self._price_type,
            )
            self._placed_amount += self._amount
            co = self.get_info().copy()
            co.update(
                status=OrderStatus.ORDER_TBC if status == StatusCode.SUCCESS else OrderStatus.SCRAPPED,
                api_order_id=res if status == StatusCode.SUCCESS else "SCRAPPED_{}".format(self._order_id),
                order_type=OrderType.CORDER,
            )
            self.add_corder(co)
            self._om_handler.register_order(co)

    cpdef void cancel(self):
        if self._status == OrderStatus.ALIVE:
            self._status = OrderStatus.CANCEL_TBC
            for i in self.child_orders:
                if i["status"] in [OrderStatus.ALIVE, OrderStatus.ORDER_TBC]:
                    self._om_handler.trade_api.cancel_order(i["api_order_id"])

    cpdef void update(self):
        if not self._status in [OrderStatus.ALIVE, OrderStatus.CANCEL_TBC]:
            return

        cdef double ata = 0
        cdef double pma = 0
        cdef object status
        cdef bint l1 = 0
        cdef bint l2 = 0
        cdef bint l3 = 0
        if self._placed_amount == self._amount:
            for corder in self._child_orders:
                if not corder["status"] == OrderStatus.FINISHED:
                    l1 = 1
                if not corder["status"] == OrderStatus.SCRAPPED:
                    l2 = 1
                if not corder["status"] in [OrderStatus.FINISHED, OrderStatus.SCRAPPED, OrderStatus.CANCELLED]:
                    l3 = 1
            if l1 == 0:
                status = OrderStatus.FINISHED
            elif l2 == 0:
                status = OrderStatus.SCRAPPED
            elif l3 == 0:
                status = OrderStatus.CANCELLED
            else:
                status = self._status
        else:
            status = OrderStatus.ALIVE
        
        for corder in self._child_orders:
            ata += corder["trade_amount"]
            pma += corder["trade_price"] * corder["trade_amount"]

        if (self._status != status) or (self._trade_amount != ata):
            if self._symbol in gl_market_snapshot.keys():
                self._m_dt = gl_market_snapshot[self._symbol].date_time
            else:
                self._m_dt = gl_market_snapshot.current_date_time
            self._status = status
            self._trade_amount = ata
            self._trade_price = (pma / ata) if ata > 0 else 0

        if not self._status in [OrderStatus.ALIVE, OrderStatus.CANCEL_TBC]:
            self._om_handler=None
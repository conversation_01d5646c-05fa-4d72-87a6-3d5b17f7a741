import math
from typing import Tuple

from qnt_utils.enums import OffSet
from qnt_utils.label import QSymbol

from .. import market


def calc_cash_change(symbol: QSymbol, off_set: OffSet, price: float, amount: float) -> Tuple[float, float]:
    """计算以price成交lots时, 现金的变动情况。

    Args:
        symbol (QSymbol): 标的
        off_set (OffSet): 开或平
        price (float): 成交价格
        amount (float): 成交数量

    Returns:
        Tuple[float, float]: 非现金类资金权益的变动(不包含手续费)对现金的影响, 支付手续费现金的变动
    """
    if amount == 0:
        return 0, 0
    tp = market.gl_trade_parameters.setdefault(symbol, market.init_trade_params(symbol))
    asset_value = price * amount * tp.multiplier
    occupation = asset_value * tp.margin
    if tp.transaction_type == 1:
        transaction_fee = max(asset_value * tp.transaction_cost, tp.min_transaction_fee)
    else:
        transaction_fee = max(tp.transaction_cost * amount, tp.min_transaction_fee)
    if off_set == OffSet.OPEN:
        return -occupation, -transaction_fee
    else:
        return occupation, -transaction_fee


def calc_amount(symbol, off_set, money, order_price):
    """根据下单金额计算下单数量"""
    trade_parameters = market.gl_trade_parameters.setdefault(symbol, market.init_trade_params(symbol))
    if off_set == OffSet.OPEN:
        if abs(sum(calc_cash_change(symbol, off_set, order_price, trade_parameters.min_lots))) > money:
            return 0
        lots = money // (order_price * trade_parameters.multiplier * trade_parameters.margin)
        lots = (
            lots - trade_parameters.min_lots
        ) // trade_parameters.lots_change * trade_parameters.lots_change + trade_parameters.min_lots
        while abs(sum(calc_cash_change(symbol, off_set, order_price, lots))) > money:
            lots -= trade_parameters.lots_change
        return lots
    else:
        if abs(sum(calc_cash_change(symbol, off_set, order_price, trade_parameters.min_lots))) > money:
            return trade_parameters.min_lots
        lots = money // (order_price * trade_parameters.multiplier * trade_parameters.margin)
        lots = (
            math.ceil((lots - trade_parameters.min_lots) / trade_parameters.lots_change) * trade_parameters.lots_change
            + trade_parameters.min_lots
        )
        while abs(sum(calc_cash_change(symbol, off_set, order_price, lots))) < money:
            lots += trade_parameters.lots_change
        return lots


def calc_transaction(new_order, last_order):
    """计算出最新成交的交易价格和交易数量"""
    if last_order == {}:
        return new_order["trade_amount"], new_order["trade_price"]
    amount = new_order["trade_amount"] - last_order["trade_amount"]
    if amount != 0:
        price = (
            new_order["trade_price"] * new_order["trade_amount"]
            - last_order["trade_price"] * last_order["trade_amount"]
        ) / amount
    else:
        price = 0
    return amount, price

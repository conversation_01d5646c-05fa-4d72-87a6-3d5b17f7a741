from qnt_trader.client import TraderClient
from qnt_utils.config import get_config
from qnt_utils.enums import Direction, OffSet, OrderStatus, OrderType, PriceType, StatusCode
from qnt_utils.label import QSymbol

from .observer_pattern import AbstractEventDriveEngine


class TradeAPI(TraderClient):
    def __init__(self, account: str, is_credit: bool):
        """初始化交易接口

        Args:
            account (str): 账号
            is_credit (bool): 是否信用账户
        """
        super().__init__(
            account,
            is_credit,
            "EventDriveEngine",
            get_config()["trader"]["settings"]["host"],
            get_config()["trader"]["settings"]["port"],
        )
        self._proxy: AbstractEventDriveEngine

    def register_event_engine(self, proxy: AbstractEventDriveEngine):
        self._proxy = proxy
        self.register_order_push(self._on_order)

    def _on_order(self, order):
        order = order.copy()
        order["order_type"] = OrderType.CORDER
        order["api_order_id"] = order.pop("order_id", "")
        order["price_type"] = PriceType[order["price_type"]]
        order["direction"] = Direction[order["direction"]]
        order["offset"] = OffSet[order["offset"]]
        order["status"] = OrderStatus[order["status"]]
        self._proxy.on_order(order)

    def order(
        self,
        symbol: QSymbol,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: str | PriceType = "LIMIT",
        group: str = "",
        **kwargs,
    ) -> tuple[StatusCode, str | None]:
        return super().order(symbol, direction, offset, amount, price, price_type, group, **kwargs)

import re
from functools import lru_cache
from typing import Dict, List, Literal, Optional, TypedDict, Union, overload

import datetype
import numpy as np
import pandas as pd

from qnt_utils.config import get_config
from qnt_utils.enums import Exchange
from qnt_utils.label import QSymbol
from qnt_utils.toolset import decrypt, to_nstimestamp

from .dbms.database_manager import DatabaseManager, HistoricalDataReq
from .dbms.sql_handler import SQLHandlerOfBasicInfo, SQLHandlerOfQuotation, SQLHandlerOrmOfFactor

_gl_sql_dict = {
    "drivername": "postgresql+psycopg2",
    "username": decrypt(get_config()["database"]["username"]),
    "password": decrypt(get_config()["database"]["password"]),
    "host": get_config()["database"]["host"],
    "port": get_config()["database"]["port"],
}


class SQLHandlerDict(TypedDict):
    basic_info: SQLHandlerOfBasicInfo
    quotation: SQLHandlerOfQuotation
    factor: SQLHandlerOrmOfFactor


_gl_sql_handlers: SQLHandlerDict


def make_sql_handler():
    global _gl_sql_handlers
    _gl_sql_handlers = {
        "basic_info": SQLHandlerOfBasicInfo(**_gl_sql_dict),
        "quotation": SQLHandlerOfQuotation(**_gl_sql_dict),
        "factor": SQLHandlerOrmOfFactor(**_gl_sql_dict),
    }


make_sql_handler()


def set_sql(**kwargs):
    global _gl_sql_dict
    _gl_sql_dict.update(kwargs)
    make_sql_handler()


@lru_cache
def get_all_qcodes():
    return _gl_sql_handlers["basic_info"].get_qcode_series()


def get_qcode(type_: Literal["exchange", "futures", "symbol"], key):
    x = get_all_qcodes()
    if type_ == "exchange":
        return x["exchange"][key]
    elif type_ == "futures":
        return x["futures"][key]
    elif type_ == "symbol":
        return x["symbol"][key if isinstance(key, tuple) else tuple(key.split("."))]
    else:
        raise ValueError


def fq_adjust(
    symbol: QSymbol, dataframe: pd.DataFrame, fq: Literal["forward", "backward"] | None, standard: int = 2, method=2
) -> pd.DataFrame:
    """对价格进行复权处理

    Args:
        symbol (QSymbol): 标的
        dataframe (pd.DataFrame): 数据
        fq (str | None): 复权方式，None - 不进行复权, forward - 前复权, backward - 后复权
        standard (int, optional): _description_. Defaults to 2.
        method (int, optional): _description_. Defaults to 2.

    Raises:
        ValueError: 复权方式参数错误

    Returns:
        pd.DataFrame: 复权处理之后的数据
    """
    if fq is None:
        return dataframe.copy()

    sql_handler: SQLHandlerOfBasicInfo = _gl_sql_handlers["basic_info"]
    factor = sql_handler.get_fq_factor(symbol, standard, method)
    if factor.empty:
        return dataframe.copy()

    data1 = dataframe.copy()
    if fq == "forward":
        data1["factor"] = 1 / factor
        data1["factor"] = data1["factor"].fillna(1)
        data1["factor"] = data1["factor"][::-1].cumprod()[::-1]
    elif fq == "backward":
        data1["factor"] = factor
        data1["factor"] = data1["factor"].shift(1)
        data1["factor"] = data1["factor"].fillna(1)
        data1["factor"] = data1["factor"].cumprod()
    else:
        raise ValueError("fq should be 'forward' or 'backward'")
    for i in [
        "open",
        "high",
        "low",
        "close",
        "uplimit_price",
        "downlimit_price",
        "avg_price",
        "pre_price",
        "quote_rate",
        "settle",
        "pre_settle",
    ]:
        if i in data1.columns:
            data1[i] = data1[i] * data1["factor"]
    return data1.reindex(index=dataframe.index, columns=dataframe.columns)


@overload
def get_price(
    symbols: QSymbol,
    start_dt: Optional[str],
    end_dt: Optional[str],
    frequency: str,
    fields: List[str] = ["open", "high", "low", "close", "volume", "turnover", "settle", "open_interest"],
    skip_paused: bool = False,
    fq: Literal["forward", "backward"] | None = None,
    bar_count: int = 0,
    enable_external_data: bool = False,
) -> pd.DataFrame: ...


@overload
def get_price(
    symbols: List[QSymbol],
    start_dt: Optional[str],
    end_dt: Optional[str],
    frequency: str,
    fields: List[str] = ["open", "high", "low", "close", "volume", "turnover", "settle", "open_interest"],
    skip_paused: bool = False,
    fq: Literal["forward", "backward"] | None = None,
    bar_count: int = 0,
    enable_external_data: bool = False,
) -> dict[QSymbol, pd.DataFrame]: ...


def get_price(
    symbols: Union[QSymbol, List[QSymbol]],
    start_dt: Optional[str],
    end_dt: Optional[str],
    frequency: str,
    fields: List[str] = ["open", "high", "low", "close", "volume", "turnover", "settle", "open_interest"],
    skip_paused: bool = False,
    fq: Literal["forward", "backward"] | None = None,
    bar_count: int = 0,
    enable_external_data: bool = False,
) -> Union[pd.DataFrame, dict[QSymbol, pd.DataFrame]]:
    """获取行情数据

    Args:
        symbols (Union[QSymbol, List[QSymbol]]): _description_
        start_dt (Union[None,str]): 开始时间. 与bar_count同时存在时，忽略bar_count
        end_dt (str): 结束时间
        frequency (str): 频率
        fields (List[str], optional): 获取字段 . Defaults to BAR_FIELDS.
        skip_paused (bool, optional): 是否跳过停牌. Defaults to False.
        fq (Literal[&quot;forward&quot;, &quot;backward&quot;] | None, optional): 复权方式, None - 不进行复权, forward - 前复权, backward - 后复权. Defaults to None.
        bar_count (int, optional): 数据条数. 与start_dt同时存在时，忽略bar_count. Defaults to 0.

    Returns:
        _type_: _description_
    """
    if re.match("[0-9]+m", frequency):
        if isinstance(symbols, str):
            hdr = HistoricalDataReq(
                symbol=symbols,
                basic_data="MINUTE",
                start_time=start_dt,
                end_time=end_dt,
                fields=fields,
                bar_count=bar_count,
            )
            if enable_external_data:
                dm = DatabaseManager(get_config())
                return fq_adjust(symbols, dm.get_data(hdr), fq)
            else:
                return fq_adjust(symbols, _gl_sql_handlers["quotation"].read_sql(hdr), fq)

        else:
            hdr = [
                HistoricalDataReq(
                    symbol=i,
                    basic_data="MINUTE",
                    start_time=start_dt,
                    end_time=end_dt,
                    fields=fields,
                    bar_count=bar_count,
                )
                for i in symbols
            ]
            if enable_external_data:
                dm = DatabaseManager(get_config())
                return {hdr_.symbol: fq_adjust(hdr_.symbol, dm.get_data(hdr_), fq) for hdr_ in hdr}
            else:
                return {
                    hdr_.symbol: fq_adjust(hdr_.symbol, _gl_sql_handlers["quotation"].read_sql(hdr_), fq)
                    for hdr_ in hdr
                }

    elif re.match("[0-9]+d", frequency):
        start_dt = f"{start_dt} 00:00" if start_dt is not None else None
        end_dt = f"{end_dt} 23:59" if end_dt is not None else None
        if isinstance(symbols, str):
            hdr = HistoricalDataReq(
                symbol=symbols,
                basic_data="DAY",
                start_time=start_dt,
                end_time=end_dt,
                fields=fields,
                bar_count=bar_count,
            )
            if enable_external_data:
                dm = DatabaseManager(get_config())
                return fq_adjust(symbols, dm.get_data(hdr), fq)
            else:
                return fq_adjust(symbols, _gl_sql_handlers["quotation"].read_sql(hdr), fq)

        else:
            hdr = [
                HistoricalDataReq(
                    symbol=i, basic_data="DAY", start_time=start_dt, end_time=end_dt, fields=fields, bar_count=bar_count
                )
                for i in symbols
            ]
            if enable_external_data:
                dm = DatabaseManager(get_config())
                return {hdr_.symbol: fq_adjust(hdr_.symbol, dm.get_data(hdr_), fq) for hdr_ in hdr}
            else:
                return {
                    hdr_.symbol: fq_adjust(hdr_.symbol, _gl_sql_handlers["quotation"].read_sql(hdr_), fq)
                    for hdr_ in hdr
                }
    else:
        raise ValueError(f"unsupported frequency: {frequency}")


def get_trade_days(
    exchange: Union[str, Exchange], start_dt: str | None, end_dt: str, count: int = 0
) -> List[datetype.AwareDateTime]:
    """获取交易日

    Args:
        exchange (Union[str, Exchange]): 交易所
        start_dt (str | None): 开始日期. 与count同时存在时，忽略count
        end_dt (str): 结束日期
        count (int): 天数. Defaults to 0.

    Returns:
        List[datetype.AwareDateTime]: 交易日
    """
    return _gl_sql_handlers["basic_info"].get_trade_days(exchange, start_dt, end_dt, count)


def get_workday(
    country: str, start: str | None, end: str | None, ty: Literal["all", "workday", "holiday"]
) -> List[datetype.AwareDateTime]:
    """获取工作日安排

    Args:
        country (str): 国家
        start (str | None): 开始日期
        end (str | None): 结束日期
        ty (Literal[&quot;all&quot;, &quot;workday&quot;, &quot;holiday&quot;]): 类型，工作日、节假日、所有

    Returns:
        List[datetype.AwareDateTime]: 日期列表
    """
    return _gl_sql_handlers["basic_info"].get_workday(country, start, end, ty)


def get_trade_day_open_time(
    exchange: Union[str, Exchange], trade_day: str, mins_in_advance: int = 5
) -> datetype.AwareDateTime:
    """获取交易所的开盘时间

    Args:
        exchange (Union[str, Exchange]): 交易所
        trade_day (str): 交易日
        mins_in_advance (int): 提前的分钟数

    Raises:
        ValueError: 传入的日期不是交易日；传入的交易所不支持

    Returns:
        datetype.AwareDateTime: 开盘时间
    """
    exchange_ = Exchange[exchange] if isinstance(exchange, str) else exchange
    trade_day_ = pd.Timestamp(trade_day, tz="Asia/Shanghai")
    trade_date_lst = get_trade_days(exchange, None, trade_day, 2)
    if trade_day_ not in trade_date_lst:
        raise ValueError(f"{trade_day_} 不是交易日")

    if exchange_ in [Exchange.SHFE, Exchange.INE, Exchange.DCE, Exchange.CZCE]:
        last_trade_day = trade_date_lst[0]
        a = get_workday("中国", None, None, "holiday")
        last_holiday = a[np.searchsorted(a, trade_day_, "left") - 1]  # type: ignore
        if last_holiday > last_trade_day:
            return trade_day_.replace(hour=9) - pd.Timedelta(minutes=mins_in_advance)
        else:
            return last_trade_day.replace(hour=21) - pd.Timedelta(minutes=mins_in_advance)
    elif exchange_ in [Exchange.GFEX]:
        return trade_day_.replace(hour=9) - pd.Timedelta(minutes=mins_in_advance)
    elif exchange_ in [Exchange.CFFEX, Exchange.SSE, Exchange.SZSE, Exchange.BSE]:
        return trade_day_.replace(hour=9, minute=15) - pd.Timedelta(minutes=mins_in_advance)
    else:
        raise ValueError(f"暂不支持 {exchange_} 交易所")


def is_within_trading_time(exchange: Union[str, Exchange], time_, offset_sec: int = 0) -> bool:
    if offset_sec == 0:
        trade_days = get_trade_days(
            exchange,
            (time_ - pd.Timedelta(days=20)).strftime("%Y-%m-%d"),
            (time_ + pd.Timedelta(days=20)).strftime("%Y-%m-%d"),
        )
        trade_days = [i.replace(hour=15) for i in trade_days]
        next_close_time_index = np.searchsorted(trade_days, time_, "right")  # type: ignore
        open = get_trade_day_open_time(exchange, trade_days[next_close_time_index].strftime("%Y-%m-%d"))
        close = trade_days[next_close_time_index]
        if open.date() != close.date():
            if time_ < (tmp := (open + pd.Timedelta(days=1)).replace(hour=2, minute=30)):
                close = tmp
            else:
                open = close.replace(hour=9, minute=0)
        return open <= time_ < close
    else:
        return is_within_trading_time(exchange, time_ - pd.Timedelta(seconds=offset_sec)) or is_within_trading_time(
            exchange, time_ + pd.Timedelta(seconds=offset_sec)
        )


def get_all_symbols(exchange: Union[str, Exchange], ty: str, start_dt: str, end_dt: str) -> pd.Series:
    """获取指定交易日的代码列表

    Args:
        exchange (Union[str, Exchange]): 交易所
        ty (str): 类型
        start_dt (str): 开始日期
        end_dt (str): 结束日期

    Returns:
        pd.Series: 代码列表
    """
    return _gl_sql_handlers["basic_info"].get_all_symbols(exchange, ty, start_dt, end_dt)


@lru_cache
def get_contract_info(symbol: QSymbol, date: str = "now") -> Optional[Dict]:
    """查询基本信息

    Args:
        symbol (QSymbol): symbol
        date (str): 日期

    Returns:
        Optional[Dict]: 基本信息
    """
    return _gl_sql_handlers["basic_info"].get_contract_info(symbol, date)


def get_all_futures(exchange: Optional[Union[str, List]] = None):
    return _gl_sql_handlers["basic_info"].get_all_futures(exchange)


def get_contract_list(
    exchange: Optional[str] = None,
    futcodes: List = [],
    start_m: int = 0,
    end_m: int = 999999,
    is_delisted: Optional[bool] = None,
) -> List:
    """获取指定品种的所有合约, 不包含主力和指数合约

    Args:
        exchange (Optional[str], optional): 交易所. Defaults to None.
        futcodes (List, optional): 期货代码. Defaults to [].
        start_m (int, optional): 起始月份. Defaults to 0.
        end_m (int, optional): 结束月份. Defaults to 999999.
        expired (Optional[bool], 合约是否已过期): _description_. Defaults to None.

    Returns:
        List: 合约列表
    """
    return _gl_sql_handlers["basic_info"].get_contract_list(exchange, futcodes, start_m, end_m, is_delisted)


def get_mfut(tv: str, exchange: str) -> QSymbol:
    """获取主力合约

    Args:
        tv (str): 期货品种
        exchange (str): 交易所

    Returns:
        QSymbol: 主力合约QSymbol
    """
    if "-1" not in tv:
        return "{}9999.{}".format(tv.upper(), exchange.upper())
    else:
        return "{}9999-1.{}".format(tv.replace("-1", "").upper(), exchange.upper())


def get_all_mfut(dt=None) -> List[QSymbol]:
    d = get_all_futures()
    if dt is not None:
        d = d.loc[d["delisting_date"].isnull() | (d["delisting_date"] > pd.Timestamp(dt).date())]
    return [get_mfut(*x) for x in zip(d["futures_code"], d["exchange"])]


def get_ifut(tv: str, exchange: str) -> QSymbol:
    """获取加权指数合约

    Args:
        tv (str): 期货品种
        exchange (str): 交易所

    Returns:
        QSymbol: 加权指数合约QSymbol
    """
    if "-1" not in tv:
        return "{}8888.{}".format(tv.upper(), exchange.upper())
    else:
        return "{}8888-1.{}".format(tv.replace("-1", "").upper(), exchange.upper())


def get_all_ifut(dt=None) -> List[QSymbol]:
    d = get_all_futures()
    if dt is not None:
        d = d.loc[d["delisting_date"].isnull() | (d["delisting_date"] > pd.Timestamp(dt).date())]
    return [get_ifut(*x) for x in zip(d["futures_code"], d["exchange"])]


def get_index_constituents(
    index: Union[List, str], start_dt: Union[str, pd.Timestamp], end_dt: Union[str, pd.Timestamp]
) -> Dict:
    """获取指定日期对应的成分股

    Args:
        index (Union[List, str]): 指数
        start_dt (Union[str, pd.Timestamp]): 开始日期
        end_dt (Union[str, pd.Timestamp]): 结束日期

    Returns:
        Dict: 成分股
    """
    return _gl_sql_handlers["basic_info"].get_index_constituents(index, start_dt, end_dt)


def get_fut_contracts(exchange: Union[str, Exchange], futures_code: str, start_dt: str, end_dt: str) -> pd.Series:
    """获取指定品种的合约代码列表，不包含连续和加权指数合约

    Args:
        exchange (Union[str, Exchange]): 交易所
        futures_code (str): 期货品种代码
        start_dt (str): 开始日期
        end_dt (str): 结束日期

    Returns:
        pd.Series: 代码列表
    """
    return _gl_sql_handlers["basic_info"].get_fut_contracts(exchange, futures_code, start_dt, end_dt)


def get_fut_mcontract(
    exchange: Union[str, Exchange], futures_code: str, start_dt: str, end_dt: str, standard: int = 2
) -> pd.Series:
    """获取指定日期对应的主力合约

    Args:
        exchange (Union[str, Exchange]): 交易所
        futures_code (str): 期货品种代码
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        standard (int, optional): 换月标准. Defaults to 2.

    Returns:
        pd.Series: 代码列表
    """
    sh: SQLHandlerOfBasicInfo = _gl_sql_handlers["basic_info"]
    return sh.get_fut_mcontract(exchange, futures_code, start_dt, end_dt, standard)


def get_factors(
    symbol_list: str | list[str], table: str, factors: str | list[str], start_date: str, end_date: str
) -> dict:
    return _gl_sql_handlers["factor"].get_factors(symbol_list, table, factors, start_date, end_date)


def get_last_price(symbol: QSymbol, dt: str) -> float:
    d1 = get_price(symbol, None, dt, "1d", ["close"], bar_count=5)
    ii1 = np.searchsorted(d1.index.values, to_nstimestamp(dt), "right") - 1
    if ii1 < 0:
        di1 = -np.inf
    else:
        di1 = d1.index[ii1]
    d2 = get_price(symbol, None, dt, "1m", ["close"], bar_count=5)
    ii2 = np.searchsorted(d2.index.values, to_nstimestamp(dt), "right") - 1
    if ii2 < 0:
        di2 = -np.inf
    else:
        di2 = d2.index[ii2]
    if di2 > di1:
        return d2.at[di2, "close"]
    else:
        if np.isinf(di1):
            return np.nan
        return d1.at[di1, "close"]

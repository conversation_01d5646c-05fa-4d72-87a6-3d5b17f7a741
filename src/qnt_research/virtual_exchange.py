import datetime
import uuid
from typing import Any, Dict, Tuple

from qnt_utils.enums import Direction, OffSet, OrderStatus, OrderType, PriceType, StatusCode
from qnt_utils.label import QSymbol

from .market import gl_market_snapshot, gl_trade_parameters, init_trade_params
from .observer_pattern import AbstractEventDriveEngine, DataObserver, OrderSubject


class VirtualExchange(OrderSubject, DataObserver):
    def __init__(self):
        OrderSubject.__init__(self)
        self._engine: AbstractEventDriveEngine | None = None
        self._orders: Dict[QSymbol, Dict[str, Dict[str, Any]]] = {}
        self._is_transit_mode = False

    def clear(self):
        self._orders = {}

    def register_event_engine(self, event_engine):
        self._engine = event_engine
        self._is_transit_mode = True

    def notify(self, msg: Any):
        if not self._is_transit_mode:
            super().notify(msg)
        else:
            if self._engine is not None:
                self._engine.on_order(msg)

    def on_data(self, msg, **kwargs):
        if not self._orders or not self._orders.get(msg["code"], {}):
            return

        tmp = self._orders[msg["code"]]
        self._orders[msg["code"]] = {}
        for k, v in tmp.items():
            self._match(v)
            if v["status"] not in [OrderStatus.FINISHED, OrderStatus.SCRAPPED, OrderStatus.CANCELLED]:
                self._orders[msg["code"]][k] = v

    def _match(self, order):
        is_deal = False
        last_price = gl_market_snapshot[order["symbol"]].last_price
        tp = gl_trade_parameters.setdefault(order["symbol"], init_trade_params(order["symbol"]))
        if order["direction"] in [Direction.BUY, Direction.CREDIT_BUY]:
            tmp_price = last_price + tp.slippage * tp.min_price
            if order["price"] >= tmp_price:
                is_deal = True
        else:
            tmp_price = last_price - tp.slippage * tp.min_price
            if order["price"] <= tmp_price:
                is_deal = True
        if is_deal:
            # TODO: 先做单次全部能够成交, 不考虑成交量限制
            order["trade_price"] = tmp_price
            order["trade_amount"] = order["amount"]
            order["m_dt"] = gl_market_snapshot[order["symbol"]].date_time
            order["status"] = OrderStatus.FINISHED
            self.notify(dict(order.copy()))
            return

        # TODO 清算时间暂定15点
        if gl_market_snapshot[order["symbol"]].date_time.time() == datetime.time(15):
            order["m_dt"] = gl_market_snapshot[order["symbol"]].date_time
            order["status"] = OrderStatus.CANCELLED
            self.notify(dict(order.copy()))
            return

    def order(
        self,
        symbol: QSymbol,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: str | PriceType = PriceType.LIMIT,
    ) -> Tuple[StatusCode, str]:
        """创建委托, 异步下单"""
        insert_order = {
            "symbol": symbol,
            "direction": Direction.efrom(direction),
            "offset": OffSet.efrom(offset),
            "amount": amount,
            "price": price,
            "price_type": PriceType.efrom(price_type),
            "c_dt": gl_market_snapshot[symbol].date_time,
            "m_dt": gl_market_snapshot[symbol].date_time,
            "status": OrderStatus.ALIVE,
            "trade_amount": 0,
            "trade_price": 0,
            "api_order_id": str(uuid.uuid1()),
            "order_type": OrderType.CORDER,  # M-母单, C-子单
        }
        self.notify(insert_order.copy())
        self._match(insert_order)
        if insert_order["status"] == OrderStatus.ALIVE:
            if symbol not in self._orders.keys():
                self._orders[symbol] = {}
            self._orders[symbol][insert_order["api_order_id"]] = insert_order
        return StatusCode.SUCCESS, insert_order["api_order_id"]

    def cancel_order(self, order_id: str) -> Tuple[StatusCode, bool]:
        for k, v in self._orders.items():
            if order_id in v.keys():
                if (order := self._orders[k][order_id])["status"] not in [
                    OrderStatus.FINISHED,
                    OrderStatus.SCRAPPED,
                    OrderStatus.CANCELLED,
                ]:
                    order["m_dt"] = gl_market_snapshot[order["symbol"]].date_time
                    order["status"] = OrderStatus.CANCELLED
                    self.notify(dict(order.copy()))
                    self._orders[k].pop(order_id, None)
                    return StatusCode.SUCCESS, True
                else:
                    return StatusCode.CANCEL_ORDER_FAILED, False
        else:
            return StatusCode.CANCEL_ORDER_FAILED, False


gl_virtual_exchange = VirtualExchange()

from dataclasses import dataclass, field
from typing import Literal

import datetype
from dataclasses_json import dataclass_json

from qnt_utils.enums import PositionType
from qnt_utils.label import QSymbol


@dataclass_json
@dataclass
class DCPortfolio:
    asset: float
    """总资产"""

    debt: float
    """负债"""

    equity: float
    """负债"""

    available_cash: float
    """可用现金"""

    frozen_cash: float
    """冻结资金"""

    market_value: float
    """持仓市值"""

    margin: float
    """期货保证金占用"""

    available_credit: float
    """可用保证金，用于信用账户"""

    interest_of_financed_funds: float
    """融资利率，用于信用账户"""

    interest_of_financed_bonds: float
    """融券利率，用于信用账户"""


@dataclass_json
@dataclass
class DCPosition:
    symbol: QSymbol
    """证券代码"""

    amount: float
    """持仓数量"""

    available: float
    """可用"""

    frozen: float
    """冻结"""

    amount_today: float
    """今仓"""

    amount_his: float
    """昨仓"""

    amount_today_available: float
    """今仓可用"""

    amount_his_available: float
    """昨仓可用"""

    cost_basis: float
    """成本价"""

    last_price: float
    """最新价"""

    margin: float
    """保证金占用"""

    market_value: float
    """市值"""

    profit: float
    """浮动盈亏"""

    profit_rate: float
    """利润率"""

    name: str
    """证券名称"""

    position_type: PositionType
    """多仓or空仓"""


@dataclass
class DCUnderlyingInfo:
    slippage: int
    """滑点"""

    min_transaction_fee: float
    """最小手续费"""

    margin: float
    """保证金率"""

    transaction_type: Literal[1, 2]
    """1为按金额*费率, 2为手数*每手费用"""

    transaction_cost: float
    """手续费率或每手费用"""

    min_price: float
    """最小变动价位"""

    multiplier: float
    """合约乘数"""

    min_lots: float
    """最小下单数量"""

    lots_change: float
    """下单数量变动"""


@dataclass
class DCMarketSnapshot:
    date_time: datetype.NaiveDateTime
    last_price: float
    underlying_name: str = ""
    bidorder_price: list[float] = field(default_factory=list)
    bidorder_volume: list[float] = field(default_factory=list)
    askorder_price: list[float] = field(default_factory=list)
    askorder_volume: list[float] = field(default_factory=list)

import numpy as np


class TR:
    def __init__(self, n):
        self.n = n

    def __call__(self, input_data, output):
        return max(
            input_data.high[-self.n :].max() - input_data.low[-self.n :].min(),
            abs(input_data.high[-self.n :].max() - input_data.close[-self.n - 1]),
            abs(input_data.low[-self.n :].min() - input_data.close[-self.n - 1]),
        )


class Settle:
    def __call__(self, input_data, output):
        close = input_data.close[int(input_data.n_trade_day[-1]) :]
        volume = input_data.volume[int(input_data.n_trade_day[-1]) :]
        if (tmp := volume.sum()) == 0:
            return np.nan
        return (volume * close).sum() / tmp


class HHVToday:
    def __init__(self, name):
        self.name = name

    def __call__(self, input_data, output):
        return getattr(input_data, self.name)[-int(input_data.n_trade_day[-1]) :].max()


class LLVToday:
    def __init__(self, name):
        self.name = name

    def __call__(self, input_data, output):
        return getattr(input_data, self.name)[-int(input_data.n_trade_day[-1]) :].min()


class LLV:
    def __init__(self, name, n):
        self.name = name
        self.n = n

    def __call__(self, input_data, output):
        return getattr(input_data, self.name)[-int(self.n) :].min()


class HHV:
    def __init__(self, name, n):
        self.name = name
        self.n = n

    def __call__(self, input_data, output):
        return getattr(input_data, self.name)[-int(self.n) :].max()

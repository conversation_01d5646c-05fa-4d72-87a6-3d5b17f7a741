import copy
from typing import Any, Callable

import numpy as np


def _calc(fnc):
    """装饰器, MSeries参与运算时转换成属性data, 并将返回值转换成MSeries"""

    def wrap_fnc(*args):
        assert all([(isinstance(i, Series) or isinstance(i, np.ndarray) or isinstance(i, float) or isinstance(i, int)) for i in args])
        class_ = args[0].__class__
        return class_().from_ndarray(fnc(*[(i.data if isinstance(i, Series) else i) for i in args]))

    return wrap_fnc


class Series:
    def __init__(self, size: int = 300, data_type: str = "float", name: str = ""):
        """序列

        Args:
            size (int, optional): 序列的size. Defaults to 300.
            data_type (str, optional): 序列的类型. Defaults to "float".
        """
        if data_type == "float":
            self._data = np.full((size,), np.nan)
        else:
            self._data = np.empty(size, data_type)
        self._count = 0
        self._size = size
        self._name = name

    def from_ndarray(self, nd):
        self._data = copy.deepcopy(nd)
        self._count = len(nd)
        self._size = len(nd)
        return self

    def update(self, data: Any):
        """更新序列内的值, 把旧值向前推一格并填入新值

        Args:
            data (Any): 最新值
        """
        self._data[:-1] = self._data[1:]
        self._data[-1] = data
        self._count += 1

    def modify(self, data: Any):
        """更新序列当前值

        Args:
            data (Any): 最新值
        """
        self._data[-1] = data

    @property
    def data(self):
        """返回序列值"""
        return self._data

    @property
    def size(self):
        """返回序列长度"""
        return self._size

    @property
    def count(self):
        return self._count

    @property
    def name(self):
        return self._name

    def is_inited(self) -> bool:
        """判定是否初始化完成

        Returns:
            bool: 当填入值的count>=size时返回True
        """
        return self._count >= self._size

    def __getitem__(self, item):
        return self._data.__getitem__(item)

    @_calc
    def __lt__(self, other):
        return self.__lt__(other)

    @_calc
    def __le__(self, other):
        return self.__le__(other)

    @_calc
    def __eq__(self, other):
        return self.__eq__(other)

    @_calc
    def __ne__(self, other):
        return self.__ne__(other)

    @_calc
    def __gt__(self, other):
        return self.__gt__(other)

    @_calc
    def __ge__(self, other):
        return self.__ge__(other)

    @_calc
    def __add__(self, other):
        return self.__add__(other)

    @_calc
    def __sub__(self, other):
        return self.__sub__(other)

    @_calc
    def __mul__(self, other):
        return self.__mul__(other)

    @_calc
    def __truediv__(self, other):
        return self.__truediv__(other)

    @_calc
    def __radd__(self, other):
        return self.__radd__(other)

    @_calc
    def __rsub__(self, other):
        return self.__rsub__(other)

    @_calc
    def __rmul__(self, other):
        return self.__rmul__(other)

    @_calc
    def __rtruediv__(self, other):
        return self.__rtruediv__(other)

    @_calc
    def __and__(self, other):
        return self.__add__(other)

    @_calc
    def __rand__(self, other):
        return self.__rand__(other)

    @_calc
    def __or__(self, other):
        return self.__or__(other)

    @_calc
    def __ror__(self, other):
        return self.__ror__(other)


class IndicatorSeries(Series):
    """指标类策略专用, 指标存储序列"""

    def update(self, data: Any):
        """如果输入数据也为指标序列, 只取最后一个值"""
        if isinstance(data, Series):
            super().update(data.data[-1])
        else:
            return super().update(data)

    @property
    def value(self):
        """返回指标最新值"""
        return self.data[-1]

    def __bool__(self):
        """对指标最新值进行bool判断"""
        return False if np.isnan(self.value) else bool(self.value)


class ASeries(Series):
    def __init__(self, am, fnc: Callable, size: int = 300, data_type="float", name: str = ""):
        """用于创建数值序列, 初始化时绑定策略, 并将self.ts_update注册到策略的ts_updates中

        Args:
            am: 绑定ArrayManager
            fnc: 更新规则, 参数为Strategy.ArrayManager,self._data
            size: 长度
            data_type: 数据类型
        """
        super().__init__(size, data_type, name)
        self._am = am
        self._fnc = fnc

    def update(self, data):
        """此函数注册到所绑定策略的ts_updates, 策略执行过程中更新"""
        super().update(self._fnc(self._am, self._data))

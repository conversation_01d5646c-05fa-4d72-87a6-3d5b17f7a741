from typing import Literal, Optional, Union

import pandas as pd
from loguru import logger

from qnt_qds.client import DataClient
from qnt_utils.config import get_config
from qnt_utils.enums import Action, BasicData
from qnt_utils.fields import (
    BAR_FIELDS,
    TICK_ORDER_FIELDS,
    TICK_SNAPSHOT_FIELDS,
    TICK_TRANSACTION_FIELDS,
    Bar,
    TickSnapshot,
)
from qnt_utils.label import DataLabel, QSymbol, generate_data_label
from qnt_utils.toolset import to_nstimestamp

from ..api import get_price
from ..market import gl_market_snapshot
from ..observer_pattern import AbstractEventDriveEngine


def get_historical_data(
    symbol: QSymbol,
    basic_data: Union[str, BasicData],
    start_dt: str,
    end_dt: Optional[str] = None,
    fq: Literal["backward", "forward"] | None = "backward",
) -> pd.DataFrame:
    """获取历史数据

    Args:
        symbol (QSymbol): 证券代码
        basic_data (Union[str, BasicData]): 数据类型
        start_dt (str): 开始时间
        end_dt (Optional[str], optional): 结束时间，如果为None，则取当前时间. Defaults to None.
        fq (Literal["backward", "forward"] | None, optional): 复权类型，backward - 后复权，forward - 前复权. Defaults to "backward".

    Returns:
        pd.DataFrame: 历史数据
    """
    basic_data = basic_data if isinstance(basic_data, BasicData) else BasicData[basic_data]
    start_dt = start_dt
    end_dt = end_dt if end_dt is not None else pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
    fields = {
        BasicData.DAY: list(BAR_FIELDS.keys()),
        BasicData.MINUTE: list(BAR_FIELDS.keys()),
        BasicData.SNAPSHOT: TICK_SNAPSHOT_FIELDS,
        BasicData.ORDER: TICK_ORDER_FIELDS,
        BasicData.TRANS: TICK_TRANSACTION_FIELDS,
    }.get(basic_data, [])

    if basic_data in [BasicData.DAY, BasicData.MINUTE]:
        try:
            frequency = {
                BasicData.DAY: "1d",
                BasicData.MINUTE: "1m",
            }[basic_data]
            data = get_price(
                symbol, start_dt, end_dt, frequency=frequency, fields=fields, fq=fq, enable_external_data=True
            )
            return data
        except Exception:
            logger.warning("get historical data failed!")
            return pd.DataFrame()
    else:
        return pd.DataFrame()


class OriginalData:
    """
    用于数据提取, 包括实时数据和历史数据, 并汇总输出, 保持时间顺序
    #### 1.register_event_engine
    #### 2.register_addr
    #### 3.set_time_interval
    #### 4.subscribe
    #### 5.init_data
    """

    __instance = None

    def __init__(self):
        if OriginalData.__instance is not None:
            raise Exception("This class is a singleton!")
        else:
            OriginalData.__instance = self
        self._start_dt: str
        self._end_dt: str
        self._symbol_set = set()
        self._his_end_timestamp: dict[DataLabel, int] = {}
        """存储历史数据的最后时间戳"""

        self._sub_quotation: bool
        """是否订阅行情"""
        self._data_sdk: DataClient
        """实时行情SDK"""
        self._sub_info: list[tuple[Action, BasicData, list]] = []
        """list，存放订阅信息"""

        self._transit_station: AbstractEventDriveEngine

    def _on_snapshot(self, data: TickSnapshot):
        """订阅数据的回调, 如果连接中断, 队列中放入None, 否则放入(label, data)
        如果注册了EventDriveEngine, 则会调用on_data，存放到EventDriveEngine内部的队列；否则放入自身内部队列"""
        data_label = generate_data_label(data["code"], BasicData.SNAPSHOT)
        nstimestamp = to_nstimestamp("{} {:0>10.3f}".format(data["date"], data["time"] / 1000))
        if nstimestamp <= self._his_end_timestamp.get(data_label, 0):
            return
        else:
            self._his_end_timestamp[data_label] = nstimestamp
            tmp = (data_label, data)
            self._transit_station.on_data(tmp)

    def _on_minute(self, data: Bar):
        """订阅数据的回调, 如果连接中断, 队列中放入None, 否则放入(label, data)
        如果注册了EventDriveEngine, 则会调用on_data，存放到EventDriveEngine内部的队列；否则放入自身内部队列"""
        data_label = generate_data_label(data["code"], BasicData.MINUTE)
        nstimestamp = to_nstimestamp("{} {:0>10.3f}".format(data["date"], data["time"] / 1000))
        if nstimestamp <= self._his_end_timestamp.get(data_label, 0):
            return
        else:
            self._his_end_timestamp[data_label] = nstimestamp
            tmp = (data_label, data)
            self._transit_station.on_data(tmp)

    def register_event_engine(self, transit_station: AbstractEventDriveEngine):
        self._transit_station = transit_station

    def set_time_interval(
        self, start_dt: Optional[str] = None, end_dt: Optional[str] = None, sub_quotation: bool = False
    ):
        """设置数据的时间区间

        Args:
            start_dt (Optional[str], optional): 开始时间. Defaults to None, 默认为结束时间向推推60个交易日.
            end_dt (Optional[str], optional): 结束时间. Defaults to None, 默认为当前时间.
            sub_quotation (bool, optional): 是否需要实时行情. Defaults to False.
        """
        if start_dt is None:
            if end_dt is None:
                self._end_dt = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M")
                self._start_dt = (pd.Timestamp.now() - pd.Timedelta(days=60)).strftime("%Y-%m-%d %H:%M")
            else:
                self._end_dt = end_dt
                self._start_dt = (pd.Timestamp(end_dt) - pd.Timedelta(days=60)).strftime("%Y-%m-%d %H:%M")
        else:
            if end_dt is None:
                self._end_dt = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M")
                self._start_dt = start_dt
            else:
                self._start_dt = start_dt
                self._end_dt = end_dt

        self._sub_quotation = sub_quotation

    def subscribe(self, symbol: QSymbol, basic_data: Union[str, BasicData]) -> list[dict]:
        """订阅数据

        Args:
            symbol (QSymbol): 证券代码
            basic_data (Union[str, BasicData]): 数据类型

        Returns:
            list[dict]: 历史数据，如[{"code": "600000.SSE", "open": 10.0, "date": 20210101}, {"code": "600000.SSE", "open": 10.0, "date": 20210102}]
        """
        basic_data = basic_data if isinstance(basic_data, BasicData) else BasicData[basic_data]
        dl = generate_data_label(symbol, basic_data)
        if dl not in self._symbol_set:
            self._symbol_set.add(dl)

            existing_data = get_historical_data(symbol, basic_data, self._start_dt, self._end_dt)
            if existing_data.empty:
                self._his_end_timestamp[dl] = 0
            else:
                self._his_end_timestamp[dl] = existing_data.index.to_list()[-1]
            current = to_nstimestamp(
                gl_market_snapshot.current_date_time.strftime("%Y-%m-%d %H:%M:%S.%f"), tz="Asia/Shanghai"
            )
            his = existing_data.loc[existing_data.index <= current]
            fut = existing_data.loc[existing_data.index > current]
            self._transit_station.extend_data([(dl, x) for x in fut.to_dict("records")])

            if self._sub_quotation:
                config = get_config()
                if not hasattr(self, "_data_sdk"):
                    self._data_sdk = DataClient(
                        zk_hosts=f"{config['zookeeper']['host']}:{config['zookeeper']['port']}",
                        mq_host=f"{config['rabbitmq']['host']}:{config['rabbitmq']['port']}",
                        fnc_snapshot=self._on_snapshot,
                        fnc_minute=self._on_minute,
                    )
                self._data_sdk.subscribe(Action.ADD_SUB, basic_data, [symbol])
            else:
                self._transit_station.add_end_signal()
            return [x for x in his.to_dict("records")]
        else:
            return []

    def clear(self):
        self._symbol_set = set()


gl_original_data = OriginalData()
"""
用于数据提取, 包括实时数据和历史数据, 并汇总输出, 保持时间顺序
#### 1.register_event_engine
#### 2.register_addr
#### 3.set_time_interval
#### 4.fetch_data
"""

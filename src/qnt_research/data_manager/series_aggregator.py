from abc import abstractmethod
from typing import Callable, Dict, List, Optional

import datetype
import numpy as np

from qnt_utils.enums import BasicData, FinancialAsset
from qnt_utils.label import QSymbol
from qnt_utils.toolset import get_financial_asset

from .series import ASeries, IndicatorSeries, Series


class SeriesAggregator:
    """time series container"""

    def __init__(self, symbol: QSymbol, size: int, merge_num: int, basic_data: BasicData, fields: Dict[str, str]):
        """_summary_

        Args:
            symbol (QSymbol ): 证券代码
            size (int): 保存的数据长度
            merge_num (int): 合成数据的数量
            basic_data (BasicData): 数据类型
            fields (Dict[str, str]): 数据字段
        """
        self._symbol: QSymbol = symbol
        self._size: int = size
        self._merge_num: int = merge_num
        self._basic_data: BasicData = basic_data
        self._fields: Dict[str, str] = fields
        self._financial_asset: FinancialAsset = get_financial_asset(symbol)

        self._count: int = 0
        self._new_data: dict = {}
        self._last_recv_time: Optional[datetype.NaiveDateTime] = None
        self._inited: List[bool] = [False, False]
        self._series_update: List[Callable] = []
        self._aseries_is_inited: List[Callable] = []
        self._mseries_is_inited: List[Callable] = []
        """ASeries的update"""

        for i, j in self._fields.items():
            setattr(self, i, np.empty(self._size, dtype=j))
        tmp = list(self.__dict__.keys())
        for i in tmp:
            if i in self._fields.keys():
                continue
            if isinstance(getattr(self, i), Series):
                delattr(self, i)
            elif isinstance(getattr(self, i), np.ndarray):
                setattr(self, i, np.empty_like(getattr(self, i)))

    @abstractmethod
    def _merge_basic_data(self, basic_data):
        pass

    def create_series(self, name: str, fnc: Optional[Callable] = None, data_type: str = "float", is_ind: bool = False):
        """创建序列

        Args:
            name (str): 序列名称
            fnc (Optional[Callable], optional): 非None创建ASeires, 否则创建Series. MSeires在Strategy.calc中更新,
                                                Strategy.calc只有在ArrayManager完成初始化, 即基础行情数据和Aseries初始化完成后才会运行.
                                                Defaults to None.
            data_type (str, optional): 序列内的数值类型. Defaults to "float".
        """
        if fnc is None:
            if is_ind:
                setattr(self, name, IndicatorSeries(self._size, data_type, name))
            else:
                setattr(self, name, Series(self._size, data_type, name))
            self._mseries_is_inited.append(getattr(self, name).is_inited)
        else:
            setattr(self, name, ASeries(self, fnc, self._size, data_type, name))
            self._series_update.append(getattr(self, name).update)
            self._aseries_is_inited.append(getattr(self, name).is_inited)

    def on_data(self, msg, basic_data):
        """从data_manager的输出队列中取数据, 用于更新数据序列, 当前所有时间序列向前推一格, 并将最新bar里的值填充到序列末尾"""
        if not (self._symbol == msg["code"] and basic_data == self._basic_data):
            return

        self._merge_basic_data(msg)
        if self.new_data:
            self._update_series()
            for callback in self._series_update:
                try:
                    callback(None)
                except:
                    pass
            if self._count >= self._size and all([i() for i in self._aseries_is_inited]):
                self._inited[0] = True
            if self._inited[0] and all([i() for i in self._mseries_is_inited]):
                self._inited[1] = True

    def _update_series(self):
        self._count += 1
        for i in self._fields.keys():
            if i in self._new_data.keys():
                tmp = getattr(self, i)
                tmp[:-1] = tmp[1:]
                tmp[-1] = self._new_data[i]

    @property
    def new_data(self):
        return self._new_data

    @property
    def count(self) -> int:
        """返回已经接收行情的数量

        Returns:
            int: 已经处理的行情数量
        """
        return self._count

    @property
    def is_inited(self) -> List[bool]:
        """获取当前的初始化状态

        Returns:
            List[bool]: 初始化判定标准为count>=size, 返回值内部有2个值, 第一个表示基础行情和ASeries是否初始化, 第二个表示Series是否初始化
        """
        return self._inited

    @property
    def symbol(self):
        return self._symbol

    @property
    def size(self):
        return self._size

    @property
    def period(self):
        return self._merge_num, self._basic_data

    @property
    def fields(self):
        return list(self._fields.keys())

    @property
    def financial_asset(self):
        return self._financial_asset


# TODO: 持久化
"""
    def __getstate__(self):
        return self.__dict__.copy()

    def __setstate__(self, state):
        for key, value in state.items():
            self.__dict__[key] = state[key]

    def resume(self):
        self._data_extractor.resume()
        threading.Thread(target=self._merge_algorithm, daemon=True).start()
"""

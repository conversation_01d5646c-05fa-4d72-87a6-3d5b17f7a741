import numpy as np
import pandas as pd
from qnt_research.engine import gl_event_drive_engine
from qnt_research.strategy.base import CustomStrategy
from qnt_research.trade_api import TradeAPI
from qnt_research.trader.trader import Trader
from qnt_research.utils.algo import TR, HHVToday, LLVToday, Settle
from qnt_research.virtual_exchange import gl_virtual_exchange
from qnt_utils.ctoolset import backward_search_true
from qnt_utils.enums import BasicData, StatusCode, StrategyMode
from qnt_utils.label import QSymbol

pd.set_option("display.max_rows", None)


class FundStrategy(CustomStrategy):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.p1: int
        self.p2: int
        self.p3: int
        self.p4: int
        self.xn2: int
        self.xn5: int
        self.xn4: int
        self.xn3: int
        self.symbol: QSymbol
        self.amount: int

    def init(self):
        data = self.subscribe(self.symbol, 300, 5, BasicData.MINUTE)
        data.create_series("tr", TR(3))
        data.create_series("llv_low_today", LLVToday("low"))
        data.create_series("llv_close_today", LLVToday("close"))
        data.create_series("hhv_high_today", HHVToday("high"))
        data.create_series("hhv_close_today", HHVToday("close"))
        data.create_series("my_settle", Settle())
        data.create_series("cfj1")
        data.create_series("upperbound")
        data.create_series("lowerbound")
        data.create_series("greater_than_upperbound")
        data.create_series("lower_than_lowerbound")

    def on_bar(self, data):
        close = data.close[-1]
        open_ = data.open[-1]

        positions = self.trader.positions[1]
        portfolio = self.trader.portfolio[1]

        symbol = data.symbol
        n = data.n_trade_day[-1]
        hh_yestoday = data.hhv_high_today[-1 - n]
        hc_today = data.hhv_close_today[-1]
        ll_yestoday = data.llv_low_today[-1 - n]
        lc_today = data.llv_close_today[-1]
        cc_yestoday = data.close[-1 - n]
        oo_today = data.open[-n]

        cfj1 = max(hh_yestoday - cc_yestoday, cc_yestoday - ll_yestoday) * self.p1 / 100 if n == 1 else 0
        data.cfj1.update(cfj1)
        cfj = data.cfj1[backward_search_true(data.n_trade_day == 1, self.p4) :].sum() / self.p4

        upperbound = min(max(cc_yestoday + cfj, oo_today + self.p2 / 100 * cfj), oo_today + self.p3 / 100 * cfj)
        data.upperbound.update(upperbound)
        data.greater_than_upperbound.update(close > upperbound)

        lowerbound = max(min(cc_yestoday - cfj, oo_today - self.p2 / 100 * cfj), oo_today - self.p3 / 100 * cfj)
        data.lowerbound.update(lowerbound)
        data.lower_than_lowerbound.update(close < lowerbound)

        if not symbol in positions.keys() or (
            positions[symbol]["LONG"].amount == 0 and positions[symbol]["SHORT"].amount == 0 and portfolio.frozen_cash == 0
        ):
            con1_l = close > upperbound
            con2_l = close == hc_today
            con3_l = close >= open_
            con4_l = 1
            con5_l = close < cc_yestoday * (1 + 45 / 1000)

            con1_s = close < lowerbound
            con2_s = close == lc_today
            con3_s = close <= open_
            con4_s = 1
            con5_s = close > cc_yestoday * (1 - 45 / 1000)

            if con1_l and con2_l and con3_l and con4_l and con5_l:
                status_code, _ = self.buy_open(symbol, self.amount)
                # print(self.date_time, "upperbound", upperbound)
                if status_code == StatusCode.SUCCESS:
                    self.bkhigh = -np.inf
                    self.bkprice = close
            elif con1_s and con2_s and con3_s and con4_s and con5_s:
                status_code, _ = self.sell_open(symbol, self.amount)
                # print(self.date_time, "lowerbound", lowerbound)
                if status_code == StatusCode.SUCCESS:
                    self.sklow = np.inf
                    self.skprice = close

        elif positions[symbol]["LONG"].available > 0:
            self.bkhigh = max(self.bkhigh, data.high[-1])
            if close <= self.bkprice * (1 - self.xn2 / 1000) and close <= open_:
                # print(self.date_time, "bkhigh1", self.bkhigh)
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
            elif close <= self.bkhigh * (1 - self.xn5 / 1000) and close < self.bkprice * (1 + self.xn4 / 1000) and close <= open_:
                # print(self.date_time, "bkhigh2", self.bkhigh)
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
            elif (
                close <= self.bkhigh * (1 - self.xn3 / 1000) and close >= self.bkprice * (1 + self.xn4 / 1000) and close <= open_
            ):
                # print(self.date_time, "bkhigh3", self.bkhigh)
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)

        elif positions[symbol]["SHORT"].available > 0:
            self.sklow = min(self.sklow, data.low[-1])
            if close >= self.skprice * (1 + self.xn2 / 1000) and close > open_:
                # print(self.date_time, "sklow1", self.sklow)
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)
            elif close >= self.sklow * (1 + self.xn5 / 1000) and close > self.skprice * (1 - self.xn4 / 1000) and close >= open_:
                # print(self.date_time, "sklow2", self.sklow)
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)
            elif close >= self.sklow * (1 + self.xn3 / 1000) and close <= self.skprice * (1 - self.xn4 / 1000) and close >= open_:
                # print(self.date_time, "sklow3", self.sklow)
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)


if __name__ == "__main__":
    gl_event_drive_engine.set_env(StrategyMode.TRADE, "20230901 0930")
    strategy = FundStrategy(
        **{
            "p1": 140,
            "p2": 60,
            "p3": 120,
            "p4": 4,
            "xn2": 13,
            "xn3": 50,
            "xn4": 18,
            "xn5": 26,
            "symbol": "162605.SZSE",
            "amount": 2900,
        }
    )
    strategy.register_trader(Trader(200000, gl_virtual_exchange))
    # strategy.register_trader(Trader(200000, TradeAPI("541300123639", False)))
    gl_event_drive_engine.register_strategy(strategy)
    gl_event_drive_engine.run()

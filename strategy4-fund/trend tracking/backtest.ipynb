{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a0860c25-12f7-4827-b686-10aa3c480246", "metadata": {"execution": {"iopub.execute_input": "2024-01-31T16:47:46.382879Z", "iopub.status.busy": "2024-01-31T16:47:46.382679Z", "iopub.status.idle": "2024-01-31T16:47:46.405138Z", "shell.execute_reply": "2024-01-31T16:47:46.404329Z", "shell.execute_reply.started": "2024-01-31T16:47:46.382862Z"}}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "id": "d0cd917a-2f0d-45dd-b416-c5f6b370653b", "metadata": {"execution": {"iopub.execute_input": "2024-01-31T16:47:46.406887Z", "iopub.status.busy": "2024-01-31T16:47:46.406454Z", "iopub.status.idle": "2024-01-31T16:47:47.711045Z", "shell.execute_reply": "2024-01-31T16:47:47.710249Z", "shell.execute_reply.started": "2024-01-31T16:47:46.406866Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from fund import FundStrategy\n", "from qnt_research.data_manager.original_data import gl_original_data\n", "from qnt_research.engine import EventDriveEngine, gl_event_drive_engine\n", "from qnt_research.trader.trader import Trader\n", "from qnt_research.virtual_exchange import gl_virtual_exchange\n", "from qnt_utils.enums import BasicData, StrategyMode\n", "from qnt_utils.label import QSymbol"]}, {"cell_type": "code", "execution_count": 4, "id": "9bd22f7e-57c5-4d46-a3f3-847383690442", "metadata": {"execution": {"iopub.execute_input": "2024-01-31T16:49:25.251833Z", "iopub.status.busy": "2024-01-31T16:49:25.251541Z", "iopub.status.idle": "2024-01-31T16:49:29.320700Z", "shell.execute_reply": "2024-01-31T16:49:29.319791Z", "shell.execute_reply.started": "2024-01-31T16:49:25.251816Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code_repositories/quant/src/qnt_research/utils/algo.py:22: RuntimeWarning: overflow encountered in multiply\n", "  return (volume * close).sum() / tmp\n", "2024-01-31 16:49:27,483 - FundStrategy - INFO \t2023-08-15 13:40:00 \tSELL            OPEN            162605.SZSE \tprice: 2.2390 \tamount: 12600 failed, NOT INITIATED\n", "2024-01-31 16:49:27,496 - FundStrategy - INFO \t2023-08-17 10:35:00 \tBUY             OPEN            162605.SZSE \tprice: 2.2810 \tamount: 12600\n", "2024-01-31 16:49:27,515 - FundStrategy - INFO \t2023-08-18 15:00:00 \tSELL            CLOSE           162605.SZSE \tprice: 2.2490 \tamount: 12600\n", "2024-01-31 16:49:27,570 - FundStrategy - INFO \t2023-08-28 09:35:00 \tSELL            OPEN            162605.SZSE \tprice: 2.2790 \tamount: 12600\n", "2024-01-31 16:49:27,621 - FundStrategy - INFO \t2023-09-04 09:35:00 \tBUY             CLOSE           162605.SZSE \tprice: 2.2880 \tamount: 12600\n", "2024-01-31 16:49:27,625 - FundStrategy - INFO \t2023-09-04 10:15:00 \tBUY             OPEN            162605.SZSE \tprice: 2.3010 \tamount: 12600\n", "2024-01-31 16:49:27,656 - FundStrategy - INFO \t2023-09-07 09:35:00 \tSELL            CLOSE           162605.SZSE \tprice: 2.2640 \tamount: 12600\n", "2024-01-31 16:49:27,661 - FundStrategy - INFO \t2023-09-07 10:55:00 \tSELL            OPEN            162605.SZSE \tprice: 2.2540 \tamount: 12600\n", "2024-01-31 16:49:27,982 - FundStrategy - INFO \t2023-10-27 13:20:00 \tBUY             CLOSE           162605.SZSE \tprice: 2.1500 \tamount: 12600\n", "2024-01-31 16:49:27,984 - FundStrategy - INFO \t2023-10-27 13:30:00 \tBUY             OPEN            162605.SZSE \tprice: 2.1500 \tamount: 12600\n", "2024-01-31 16:49:28,110 - FundStrategy - INFO \t2023-11-13 10:05:00 \tSELL            CLOSE           162605.SZSE \tprice: 2.1840 \tamount: 12600\n", "2024-01-31 16:49:28,114 - FundStrategy - INFO \t2023-11-13 11:00:00 \tSELL            OPEN            162605.SZSE \tprice: 2.1710 \tamount: 12600\n", "2024-01-31 16:49:28,123 - FundStrategy - INFO \t2023-11-14 09:35:00 \tBUY             CLOSE           162605.SZSE \tprice: 2.2050 \tamount: 12600\n", "2024-01-31 16:49:28,124 - FundStrategy - INFO \t2023-11-14 09:40:00 \tBUY             OPEN            162605.SZSE \tprice: 2.2110 \tamount: 12600\n", "2024-01-31 16:49:28,133 - FundStrategy - INFO \t2023-11-14 13:10:00 \tSELL            CLOSE           162605.SZSE \tprice: 2.1800 \tamount: 12600\n", "2024-01-31 16:49:28,238 - FundStrategy - INFO \t2023-11-27 09:40:00 \tSELL            OPEN            162605.SZSE \tprice: 2.1510 \tamount: 12600\n", "2024-01-31 16:49:28,487 - FundStrategy - INFO \t2023-12-28 13:45:00 \tBUY             CLOSE           162605.SZSE \tprice: 2.0560 \tamount: 12600\n", "2024-01-31 16:49:28,503 - FundStrategy - INFO \t2024-01-02 09:55:00 \tSELL            OPEN            162605.SZSE \tprice: 2.0090 \tamount: 12600\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1750x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最终权益：203,309.44,  年化单利：3.31%,  年化复利：3.34%\n", "\n", "最大权益回撤：1,881.82,  最大损益回撤：846.62\n", "\n", "score: 0.6565,  夏普比率：1.74,  卡玛比率：7.82\n", "\n", "手续费：-117.76,  滑点成本：-214.20\n", "\n", "总盈利：1,022.57,  多：-887.73,  空：1,910.30\n", "\n", "总次数：8,  多：4,  空：4\n", "\n", "日胜率：57.58%,  胜率：37.50%,  多：25.00%,  空：50.00%\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>total_profit</th>\n", "      <th>number_of_trade</th>\n", "      <th>number_of_win</th>\n", "      <th>winning_percentage</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>position_type</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">162605.SZSE</th>\n", "      <th>LONG</th>\n", "      <td>-887.7330</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>1910.3049</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>0.50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           total_profit  number_of_trade  number_of_win  \\\n", "symbol      position_type                                                 \n", "162605.SZSE LONG              -887.7330                4              1   \n", "            SHORT             1910.3049                4              2   \n", "\n", "                           winning_percentage  \n", "symbol      position_type                      \n", "162605.SZSE LONG                         0.25  \n", "            SHORT                        0.50  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-31 16:49:29      Take time: 0 hours 0 mins 4 s\n"]}, {"data": {"text/plain": ["[{'overview': {'final equity': 203309.44355000483,\n", "   'annualized simple interest': 0.0330944355000482,\n", "   'annualized compound interest': 0.03336824591531484,\n", "   'maximum drawdown': 1881.8162999999477,\n", "   'maximum drawdown with no position': 846.6160499999933,\n", "   'number of trades': 8,\n", "   'win rate': 0.375,\n", "   'daily win rate': 0.5757575757575758},\n", "  'trade_log':                    c_dt                 m_dt        offset       price_type  \\\n", "  0   2023-08-17 10:35:00  2023-08-17 10:35:00   OffSet.OPEN  PriceType.LIMIT   \n", "  1   2023-08-18 15:00:00  2023-08-18 15:00:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  2   2023-08-28 09:35:00  2023-08-28 09:35:00   OffSet.OPEN  PriceType.LIMIT   \n", "  3   2023-09-04 09:35:00  2023-09-04 09:35:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  4   2023-09-04 10:15:00  2023-09-04 10:15:00   OffSet.OPEN  PriceType.LIMIT   \n", "  5   2023-09-07 09:35:00  2023-09-07 09:35:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  6   2023-09-07 10:55:00  2023-09-07 10:55:00   OffSet.OPEN  PriceType.LIMIT   \n", "  7   2023-10-27 13:20:00  2023-10-27 13:20:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  8   2023-10-27 13:30:00  2023-10-27 13:30:00   OffSet.OPEN  PriceType.LIMIT   \n", "  9   2023-11-13 10:05:00  2023-11-13 10:05:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  10  2023-11-13 11:00:00  2023-11-13 11:00:00   OffSet.OPEN  PriceType.LIMIT   \n", "  11  2023-11-14 09:35:00  2023-11-14 09:35:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  12  2023-11-14 09:40:00  2023-11-14 09:40:00   OffSet.OPEN  PriceType.LIMIT   \n", "  13  2023-11-14 13:10:00  2023-11-14 13:10:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  14  2023-11-27 09:40:00  2023-11-27 09:40:00   OffSet.OPEN  PriceType.LIMIT   \n", "  15  2023-12-28 13:45:00  2023-12-28 13:45:00  OffSet.CLOSE  PriceType.LIMIT   \n", "  16  2024-01-02 09:55:00  2024-01-02 09:55:00   OffSet.OPEN  PriceType.LIMIT   \n", "  \n", "      price                status       symbol is_credit       direction  \\\n", "  0   2.281  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  1   2.249  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  2   2.279  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  3   2.288  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  4   2.301  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  5   2.264  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  6   2.254  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  7    2.15  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  8    2.15  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  9   2.184  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  10  2.171  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  11  2.205  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  12  2.211  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  13   2.18  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  14  2.151  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  15  2.056  OrderStatus.FINISHED  162605.SZSE     False   Direction.BUY   \n", "  16  2.009  OrderStatus.FINISHED  162605.SZSE     False  Direction.SELL   \n", "  \n", "     trade_price account trade_amount   amount  transaction_fee  slippage_cost  \\\n", "  0        2.281              12600.0  12600.0         -7.18515          -12.6   \n", "  1        2.249              12600.0  12600.0         -7.08435          -12.6   \n", "  2        2.279              12600.0  12600.0         -7.17885          -12.6   \n", "  3        2.288              12600.0  12600.0         -7.20720          -12.6   \n", "  4        2.301              12600.0  12600.0         -7.24815          -12.6   \n", "  5        2.264              12600.0  12600.0         -7.13160          -12.6   \n", "  6        2.254              12600.0  12600.0         -7.10010          -12.6   \n", "  7         2.15              12600.0  12600.0         -6.77250          -12.6   \n", "  8         2.15              12600.0  12600.0         -6.77250          -12.6   \n", "  9        2.184              12600.0  12600.0         -6.87960          -12.6   \n", "  10       2.171              12600.0  12600.0         -6.83865          -12.6   \n", "  11       2.205              12600.0  12600.0         -6.94575          -12.6   \n", "  12       2.211              12600.0  12600.0         -6.96465          -12.6   \n", "  13        2.18              12600.0  12600.0         -6.86700          -12.6   \n", "  14       2.151              12600.0  12600.0         -6.77565          -12.6   \n", "  15       2.056              12600.0  12600.0         -6.47640          -12.6   \n", "  16       2.009              12600.0  12600.0         -6.32835          -12.6   \n", "  \n", "      delta_holding        value position_type     hold      profit  \n", "  0         12600.0 -28747.78515          LONG  12600.0         NaN  \n", "  1        -12600.0  28330.31565          LONG      0.0  -417.46950  \n", "  2        -12600.0  28708.22115         SHORT -12600.0         NaN  \n", "  3         12600.0 -28836.00720         SHORT      0.0  -127.78605  \n", "  4         12600.0 -28999.84815          LONG  12600.0         NaN  \n", "  5        -12600.0  28519.26840          LONG      0.0  -480.57975  \n", "  6        -12600.0  28393.29990         SHORT -12600.0         NaN  \n", "  7         12600.0 -27096.77250         SHORT      0.0  1296.52740  \n", "  8         12600.0 -27096.77250          LONG  12600.0         NaN  \n", "  9        -12600.0  27511.52040          LONG      0.0   414.74790  \n", "  10       -12600.0  27347.76135         SHORT -12600.0         NaN  \n", "  11        12600.0 -27789.94575         SHORT      0.0  -442.18440  \n", "  12        12600.0 -27865.56465          LONG  12600.0         NaN  \n", "  13       -12600.0  27461.13300          LONG      0.0  -404.43165  \n", "  14       -12600.0  27095.82435         SHORT -12600.0         NaN  \n", "  15        12600.0 -25912.07640         SHORT      0.0  1183.74795  \n", "  16       -12600.0  25307.07165         SHORT -12600.0         NaN  }]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["gl_event_drive_engine.set_env(StrategyMode.BACKTEST, \"20230101 0930\")\n", "strategy = FundStrategy(**{\"p1\": 140, \"p2\": 60, \"p3\": 120, \"p4\": 4, \"xn2\": 13, \"xn3\": 50, \"xn4\": 18, \"xn5\": 26, \"symbol\": \"162605.SZSE\", \"amount\": 12600})\n", "strategy.register_trader(Trader(200000, gl_virtual_exchange))\n", "gl_event_drive_engine.register_strategy(strategy)\n", "gl_event_drive_engine.run()"]}, {"cell_type": "code", "execution_count": null, "id": "f99ddaea-7fca-4a9e-8937-b2f2103ebf7d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
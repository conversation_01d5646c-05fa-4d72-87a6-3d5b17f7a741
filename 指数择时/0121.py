import pathlib
import pickle
from itertools import product

import aichemy.project as yf_prj
import numpy as np
import pandas as pd
from aichemy.factor_analyse.analyse import FastBacktest
from aichemy.factor_analyse.fast_method import calc_normal_ic
from aichemy.ml.experiments_idx import *
from loguru import logger
from tqdm import tqdm
import torch


class NormIC(torch.nn.Module):
    def __init__(self):
        super().__init__()

    def forward(self, y_hat: torch.Tensor, y: torch.Tensor):
        y_hat = y_hat.flatten()
        y = y.flatten()
        t1 = torch.mean(y_hat * y)
        t2 = torch.sqrt(torch.mean(y_hat**2) * torch.mean(y**2))
        return -t1 / t2


class ExpTimeMixer2(ExpTimeMixer1):
    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        y_hat = y_hat[:, -1, 0].flatten().astype(np.float64)
        y = y[:, -1, 0].flatten().astype(np.float64)
        mask = np.isfinite(y) & np.isfinite(y_hat)
        y = y[mask]
        y_hat = y_hat[mask]
        ret = {flag: {"IC": calc_normal_ic(y_hat, y)}}
        logger.info(ret)
        return ret

    def select_criterion(self):
        return NormIC()

    @staticmethod
    def decide_metrics(loss, evl):
        return evl["IC"]


class ExpTimeMixer3(ExpTimeMixer1):
    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        y_hat = y_hat[:, -1, 0].flatten().astype(np.float64)
        y = y[:, -1, 0].flatten().astype(np.float64)
        mask = np.isfinite(y) & np.isfinite(y_hat)
        y = y[mask]
        y_hat = y_hat[mask]
        ret = {flag: {"IC": np.corrcoef(y, y_hat)[0, 1]}}
        logger.info(ret)
        return ret

    @staticmethod
    def decide_metrics(loss, evl):
        return evl["IC"]


logger.add("log", filter=lambda record: "begin" in record["message"] or "Finally" in record["message"])

with open("./features.pkl", "rb") as f:
    gl_data = pickle.load(f)

try:
    # cols = ["000001.SH", "399001.SZ"]
    data = {}
    for i in gl_data.keys():
        data[i] = gl_data[i].copy()  # .reindex(columns=cols)

    label_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    fret_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    bret_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    bret_rolling_mean = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    bret_rolling_std = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])

    length = 128
    num = 128
    interval = int(length / num)

    for i in tqdm(label_df.columns):
        tmp = data["[x]close"][i].dropna()

        br = tmp / tmp.shift(interval) - 1
        fr = tmp.shift(-interval) / tmp - 1

        tmp1 = pd.Series(dtype=np.float64).reindex(index=br.index)
        tmp2 = pd.Series(dtype=np.float64).reindex(index=br.index)
        for j in range(interval):
            t = br.iloc[j::interval]
            tmp1.loc[t.index] = t.rolling(num).mean()
            tmp2.loc[t.index] = t.rolling(num).std()

        # label_df.loc[fr.index, i] = fr
        label_df.loc[fr.index, i] = ((fr - tmp1) / tmp2).loc[fr.index]
        bret_rolling_mean.loc[tmp1.index, i] = tmp1
        bret_rolling_std.loc[tmp2.index, i] = tmp2
        fret_df.loc[fr.index, i] = fr
        bret_df.loc[br.index, i] = br

    data["[y]"] = label_df
    t1, t2, t3 = "20170101", "20231231", "20241231"

    yf_prj.dump(
        version="test",
        g=globals(),
        t1=t1,
        t2=t2,
        t3=t3,
        kwargs={
            "num_steps": length,
            "leading_days": 500,
            "idx": True,
            "shuffle": False,
            "drop_threshold_nan_ratio": 0.2,
            "non_training_size": 0.1,
            "cs_x_scaling_method": "none",
            "cs_y_scaling_method": "none",
            "cs_scaling_fillna": False,
            "x_scaling_method": "none",
            "y_scaling_method": "none",
            "scaling_fillna": False,
        },
    )

    log_id = logger.add(pathlib.Path(path, "log"))
    logger.info(f"Exp {project_id} begin: start: {t1}, mid: {t2}, end: {t3}")

    r_data = yf_prj.process_train_data(data, t1, t2, path=path, **kwargs, exchange="SHFE")

    exp = ExpTimeMixer2(path)

    d = exp.construct_index_dataset(*r_data, test_size=0.0, dump=False)
    exp.load_data(*d)
    _ = exp.train(enable_log=True, progress_log=0, tensorboard_comment=project_id)

    vali = yf_prj.predict_apply_data(exp, data, t1, t2, path=path, enable_log=False, **kwargs)
    test = yf_prj.predict_apply_data(exp, data, t2, "2024-12-31", path=path, enable_log=False, **kwargs)
    # pickle.dump(test, open(pathlib.Path(path, "test.pkl"), "wb"))
    # pickle.dump(vali, open(pathlib.Path(path, "vali.pkl"), "wb"))
except Exception as e:
    logger.exception(e)

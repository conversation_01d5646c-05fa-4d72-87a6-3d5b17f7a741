import pathlib
import pickle
from itertools import product
import uuid

from optuna import create_study
import optuna

import aichemy.project as yf_prj
import numpy as np
import pandas as pd
from aichemy.factor_analyse.analyse import FastBacktest
from aichemy.factor_analyse.fast_method import calc_normal_ic
from aichemy.ml.experiments_idx import *
from loguru import logger
from tqdm import tqdm
import torch


class NormIC(torch.nn.Module):
    def __init__(self):
        super().__init__()

    def forward(self, y_hat: torch.Tensor, y: torch.Tensor):
        y_hat = y_hat.flatten()
        y = y.flatten()
        t1 = torch.mean(y_hat * y)
        t2 = torch.sqrt(torch.mean(y_hat**2))
        # t2 = torch.sqrt(torch.mean(y_hat**2) * torch.mean(y**2))
        # return -t1
        return -t1 / t2


class ExpTimeMixer2(ExpTimeMixer1):
    def evaluate(self, y_hat: np.ndarray, y: np.n<PERSON><PERSON>, flag):
        y_hat = y_hat[:, -1, 0].flatten().astype(np.float64)
        y = y[:, -1, 0].flatten().astype(np.float64)
        mask = np.isfinite(y) & np.isfinite(y_hat)
        y = y[mask]
        y_hat = y_hat[mask]
        ret = {flag: {"IC": calc_normal_ic(y_hat, y)}}
        logger.info(ret)
        return ret

    # def select_criterion(self):
    #     return NormIC()

    @staticmethod
    def decide_metrics(loss, evl):
        return evl["IC"]


class ExpTimeMixer3(ExpTimeMixer1):
    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        y_hat = y_hat[:, -1, 0].flatten().astype(np.float64)
        y = y[:, -1, 0].flatten().astype(np.float64)
        mask = np.isfinite(y) & np.isfinite(y_hat)
        y = y[mask]
        y_hat = y_hat[mask]
        ret = {flag: {"IC": np.corrcoef(y, y_hat)[0, 1]}}
        logger.info(ret)
        return ret

    @staticmethod
    def decide_metrics(loss, evl):
        return evl["IC"]


logger.add("log", filter=lambda record: "begin" in record["message"] or "Finally" in record["message"])

with open("./features_of_open_to_open.pkl", "rb") as f:
    gl_data = pickle.load(f)

try:
    # cols = ["000001.SH", "399001.SZ"]
    data = {}
    for i in gl_data.keys():
        data[i] = gl_data[i].copy()  # .reindex(columns=cols)

    label_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    fret_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    bret_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    bret_rolling_mean = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
    bret_rolling_std = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])

    # length = 16
    steps = 128
    num = 128
    interval = 1
    # interval = int(length / num)

    for i in tqdm(label_df.columns):
        tmp = data["[x]open"][i].dropna()

        br = tmp.shift(-1) / tmp.shift(interval - 1) - 1
        fr = tmp.shift(-interval - 1) / tmp.shift(-1) - 1

        tmp1 = pd.Series(dtype=np.float64).reindex(index=br.index)
        tmp2 = pd.Series(dtype=np.float64).reindex(index=br.index)
        for j in range(interval):
            t = br.iloc[j::interval]
            tmp1.loc[t.index] = t.rolling(num).mean()
            tmp2.loc[t.index] = t.rolling(num).std()

        # label_df.loc[fr.index, i] = fr
        label_df.loc[fr.index, i] = ((fr - tmp1) / tmp2).loc[fr.index]
        bret_rolling_mean.loc[tmp1.index, i] = tmp1
        bret_rolling_std.loc[tmp2.index, i] = tmp2
        fret_df.loc[fr.index, i] = fr
        bret_df.loc[br.index, i] = br

    data["[y]"] = label_df
    t1, t2, t3 = "20170101", "20231231", "20251231"

    yf_prj.dump(
        version="test",
        g=globals(),
        t1=t1,
        t2=t2,
        t3=t3,
        kwargs={
            "num_steps": steps,
            "leading_days": 500,
            "idx": True,
            "shuffle": False,
            "drop_threshold_nan_ratio": 0.2,
            "non_training_size": 0.1,
            "cs_x_scaling_method": "none",
            "cs_y_scaling_method": "none",
            "cs_scaling_fillna": False,
            "x_scaling_method": "none",
            "y_scaling_method": "none",
            "scaling_fillna": False,
        },
    )

    log_id = logger.add(pathlib.Path(path, "log"))
    logger.info(f"Exp {project_id} begin: start: {t1}, mid: {t2}, end: {t3}")

    r_data = yf_prj.process_train_data(data, t1, t2, path=path, **kwargs, exchange="SHFE")

    exp = ExpTimeMixer2(path)

    d = exp.construct_index_dataset(*r_data, test_size=0.0, dump=True)

    def objective(trial):
        args = {
            "lr": trial.suggest_float("lr", 5e-5, 1e-2, log=True),
            "criterion": trial.suggest_categorical("criterion", ["mae", "mse"]),
            # "beta1": trial.suggest_float("beta1", 0.8, 0.99),
            # "beta2": trial.suggest_float("beta2", 0.9, 0.999),
            "weight_decay": trial.suggest_float("weight_decay", 5e-6, 5e-4, log=True),
            "symbol_batch_norm": trial.suggest_categorical("symbol_batch_norm", [True]),
            # "factor": trial.suggest_float("factor", 0.2, 0.8, step=0.1),
            # "patience": trial.suggest_int("patience", 1, 4, step=1),
        }

        # exp = ExpLinear(path)
        exp_id = str(uuid.uuid4())
        logger.info(f"Exp {exp_id} begin")
        tmp_path = str(pathlib.Path(path, exp_id))

        exp = ExpTimeMixer2(tmp_path)
        exp.update_args(args)
        # exp.update_args({"train_epochs": 10, "batch_size": 512, "dropout": 0.0})
        exp.load_data(data_load_mode=2, test_size=0.0, dataset_path=path)
        exp.train(
            enable_log=0,
            progress_log=0,
            trial=trial,
        )
        res = exp.matrics_tracker.best_vali_metrics
        return res

    study = create_study(
        direction="maximize",
        pruner=optuna.pruners.MedianPruner(n_startup_trials=10),
        storage="sqlite:///db.sqlite3",
        # storage="postgresql+psycopg2://postgres:123456@192.168.1.202:5432/optuna_dashboard",
        study_name="tuning",
        load_if_exists=True,
    )

    # 开始优化
    study.optimize(objective, n_trials=50, show_progress_bar=True)
except Exception as e:
    logger.exception(e)

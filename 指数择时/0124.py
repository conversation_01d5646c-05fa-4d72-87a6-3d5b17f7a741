import pathlib
import pickle
from itertools import product

import aichemy.project as yf_prj
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import torch
from aichemy.factor_analyse.analyse import (
    FactorAnalyse,
    FastBacktest,
    align_df,
    binary_signal_transfer,
)
from aichemy.factor_analyse.fast_method import calc_normal_ic
from aichemy.ml.experiments_idx import *
from IPython.display import display
from loguru import logger
from torch import nn
from tqdm import tqdm


class SimpleLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.25, scale=4):
        super(SimpleLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.fc1 = nn.Sequential(nn.Linear(input_size, hidden_size), nn.GELU())
        # LSTM层
        self.lstm = nn.LSTM(hidden_size, hidden_size, num_layers, batch_first=True, dropout=dropout)

        # 全连接层
        self.fc2 = nn.Sequential()
        while hidden_size > max(scale, output_size):
            self.fc2.append(nn.Linear(hidden_size, hidden_size // scale))
            hidden_size = hidden_size // scale
            # self.fc2.append(nn.GELU())
        self.fc2.append(nn.Linear(hidden_size, output_size))

    def forward(self, x):
        x = self.fc1(x)
        # 初始化隐藏状态和单元状态
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        # 前向传播LSTM
        out, _ = self.lstm(x, (h0, c0))  # out: tensor of shape (batch_size, seq_length, hidden_size)

        # 取最后一个时刻的状态作为输出

        out = self.fc2(out[:, [-1], :])  # 只需要最后一个时刻的输出

        return out


class NormIC(torch.nn.Module):
    def __init__(self):
        super().__init__()

    def forward(self, y_hat: torch.Tensor, y: torch.Tensor):
        y_hat = y_hat.flatten()
        y = y.flatten()
        t1 = torch.mean(y_hat * y)
        t2 = torch.sqrt(torch.mean(y_hat**2))
        # t2 = torch.sqrt(torch.mean(y_hat**2) * torch.mean(y**2))
        # return -t1
        return -t1 / t2


class ExpTimeMixer2(ExpTSPrd):
    def build_model(self, input_size):
        net = SimpleLSTM(
            input_size[-1],
            self.args.get("hidden_size", 16),
            1,
            1,
            dropout=self.args.get("dropout", 0.25),
            scale=self.args.get("scale", 4),
        )
        return net

    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        y_hat = y_hat[:, -1, 0].flatten().astype(np.float64)
        y = y[:, -1, 0].flatten().astype(np.float64)
        mask = np.isfinite(y) & np.isfinite(y_hat)
        y = y[mask]
        y_hat = y_hat[mask]
        ret = {flag: {"IC": calc_normal_ic(y_hat, y)}}
        logger.info(ret)
        return ret

    # def select_criterion(self):
    #     return NormIC()

    # @staticmethod
    # def decide_metrics(loss, evl):
    #     return evl["IC"]


class ExpTimeMixer3(ExpTimeMixer1):
    def evaluate(self, y_hat: np.ndarray, y: np.ndarray, flag):
        y_hat = y_hat[:, -1, 0].flatten().astype(np.float64)
        y = y[:, -1, 0].flatten().astype(np.float64)
        mask = np.isfinite(y) & np.isfinite(y_hat)
        y = y[mask]
        y_hat = y_hat[mask]
        ret = {flag: {"IC": np.corrcoef(y, y_hat)[0, 1]}}
        logger.info(ret)
        return ret

    @staticmethod
    def decide_metrics(loss, evl):
        return evl["IC"]


logger.add("log", filter=lambda record: "begin" in record["message"] or "Finally" in record["message"])

with open("./features_of_open_to_open.pkl", "rb") as f:
    gl_data = pickle.load(f)


for num in [16, 64, 128, 256, 512, 1024]:
    try:
        # cols = ["000001.SH", "399001.SZ"]
        data = {}
        for i in gl_data.keys():
            data[i] = gl_data[i].copy()  # .reindex(columns=cols)

        label_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
        fret_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
        bret_df = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
        bret_rolling_mean = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])
        bret_rolling_std = pd.DataFrame(dtype=np.float64).reindex_like(data["[x]close"])

        # length = 16
        steps = 128
        interval = 1
        # interval = int(length / num)

        for i in tqdm(label_df.columns):
            tmp = data["[x]open"][i].dropna()

            br = tmp.shift(-1) / tmp.shift(interval - 1) - 1
            fr = tmp.shift(-interval - 1) / tmp.shift(-1) - 1

            tmp1 = pd.Series(dtype=np.float64).reindex(index=br.index)
            tmp2 = pd.Series(dtype=np.float64).reindex(index=br.index)
            for j in range(interval):
                t = br.iloc[j::interval]
                tmp1.loc[t.index] = t.rolling(num).mean()
                tmp2.loc[t.index] = t.rolling(num).std()

            label_df.loc[fr.index, i] = fr
            # label_df.loc[fr.index, i] = ((fr - tmp1) / tmp2).loc[fr.index]
            bret_rolling_mean.loc[tmp1.index, i] = tmp1
            bret_rolling_std.loc[tmp2.index, i] = tmp2
            fret_df.loc[fr.index, i] = fr
            bret_df.loc[br.index, i] = br

        data["[y]"] = label_df
        t1, t2, t3 = "20170101", "20221231", "20231231"

        yf_prj.dump(
            version="test",
            g=globals(),
            t1=t1,
            t2=t2,
            t3=t3,
            kwargs={
                "num_steps": steps,
                "leading_days": 500,
                "idx": True,
                "shuffle": False,
                "drop_threshold_nan_ratio": 0.2,
                "non_training_size": 0.1,
                "cs_x_scaling_method": "none",
                "cs_y_scaling_method": "none",
                "cs_scaling_fillna": False,
                "x_scaling_method": "none",
                "y_scaling_method": "none",
                "scaling_fillna": False,
            },
        )

        log_id = logger.add(pathlib.Path(path, "log"))
        logger.info(f"Exp {project_id} begin: start: {t1}, mid: {t2}, end: {t3}")

        r_data = yf_prj.process_train_data(data, t1, t2, path=path, **kwargs, exchange="SHFE")

        # exp = ExpAngosformer1(path)
        exp = ExpTimeMixer2(path)

        d = exp.construct_index_dataset(*r_data, test_size=0.0, dump=False)
        # d = exp.construct_index_dataset(*[r_data[0],r_data[0],r_data[2],r_data[3]], test_size=0.0, dump=False)
        exp.load_data(*d)
        _ = exp.train(enable_log=0, progress_log=0, tensorboard_comment=project_id)

        vali = yf_prj.predict_apply_data(exp, data, "2022-01-01", t2, path=path, enable_log=False, **kwargs)
        test = yf_prj.predict_apply_data(exp, data, t2, "2023-12-31", path=path, enable_log=False, **kwargs)
        # pickle.dump(test, open(pathlib.Path(path, "test.pkl"), "wb"))
        # pickle.dump(vali, open(pathlib.Path(path, "vali.pkl"), "wb"))

        print(num)
        for pred in [vali, test]:
            pred = (
                pred * bret_rolling_std.loc[pred.index, pred.columns] + bret_rolling_mean.loc[pred.index, pred.columns]
            )

            fa = FactorAnalyse(pred, label_df.loc[pred.index, pred.columns])
            display(fa.ic(0, 0, False, 1, 0).stats)
            display(fa.rank_ic(0, 0, False, 1, 0).stats)

            a = fret_df.loc[pred.index, pred.columns].values.flatten()
            b = pred.values.flatten()
            tmp = np.isfinite(a) & np.isfinite(b)
            a = a[tmp]
            b = b[tmp]

            print(torch.nn.MSELoss()(torch.tensor(a * 100), torch.tensor(b * 100)))
    except Exception as e:
        logger.exception(e)

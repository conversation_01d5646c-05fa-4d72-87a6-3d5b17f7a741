import argparse
import datetime
import json
import socket
import uuid
from typing import Literal

import datetype
import numpy as np
import pandas as pd
import redis
from loguru import logger
from qnt_research.api import get_price
from qnt_research.communicator.instructor import RabbitMQInstructorForEDA
from qnt_research.communicator.reporter import RabbitMQReporter
from qnt_research.engine import gl_event_drive_engine
from qnt_research.strategy.base import CustomStrategy
from qnt_research.timer import Timer
from qnt_research.trade_api import TradeAPI
from qnt_research.trader.trader import Trader
from qnt_research.virtual_exchange import gl_virtual_exchange
from qnt_utils.config import get_config
from qnt_utils.ctoolset import generate_datetime
from qnt_utils.enums import BasicData, CommunicatorMessageType, Direction, OrderStatus, StatusCode, StrategyMode
from qnt_utils.fields import BAR_FIELDS
from qnt_utils.label import QSymbol
from qnt_utils.logger import WebhookHandlerForLoguru

"""
v1.1.0 - 支持平仓监控
v1.1.1 - 修复平仓监控时平仓失败的问题
v1.1.2 - 修复平仓监控会重复下单的问题
v1.1.3 - 修复平仓监控任务下单数量为0的问题
v1.2.0 - 支持Task初始化时能计算历史行情;涨幅太大不开仓;支持信用和普通账户
v1.2.1 - 完善日志记录
v1.3.0 - server中已经删掉的任务，controller面板显示的任务信息中还会有；多仓阴线、空仓阳线才平仓；开盘前10分钟止盈放宽2个点
v1.4.0 - 支持从redis获取当前盘口，根据盘口进行下单；接近涨跌停时提前平仓
v1.4.1 - 崩溃时打印日志；任务创建流程优化
v1.5.0 - 支持指定账户
v1.6.0 - 修改下跌反弹阈值的判定
v1.7.0 - 收盘那个K线不做交易，只更新数据；Task1只运行1天就过期，除非有持仓
v1.8.0 - 删除任务时支持立即平仓
v1.9.0 - 支持半仓主动止盈；精确下单数量；支持增加任务时立刻开仓
"""
__version__ = "1.9.3.0814"

args = argparse.ArgumentParser()
args.add_argument("--is_real_trade", "-r", help="是否实盘交易", action="store_true")
args.add_argument("--account", "-a", help="账户", type=str)
args.add_argument("--is_credit", "-c", help="是否使用信用账户", action="store_true")
args = args.parse_args()

hostname = socket.gethostname()

logger.add(
    WebhookHandlerForLoguru(
        "https://oapi.dingtalk.com/robot/send?access_token=78c845d3da845986d6c75063a6a38371fd08b15d5d3d75086110a12ee136a664",
        "SEC88e0357a90376df97f317a31735856c2e0f88817197d4f53f482afbc6b8f7899",
    ),
    level="WEBHOOK",
    format=f"<green>{{time:YYYY-MM-DD HH:mm:ss.SSS}}</green> | {hostname} - <level>{{message}}</level>",
    enqueue=True,
)
logger.add(
    f"logs/easy_strategy_{hostname}_{{time:YYYY-MM-DD}}.log",
    rotation="00:00",  # Create a new file at midnight
    retention="7 days",  # Keep logs for 7 days
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    encoding="utf-8",
)


def adjust_shares(max_shares: float, symbol: str) -> int:
    """计算可买入的股票数量

    根据可用金额、股票代码和股票价格计算可以买入的股票数量。
    如果计算出的数量不满足最小申报数量要求，则返回0。

    Args:
        max_shares (float): 可以买入的股票数量的上限
        symbol (str): 股票代码

    Returns:
        int: 可以买入的股票数量
    """
    # 根据股票代码判断所属市场
    if symbol.startswith("68"):  # 科创板
        minimum_size = 200
        step_size = 1
    elif symbol.startswith("8") or symbol.startswith("920"):  # 北交所
        minimum_size = 100
        step_size = 1
    else:  # 主板
        minimum_size = 100
        step_size = 100

    # 确保可以买入的数量是step_size的整数倍
    buyable_shares = (max_shares // step_size) * step_size

    # 如果可以买入的数量不满足最小申报数量要求，则返回0
    if buyable_shares < minimum_size:
        return 0

    return int(buyable_shares)


def calculate_shares(money: float, symbol: str, price: float) -> int:
    """计算可买入的股票数量

    根据可用金额、股票代码和股票价格计算可以买入的股票数量。
    如果计算出的数量不满足最小申报数量要求，则返回0。

    Args:
        money (float): 可用金额
        symbol (str): 股票代码
        price (float): 股票价格

    Returns:
        int: 可以买入的股票数量
    """
    # 计算可以买入的最大数量
    max_shares = money / price
    return adjust_shares(max_shares, symbol)


class GetPrice:
    _redis_client = redis.Redis(host=get_config()["redis"]["host"], port=get_config()["redis"]["port"])

    @classmethod
    def get_last_price(cls, symbol: str) -> float:
        try:
            snapshot = json.loads(cls._redis_client.get(f"{symbol}-SNAPSHOT"))  # type: ignore
            return snapshot["new_price"]
        except Exception:
            return np.nan

    @classmethod
    def get_buy_price(cls, symbol: str, last_price: float) -> float:
        try:
            snapshot = json.loads(cls._redis_client.get(f"{symbol}-SNAPSHOT"))  # type: ignore
            a = np.array(snapshot["askorder_price"])
            a = a[a > 0]
            if len(a) > 0:
                v = a[min(2, len(a) - 1)]
            else:
                v = np.inf
            return round(min(v, snapshot["new_price"] * 1.017, snapshot["uplimit_price"]), 2)
        except Exception:
            return round(last_price * 1.005, 2)

    @classmethod
    def get_sell_price(cls, symbol: str, last_price: float) -> float:
        try:
            snapshot = json.loads(cls._redis_client.get(f"{symbol}-SNAPSHOT"))  # type: ignore
            a = np.array(snapshot["bidorder_price"])
            a = a[a > 0]
            if len(a) > 0:
                v = a[min(2, len(a) - 1)]
            else:
                v = -np.inf
            return round(max(v, snapshot["new_price"] * 0.983, snapshot["downlimit_price"]), 2)
        except Exception:
            return round(last_price * 0.995, 2)

    @classmethod
    def get_uplimit_price(cls, symbol: str) -> float:
        try:
            snapshot = json.loads(cls._redis_client.get(f"{symbol}-SNAPSHOT"))  # type: ignore
            uplimit_price = snapshot["uplimit_price"]
            if uplimit_price <= 0:
                raise
            return uplimit_price
        except Exception:
            return np.nan

    @classmethod
    def get_downlimit_price(cls, symbol: str) -> float:
        try:
            snapshot = json.loads(cls._redis_client.get(f"{symbol}-SNAPSHOT"))  # type: ignore
            downlimit_price = snapshot["downlimit_price"]
            if downlimit_price <= 0:
                raise
            return downlimit_price
        except Exception:
            return np.nan

    @classmethod
    def get_pre_price(cls, symbol: str) -> float:
        try:
            snapshot = json.loads(cls._redis_client.get(f"{symbol}-SNAPSHOT"))  # type: ignore
            pre_price = snapshot["pre_price"]
            if pre_price <= 0:
                raise
            return pre_price
        except Exception:
            return np.nan


class Task:
    def __init__(
        self,
        money: float,
        symbol: str,
        is_based_on_his_signals: bool,
        is_credit: bool,
        is_financing_first: bool,
        trade_direction: Literal[-1, 0, 1],
        is_intraday_trade: bool,
    ):
        self.task_id = str(uuid.uuid1())
        """任务ID"""

        # 基本信息
        self.money = money
        """分配资金"""
        self.symbol = symbol
        """标的"""
        self.is_based_on_his_signals = is_based_on_his_signals
        """是否基于历史信号"""
        self.is_credit = is_credit
        """是否使用信用账户"""
        self.is_financing_first = is_financing_first
        """是否融资优先"""
        self.trade_direction = trade_direction
        """0 - 多空双边;1 - 只做多;-1 - 只做空"""
        self.is_intraday_trade = is_intraday_trade
        """是否允许日内交易"""

        # 任务状态
        self.invalid: bool = False
        """是否无效, True & !is_wrong - 可以删掉"""
        self.is_wrong: bool = False
        """是否错误，报错的不自动删掉，要手动来删"""

        # 持仓信息
        self.ideal_amount: float = 0
        """理论持仓，分正负"""
        self.amount: float = 0
        """实际的持仓数量，分正负"""
        self.cost_basis: float = np.nan
        """持仓成本价"""

        # 时间
        self.trade_date: None | datetime.datetime = None
        """开仓时间"""
        self.current_dt: datetype.NaiveDateTime = datetime.datetime.min  # type: ignore
        """任务运行到的时间"""
        self.ctime: datetime.datetime = datetime.datetime.now(tz=datetime.timezone(datetime.timedelta(hours=8)))
        """任务创建时间"""
        self.running_date: float = float(self.ctime.strftime("%Y%m%d"))
        if self.ctime.time() >= datetime.time(15, 0):
            self.running_date += 0.5
        self.running_day_count = 0
        """任务持续运行天数，如果盘中创建任务，当天为1，如果盘后创建任务，次日为1"""

    def on_data(self, data):
        if data["date"] >= self.running_date:
            self.running_day_count += 1
            self.running_date = data["date"] + 0.5
        self.current_dt = max(self.current_dt, generate_datetime(data["date"], data["time"]))

    @property
    def info(self):
        return {
            "task_id": self.task_id,
            "version": __version__,
            "task_type": self.__class__.__name__,
            "params": {
                "money": self.money,
                "symbol": self.symbol,
                "is_based_on_his_signals": self.is_based_on_his_signals,
                "is_credit": self.is_credit,
                "is_financing_first": self.is_financing_first,
                "trade_direction": self.trade_direction,
                "is_intraday_trade": self.is_intraday_trade,
            },
            "status": {
                "is_wrong": self.is_wrong,
                "invalid": self.invalid,
                "ideal_amount": self.ideal_amount,
                "trade_date": self.trade_date
                if self.trade_date is None
                else self.trade_date.strftime("%Y-%m-%d %H:%M:%S"),
                "amount": self.amount,
                "cost_basis": self.cost_basis,
                "last_update_time": self.current_dt.strftime("%Y-%m-%d %H:%M:%S"),
                "ctime": self.ctime.strftime("%Y-%m-%d %H:%M:%S"),
                "running_day_count": self.running_day_count,
            },
        }

    def modify_status(self, invalid=None, is_wrong=None):
        """
        修改状态:
        * invalid - True, is_wrong - True: 任务无效，而且运行过程出错，需要手动处理
        * invalid - True, is_wrong - False: 任务无效，但是运行过程没有出错，后面会自动删掉
        * invalid - False, is_wrong - False: 任务有效，状态正常
        """
        self.invalid = invalid or self.invalid
        self.is_wrong = is_wrong or self.is_wrong

    def modify_position(self, cost_basis: float | None, amount: float | None, ideal_amount: float | None = None):
        """委托了结之后修改持仓数据，包括持仓成本价、实际持仓数量和理论持仓数量

        Args:
            cost_basis (float | None): 成交价
            amount (float | None): 成交数量， 正数
            ideal_amount (float | None, optional): 理论持仓，正数. Defaults to None.
        """
        if cost_basis is not None:
            self.cost_basis = cost_basis
        if amount is not None:
            self.amount = int(amount * np.sign(self.ideal_amount))
        if ideal_amount is not None:
            self.ideal_amount = int(ideal_amount * np.sign(self.ideal_amount))

    def close(self):
        ret = None
        if self.is_intraday_trade or (
            self.trade_date is not None and datetime.datetime.now().date() > self.trade_date.date()
        ):
            if self.ideal_amount > 0 and self.amount > 0:
                self.ideal_amount = 0
                ret = {
                    "symbol": self.symbol,
                    "direction": "SELL",
                    "offset": "CLOSE",
                    "price": GetPrice.get_sell_price(self.symbol, np.nan),
                    "amount": self.amount,
                }
            elif self.ideal_amount < 0 and self.amount < 0:
                self.ideal_amount = 0
                ret = {
                    "symbol": self.symbol,
                    "direction": "BUY",
                    "offset": "CLOSE",
                    "price": GetPrice.get_buy_price(self.symbol, np.nan),
                    "amount": -self.amount,
                }
        if ret is not None and np.isnan(ret["price"]):
            ret = None
        return ret


class Task1(Task):
    def __init__(
        self,
        money: float,
        symbol: str,
        increase: float,
        stoploss: float,
        stopearn: float,
        is_based_on_his_signals: bool,
        is_credit: bool,
        is_financing_first: bool,
        trade_direction: Literal[-1, 0, 1],
        is_intraday_trade: bool,
        rise_or_fall_threshold: float,
        is_take_profit: bool,
        **kwargs,
    ):
        super().__init__(
            money, symbol, is_based_on_his_signals, is_credit, is_financing_first, trade_direction, is_intraday_trade
        )
        self.increase = increase
        """涨跌幅达到increase%触发信号"""
        self.stoploss = stoploss
        """止损幅度stoploss%"""
        self.stopearn = stopearn
        """回落止盈幅度stopearn%"""
        self.rise_or_fall_threshold = rise_or_fall_threshold
        """涨跌幅阈值rise_or_fall_threshold%, 超出不开仓"""
        self.is_take_profit = is_take_profit
        """是否开启1/2主动止盈"""

        d = get_price(
            symbol,
            None,
            pd.Timestamp.now(tz="Asia/Shanghai"),
            "1m",
            fields=BAR_FIELDS.keys(),
            bar_count=250,
            enable_external_data=1,
        )
        d["pre_price"] = d["close"].shift(1)
        self.last_bar: dict = d.iloc[-1].to_dict()
        d = d.loc[d["date"] == d["date"].max()]
        self.today_open = d.iloc[0]["open"]
        self.yestoday_close = d.iloc[0]["pre_price"]
        self.llv_today = d["low"].min()
        self.hhv_today = d["high"].max()

        self.stop_loss_price = 0
        """开仓时确定，仅作为展示用"""
        self.highest_price = 0
        """开仓时初始化，后续每个bar更新"""
        self.lowest_price = 0
        """开仓时初始化，后续每个bar更新"""
        self.has_taken_profit = False
        """是否已经执行1/2止盈"""

    def on_data(self, data):
        super().on_data(data)

        ret = None
        if self.invalid:
            return None

        current_dt = self.current_dt
        delay_time = abs(datetime.datetime.now() - current_dt).total_seconds()
        if self.last_bar is None:
            self.today_open = data["open"]
            self.yestoday_close = data["open"]
            self.llv_today = data["low"]
            self.hhv_today = data["high"]
        elif self.last_bar["date"] != data["date"]:
            self.today_open = data["open"]
            self.yestoday_close = self.last_bar["close"]
            self.llv_today = data["low"]
            self.hhv_today = data["high"]
        else:
            self.llv_today = min(self.llv_today, data["low"])
            self.hhv_today = max(self.hhv_today, data["high"])
        self.last_bar = data

        if self.ideal_amount == 0 and self.amount == 0:
            if (
                (
                    (data["close"] > self.yestoday_close * (1 + self.increase))
                    or (
                        data["close"] > self.llv_today * (1 + 2 * self.increase) and data["close"] < self.yestoday_close
                    )
                )
                and data["close"] / self.yestoday_close < 1 + self.rise_or_fall_threshold
                and self.trade_direction in [1, 0]
                and self.current_dt.time() < datetime.time(15, 0)
            ):
                if self.is_based_on_his_signals or delay_time < 30:
                    ret = {
                        "symbol": self.symbol,
                        "direction": "CREDIT_BUY" if self.is_financing_first else "BUY",
                        "offset": "OPEN",
                        "price": (t_price := GetPrice.get_buy_price(self.symbol, data["close"])),
                        "amount": (t_amount := calculate_shares(self.money, self.symbol, t_price)),
                    }
                    if t_amount == 0:
                        raise AssertionError("计算出的买入数量为0")
                    self.ideal_amount = t_amount
                    self.stop_loss_price = data["close"] * (1 - self.stoploss)  # 亏损超过7个点的止损价
                    self.highest_price = data["close"]  # 最高价初始化为买入价
                    self.lowest_price = data["close"]  # 最低价初始化为买入价
                    self.trade_date = datetime.datetime.now()
                elif delay_time >= 30:
                    logger.warning("task1 {} 延迟 {} seconds，买入{}失败".format(self.task_id, delay_time, self.symbol))

            elif (
                (
                    (data["close"] < self.yestoday_close * (1 - self.increase))
                    or (
                        data["close"] < self.hhv_today * (1 - 2 * self.increase) and data["close"] > self.yestoday_close
                    )
                )
                and data["close"] / self.yestoday_close > 1 - self.rise_or_fall_threshold
                and self.trade_direction in [-1, 0]
                and self.current_dt.time() < datetime.time(15, 0)
            ):
                if self.is_based_on_his_signals or delay_time < 30:
                    ret = {
                        "symbol": self.symbol,
                        "direction": "SELL",
                        "offset": "OPEN",
                        "price": (t_price := GetPrice.get_sell_price(self.symbol, data["close"])),
                        "amount": (t_amount := calculate_shares(self.money, self.symbol, t_price)),
                    }
                    if t_amount == 0:
                        raise AssertionError("计算出的卖出数量为0")
                    self.ideal_amount = -t_amount
                    self.stop_loss_price = data["close"] * (1 + self.stoploss)  # 亏损超过7个点的止损价
                    self.highest_price = data["close"]  # 最高价初始化为买入价
                    self.lowest_price = data["close"]  # 最低价初始化为买入价
                    self.trade_date = datetime.datetime.now()
                elif delay_time >= 30:
                    logger.warning("task1 {} 延迟 {} seconds，卖出{}失败".format(self.task_id, delay_time, self.symbol))

        elif self.ideal_amount > 0:
            self.highest_price = max(self.highest_price, data["close"], data["open"])
            self.lowest_price = min(self.lowest_price, data["close"])
            downlimit_price = GetPrice.get_downlimit_price(self.symbol)
            pre_price = GetPrice.get_pre_price(self.symbol)

            if self.amount > 0:
                drawdown_percent = (self.highest_price - data["close"]) / self.highest_price  # 当前回撤比例
                return_pct = (data["close"] - self.cost_basis) / self.cost_basis  # 当前盈利比例，盈利为正
                profit = (data["close"] - self.cost_basis) * self.amount

                logger.opt(colors=True).info(
                    "task1 {s0} {s1} {s14} 多 FPL: {s2}(<yellow>{s10:.4f}</yellow> | C: {s11:.4f} H: {s12:.0f} PR: {s13}), HP: {s3:.4f}(<red>{s4:+.2%}</red> - <green>{s5:.2%}</green> | {s6:.2%}), LP: {s7:.4f}(<green>{s8:+.2%}</green> | {s9:.2%})".format(
                        s0=self.task_id,
                        s1=current_dt,
                        s14=self.symbol,
                        s2=f"<red>{return_pct:+.2%}</red>" if return_pct > 0 else f"<green>{return_pct:+.2%}</green>",
                        s10=data["close"],
                        s11=self.cost_basis,
                        s12=self.amount,
                        s13=f"<red>{profit:+.2f}</red>" if profit > 0 else f"<green>{profit:+.2f}</green>",
                        s3=self.highest_price,
                        s4=tmp if (tmp := self.highest_price / self.cost_basis - 1) >= 0 else np.nan,
                        s5=drawdown_percent,
                        s6=self.stopearn,
                        s7=self.lowest_price,
                        s8=tmp if (tmp := self.lowest_price / self.cost_basis - 1) <= 0 else np.nan,
                        s9=self.stoploss,
                    )
                )

                if self.is_intraday_trade or datetime.datetime.now().date() > self.trade_date.date():
                    is_sell = False
                    if return_pct < -self.stoploss and data["close"] < data["open"]:  # 止损
                        is_sell = True
                        t_amount = self.ideal_amount
                    elif (
                        self.is_take_profit
                        and (self.highest_price - self.cost_basis) / self.highest_price >= self.stopearn
                        and not self.has_taken_profit
                    ):  # 主动止盈
                        self.has_taken_profit = True
                        t_amount = adjust_shares(self.ideal_amount / 2, self.symbol)
                        if t_amount > 0:
                            is_sell = True
                    elif (
                        current_dt.time() <= datetime.time(9, 40)
                        and drawdown_percent > self.stopearn + 0.02
                        and data["close"] < data["open"]
                    ):  # 止盈，早盘前10分钟放宽2%
                        is_sell = True
                        t_amount = self.ideal_amount
                    elif (
                        current_dt.time() > datetime.time(9, 40)
                        and drawdown_percent > self.stopearn
                        and data["close"] < data["open"]
                    ):  # 止盈
                        is_sell = True
                        t_amount = self.ideal_amount
                    elif (
                        np.isfinite(downlimit_price)
                        and np.isfinite(pre_price)
                        and data["close"] < pre_price - (pre_price - downlimit_price) * 0.9
                    ):  # 跌停
                        is_sell = True
                        t_amount = self.ideal_amount

                    if is_sell and self.current_dt.time() < datetime.time(15, 0):
                        self.ideal_amount -= t_amount
                        ret = {
                            "symbol": self.symbol,
                            "direction": "SELL",
                            "offset": "CLOSE",
                            "price": (t_price := GetPrice.get_sell_price(self.symbol, data["close"])),
                            "amount": int(t_amount),
                        }

        elif self.ideal_amount < 0:
            self.highest_price = max(self.highest_price, data["close"])
            self.lowest_price = min(self.lowest_price, data["close"], data["open"])
            uplimit_price = GetPrice.get_uplimit_price(self.symbol)
            pre_price = GetPrice.get_pre_price(self.symbol)

            if self.amount < 0:
                drawdown_percent = (data["close"] - self.lowest_price) / self.lowest_price  # 当前回撤比例
                return_pct = (self.cost_basis - data["close"]) / self.cost_basis  # 当前盈利比例，盈利为正
                profit = -(self.cost_basis - data["close"]) * self.amount

                logger.opt(colors=True).info(
                    "task1 {s0} {s1} {s14} 空 FPL: {s2}(<yellow>{s10:.4f}</yellow> | C: {s11:.4f} H: {s12:.0f} PR: {s13}), LP: {s3:.4f}(<red>{s4:+.2%}</red> - <green>{s5:.2%}</green> | {s6:.2%}), HP: {s7:.4f}(<green>{s8:+.2%}</green> | {s9:.2%})".format(
                        s0=self.task_id,
                        s1=current_dt,
                        s14=self.symbol,
                        s2=f"<red>{return_pct:+.2%}</red>" if return_pct > 0 else f"<green>{return_pct:+.2%}</green>",
                        s10=data["close"],
                        s11=self.cost_basis,
                        s12=-self.amount,
                        s13=f"<red>{profit:+.2f}</red>" if profit > 0 else f"<green>{profit:+.2f}</green>",
                        s3=self.lowest_price,
                        s4=tmp if (tmp := 1 - self.lowest_price / self.cost_basis) >= 0 else np.nan,
                        s5=drawdown_percent,
                        s6=self.stopearn,
                        s7=self.highest_price,
                        s8=tmp if (tmp := 1 - self.highest_price / self.cost_basis) <= 0 else tmp,
                        s9=self.stoploss,
                    )
                )

                if self.is_intraday_trade or datetime.datetime.now().date() > self.trade_date.date():
                    is_buy = False
                    if return_pct < -self.stoploss and data["close"] > data["open"]:
                        is_buy = True
                        t_amount = -self.ideal_amount
                    elif (
                        self.is_take_profit
                        and (self.cost_basis - self.lowest_price) / self.lowest_price >= self.stopearn
                        and not self.has_taken_profit
                    ):
                        self.has_taken_profit = True
                        t_amount = adjust_shares(-self.ideal_amount / 2, self.symbol)
                        if t_amount > 0:
                            is_buy = True
                    elif (
                        current_dt.time() <= datetime.time(9, 40)
                        and drawdown_percent > self.stopearn + 0.02
                        and data["close"] > data["open"]
                    ):
                        is_buy = True
                        t_amount = -self.ideal_amount
                    elif (
                        current_dt.time() > datetime.time(9, 40)
                        and drawdown_percent > self.stopearn
                        and data["close"] > data["open"]
                    ):
                        is_buy = True
                        t_amount = -self.ideal_amount
                    elif (
                        not np.isnan(uplimit_price)
                        and not np.isnan(pre_price)
                        and data["close"] > pre_price + (uplimit_price - pre_price) * 0.9
                    ):  # 涨停
                        is_buy = True
                        t_amount = -self.ideal_amount

                    if is_buy and self.current_dt.time() < datetime.time(15, 0):
                        self.ideal_amount += t_amount
                        ret = {
                            "symbol": self.symbol,
                            "direction": "BUY",
                            "offset": "CLOSE",
                            "price": (t_price := GetPrice.get_buy_price(self.symbol, data["close"])),
                            "amount": int(t_amount),
                        }

        return ret

    @property
    def info(self):
        info = super().info
        info["params"].update(
            {
                "increase": self.increase,
                "stoploss": self.stoploss,
                "stopearn": self.stopearn,
                "rise_or_fall_threshold": self.rise_or_fall_threshold,
                "is_take_profit": self.is_take_profit,
            }
        )
        info["status"].update(
            {
                "today_open": self.today_open,
                "yesterday_close": self.yestoday_close,
                "llv_today": self.llv_today,
                "hhv_today": self.hhv_today,
                "stop_loss_price": self.stop_loss_price,
                "highest_price": self.highest_price,
                "lowest_price": self.lowest_price,
                "has_taken_profit": self.has_taken_profit,
            }
        )
        return info


class Task2(Task):
    def __init__(
        self,
        symbol,
        is_credit,
        is_financing_first,
        cost_basis,
        amount,
        highest_price,
        lowest_price,
        stop_loss_ratio,
        take_profit_threshold,
        take_profit_withdraw1,
        take_profit_withdraw2,
        **kwargs,
    ):
        from strategy2lib import RTask2

        super().__init__(0, symbol, False, is_credit, is_financing_first, 0, True)
        self.cost_basis = cost_basis
        self.amount = amount
        self.ideal_amount = amount

        self.task: RTask2 = RTask2(
            int(np.sign(amount)),
            self.cost_basis,
            abs(self.amount),
            highest_price,
            lowest_price,
            stop_loss_ratio,
            take_profit_threshold,
            take_profit_withdraw1,
            take_profit_withdraw2,
        )

        self.ctime = datetime.datetime.now()

    def on_data(self, data):
        super().on_data(data)

        if (
            self.invalid
            or self.amount == 0
            or self.ideal_amount == 0
            or generate_datetime(data["date"], data["time"]) < self.ctime
        ):
            return None
        signal = self.task.on_data(data["close"])
        if signal:
            self.ideal_amount = 0
            if self.amount > 0:
                return {
                    "symbol": self.symbol,
                    "direction": "SELL",
                    "offset": "CLOSE",
                    "price": GetPrice.get_sell_price(self.symbol, data["close"]),
                    "amount": abs(self.amount),
                }
            else:
                return {
                    "symbol": self.symbol,
                    "direction": "CREDIT_BUY" if self.is_financing_first else "BUY",
                    "offset": "CLOSE",
                    "price": GetPrice.get_buy_price(self.symbol, data["close"]),
                    "amount": abs(self.amount),
                }
        return None

    @property
    def info(self):
        info = super().info
        info["params"].update(self.task.get_params())
        info["status"].update(self.task.get_status())
        return info


class EasyStrategy(CustomStrategy):
    def __init__(self, is_credit, **kwargs):
        super().__init__(**kwargs)
        self.is_credit = is_credit
        self.taskpool: dict[QSymbol, dict[str, Task]] = {}
        self.order_task_map = {}
        self.reporter = RabbitMQReporter()

    def init(self):
        logger.info("init")
        self.subscribe("000001.SZSE", 1, 1, BasicData.SNAPSHOT)

    def trade(self, task_id, ins):
        ins["price"] = None if "price" not in ins else float(ins["price"])
        ins["amount"] = float(ins["amount"])

        order_status, order_id = self.trader.order(**ins)
        if order_status == StatusCode.SUCCESS:
            self.order_task_map[order_id] = task_id
            if ins["offset"] == "OPEN":
                logger.opt(colors=True).success(
                    f"task {task_id} 开仓执行成功   以 {ins['price']:.4f} 买入 {ins['symbol']} {ins['amount']:.0f} 股  order_id: {order_id}"
                )
            elif ins["offset"] == "CLOSE":
                logger.opt(colors=True).success(
                    f"task {task_id} 平仓执行成功   以 {ins['price']:.4f} 卖出 {ins['symbol']} {ins['amount']:.0f} 股  order_id: {order_id}"
                )
        else:
            if ins["offset"] == "OPEN":
                logger.opt(colors=True).error(
                    f"task {task_id} 开仓执行失败   以 {ins['price']:.4f} 买入 {ins['symbol']} {ins['amount']:.0f} 股， 原因：{order_status}"
                )
            elif ins["offset"] == "CLOSE":
                logger.opt(colors=True).error(
                    f"task {task_id} 平仓执行失败   以 {ins['price']:.4f} 卖出 {ins['symbol']} {ins['amount']:.0f} 股， 原因：{order_status}"
                )

    def on_data(self, data, **kwargs):
        # 数据延迟超过10秒，则发出警告
        if not kwargs["basic_data"] == BasicData.MINUTE:
            return

        current_dt = generate_datetime(data["date"], data["time"])
        delay_time = abs(datetime.datetime.now() - current_dt).total_seconds()
        if delay_time > 10:
            logger.warning(f"{data['code']} 数据延迟 {delay_time} 秒")

        logger.trace(
            "date: {}, time: {}, symbol: {}, close: {:.02f}".format(
                data["date"], data["time"], data["code"], data["close"]
            )
        )
        if data["code"] in self.taskpool:
            task_ids_lst = list(self.taskpool[data["code"]].keys())
            for task_id in task_ids_lst:
                if not self.taskpool[data["code"]][task_id].invalid:
                    # 执行任务on_data
                    try:
                        ret = self.taskpool[data["code"]][task_id].on_data(data)
                    except Exception:
                        self.taskpool[data["code"]][task_id].modify_status(is_wrong=True, invalid=True)
                        ret = None

                    # 如果任务on_data返回的不是None，说明要下单，则执行下单
                    if ret is not None:
                        self.trade(task_id, ret)

                    # Task1如果运行天数>=1，且当前时间是15:00，且没有开仓，则取消
                    if (
                        isinstance(self.taskpool[data["code"]][task_id], Task1)
                        and self.taskpool[data["code"]][task_id].current_dt.time() == datetime.time(15, 0)
                        and self.taskpool[data["code"]][task_id].ideal_amount == 0
                        and self.taskpool[data["code"]][task_id].amount == 0
                        and self.taskpool[data["code"]][task_id].running_day_count >= 1
                    ):
                        self.taskpool[data["code"]][task_id].modify_status(invalid=True)
                        logger.warning(f"task {task_id} for {data['code']} cancelled")
                # else:
                #     if not self.taskpool[data["code"]][task_id].is_wrong:
                #         self.taskpool[data["code"]].pop(task_id)

    def on_order(self, msg, **kwargs):
        if msg["status"] in [OrderStatus.FINISHED, OrderStatus.CANCELLED, OrderStatus.SCRAPPED]:
            # 委托状态已经完结，才去处理任务里的交易情况
            if (
                msg["symbol"] in self.taskpool
                and msg["order_id"] in self.order_task_map
                and self.order_task_map[msg["order_id"]] in self.taskpool[msg["symbol"]]
            ):
                task = self.taskpool[msg["symbol"]][self.order_task_map[msg["order_id"]]]
                logger.opt(colors=True).info(
                    "task {s0} OrderStatus Updates - {s8} D: {s6} O: {s7} P: {s1:.4f} Q: {s2:.0f} <yellow>TP: {s3:.4f}</yellow> <yellow>TQ: {s4:.0f}</yellow> <yellow>S: {s5}</yellow>".format(
                        s0=task.task_id,
                        s8=msg["symbol"],
                        s1=msg["price"],
                        s2=msg["amount"],
                        s3=msg["trade_price"],
                        s4=msg["trade_amount"],
                        s5=msg["status"],
                        s6=msg["direction"],
                        s7=msg["offset"],
                    )
                )

                # 开仓委托
                if abs(task.ideal_amount) > abs(task.amount):
                    if msg["trade_amount"] == 0:
                        # 如果成交数量为0，说明委托状态为SCARAPPED，任务后续不再执行
                        task.modify_status(invalid=True)
                        logger.warning(f"task {task.task_id} for {msg['symbol']} cancelled")
                    else:
                        if (
                            task.ideal_amount > 0
                            and not (
                                (task.amount == 0 or (task.amount > 0 and np.isfinite(task.cost_basis)))
                                and msg["direction"] in [Direction.BUY, Direction.CREDIT_BUY]
                            )
                        ) or (
                            task.ideal_amount < 0
                            and not (
                                (task.amount == 0 or (task.amount < 0 and np.isfinite(task.cost_basis)))
                                and msg["direction"] in [Direction.SELL]
                            )
                        ):
                            task.modify_status(invalid=True, is_wrong=True)
                            logger.warning(
                                f"task {task.task_id} for {msg['symbol']} order wrong: task ideal_amount is {task.ideal_amount}, amount is {task.amount}, cost_basis is {task.cost_basis}, and order direction is {msg['direction']}"
                            )
                        else:
                            # 如果成交数量不为0，任务还要继续执行下去，即使成交不足，成交不足时把理论持仓修正了
                            if task.amount == 0:
                                task.modify_position(
                                    cost_basis=msg["trade_price"],
                                    amount=msg["trade_amount"],
                                    ideal_amount=msg["trade_amount"],
                                )
                            else:
                                cost_basis = (
                                    task.cost_basis * abs(task.amount) + msg["trade_price"] * msg["trade_amount"]
                                ) / (abs(task.amount) + msg["trade_amount"])  # 只有在开仓时会更新成本价，平仓不会
                                amount = abs(task.amount) + msg["trade_amount"]
                                task.modify_position(cost_basis=cost_basis, amount=amount, ideal_amount=amount)

                # 平仓委托
                elif abs(task.ideal_amount) < abs(task.amount):
                    if (
                        msg["amount"] != msg["trade_amount"]
                        or (
                            task.amount > 0
                            and not (
                                np.isfinite(task.cost_basis)
                                and task.ideal_amount >= 0
                                and abs(task.amount) - abs(task.ideal_amount) == abs(msg["trade_amount"])
                                and msg["direction"] in [Direction.SELL]
                            )
                        )
                        or (
                            task.amount < 0
                            and not (
                                np.isfinite(task.cost_basis)
                                and task.ideal_amount <= 0
                                and abs(task.amount) - abs(task.ideal_amount) == abs(msg["trade_amount"])
                                and msg["direction"] in [Direction.BUY, Direction.CREDIT_BUY]
                            )
                        )
                    ):
                        task.modify_status(invalid=True, is_wrong=True)
                        logger.warning(
                            f"task {task.task_id} for {msg['symbol']} order wrong: task amount is {task.amount}, ideal_amount is {task.ideal_amount}, cost_basis is {task.cost_basis}, and order direction is {msg['direction']}"
                        )
                    else:
                        if task.ideal_amount == 0:
                            task.modify_position(cost_basis=np.nan, amount=0)
                            task.modify_status(invalid=True)
                            logger.success(f"task {task.task_id} for {msg['symbol']} finished")
                        else:
                            task.modify_position(cost_basis=None, amount=abs(task.amount) - msg["trade_amount"])

                else:
                    task.modify_status(invalid=True, is_wrong=True)
                    logger.error(f"task {task.task_id} for {msg['symbol']} wrong")

    def on_instruction(self, msg, **kwargs):
        if msg["type"] == CommunicatorMessageType.INSTRUCTION and msg["strategy"] == 2:
            msg = msg["data"]
            if msg["action"] == "ADD":
                try:
                    config = msg["config"]
                    if config["is_credit"] != self.is_credit:
                        return
                    task_type = config.pop("task_type")

                    if task_type == "Task1":
                        task = Task1(**config)
                        # 当increase为-1时，立刻开仓
                        if config["increase"] == -1:
                            new_bar = task.last_bar.copy()
                            date_int, time_int = [
                                int(i) for i in datetime.datetime.now().strftime("%Y%m%d %H%M%S000").split(" ")
                            ]
                            if new_bar["date"] != date_int:
                                raise AssertionError

                            new_bar["time"] = time_int
                            new_bar["open"] = new_bar["close"]
                            new_bar["close"] = GetPrice.get_last_price(config["symbol"])
                            new_bar["high"] = max(new_bar["open"], new_bar["close"])
                            new_bar["low"] = min(new_bar["open"], new_bar["close"])

                            ret = task.on_data(new_bar)
                            if ret is not None:
                                self.trade(task.task_id, ret)
                    elif task_type == "Task2":
                        task = Task2(**config)

                    if config["symbol"] not in self.taskpool:
                        self.taskpool[config["symbol"]] = {}
                    self.taskpool[config["symbol"]][task.task_id] = task
                    self.subscribe(config["symbol"], 0, 1, BasicData.MINUTE)
                    logger.success(f"task {task.task_id} for {config['symbol']} added")
                except Exception as e:
                    logger.error(f"add task for {config['symbol']} failed")
                    logger.exception(e)

            elif msg["action"] == "DELETE":
                try:
                    for symbol in self.taskpool:
                        if msg["config"]["task_id"] in self.taskpool[symbol]:
                            tmp_task: Task = self.taskpool[symbol][msg["config"]["task_id"]]
                            if not tmp_task.invalid:
                                if msg["config"]["is_close"]:
                                    ins = tmp_task.close()
                                    if ins is not None:
                                        self.trade(msg["config"]["task_id"], ins)
                                    else:
                                        tmp_task.modify_status(invalid=True)
                                        logger.success(f"task {msg['config']['task_id']} for {symbol} deleted")
                                else:
                                    tmp_task.modify_status(invalid=True)
                                    logger.success(f"task {msg['config']['task_id']} for {symbol} deleted")
                            else:
                                logger.info(f"task {msg['config']['task_id']} for {symbol} is invalid")
                            break
                    else:
                        logger.info(f"task {msg['config']['task_id']} not found")
                except Exception:
                    logger.error(f"delete task {msg['config']['task_id']} failed")

    def on_tick(self, msg, **kwargs):
        if msg == 1:
            portfolio = self.trader.portfolio[1].to_json()
            self.reporter.publish(portfolio, CommunicatorMessageType.PORTFOLIOUPDATE)
            positions = {
                key: {k: v.to_json() for k, v in value.items()} for key, value in self.trader.positions[1].items()
            }
            self.reporter.publish(positions, CommunicatorMessageType.POSITIONSUPDATE)
            tasks = []
            for symbol in self.taskpool:
                for task in self.taskpool[symbol].values():
                    tasks.append(task.info)
            self.reporter.publish(tasks, CommunicatorMessageType.INFO)


def main():
    if args.is_real_trade:
        gl_event_drive_engine.set_env(StrategyMode.TRADE, datetime.datetime.now().strftime("%Y%m%d %H%M"))
        if args.is_credit:
            strategy = EasyStrategy(is_credit=True)
            strategy.register_trader(Trader(150000, TradeAPI(args.account, True), is_verify=False))
        else:
            strategy = EasyStrategy(is_credit=False)
            strategy.register_trader(Trader(150000, TradeAPI(args.account, False), is_verify=False))
    else:
        gl_event_drive_engine.set_env(
            StrategyMode.BACKTEST,
            (datetime.datetime.now() - datetime.timedelta(days=20)).replace(hour=9, minute=30).strftime("%Y%m%d %H%M"),
        )
        strategy = EasyStrategy(is_credit=False)
        strategy.register_trader(Trader(150000, gl_virtual_exchange, False))

    gl_event_drive_engine.register_strategy(strategy)
    rmq_instructor = RabbitMQInstructorForEDA()
    rmq_instructor.write(is_credit=args.is_credit, version=__version__)
    gl_event_drive_engine.register_instructor(rmq_instructor)

    @gl_event_drive_engine.wrap_job
    def job1():
        return 1

    scheduler = Timer()
    scheduler.every(3).seconds.do(job1)
    gl_event_drive_engine.register_scheduler(scheduler)

    logger.info("启动策略")
    gl_event_drive_engine.run()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.opt(exception=e).error(e)
        logger.log("WEBHOOK", "策略崩溃")

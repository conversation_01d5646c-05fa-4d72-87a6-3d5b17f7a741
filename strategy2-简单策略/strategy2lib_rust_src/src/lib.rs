use pyo3::prelude::*;
use std::collections::HashMap;

struct Position {
    cost_basis: f64,
    amount: f64,
    highest_price: f64,
    lowest_price: f64,
    last_price: f64,
}
impl Position {
    fn update(&mut self, last_price: f64) {
        self.last_price = last_price;
        if last_price > self.highest_price {
            self.highest_price = last_price
        }
        if last_price < self.lowest_price {
            self.lowest_price = last_price
        }
    }
    fn get_status(&self) -> HashMap<String, f64> {
        let mut ret = HashMap::new();
        ret.insert(String::from("cost_basis"), self.cost_basis);
        ret.insert(String::from("amount"), self.amount);
        ret.insert(String::from("highest_price"), self.highest_price);
        ret.insert(String::from("lowest_price"), self.lowest_price);
        ret
    }
}
enum PositionType {
    Empty,
    Long(Position),
    Short(Position),
}

trait StopLossTrait {
    fn calc_stop_loss_signal(&self, position: &PositionType, stop_loss_ratio: f64) -> bool {
        match position {
            PositionType::Empty => false,
            PositionType::Long(x) => x.lowest_price < x.cost_basis * (1.0 - stop_loss_ratio),
            PositionType::Short(x) => x.highest_price > x.cost_basis * (1.0 + stop_loss_ratio),
        }
    }
}
trait TakeProfitTrait {
    fn calc_take_profit_signal(
        &self,
        position: &PositionType,
        threshold: f64,
        withdraw1: f64,
        withdraw2: f64,
    ) -> bool {
        match position {
            PositionType::Empty => false,
            PositionType::Long(x) => {
                let tmp = x.last_price / x.cost_basis - 1.0;
                if tmp < threshold {
                    x.last_price < x.highest_price * (1.0 - withdraw1)
                } else {
                    x.last_price < x.highest_price * (1.0 - withdraw2)
                }
            }
            PositionType::Short(x) => {
                let tmp = 1.0 - x.last_price / x.cost_basis;
                if tmp < threshold {
                    x.last_price > x.lowest_price * (1.0 + withdraw1)
                } else {
                    x.last_price > x.lowest_price * (1.0 + withdraw2)
                }
            }
        }
    }
}

#[pyclass]
struct RTask2 {
    position: PositionType,
    stop_loss_ratio: f64,
    take_profit_threshold: f64,
    take_profit_withdraw1: f64,
    take_profit_withdraw2: f64,
}

impl StopLossTrait for RTask2 {}
impl TakeProfitTrait for RTask2 {}

#[pymethods]
impl RTask2 {
    #[new]
    fn new(
        position_type: i32,
        cost_basis: f64,
        amount: f64,
        highest_price: f64,
        lowest_price: f64,
        stop_loss_ratio: f64,
        take_profit_threshold: f64,
        take_profit_withdraw1: f64,
        take_profit_withdraw2: f64,
    ) -> Self {
        RTask2 {
            position: {
                let tmp = Position {
                    cost_basis: cost_basis,
                    amount: amount,
                    highest_price: highest_price,
                    lowest_price: lowest_price,
                    last_price: 0.0,
                };
                if position_type > 0 {
                    PositionType::Long(tmp)
                } else {
                    PositionType::Short(tmp)
                }
            },
            stop_loss_ratio: stop_loss_ratio,
            take_profit_threshold: take_profit_threshold,
            take_profit_withdraw1: take_profit_withdraw1,
            take_profit_withdraw2: take_profit_withdraw2,
        }
    }
    fn on_data(&mut self, last_price: f64) -> bool {
        match &mut self.position {
            PositionType::Empty => {}
            PositionType::Long(x) => {
                x.update(last_price);
            }
            PositionType::Short(x) => {
                x.update(last_price);
            }
        }

        self.calc_stop_loss_signal(&self.position, self.stop_loss_ratio)
            | self.calc_take_profit_signal(
                &self.position,
                self.take_profit_threshold,
                self.take_profit_withdraw1,
                self.take_profit_withdraw2,
            )
    }
    fn get_params(&self) -> HashMap<String, f64> {
        let mut ret = HashMap::new();
        ret.insert(String::from("stop_loss_ratio"), self.stop_loss_ratio);
        ret.insert(
            String::from("take_profit_threshold"),
            self.take_profit_threshold,
        );
        ret.insert(
            String::from("take_profit_withdraw1"),
            self.take_profit_withdraw1,
        );
        ret.insert(
            String::from("take_profit_withdraw2"),
            self.take_profit_withdraw2,
        );
        ret
    }
    fn get_status(&self) -> HashMap<String, f64> {
        match &self.position {
            PositionType::Empty => return HashMap::new(),
            PositionType::Long(x) => return x.get_status(),
            PositionType::Short(x) => {
                let mut ret = x.get_status();
                ret.insert(String::from("amount"), x.amount * -1.0);
                return ret;
            }
        }
    }
}

#[pymodule]
fn strategy2lib(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_class::<RTask2>()?;
    Ok(())
}

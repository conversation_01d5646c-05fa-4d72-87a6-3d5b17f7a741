[package]
name = "strategy2lib"
version = "1.0.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "strategy2lib"
# "cdylib" is necessary to produce a shared library for Python to import from.
crate-type = ["cdylib"]

[dependencies]
chrono = "0.4.38"
pyo3 = { version = ">=0.20.0", features = [
    "extension-module",
    "abi3-py38",
    "chrono",
] }

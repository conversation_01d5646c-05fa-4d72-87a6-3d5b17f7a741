import datetime
from itertools import chain

import numpy as np
import pandas as pd
import setproctitle
from qnt_research.engine import gl_event_drive_engine
from qnt_research.strategy.base import CustomStrategy
from qnt_research.trade_api import TradeAPI
from qnt_research.trader.trader import Trader
from qnt_research.virtual_exchange import gl_virtual_exchange
from qnt_utils.ctoolset import generate_datetime
from qnt_utils.enums import BasicData, StrategyMode

pd.set_option("display.max_rows", None)


MONEY = 20000
SYMBOL = "UR8888.CZCE"
PRICE_MOVEMENT = 0.015
STOP_LOSS = 0.02
STOP_EARN = 0.08
# TODO
# IS_BASED_ON_HIS_SIGNALS=False
IS_BASED_ON_HIS_SIGNALS = True
IS_FINANCING_FIRST = False

MARGIN = 0.1
ADJUST_PROFIT_RATE = 0.05
FIRST_OPEN_POS = 2


class AddToPositions:
    def __init__(self, init_positions):
        self.positions = [init_positions]
        self.side = np.sign(init_positions[1])

    @staticmethod
    def _profit(last_price, cost_price, lots):
        return (last_price - cost_price) * lots

    def profit(self, last_price):
        ret = 0
        for cost_price, lots in self.positions:
            ret += self._profit(last_price, cost_price, lots)
        return ret

    @staticmethod
    def margin_occ(price, lots):
        return -abs(price * lots * MARGIN)

    def positions_margin_occ(self, last_price):
        ret = 0
        for _, lots in self.positions:
            ret += self.margin_occ(last_price, lots)
        return ret

    def available_cash(self, last_price):
        return self.profit(last_price) - self.margin_occ(*self.positions[0]) + self.positions_margin_occ(last_price)

    def add_pos(self, positions):
        self.positions.append(positions)

    @staticmethod
    def net_pos(*positions):
        t1 = 0
        for _, j in chain(*positions):
            t1 += j
        return t1

    @staticmethod
    def avg_price(*positions):
        t1 = 0
        for i, j in chain(*positions):
            t1 += i * j
        return t1 / tmp if (tmp := AddToPositions.net_pos(*positions)) != 0 else np.nan

    def calc_add_pos(self, last_price):
        ac = self.available_cash(last_price)
        ol = int(ac // (last_price * MARGIN))
        for open_lots in range(int(ol), -1, -1):
            if open_lots > 0 and self.side * self.avg_price(self.positions, [(last_price, open_lots * self.side)]) < self.side * (
                last_price * (1 - ADJUST_PROFIT_RATE * self.side)
            ):
                return open_lots * self.side
        else:
            return 0


class AddToPositionsStrategy(CustomStrategy):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.last_bar = None
        self.traded = False
        self.today_open = 0
        self.yestoday_close = 0
        self.stop_loss_price = 0
        self.highest_price = 0
        self.lowest_price = np.inf
        self.buy_date = None
        self.sell_date = None

        self.add_to_positions: AddToPositions

    def init(self):
        self.subscribe(SYMBOL, 1, 1, BasicData.MINUTE)

    def on_data(self, data, **kwargs):
        if data is None:
            return
        positions = self.trader.positions[1]
        portfolio = self.trader.portfolio[1]
        try:
            current_dt = generate_datetime(data["date"], data["time"])
        except:
            print(data)
            raise

        if self.last_bar is None:
            self.today_open = data["open"]
            self.yestoday_close = data["close"]
        elif self.last_bar["date"] != data["date"]:
            self.today_open = data["open"]
            self.yestoday_close = self.last_bar["close"]
        # print(
        #     datetime.datetime.now(),
        #     "\tdate: {}, time: {}, close: {:.02f}   today_open: {:.02f}, yestoday_close: {:.02f}".format(
        #         data["date"],
        #         data["time"],
        #         data["close"],
        #         self.today_open,
        #         self.yestoday_close,
        #     ),
        # )
        buy_signal = data["close"] > min(self.today_open, self.yestoday_close) * (1 + PRICE_MOVEMENT)
        sell_signal = data["close"] < max(self.today_open, self.yestoday_close) * (1 - PRICE_MOVEMENT)

        if not SYMBOL in positions.keys() and portfolio.frozen_cash == 0:
            if not self.traded and (
                IS_BASED_ON_HIS_SIGNALS
                or abs(datetime.datetime.now() - generate_datetime(data["date"], data["time"])).total_seconds() < 30
            ):
                if buy_signal:
                    self.trader.order(
                        symbol=SYMBOL,
                        direction="CREDIT_BUY" if IS_FINANCING_FIRST else "BUY",
                        offset="OPEN",
                        price=(t_price := round(data["close"] * 1.001, 2)),
                        amount=FIRST_OPEN_POS,
                    )
                    self.stop_loss_price = data["close"] * (1 - STOP_LOSS)
                    self.highest_price = data["close"]  # 最高价初始化为买入价
                    self.buy_date = datetime.date.today()
                    print(f"{current_dt}   以{t_price}买入开仓{SYMBOL}{FIRST_OPEN_POS}股")
                    self.add_to_positions = AddToPositions((data["close"], FIRST_OPEN_POS))
                    self.traded = True

                elif sell_signal:
                    self.trader.order(
                        symbol=SYMBOL,
                        direction="CREDIT_SELL" if IS_FINANCING_FIRST else "SELL",
                        offset="OPEN",
                        price=(t_price := round(data["close"] * 0.999, 2)),
                        amount=FIRST_OPEN_POS,
                    )
                    self.stop_loss_price = data["close"] * (1 + STOP_LOSS)
                    self.lowest_price = data["close"]
                    self.sell_date = datetime.date.today()
                    print(f"{current_dt}   以{t_price}卖出开仓{SYMBOL}{FIRST_OPEN_POS}股")
                    self.add_to_positions = AddToPositions((data["close"], -FIRST_OPEN_POS))
                    self.traded = True

        elif positions[SYMBOL]["LONG"].available > 0:
            self.highest_price = max(self.highest_price, data["high"])
            drawdown_percent = (self.highest_price - data["close"]) / self.highest_price

            ap = self.add_to_positions.calc_add_pos(data["close"])
            if ap != 0:
                self.add_to_positions.add_pos((data["close"], ap))
                self.trader.order(
                    symbol=SYMBOL,
                    direction="CREDIT_BUY" if IS_FINANCING_FIRST else "BUY",
                    offset="OPEN",
                    price=(t_price := round(data["close"] * 1.001, 2)),
                    amount=ap,
                )
                print(f"{current_dt}   以{t_price}加仓买入{SYMBOL}{ap}股")

            elif data["close"] < self.stop_loss_price or drawdown_percent > STOP_EARN:
                self.trader.order(
                    symbol=SYMBOL,
                    direction="SELL",
                    offset="CLOSE",
                    price=(t_price := round(data["close"] * 0.999, 2)),
                    amount=(t_amount := positions[SYMBOL]["LONG"].available),
                )
                print(f"{current_dt}   以{t_price}卖出平仓{SYMBOL}{t_amount}股")

        elif positions[SYMBOL]["SHORT"].available > 0:
            self.lowest_price = min(self.lowest_price, data["low"])
            drawdown_percent = (data["close"] - self.lowest_price) / self.lowest_price

            ap = self.add_to_positions.calc_add_pos(data["close"])
            if ap != 0:
                self.add_to_positions.add_pos((data["close"], ap))
                self.trader.order(
                    symbol=SYMBOL,
                    direction="CREDIT_SELL" if IS_FINANCING_FIRST else "SELL",
                    offset="OPEN",
                    price=(t_price := round(data["close"] * 0.999, 2)),
                    amount=ap,
                )
                print(f"{current_dt}   以{t_price}加仓卖出{SYMBOL}{ap}股")

            elif data["close"] > self.stop_loss_price or drawdown_percent > STOP_EARN:
                self.trader.order(
                    symbol=SYMBOL,
                    direction="BUY",
                    offset="CLOSE",
                    price=(t_price := round(data["close"] * 1.001, 2)),
                    amount=(t_amount := positions[SYMBOL]["SHORT"].available),
                )
                print(f"{current_dt}   以{t_price}买入平仓{SYMBOL}{t_amount}股")

        self.last_bar = data

    def on_order(self, msg, **kwargs):
        print("on order", datetime.datetime.now(), msg)


if __name__ == "__main__":
    print("AddToPositionsStrategy启动中...")
    setproctitle.setproctitle(f"AddToPositionsStrategy: {SYMBOL}")

    gl_event_drive_engine.set_env(
        StrategyMode.BACKTEST,
        # (datetime.datetime.now() - datetime.timedelta(days=20))
        # .replace(hour=9, minute=0)
        # .strftime("%Y%m%d %H%M"),
        "20230613 0900",
        "20231030 1500",
        addr={1: {"host": "*************", "port": 12330}},
    )
    # TODO is_based_on_his_signals=False
    strategy = AddToPositionsStrategy(is_based_on_his_signals=True)
    strategy.register_trader(
        Trader(
            MONEY,
            # TradeAPI("541300123639", gl_is_credit, "*************", 10000),
            gl_virtual_exchange,
        )
    )
    gl_event_drive_engine.register_strategy(strategy)
    gl_event_drive_engine.run()

from sklearn.ensemble import RandomForestClassifier
import optuna
from optuna.study import create_study


def objective(trial):
    # 设定超参数搜索空间
    args = {
        "max_depth": trial.suggest_int("max_depth", 1, 30),
        "n_estimators": trial.suggest_int("n_estimators", 10, 1000),
    }

    clf = RandomForestClassifier(n_estimators=args["n_estimators"], max_depth=args["max_depth"], n_jobs=4)

    clf.fit(train_x, train_y)

    # print(vali(clf, train_x, train_y))
    # print(vali(clf, vali_x, vali_y))
    # print(vali(clf, test_x, test_y))

    ret = vali(clf, vali_x, vali_y)
    print(
        f"max_depth: {args['max_depth']}, n_estimators: {args['n_estimators']}, acc: {ret['acc']}, precision: {ret['precision']}, recall: {ret['recall']}"
    )
    return ret["acc"]


# 创建 Study
study = create_study(
    direction="maximize",
    pruner=optuna.pruners.MedianPruner(n_startup_trials=10),
    storage="sqlite:///db.sqlite3",
    # storage="postgresql+psycopg2://postgres:123456@192.168.1.202:5432/optuna_dashboard",
    study_name="tuning_ff",
    load_if_exists=True,
)

# 开始优化
study.optimize(objective, n_trials=100, show_progress_bar=True)

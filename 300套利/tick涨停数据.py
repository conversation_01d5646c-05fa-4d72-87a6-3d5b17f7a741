import sys

sys.path.insert(0, "/home/<USER>/work/lib/")
sys.path.insert(0, "/home/<USER>/work/C端项目-板块推荐/")

from functools import lru_cache

import numpy as np
import pandas as pd
from loguru import logger
from smd_module.utils import *
from tqdm import tqdm

# pd.set_option("display.max_rows", None)
logger.add("log")

EXCEPT_CONCEPTS = [
    "885338.TI",
    "885570.TI",
    "885587.TI",
    "885699.TI",
    "885391.TI",
    "885694.TI",
    "885520.TI",
    "886082.TI",
]


# @lru_cache(maxsize=1000)
def fn(concept_stocks: List[str], end_dt: str):
    sz = [i for i in concept_stocks if i.startswith("0")]  # 深证
    sh = [i for i in concept_stocks if (i.startswith("6") and i[1] != "8")]  # 上证
    kcb = [i for i in concept_stocks if i.startswith("68")]  # 科创板
    cyb = [i for i in concept_stocks if i.startswith("3")]  # 创业板
    bjs = [i for i in concept_stocks if i.endswith(".BJ")]  # 北交所

    ret = []

    for pool_name, pool in {"sz": sz, "sh": sh, "kcb": kcb, "cyb": cyb, "bjs": bjs}.items():
        if len(pool) == 0:
            continue
        for is_st in [True, False]:
            if is_st:
                ret.append(gl_fdje_df_st[pool].sum(axis=1).rename(f"fdje_{pool_name}_st"))
                ret.append(gl_is_hl_df_st[pool].sum(axis=1).rename(f"ztsl_{pool_name}_st"))
            else:
                ret.append(gl_fdje_df_non_st[pool].sum(axis=1).rename(f"fdje_{pool_name}_non_st"))
                ret.append(gl_is_hl_df_non_st[pool].sum(axis=1).rename(f"ztsl_{pool_name}_non_st"))

    # ret.append(gl_is_hl_df.apply(lambda x: ",".join(x[x].index), axis=1).rename("ztg"))

    return pd.concat(ret, axis=1)


for end_dt in get_trade_days("2023-01-01", "2024-11-30"):
    try:
        end_dt = end_dt.strftime("%Y-%m-%d")
        with pd.HDFStore(f"./result1.h5") as store:
            if f"/{end_dt}" in store:
                logger.info(f"{end_dt} already done")
                continue

        # region
        tmp = get_price_df(
            get_all_securities("stock", end_dt).index.tolist(),
            end_dt,
            end_dt,
            fq=None,
            fields=["open", "high", "high_limit", "prev_close", "is_st"],
        )
        high_df = tmp["high"]
        open_df = tmp["open"]
        high_limit_df = tmp["high_limit"]
        pc_df = tmp["prev_close"]
        is_st_df = tmp["is_st"]

        high_limit_map = high_limit_df.iloc[-1].to_dict()
        pc_map = pc_df.iloc[-1].to_dict()
        is_st_map = is_st_df.iloc[-1].to_dict()

        tmp = (high_limit_df == high_df).iloc[0]
        stocks = tmp.loc[tmp].index.tolist()

        tick_data = get_tick(stocks, f"{end_dt} 09:15", f"{end_dt} 15:00", ["b1_p", "b1_v", "current"])
        logger.info(f"{end_dt}: {len(stocks)}")
        # endregion

        d2 = tick_data.set_index(["trade_date", "id_stock"])
        d2.index.rename([None, None], inplace=1)

        gl_b1_p_df = d2["b1_p"].unstack().ffill()
        gl_b1_v_df = d2["b1_v"].unstack().ffill()
        gl_current_df = d2["current"].unstack().ffill()

        gl_hl_price_df = gl_b1_p_df.copy()
        for i in gl_hl_price_df.columns:
            gl_hl_price_df[i] = high_limit_map[i]

        gl_is_st_df = gl_b1_p_df.copy()
        for i in gl_is_st_df.columns:
            gl_is_st_df[i] = is_st_map[i]

        gl_is_hl_df = (gl_hl_price_df == gl_current_df) & (gl_hl_price_df == gl_b1_p_df)
        gl_fdje_df = gl_b1_p_df * gl_b1_v_df * 100 / 1e8

        gl_is_hl_df_st = gl_is_hl_df & (gl_is_st_df == 1)
        gl_is_hl_df_non_st = gl_is_hl_df & (gl_is_st_df == 0)
        gl_fdje_df_st = gl_fdje_df[gl_is_hl_df_st].fillna(0)
        gl_fdje_df_non_st = gl_fdje_df[gl_is_hl_df_non_st].fillna(0)

        a = get_A_concept(end_dt)

        b = []
        for concept_thscode in tqdm(a["concept_thscode"]):
            if concept_thscode in EXCEPT_CONCEPTS:
                continue
            concept_stocks = get_concept_stocks_(concept_thscode, end_dt)
            concept_stocks = [i for i in concept_stocks if i in stocks]
            if len(concept_stocks) == 0:
                continue
            b.append(fn(concept_stocks, end_dt).add_prefix(f"{concept_thscode}_"))

        b = pd.concat(b, axis=1)

        with pd.HDFStore(f"./result1.h5") as store:
            store.put(f"{end_dt}", b)

        logger.info(f"{end_dt} done")

    except Exception as e:
        logger.error(f"{end_dt} error")

        logger.exception(e)

        continue

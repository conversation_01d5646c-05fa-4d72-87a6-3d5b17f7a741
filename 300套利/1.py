index_pool = get_A_concept()["concept_thscode"].tolist()
index_pool.extend(["000001.SH", "399001.SZ", "399006.SZ"])

tmp = get_price_df(
    index_pool, f"{start_dt} 0930", f"{end_dt} 1500", "1m", ["close", "turnover"]
)

idx_close_df = tmp["close"]
idx_turnover_df = tmp["turnover"]

idx_hhc_df = idx_close_df.groupby(
    pd.to_datetime(idx_close_df.index).strftime("%Y-%m-%d")
).cummax()
idx_tt_turnover_df = idx_turnover_df.groupby(
    pd.to_datetime(idx_turnover_df.index).strftime("%Y-%m-%d")
).cumsum()

tmp = get_price_df(index_pool, start_dt, end_dt, "1d", ["prev_close", "open",'turnover'])

idx_pc_df = tmp["prev_close"]
idx_open_df = tmp["open"]
idx_turnover_ma5_df = tmp["turnover"].rolling(5, min_periods=1).mean()


idx_pc_df = idx_pc_df.reindex(index=idx_close_df.index)
idx_pc_df = idx_pc_df.bfill()
idx_pc_df[pd.isna(idx_close_df)] = np.nan

idx_open_df = idx_open_df.reindex(index=idx_close_df.index)
idx_open_df = idx_open_df.bfill()
idx_open_df[pd.isna(idx_close_df)] = np.nan

idx_turnover_ma5_df = idx_turnover_ma5_df.shift(1)
idx_turnover_ma5_df = idx_turnover_ma5_df.reindex(index=idx_close_df.index)
idx_turnover_ma5_df = idx_turnover_ma5_df.bfill()
idx_turnover_ma5_df[pd.isna(idx_close_df)] = np.nan

idx_pc_df = idx_pc_df.reindex(index=idx_close_df.index, columns=idx_close_df.columns)
idx_open_df = idx_open_df.reindex(
    index=idx_close_df.index, columns=idx_close_df.columns
)
idx_turnover_ma5_df = idx_turnover_ma5_df.reindex(
    index=idx_close_df.index, columns=idx_close_df.columns
)

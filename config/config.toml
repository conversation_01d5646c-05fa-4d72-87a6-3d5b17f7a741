#=============================================================QDS负责向上游订阅行情============================================================
[qds.settings]
is_debug = false
log_path = './log'

[qds.tq_sdk]
available = true
account = 'y442974010f'
password = 'encrypted:QmVMaUV2RTQuMTk='
debug.available = true
debug.start_dt = '********'
debug.end_dt = '********'

[qds.ashare]
available = false

#====================================================================zookeeper======================================================================
[zookeeper]
host = 'localhost'
port = 2181

#====================================================================行情MQ=====================================================================
[rabbitmq]
host = 'localhost'
port = 5672

[redis]
host = 'localhost'
port = 6379

#==================================================================本地数据库====================================================================
[database]
username = 'encrypted:cG9zdGdyZXM='
password = 'encrypted:YmVsaWV2ZTQxOQ=='
host = 'localhost'
port = 5434

#============================================================research中调用数据接口==============================================================
[external_data.tq]
available = false
account = 'y442974010f'
password = 'encrypted:QmVMaUV2RTQuMTk='

[external_data.mindgo]
available = false
is_local = true
path = '/home/<USER>/workspace/code_repositories/python/quant/external_data/mindgo.h5'

[external_data.tb]
available = false
path = 'E:\tb\********'

[external_data.ifind]
available = false
refresh_token = 'eyJzaWduX3RpbWUiOiIyMDIzLTA0LTA0IDE0OjI5OjU5In0=.****************************.641872284B616449A05841B380F0F1997C1A0FD27FEC663095D2459EFE5F851A'

[external_data.ashare]
available = true


#====================================================================交易模块=====================================================================
[trader.settings]
is_debug = false
log_path = './log'
host = 'localhost'
port = 10001
review_time = 10   # 收盘时review的时间，单位分钟，默认10分钟，表示收盘后10分钟进行，负数表示收盘前review

[trader.em_trade_api]
available = false
account = "************"
password = 'encrypted:WHduSzRXSUpHS1Qvbmw0ZWJZWnhKa1pFSGhQcVV6dWxnbllleERjVjJrTy8yVzFpa1BobGd2ZXpNelBwYzhReDZGUmpyV1RvaFRHMzhaaXRNdGN5Q1Awbmt0TGQycTFlYWtHdzZpVG1FUHJIc3lPbVdTdWQ1OEtxTEd0blhNTVM4aFE2U1ZLbVRoSU5MZzB0ZUxPeDUrOHRDSFhwMHdnWVRrV1NLQk5SWHlVPQ=='

[trader.em_credit_trade_api]
available = false
account = "************"
password = 'encrypted:WHduSzRXSUpHS1Qvbmw0ZWJZWnhKa1pFSGhQcVV6dWxnbllleERjVjJrTy8yVzFpa1BobGd2ZXpNelBwYzhReDZGUmpyV1RvaFRHMzhaaXRNdGN5Q1Awbmt0TGQycTFlYWtHdzZpVG1FUHJIc3lPbVdTdWQ1OEtxTEd0blhNTVM4aFE2U1ZLbVRoSU5MZzB0ZUxPeDUrOHRDSFhwMHdnWVRrV1NLQk5SWHlVPQ=='

[trader.tq_trade_api]
available = false

broker.name = 'M民生期货'
broker.account = '********'
broker.password = 'encrypted:MjIwOTIz'

tq_auth.account = 'y442974010f'
tq_auth.password = 'encrypted:QmVMaUV2RTQuMTk='

# broker.name = 'simnow'
# broker.account = '178081'
# broker.password = 'encrypted:IWJlbGlldmU0MTk='

# tq_auth.account = 'yf442974010'
# tq_auth.password = 'encrypted:QmVMaUV2RTQuMTk='

[trader.gm_trade_api]
available = true

#===============================================================控制台=====================================================================
[controller.settings]
is_debug = true

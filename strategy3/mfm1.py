import json
import datetime
from functools import lru_cache

import numpy as np
from qnt_research.engine import gl_event_drive_engine
from qnt_research.strategy.base import SyncStrategy
from qnt_research.trade_api import TradeAPI
from qnt_research.trader.trader import Trader
from qnt_research.utils.algo import TR, HHVToday, LLVToday, Settle
from qnt_research.virtual_exchange import gl_virtual_exchange
from qnt_utils.ctoolset import backward_search_true
from qnt_utils.enums import BasicData, StatusCode, StrategyMode
from qnt_utils.label import QSymbol


class Group:
    def __init__(self):
        """

        Args:
            direction (int): 1 表示排名越高越好，-1 表示排名越低越好
        """
        # with open("/home/<USER>/workspace/research/因子策略/group.json") as f:
        with open("/workspace/因子策略/group.json") as f:
            self.group = json.load(f)
        self.dt_lst = list(self.group.keys())
        self.dt_lst.sort()

    @lru_cache
    def get(self, dt, symbol):
        dt_ = dt.strftime("%Y-%m-%d %H:%M:%S")
        dt_ = self.dt_lst[np.searchsorted(self.dt_lst, dt_, "right") - 1]
        for k, v in self.group[dt_].items():
            if symbol in v:
                return int(k)
        return 0


class TestStrategy(SyncStrategy):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.p1: int
        self.p2: int
        self.p3: int
        self.p4: int
        self.p5: int
        self.p6: int
        self.p7: int
        self.p8: int
        self.symbol: QSymbol

        self.bkhigh = -np.inf
        self.sklow = np.inf

        self.group = Group()

    def init(self):
        data = self.subscribe(self.symbol, 4000, 2, BasicData.MINUTE)

    def on_bar(self, data):
        positions = self.trader.positions[1]
        portfolio = self.trader.portfolio[1]

        symbol = data.symbol

        if self.date_time.time() == datetime.time(15):
            signal = self.group.get(self.date_time, symbol)
            if symbol in positions.keys():
                if positions[symbol]["LONG"].amount > 0:
                    if signal != 1:
                        self.sell_close(symbol, positions[symbol]["LONG"].amount, None)

                if positions[symbol]["SHORT"].amount > 0:
                    if signal != -1:
                        self.buy_close(symbol, positions[symbol]["SHORT"].amount, None)

                if signal == 1 and positions[symbol]["LONG"].amount == 0:
                    self.buy_open(symbol, None, self.trader.init_money * 0.03)

                if signal == -1 and positions[symbol]["SHORT"].amount == 0:
                    self.sell_open(symbol, None, self.trader.init_money * 0.03)

            else:
                if signal == 1:
                    self.buy_open(symbol, None, self.trader.init_money * 0.03)
                if signal == -1:
                    self.sell_open(symbol, None, self.trader.init_money * 0.03)


if __name__ == "__main__":
    gl_event_drive_engine.set_env(StrategyMode.BACKTEST, "20200101 0900")
    trader = Trader(1e7, gl_virtual_exchange)

    strategy1 = TestStrategy(
        False, **{"p1": 100, "p2": 6, "p3": 65, "p4": 140, "p5": 8, "p6": 27, "p7": 50, "p8": 56, "symbol": "SA8888.CZCE"}
    )
    strategy1.register_trader(trader)
    gl_event_drive_engine.register_strategy(strategy1)
    gl_event_drive_engine.run()

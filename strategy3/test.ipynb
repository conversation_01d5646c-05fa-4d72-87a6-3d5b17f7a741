{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c82ef250-dad0-4a84-9a14-b34d00a8d7fb", "metadata": {"execution": {"iopub.execute_input": "2023-12-20T15:09:27.501833Z", "iopub.status.busy": "2023-12-20T15:09:27.501155Z", "iopub.status.idle": "2023-12-20T15:09:27.539403Z", "shell.execute_reply": "2023-12-20T15:09:27.537820Z", "shell.execute_reply.started": "2023-12-20T15:09:27.501781Z"}}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "id": "2ea83151-cd8d-4160-8965-4e548d5616aa", "metadata": {"execution": {"iopub.execute_input": "2023-12-20T15:09:27.540978Z", "iopub.status.busy": "2023-12-20T15:09:27.540720Z", "iopub.status.idle": "2023-12-20T15:09:29.983932Z", "shell.execute_reply": "2023-12-20T15:09:29.982761Z", "shell.execute_reply.started": "2023-12-20T15:09:27.540960Z"}, "tags": []}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from mfm import TestStrategy\n", "from qnt_research.data_manager.original_data import gl_original_data\n", "from qnt_research.engine import gl_event_drive_engine\n", "from qnt_research.trader.trader import Trader\n", "from qnt_research.virtual_exchange import gl_virtual_exchange\n", "from qnt_utils.enums import BasicData, StrategyMode\n", "from qnt_utils.label import QSymbol\n", "\n", "pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": 3, "id": "c763b7e2-0d18-4a07-9fce-c8850ff25380", "metadata": {"execution": {"iopub.execute_input": "2023-12-20T15:09:29.988078Z", "iopub.status.busy": "2023-12-20T15:09:29.986512Z", "iopub.status.idle": "2023-12-20T15:09:30.023453Z", "shell.execute_reply": "2023-12-20T15:09:30.021882Z", "shell.execute_reply.started": "2023-12-20T15:09:29.988031Z"}, "scrolled": true, "tags": []}, "outputs": [], "source": ["gl_event_drive_engine.set_env(StrategyMode.BACKTEST, \"20220101 0900\")\n", "trader = Trader(1e7, gl_virtual_exchange)\n", "\n", "strategy1 = TestStrategy(True, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": \"SA8888.CZCE\"})\n", "strategy1.register_trader(trader)\n", "gl_event_drive_engine.register_strategy(strategy1)\n", "\n", "# strategy2 = TestStrategy(False, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": \"SA888.CZCE\"})\n", "# strategy2.register_trader(trader)\n", "# gl_event_drive_engine.register_strategy(strategy2)"]}, {"cell_type": "code", "execution_count": 4, "id": "322b2faf-de6f-4002-9af0-fcc0b3ddda8a", "metadata": {"execution": {"iopub.execute_input": "2023-12-20T15:09:30.026450Z", "iopub.status.busy": "2023-12-20T15:09:30.025922Z", "iopub.status.idle": "2023-12-20T15:10:07.794228Z", "shell.execute_reply": "2023-12-20T15:10:07.792772Z", "shell.execute_reply.started": "2023-12-20T15:09:30.026417Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x700 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最终权益：12,410,514.05,  年化单利：12.85%,  年化复利：12.20%\n", "\n", "最大权益回撤：597,004.90,  最大损益回撤：220,943.79\n", "\n", "score: 0.8876,  夏普比率：1.67,  卡玛比率：5.82\n", "\n", "手续费：-16,513.00,  滑点成本：-94,360.00\n", "\n", "总盈利：2,410,514.05,  多：2,410,514.05,  空：0.00\n", "\n", "总次数：20,  多：20,  空：0\n", "\n", "日胜率：54.87%,  胜率：50.00%,  多：50.00%,  空：nan%\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>total_profit</th>\n", "      <th>number_of_trade</th>\n", "      <th>number_of_win</th>\n", "      <th>winning_percentage</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>position_type</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SA8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>2410514.052</td>\n", "      <td>20</td>\n", "      <td>10</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           total_profit  number_of_trade  number_of_win  \\\n", "symbol      position_type                                                 \n", "SA8888.CZCE LONG            2410514.052               20             10   \n", "\n", "                           winning_percentage  \n", "symbol      position_type                      \n", "SA8888.CZCE LONG                          0.5  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2023-12-20 23:10:07      Take time: 0 hours 0 mins 37 s\n"]}], "source": ["res = gl_event_drive_engine.run()"]}, {"cell_type": "code", "execution_count": null, "id": "b62b6261-3976-41e6-a50e-710adb1c9002", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2af1a6a9-16f3-4462-8d63-e915b4116403", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "be55eebd-d8a0-4423-b286-717767c993dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98696f70-7faa-4999-81a7-a9a113f6480d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "28d3ad09-1473-4d61-b399-7d087e9d2f6f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e3cebdbf-623a-4f73-9235-7a4da59ac715", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d6f27787-6f26-4f06-ba8a-ea83ac787d78", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abe8e4b8-c49f-440e-b9c6-be59ec48f25f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
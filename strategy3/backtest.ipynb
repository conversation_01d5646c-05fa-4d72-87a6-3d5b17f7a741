{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c82ef250-dad0-4a84-9a14-b34d00a8d7fb", "metadata": {"execution": {"iopub.execute_input": "2024-01-05T02:49:27.299182Z", "iopub.status.busy": "2024-01-05T02:49:27.298842Z", "iopub.status.idle": "2024-01-05T02:49:27.320398Z", "shell.execute_reply": "2024-01-05T02:49:27.319226Z", "shell.execute_reply.started": "2024-01-05T02:49:27.299154Z"}}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "id": "2ea83151-cd8d-4160-8965-4e548d5616aa", "metadata": {"execution": {"iopub.execute_input": "2024-01-05T02:49:27.465545Z", "iopub.status.busy": "2024-01-05T02:49:27.463975Z", "iopub.status.idle": "2024-01-05T02:49:29.034174Z", "shell.execute_reply": "2024-01-05T02:49:29.033308Z", "shell.execute_reply.started": "2024-01-05T02:49:27.465518Z"}, "tags": []}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from mfm import TestStrategy\n", "from qnt_research.data_manager.original_data import gl_original_data\n", "from qnt_research.engine import gl_event_drive_engine\n", "from qnt_research.trader.trader import Trader\n", "from qnt_research.virtual_exchange import gl_virtual_exchange\n", "from qnt_utils.enums import BasicData, StrategyMode\n", "from qnt_utils.label import QSymbol\n", "\n", "pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": 3, "id": "c763b7e2-0d18-4a07-9fce-c8850ff25380", "metadata": {"execution": {"iopub.execute_input": "2024-01-03T12:00:20.974787Z", "iopub.status.busy": "2024-01-03T12:00:20.974279Z", "iopub.status.idle": "2024-01-03T12:00:20.996841Z", "shell.execute_reply": "2024-01-03T12:00:20.996047Z", "shell.execute_reply.started": "2024-01-03T12:00:20.974771Z"}, "scrolled": true, "tags": []}, "outputs": [], "source": ["gl_event_drive_engine.set_env(StrategyMode.BACKTEST, \"20200101 0900\")\n", "trader = Trader(1e7, gl_virtual_exchange)\n", "\n", "strategy1 = TestStrategy(True, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": \"SA8888.CZCE\"})\n", "strategy1.register_trader(trader)\n", "gl_event_drive_engine.register_strategy(strategy1)\n", "\n", "# strategy2 = TestStrategy(False, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": \"SA888.CZCE\"})\n", "# strategy2.register_trader(trader)\n", "# gl_event_drive_engine.register_strategy(strategy2)"]}, {"cell_type": "code", "execution_count": 4, "id": "322b2faf-de6f-4002-9af0-fcc0b3ddda8a", "metadata": {"execution": {"iopub.execute_input": "2024-01-03T12:00:21.724092Z", "iopub.status.busy": "2024-01-03T12:00:21.723568Z", "iopub.status.idle": "2024-01-03T12:01:13.523456Z", "shell.execute_reply": "2024-01-03T12:01:13.522649Z", "shell.execute_reply.started": "2024-01-03T12:00:21.724058Z"}, "scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code_repositories/quant/src/qnt_research/utils/algo.py:22: RuntimeWarning: overflow encountered in multiply\n", "  return (volume * close).sum() / tmp\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1750x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最终权益：15,558,713.91,  年化单利：14.33%,  年化复利：12.07%\n", "\n", "最大权益回撤：719,441.00,  最大损益回撤：559,192.00\n", "\n", "score: 0.9500,  夏普比率：1.85,  卡玛比率：2.56\n", "\n", "手续费：-36,946.00,  滑点成本：-211,120.00\n", "\n", "总盈利：5,558,713.91,  多：3,599,282.21,  空：1,959,431.70\n", "\n", "总次数：46,  多：15,  空：31\n", "\n", "日胜率：53.24%,  胜率：36.96%,  多：60.00%,  空：25.81%\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>total_profit</th>\n", "      <th>number_of_trade</th>\n", "      <th>number_of_win</th>\n", "      <th>winning_percentage</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>position_type</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">SA8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>3599282.212</td>\n", "      <td>15</td>\n", "      <td>9</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>1959431.698</td>\n", "      <td>31</td>\n", "      <td>8</td>\n", "      <td>0.258065</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           total_profit  number_of_trade  number_of_win  \\\n", "symbol      position_type                                                 \n", "SA8888.CZCE LONG            3599282.212               15              9   \n", "            SHORT           1959431.698               31              8   \n", "\n", "                           winning_percentage  \n", "symbol      position_type                      \n", "SA8888.CZCE LONG                     0.600000  \n", "            SHORT                    0.258065  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-03 12:01:13      Take time: 0 hours 0 mins 51 s\n"]}], "source": ["res = gl_event_drive_engine.run()"]}, {"cell_type": "code", "execution_count": null, "id": "7a47dd64-dff8-499a-af17-d380d9e17b68", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a239a217-2430-49d1-ba17-bb9a909913ba", "metadata": {}, "source": ["## FT001/factor001"]}, {"cell_type": "code", "execution_count": 3, "id": "0145cd9b-68f6-4e71-bb62-711ac96cd46d", "metadata": {"execution": {"iopub.execute_input": "2024-01-05T02:14:52.861644Z", "iopub.status.busy": "2024-01-05T02:14:52.861364Z", "iopub.status.idle": "2024-01-05T02:43:42.856039Z", "shell.execute_reply": "2024-01-05T02:43:42.855124Z", "shell.execute_reply.started": "2024-01-05T02:14:52.861628Z"}, "scrolled": true, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code_repositories/quant/src/qnt_research/utils/algo.py:22: RuntimeWarning: overflow encountered in multiply\n", "  return (volume * close).sum() / tmp\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1750x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最终权益：23,518,247.95,  年化单利：29.70%,  年化复利：20.67%\n", "\n", "最大权益回撤：3,274,641.96,  最大损益回撤：3,231,952.88\n", "\n", "score: 0.9083,  夏普比率：0.98,  卡玛比率：0.92\n", "\n", "手续费：-1,563,622.86,  滑点成本：-3,570,825.00\n", "\n", "总盈利：13,305,367.99,  多：14,057,663.85,  空：-752,295.86\n", "\n", "总次数：1227,  多：568,  空：659\n", "\n", "日胜率：50.19%,  胜率：32.60%,  多：35.04%,  空：30.50%\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>total_profit</th>\n", "      <th>number_of_trade</th>\n", "      <th>number_of_win</th>\n", "      <th>winning_percentage</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>position_type</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">AG8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>2.608577e+05</td>\n", "      <td>34</td>\n", "      <td>11</td>\n", "      <td>0.323529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-1.508172e+04</td>\n", "      <td>48</td>\n", "      <td>15</td>\n", "      <td>0.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">AP8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>-3.501424e+05</td>\n", "      <td>33</td>\n", "      <td>9</td>\n", "      <td>0.272727</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>8.965712e+05</td>\n", "      <td>28</td>\n", "      <td>12</td>\n", "      <td>0.428571</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">BU8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>1.535984e+06</td>\n", "      <td>26</td>\n", "      <td>11</td>\n", "      <td>0.423077</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>5.736335e+05</td>\n", "      <td>35</td>\n", "      <td>12</td>\n", "      <td>0.342857</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">EB8888.DCE</th>\n", "      <th>LONG</th>\n", "      <td>2.910586e+05</td>\n", "      <td>21</td>\n", "      <td>8</td>\n", "      <td>0.380952</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-5.531134e+05</td>\n", "      <td>27</td>\n", "      <td>7</td>\n", "      <td>0.259259</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">EG8888.DCE</th>\n", "      <th>LONG</th>\n", "      <td>3.589386e+05</td>\n", "      <td>25</td>\n", "      <td>10</td>\n", "      <td>0.400000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-6.562693e+04</td>\n", "      <td>35</td>\n", "      <td>11</td>\n", "      <td>0.314286</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">FG8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>1.453704e+06</td>\n", "      <td>31</td>\n", "      <td>8</td>\n", "      <td>0.258065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-1.434529e+06</td>\n", "      <td>38</td>\n", "      <td>7</td>\n", "      <td>0.184211</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">FU8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>-3.344139e+05</td>\n", "      <td>26</td>\n", "      <td>8</td>\n", "      <td>0.307692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>3.086600e+05</td>\n", "      <td>42</td>\n", "      <td>14</td>\n", "      <td>0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">I8888.DCE</th>\n", "      <th>LONG</th>\n", "      <td>7.394711e+05</td>\n", "      <td>27</td>\n", "      <td>12</td>\n", "      <td>0.444444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-1.376389e+05</td>\n", "      <td>34</td>\n", "      <td>11</td>\n", "      <td>0.323529</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">JD8888.DCE</th>\n", "      <th>LONG</th>\n", "      <td>6.790192e+05</td>\n", "      <td>26</td>\n", "      <td>10</td>\n", "      <td>0.384615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>7.550908e+05</td>\n", "      <td>24</td>\n", "      <td>10</td>\n", "      <td>0.416667</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">MA8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>1.224222e+06</td>\n", "      <td>32</td>\n", "      <td>11</td>\n", "      <td>0.343750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-1.263171e+06</td>\n", "      <td>49</td>\n", "      <td>11</td>\n", "      <td>0.224490</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">NI8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>-1.054874e+05</td>\n", "      <td>46</td>\n", "      <td>17</td>\n", "      <td>0.369565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-3.341697e+05</td>\n", "      <td>36</td>\n", "      <td>11</td>\n", "      <td>0.305556</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PG8888.DCE</th>\n", "      <th>LONG</th>\n", "      <td>1.626326e+06</td>\n", "      <td>27</td>\n", "      <td>9</td>\n", "      <td>0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>1.824261e+05</td>\n", "      <td>45</td>\n", "      <td>11</td>\n", "      <td>0.244444</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">PP8888.DCE</th>\n", "      <th>LONG</th>\n", "      <td>-8.965668e+04</td>\n", "      <td>33</td>\n", "      <td>9</td>\n", "      <td>0.272727</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-9.531434e+05</td>\n", "      <td>43</td>\n", "      <td>13</td>\n", "      <td>0.302326</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">RB8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>1.407176e+06</td>\n", "      <td>32</td>\n", "      <td>15</td>\n", "      <td>0.468750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>5.275307e+04</td>\n", "      <td>37</td>\n", "      <td>13</td>\n", "      <td>0.351351</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">RU8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>5.804449e+05</td>\n", "      <td>45</td>\n", "      <td>12</td>\n", "      <td>0.266667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>1.146702e+05</td>\n", "      <td>35</td>\n", "      <td>14</td>\n", "      <td>0.400000</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">SA8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>4.217091e+06</td>\n", "      <td>21</td>\n", "      <td>12</td>\n", "      <td>0.571429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>1.949382e+06</td>\n", "      <td>31</td>\n", "      <td>8</td>\n", "      <td>0.258065</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">TA8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>5.715694e+05</td>\n", "      <td>25</td>\n", "      <td>9</td>\n", "      <td>0.360000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>1.238844e+05</td>\n", "      <td>38</td>\n", "      <td>13</td>\n", "      <td>0.342105</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">ZN8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>-8.498672e+03</td>\n", "      <td>58</td>\n", "      <td>18</td>\n", "      <td>0.310345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SHORT</th>\n", "      <td>-9.528924e+05</td>\n", "      <td>34</td>\n", "      <td>8</td>\n", "      <td>0.235294</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           total_profit  number_of_trade  number_of_win  \\\n", "symbol      position_type                                                 \n", "AG8888.SHFE LONG           2.608577e+05               34             11   \n", "            SHORT         -1.508172e+04               48             15   \n", "AP8888.CZCE LONG          -3.501424e+05               33              9   \n", "            SHORT          8.965712e+05               28             12   \n", "BU8888.SHFE LONG           1.535984e+06               26             11   \n", "            SHORT          5.736335e+05               35             12   \n", "EB8888.DCE  LONG           2.910586e+05               21              8   \n", "            SHORT         -5.531134e+05               27              7   \n", "EG8888.DCE  LONG           3.589386e+05               25             10   \n", "            SHORT         -6.562693e+04               35             11   \n", "FG8888.CZCE LONG           1.453704e+06               31              8   \n", "            SHORT         -1.434529e+06               38              7   \n", "FU8888.SHFE LONG          -3.344139e+05               26              8   \n", "            SHORT          3.086600e+05               42             14   \n", "I8888.DCE   LONG           7.394711e+05               27             12   \n", "            SHORT         -1.376389e+05               34             11   \n", "JD8888.DCE  LONG           6.790192e+05               26             10   \n", "            SHORT          7.550908e+05               24             10   \n", "MA8888.CZCE LONG           1.224222e+06               32             11   \n", "            SHORT         -1.263171e+06               49             11   \n", "NI8888.SHFE LONG          -1.054874e+05               46             17   \n", "            SHORT         -3.341697e+05               36             11   \n", "PG8888.DCE  LONG           1.626326e+06               27              9   \n", "            SHORT          1.824261e+05               45             11   \n", "PP8888.<PERSON><PERSON>  LONG          -8.965668e+04               33              9   \n", "            SHORT         -9.531434e+05               43             13   \n", "RB8888.SHFE LONG           1.407176e+06               32             15   \n", "            SHORT          5.275307e+04               37             13   \n", "RU8888.SHFE LONG           5.804449e+05               45             12   \n", "            SHORT          1.146702e+05               35             14   \n", "SA8888.CZCE LONG           4.217091e+06               21             12   \n", "            SHORT          1.949382e+06               31              8   \n", "TA8888.<PERSON>ZCE LONG           5.715694e+05               25              9   \n", "            SHORT          1.238844e+05               38             13   \n", "ZN8888.SHFE LONG          -8.498672e+03               58             18   \n", "            SHORT         -9.528924e+05               34              8   \n", "\n", "                           winning_percentage  \n", "symbol      position_type                      \n", "AG8888.SHFE LONG                     0.323529  \n", "            SHORT                    0.312500  \n", "AP8888.CZCE LONG                     0.272727  \n", "            SHORT                    0.428571  \n", "BU8888.SHFE LONG                     0.423077  \n", "            SHORT                    0.342857  \n", "EB8888.DCE  LONG                     0.380952  \n", "            SHORT                    0.259259  \n", "EG8888.DCE  LONG                     0.400000  \n", "            SHORT                    0.314286  \n", "FG8888.CZCE LONG                     0.258065  \n", "            SHORT                    0.184211  \n", "FU8888.SHFE LONG                     0.307692  \n", "            SHORT                    0.333333  \n", "I8888.DCE   LONG                     0.444444  \n", "            SHORT                    0.323529  \n", "JD8888.DCE  LONG                     0.384615  \n", "            SHORT                    0.416667  \n", "MA8888.CZCE LONG                     0.343750  \n", "            SHORT                    0.224490  \n", "NI8888.SHFE LONG                     0.369565  \n", "            SHORT                    0.305556  \n", "PG8888.DCE  LONG                     0.333333  \n", "            SHORT                    0.244444  \n", "PP8888.DCE  LONG                     0.272727  \n", "            SHORT                    0.302326  \n", "RB8888.SHFE LONG                     0.468750  \n", "            SHORT                    0.351351  \n", "RU8888.SHFE LONG                     0.266667  \n", "            SHORT                    0.400000  \n", "SA8888.CZCE LONG                     0.571429  \n", "            SHORT                    0.258065  \n", "TA8888.CZCE LONG                     0.360000  \n", "            SHORT                    0.342105  \n", "ZN8888.SHFE LONG                     0.310345  \n", "            SHORT                    0.235294  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-05 02:43:42      Take time: 0 hours 28 mins 49 s\n"]}], "source": ["gl_event_drive_engine.set_env(StrategyMode.BACKTEST, \"20200101 0900\")\n", "trader = Trader(1e7, gl_virtual_exchange)\n", "\n", "for symbol in [\n", "    \"SA8888.CZCE\",\n", "    \"RB8888.SHFE\",\n", "    \"FG8888.CZCE\",\n", "    \"SC8888.INE\",\n", "    \"BU8888.SHFE\",\n", "    \"AG8888.SHFE\",\n", "    \"NI8888.SHFE\",\n", "    \"ZN8888.SHFE\",\n", "    \"RU8888.SHFE\",\n", "    \"FU8888.SHFE\",\n", "    \"I8888.DCE\",\n", "    \"JD8888.DCE\",\n", "    \"PP8888.DCE\",\n", "    \"PG8888.DCE\",\n", "    \"TA8888.CZCE\",\n", "    \"MA8888.CZCE\",\n", "    \"AP8888.CZCE\",\n", "    \"EG8888.DCE\",\n", "    \"EB8888.DCE\",\n", "]:\n", "    strategy1 = TestStrategy(True, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": symbol})\n", "    strategy1.register_trader(trader)\n", "    gl_event_drive_engine.register_strategy(strategy1)\n", "\n", "res = gl_event_drive_engine.run()"]}, {"cell_type": "markdown", "id": "25fed64f-7396-4a26-bcf9-f24d030b2b37", "metadata": {}, "source": ["## alpha_173"]}, {"cell_type": "code", "execution_count": 3, "id": "73d646f1-a330-4794-be7e-172483c73645", "metadata": {"execution": {"iopub.execute_input": "2024-01-05T02:49:33.597771Z", "iopub.status.busy": "2024-01-05T02:49:33.597218Z", "iopub.status.idle": "2024-01-05T03:17:34.135241Z", "shell.execute_reply": "2024-01-05T03:17:34.134350Z", "shell.execute_reply.started": "2024-01-05T02:49:33.597747Z"}, "scrolled": true, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code_repositories/quant/src/qnt_research/utils/algo.py:22: RuntimeWarning: overflow encountered in multiply\n", "  return (volume * close).sum() / tmp\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1750x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最终权益：30,177,299.76,  年化单利：44.33%,  年化复利：27.46%\n", "\n", "最大权益回撤：2,135,488.78,  最大损益回撤：2,330,483.88\n", "\n", "score: 0.9618,  夏普比率：1.72,  卡玛比率：1.90\n", "\n", "手续费：-1,261,612.20,  滑点成本：-2,614,730.00\n", "\n", "总盈利：19,938,005.06,  多：21,150,485.62,  空：-1,212,480.56\n", "\n", "总次数：839,  多：442,  空：397\n", "\n", "日胜率：52.37%,  胜率：33.25%,  多：38.24%,  空：27.71%\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>total_profit</th>\n", "      <th>number_of_trade</th>\n", "      <th>number_of_win</th>\n", "      <th>winning_percentage</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>position_type</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AG8888.SHFE</th>\n", "      <th>SHORT</th>\n", "      <td>-1.330553e+05</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AP8888.CZCE</th>\n", "      <th>SHORT</th>\n", "      <td>3.486810e+05</td>\n", "      <td>56</td>\n", "      <td>16</td>\n", "      <td>0.285714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BU8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>3.496087e+06</td>\n", "      <td>35</td>\n", "      <td>19</td>\n", "      <td>0.542857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EB8888.DCE</th>\n", "      <th>SHORT</th>\n", "      <td>6.119172e+05</td>\n", "      <td>64</td>\n", "      <td>20</td>\n", "      <td>0.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FG8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>3.084833e+06</td>\n", "      <td>83</td>\n", "      <td>23</td>\n", "      <td>0.277108</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FU8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>1.665585e+06</td>\n", "      <td>66</td>\n", "      <td>22</td>\n", "      <td>0.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>I8888.DCE</th>\n", "      <th>LONG</th>\n", "      <td>2.123210e+06</td>\n", "      <td>54</td>\n", "      <td>24</td>\n", "      <td>0.444444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MA8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>1.529099e+06</td>\n", "      <td>73</td>\n", "      <td>27</td>\n", "      <td>0.369863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NI8888.SHFE</th>\n", "      <th>SHORT</th>\n", "      <td>-4.836575e+05</td>\n", "      <td>81</td>\n", "      <td>23</td>\n", "      <td>0.283951</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PP8888.DCE</th>\n", "      <th>SHORT</th>\n", "      <td>-1.952203e+05</td>\n", "      <td>47</td>\n", "      <td>13</td>\n", "      <td>0.276596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RB8888.SHFE</th>\n", "      <th>LONG</th>\n", "      <td>5.669891e+03</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RU8888.SHFE</th>\n", "      <th>SHORT</th>\n", "      <td>-6.390185e+05</td>\n", "      <td>69</td>\n", "      <td>18</td>\n", "      <td>0.260870</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SA8888.CZCE</th>\n", "      <th>LONG</th>\n", "      <td>6.515679e+06</td>\n", "      <td>51</td>\n", "      <td>22</td>\n", "      <td>0.431373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SC8888.INE</th>\n", "      <th>LONG</th>\n", "      <td>2.730323e+06</td>\n", "      <td>78</td>\n", "      <td>31</td>\n", "      <td>0.397436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZN8888.SHFE</th>\n", "      <th>SHORT</th>\n", "      <td>-7.221271e+05</td>\n", "      <td>78</td>\n", "      <td>20</td>\n", "      <td>0.256410</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           total_profit  number_of_trade  number_of_win  \\\n", "symbol      position_type                                                 \n", "AG8888.SHFE SHORT         -1.330553e+05                2              0   \n", "AP8888.CZCE SHORT          3.486810e+05               56             16   \n", "BU8888.SHFE LONG           3.496087e+06               35             19   \n", "EB8888.DCE  SHORT          6.119172e+05               64             20   \n", "FG8888.CZCE LONG           3.084833e+06               83             23   \n", "FU8888.SHFE LONG           1.665585e+06               66             22   \n", "I8888.DCE   LONG           2.123210e+06               54             24   \n", "MA8888.CZCE LONG           1.529099e+06               73             27   \n", "NI8888.SHFE SHORT         -4.836575e+05               81             23   \n", "PP8888.DCE  SHORT         -1.952203e+05               47             13   \n", "RB8888.SHFE LONG           5.669891e+03                2              1   \n", "RU8888.SHFE SHORT         -6.390185e+05               69             18   \n", "SA8888.CZCE LONG           6.515679e+06               51             22   \n", "SC8888.INE  LONG           2.730323e+06               78             31   \n", "ZN8888.SHFE SHORT         -7.221271e+05               78             20   \n", "\n", "                           winning_percentage  \n", "symbol      position_type                      \n", "AG8888.SHFE SHORT                    0.000000  \n", "AP8888.CZCE SHORT                    0.285714  \n", "BU8888.SHFE LONG                     0.542857  \n", "EB8888.DCE  SHORT                    0.312500  \n", "FG8888.CZCE LONG                     0.277108  \n", "FU8888.SHFE LONG                     0.333333  \n", "I8888.DCE   LONG                     0.444444  \n", "MA8888.CZCE LONG                     0.369863  \n", "NI8888.SHFE SHORT                    0.283951  \n", "PP8888.DCE  SHORT                    0.276596  \n", "RB8888.SHFE LONG                     0.500000  \n", "RU8888.SHFE SHORT                    0.260870  \n", "SA8888.CZCE LONG                     0.431373  \n", "SC8888.INE  LONG                     0.397436  \n", "ZN8888.SHFE SHORT                    0.256410  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-05 03:17:34      Take time: 0 hours 28 mins 0 s\n"]}], "source": ["gl_event_drive_engine.set_env(StrategyMode.BACKTEST, \"20200101 0900\")\n", "trader = Trader(1e7, gl_virtual_exchange)\n", "\n", "for symbol in [\n", "    \"SA8888.CZCE\",\n", "    \"RB8888.SHFE\",\n", "    \"FG8888.CZCE\",\n", "    \"SC8888.INE\",\n", "    \"BU8888.SHFE\",\n", "    \"AG8888.SHFE\",\n", "    \"NI8888.SHFE\",\n", "    \"ZN8888.SHFE\",\n", "    \"RU8888.SHFE\",\n", "    \"FU8888.SHFE\",\n", "    \"I8888.DCE\",\n", "    \"JD8888.DCE\",\n", "    \"PP8888.DCE\",\n", "    \"PG8888.DCE\",\n", "    \"TA8888.CZCE\",\n", "    \"MA8888.CZCE\",\n", "    \"AP8888.CZCE\",\n", "    \"EG8888.DCE\",\n", "    \"EB8888.DCE\",\n", "]:\n", "    strategy1 = TestStrategy(True, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": symbol})\n", "    strategy1.register_trader(trader)\n", "    gl_event_drive_engine.register_strategy(strategy1)\n", "\n", "res = gl_event_drive_engine.run()"]}, {"cell_type": "code", "execution_count": null, "id": "d6f27787-6f26-4f06-ba8a-ea83ac787d78", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abe8e4b8-c49f-440e-b9c6-be59ec48f25f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
import json
from functools import lru_cache

import numpy as np
from qnt_research.engine import gl_event_drive_engine
from qnt_research.strategy.base import SyncStrategy
from qnt_research.trade_api import TradeAPI
from qnt_research.trader.trader import Trader
from qnt_research.utils.algo import TR, HHVToday, LLVToday, Settle
from qnt_research.virtual_exchange import gl_virtual_exchange
from qnt_utils.ctoolset import backward_search_true
from qnt_utils.enums import BasicData, StatusCode, StrategyMode
from qnt_utils.label import QSymbol


class Group:
    def __init__(self):
        """

        Args:
            direction (int): 1 表示排名越高越好，-1 表示排名越低越好
        """
        # with open("/home/<USER>/workspace/research/因子策略/group.json") as f:
        with open("/workspace/因子策略/group.json") as f:
            self.group = json.load(f)
        self.dt_lst = list(self.group.keys())
        self.dt_lst.sort()

    @lru_cache
    def get(self, dt, symbol):
        dt_ = dt.strftime("%Y-%m-%d %H:%M:%S")
        dt_ = self.dt_lst[np.searchsorted(self.dt_lst, dt_, "right") - 1]
        for k, v in self.group[dt_].items():
            if symbol in v:
                return int(k)
        return 0


class TestStrategy(SyncStrategy):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.p1: int
        self.p2: int
        self.p3: int
        self.p4: int
        self.p5: int
        self.p6: int
        self.p7: int
        self.p8: int
        self.symbol: QSymbol

        self.bkhigh = -np.inf
        self.sklow = np.inf

        self.group = Group()

    def init(self):
        data = self.subscribe(self.symbol, 4000, 2, BasicData.MINUTE)
        data.create_series("tr", TR(3))
        data.create_series("hhv_today", HHVToday("high"))
        data.create_series("llv_today", LLVToday("low"))
        data.create_series("llvc_today", LLVToday("close"))
        data.create_series("llvo_today", LLVToday("open"))
        data.create_series("hhvc_today", HHVToday("close"))
        data.create_series("hhvo_today", HHVToday("open"))
        data.create_series("my_settle", Settle())
        data.create_series("cfj1")
        data.create_series("upperbound")
        data.create_series("lowerbound")
        data.create_series("greater_than_upperbound")
        data.create_series("lower_than_lowerbound")

    def on_bar(self, data):
        positions = self.trader.positions[1]
        portfolio = self.trader.portfolio[1]

        symbol = data.symbol
        n = data.n_trade_day[-1]
        nnn = data.n_natural_day[-1]
        n1 = data.n_trade_day[int(-n - 1)]
        n2 = data.n_trade_day[int(-n - n1 - 1)]
        n3 = data.n_trade_day[int(-n - n1 - n2 - 1)]
        num_bars_oneday = max(n1, n2, n3)

        atr = data.tr[-int(num_bars_oneday) :].mean()
        hh_today = data.hhv_today[-1]
        hh_yestoday = data.hhv_today[-1 - n]
        ll_today = data.llv_today[-1]
        ll_yestoday = data.llv_today[-1 - n]
        cc_yestoday = data.close[-1 - n]
        oo_today = data.open[-n]

        cfj1 = max(hh_yestoday - cc_yestoday, cc_yestoday - ll_yestoday) * self.p1 / 100 if n == 1 else 0
        data.cfj1.update(cfj1)
        cfj = min(data.cfj1[backward_search_true(data.n_trade_day == 1, self.p2) :].sum() / self.p2, cc_yestoday * 45 / 10 / 100)

        upperbound = min(max(cc_yestoday + cfj, oo_today + self.p3 / 100 * cfj), oo_today + self.p4 / 100 * cfj)
        data.upperbound.update(upperbound)
        data.greater_than_upperbound.update(data.close[-1] > upperbound)

        lowerbound = max(min(cc_yestoday - cfj, oo_today - self.p3 / 100 * cfj), oo_today - self.p4 / 100 * cfj)
        data.lowerbound.update(lowerbound)
        data.lower_than_lowerbound.update(data.close[-1] < lowerbound)

        tmp_greater = data.greater_than_upperbound[:-n]
        tmp_lower = data.lower_than_lowerbound[:-n]
        tmp_greater = backward_search_true(tmp_greater == True, 1)
        tmp_lower = backward_search_true(tmp_lower == True, 1)
        tmp_greater = (data.size - n - tmp_greater - 1) if not tmp_greater == -1 else np.inf
        tmp_lower = (data.size - n - tmp_lower - 1) if not tmp_lower == -1 else np.inf

        if tmp_greater < num_bars_oneday * 2 and tmp_greater > tmp_lower:
            upperbound1 = upperbound + min(max(0, data.upperbound[-n - 1] - upperbound), 20 / 100 * cfj)
        else:
            upperbound1 = upperbound
        if tmp_lower < num_bars_oneday * 2 and tmp_lower > tmp_greater:
            lowerbound1 = lowerbound - min(max(0, lowerbound - data.lowerbound[-n - 1]), 20 / 100 * cfj)
        else:
            lowerbound1 = lowerbound

        if not symbol in positions.keys() or (
            positions[symbol]["LONG"].amount == 0 and positions[symbol]["SHORT"].amount == 0 and portfolio.frozen_cash == 0
        ):
            con1_l = data.close[-1] > upperbound1
            con2_l = data.close[-1] >= max(data.close[-max(15, n) :].max(), data.open[-max(15, n) :].max())
            con3_l = (
                not data.low[backward_search_true(data.n_trade_day == 1, 2) :].min()
                == data.low[backward_search_true(data.n_trade_day == 1, 10) :].min()
            )
            con4_l = (
                (n >= 10 or (n > 2 and data.close[-1] > data.high[-n:-1].max()))
                if data.close[-n] / data.close[-n - 1] - 1 > 10 / 1000
                else True
            )
            con5_l = (
                (nnn >= 10 or (nnn > 2 and data.close[-1] > data.high[-nnn:-1].max()))
                if data.close[-nnn] / data.close[-nnn - 1] - 1 > 10 / 1000
                else True
            )
            con6_l = data.close[-1] < data.my_settle[-n - 1] * (1 + 45 / 1000)

            con1_s = data.close[-1] < lowerbound1
            con2_s = data.close[-1] <= min(data.close[-max(15, n) :].min(), data.open[-max(15, n) :].min())
            con3_s = (
                not data.high[backward_search_true(data.n_trade_day == 1, 2) :].max()
                == data.high[backward_search_true(data.n_trade_day == 1, 10) :].max()
            )
            con4_s = (
                (n >= 10 or (n > 2 and data.close[-1] < data.low[-n:-1].min()))
                if 1 - data.close[-n] / data.close[-n - 1] > 10 / 1000
                else True
            )
            con5_s = (
                (nnn >= 10 or (nnn > 2 and data.close[-1] < data.low[-nnn:-1].min()))
                if 1 - data.close[-nnn] / data.close[-nnn - 1] > 10 / 1000
                else True
            )
            con6_s = data.close[-1] > data.my_settle[-n - 1] * (1 - 45 / 1000)
            if con1_l and con2_l and con3_l and con4_l and con5_l and con6_l and self.group.get(self.date_time, symbol) == 1:
                status_code, _ = self.buy_open(symbol, None, self.trader.init_money * 0.05)
                if status_code == StatusCode.SUCCESS:
                    self.bkhigh = -np.inf
            elif con1_s and con2_s and con3_s and con4_s and con5_s and con6_s and self.group.get(self.date_time, symbol) == -1:
                status_code, _ = self.sell_open(symbol, None, self.trader.init_money * 0.05)
                if status_code == StatusCode.SUCCESS:
                    self.sklow = np.inf

        elif positions[symbol]["LONG"].available > 0:
            self.bkhigh = max(self.bkhigh, data.high[-1])
            if (
                data.close[-1] < (positions[symbol]["LONG"].cost_basis - 1) * (1 - self.p5 / 1000)
                and data.close[-1] < data.my_settle[-1]
            ):
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
            elif data.close[-1] < self.bkhigh * (1 - self.p6 / 1000) and data.close[-1] < (
                positions[symbol]["LONG"].cost_basis - 1
            ) * (1 + self.p7 / 1000):
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
            elif data.close[-1] < self.bkhigh * (1 - self.p8 / 1000) and data.close[-1] >= (
                positions[symbol]["LONG"].cost_basis - 1
            ) * (1 + self.p7 / 1000):
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
            elif data.close[-1] < data.hhv_today[-1] * (1 - 52 / 1000):
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)

        elif positions[symbol]["SHORT"].available > 0:
            self.sklow = min(self.sklow, data.low[-1])
            if (
                data.close[-1] > (positions[symbol]["SHORT"].cost_basis + 1) * (1 + self.p5 / 1000)
                and data.close[-1] > data.my_settle[-1]
            ):
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)
            elif data.close[-1] > self.sklow * (1 + self.p6 / 1000) and data.close[-1] > (
                positions[symbol]["SHORT"].cost_basis + 1
            ) * (1 - self.p7 / 1000):
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)
            elif data.close[-1] > self.sklow * (1 + self.p8 / 1000) and data.close[-1] <= (
                positions[symbol]["SHORT"].cost_basis + 1
            ) * (1 - self.p7 / 1000):
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)
            elif data.close[-1] > data.llv_today[-1] * (1 + 52 / 1000):
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)


if __name__ == "__main__":
    gl_event_drive_engine.set_env(StrategyMode.TRADE, "20230101 0900", addr={1: {"host": "*************", "port": 12330}})
    strategy = TestStrategy(
        **{"p1": 100, "p2": 6, "p3": 65, "p4": 140, "p5": 8, "p6": 27, "p7": 56, "p8": 50, "symbol": "SC8888.INE"}
    )
    strategy.register_trader(
        Trader(200000, gl_virtual_exchange),
        Trader(200000, TradeAPI("178081", False, "*************", 10000)),
        {"SC8888.INE": "SC2309.INE"},
    )
    gl_event_drive_engine.register_strategy(strategy)
    gl_event_drive_engine.run()

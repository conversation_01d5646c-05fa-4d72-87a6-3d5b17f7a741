import pathlib
import sys

sys.path.append(str(pathlib.Path(__file__).parent.parent))

import altair as alt
import pandas as pd
import streamlit as st
from qnt_utils.config import get_config
from qnt_utils.toolset import decrypt
from risk_control import RiskControl
from sqlalchemy import create_engine
from qnt_research.api import get_trade_days

st.set_page_config(page_title="资产分析", page_icon=None, layout="wide", initial_sidebar_state="auto", menu_items=None)
DEBUG = get_config()["controller"]["settings"]["is_debug"]

engine = create_engine(
    f"postgresql+psycopg2://{decrypt(get_config()['database']['username'])}:{decrypt(get_config()['database']['password'])}@{get_config()['database']['host']}:{get_config()['database']['port'] if not DEBUG else 5433}/trader"
)


# @st.fragment(run_every="15s")
def display_risk_control():
    with st.container():
        risk_control = RiskControl(3.6e5)
        st.header("资产分析")
        df = pd.read_sql("select * from portfolio;", engine)

        df = df.groupby("date").sum()[["equity", "market_value"]]
        for _, row in df.iterrows():
            risk_control.register_portfolio(row["equity"], row["market_value"])
            if risk_control.iopv > 1.25:
                risk_control = RiskControl(row["equity"])

        df = df.reset_index()
        df["date"] = pd.to_datetime(df["date"])
        df = df.loc[df["date"] >= pd.Timestamp("2025-01-01")]

        orders_df = pd.read_sql("select * from orders;", engine)

        buy_orders_df = orders_df.loc[
            orders_df["direction"].isin(["BUY", "CREDIT_BUY"])
            & (orders_df["trade_amount"] > 0)
            & (orders_df["order_type"] == "C")
        ]
        sell_orders_df = orders_df.loc[
            orders_df["direction"].isin(["SELL", "CREDIT_SELL"])
            & (orders_df["trade_amount"] > 0)
            & (orders_df["order_type"] == "C")
        ]

        buying_amount = (
            (buy_orders_df["trade_price"] * buy_orders_df["trade_amount"])
            .groupby(pd.Index(buy_orders_df["c_dt"]).tz_convert("Asia/Shanghai").normalize())  # type: ignore
            .sum()
        )
        buying_amount.index = buying_amount.index.tz_localize(None)
        buying_amount = buying_amount.rename("buying_amount").reset_index().rename(columns={"c_dt": "date"})
        df = pd.merge(df, buying_amount, how="left", on="date")

        selling_amount = (
            (sell_orders_df["trade_price"] * sell_orders_df["trade_amount"])
            .groupby(pd.Index(sell_orders_df["c_dt"]).tz_convert("Asia/Shanghai").normalize())  # type: ignore
            .sum()
        )
        selling_amount.index = selling_amount.index.tz_localize(None)
        selling_amount = selling_amount.rename("selling_amount").reset_index().rename(columns={"c_dt": "date"})
        df = pd.merge(df, selling_amount, how="left", on="date")

        df1 = df[["date", "equity"]].copy()
        df1.rename(columns={"equity": "value"}, inplace=True)
        df1["type"] = "equity"

        df2 = df[["date", "market_value"]].copy()
        df2.rename(columns={"market_value": "value"}, inplace=True)
        df2["type"] = "market_value"

        df3 = df[["date", "buying_amount"]].copy()
        df3.rename(columns={"buying_amount": "value"}, inplace=True)
        df3["type"] = "buying_amount"

        df4 = df[["date", "selling_amount"]].copy()
        df4.rename(columns={"selling_amount": "value"}, inplace=True)
        df4["type"] = "selling_amount"

        concat_df = pd.concat([df1, df2, df3, df4], axis=0)
        concat_df = concat_df.loc[
            concat_df["date"].isin(pd.Index(get_trade_days("SSE", "2024-01-01", "2025-12-31")).tz_localize(None))  # type: ignore
        ]

        brush = alt.selection_interval(encodings=["x"], value={"x": (concat_df["date"].min(), concat_df["date"].max())})
        nearest1 = alt.selection_point(nearest=True, on="pointerover", fields=["date"], empty=False)
        when_near1 = alt.when(nearest1)
        rules1 = (
            alt.Chart(concat_df)
            .transform_pivot("type", value="value", groupby=["date"])
            .mark_rule(color="gray")
            .encode(
                x="date:T",
                opacity=when_near1.then(alt.value(0.3)).otherwise(alt.value(0)),
                tooltip=[
                    alt.Tooltip("date", type="temporal"),
                    alt.Tooltip("equity", type="quantitative"),
                    alt.Tooltip("market_value", type="quantitative"),
                    alt.Tooltip("buying_amount", type="quantitative"),
                    alt.Tooltip("selling_amount", type="quantitative"),
                ],
            )
            .add_params(nearest1)
        )

        chart1 = (
            alt.Chart(concat_df)
            .mark_line()
            .encode(
                x=alt.X("date:T", title="", axis=alt.Axis(format="%Y-%m-%d", labels=False, ticks=False)),
                y=alt.Y("value:Q", title="equity", scale=alt.Scale(zero=False)),
            )
            .transform_filter(alt.datum.type == "equity")
        )
        points1 = chart1.mark_point().encode(opacity=when_near1.then(alt.value(1)).otherwise(alt.value(0)))
        chart1 = alt.layer(chart1, points1, rules1).properties(height=260)

        chart2 = (
            alt.Chart(concat_df)
            .mark_line(color="gray")
            .encode(
                x=alt.X("date:T", title="", axis=alt.Axis(format="%Y-%m-%d", labels=False, ticks=False)),
                y=alt.Y("value:Q", title="market_value", scale=alt.Scale(zero=False)),
            )
            .transform_filter(alt.datum.type == "market_value")
            .properties(height=200)
        )
        points2 = chart2.mark_point().encode(opacity=when_near1.then(alt.value(1)).otherwise(alt.value(0)))
        chart2 = alt.layer(chart2, points2, rules1).properties(height=200)

        chart3 = (
            alt.Chart(concat_df)
            .mark_bar(color="red")
            .encode(
                x=alt.X("date:T", title="", axis=alt.Axis(format="%Y-%m-%d", labels=False, ticks=False)),
                y=alt.Y("value:Q", title="buy", scale=alt.Scale(zero=False)),
            )
            .transform_filter(alt.datum.type == "buying_amount")
        )
        points3 = chart3.mark_point().encode(opacity=when_near1.then(alt.value(1)).otherwise(alt.value(0)))
        chart3 = alt.layer(chart3, points3, rules1).properties(height=60)

        chart4 = (
            alt.Chart(concat_df)
            .mark_bar(color="green")
            .encode(
                x=alt.X("date:T", title="", axis=alt.Axis(format="%Y-%m-%d", labels=False, ticks=False)),
                y=alt.Y("value:Q", title="sell", scale=alt.Scale(zero=False)),
            )
            .transform_filter(alt.datum.type == "selling_amount")
        )
        points4 = chart4.mark_point().encode(opacity=when_near1.then(alt.value(1)).otherwise(alt.value(0)))
        chart4 = alt.layer(chart4, points4, rules1).properties(height=60)

        upper1 = chart1.encode(alt.X("date:T").scale(domain=brush))
        upper2 = chart2.encode(alt.X("date:T").scale(domain=brush))
        upper3 = chart3.encode(alt.X("date:T").scale(domain=brush))
        upper4 = chart4.encode(alt.X("date:T").scale(domain=brush))
        lower = (
            alt.Chart(concat_df)
            .mark_line(interpolate="basis")
            .encode(
                x=alt.X("date:T", title="date", axis=alt.Axis(format="%Y-%m-%d")),
                y=alt.Y("value:Q", title="time", scale=alt.Scale(zero=False)),
            )
            .transform_filter(alt.datum.type == "equity")
            .properties(height=30)
            .add_params(brush)
        )

        st.altair_chart(upper1 & upper2 & upper3 & upper4 & lower, use_container_width=True)


with st.empty():
    display_risk_control()

import pathlib
import sys

sys.path.append(str(pathlib.Path(__file__).parent.parent))

import json
import os
import pickle
import threading
import time
from pathlib import Path

import pandas as pd
import pika
import streamlit as st
from extern_api.api import get_stock_name, symbol_compelted
from kazoo.client import Kazoo<PERSON>lient
from qnt_qds.client import DataClient
from qnt_research.communicator.instructor import ExternalRabbitMQInstructor
from qnt_research.entity import DCPortfolio
from qnt_utils.config import get_config
from qnt_utils.enums import CommunicatorMessageType
from qnt_utils.toolset import compare_versions
from trader_snapshot import TraderSnapshot

st.set_page_config(page_title="任务监控", page_icon=None, layout="wide", initial_sidebar_state="auto", menu_items=None)
st.markdown(
    """
    <style>
        section[data-testid="stSidebar"] {
            width: 400px !important; # Set the width to your desired value
        }
    </style>
    """,
    unsafe_allow_html=True,
)
DEBUG = get_config()["controller"]["settings"]["is_debug"]

gl_instructor = ExternalRabbitMQInstructor()
gl_data_client = DataClient(
    f"controller@{get_config()['zookeeper']['host']}:{get_config()['zookeeper']['port']}", None, zk_ephemeral=False
)
st.session_state.order_type_options1 = ["信用融资", "信用担保", "普通账户"]
st.session_state.order_type_options2 = ["信用融资", "信用担保", "普通账户"]

# Load traders and tasks from pickle files if they exist
TRADERS_PATH = Path("traders.pkl")
TASKS_PATH = Path("tasks.pkl")

if TRADERS_PATH.exists():
    try:
        with TRADERS_PATH.open("rb") as f:
            gl_traders = pickle.load(f)
    except Exception:
        TRADERS_PATH.unlink(missing_ok=True)
        gl_traders = {}
else:
    gl_traders: dict[str, TraderSnapshot] = {}  # dict， key为communicator_id

if TASKS_PATH.exists():
    try:
        with TASKS_PATH.open("rb") as f:
            gl_tasks = pickle.load(f)
    except Exception:
        TASKS_PATH.unlink(missing_ok=True)
        gl_tasks = {}
else:
    gl_tasks = {}


gl_location: dict[str, dict] = {}  # dict， key为communicator_id， value为dict， key为hostname, version

zk = KazooClient(hosts=f"{get_config()['zookeeper']['host']}:{get_config()['zookeeper']['port']}")
zk.start()
for child in zk.get_children("/communicators"):
    data = zk.get(f"/communicators/{child}")[0]
    if not data:
        continue
    gl_location[child] = json.loads(data)

gl_hostname_map = {v["hostname"]: k for k, v in gl_location.items()}
# """key为hostname, value为communicator_id"""


def save_state():
    if DEBUG:
        return
    try:
        global gl_traders, gl_tasks, TRADERS_PATH, TASKS_PATH
        # Save to temporary files first
        temp_traders = TRADERS_PATH.with_suffix(".tmp")
        temp_tasks = TASKS_PATH.with_suffix(".tmp")

        # Write to temp files
        with temp_traders.open("wb") as f:
            pickle.dump(gl_traders, f)
            f.flush()
            os.fsync(f.fileno())

        with temp_tasks.open("wb") as f:
            pickle.dump(gl_tasks, f)
            f.flush()
            os.fsync(f.fileno())

        # Atomic rename of temp files to final files
        os.replace(temp_traders, TRADERS_PATH)
        os.replace(temp_tasks, TASKS_PATH)

    except Exception:
        pass


def callback(body):
    global gl_location, gl_tasks, gl_traders
    data = json.loads(body)
    from_ = data["from"]
    if from_ not in gl_location:
        gl_location[from_] = {"hostname": data.get("hostname", "")}
        gl_location[from_]["version"] = data["data"].get("version", "")

    if from_ not in gl_traders:
        gl_traders[from_] = TraderSnapshot()
    if from_ not in gl_tasks:
        gl_tasks[from_] = {}

    if data["type"] == 2:
        gl_traders[from_].update(portfolio=data)
        save_state()
    elif data["type"] == 1:
        gl_traders[from_].update(positions=data)
        save_state()
    elif data["type"] == 5:
        # TODO 向前兼容，旧版本easy_strategy_server发的消息中data字段不是list
        if not isinstance(data["data"], list):
            task_info_lst = [data["data"]]
        else:
            task_info_lst = data["data"]
            gl_tasks[from_] = {}
        for task_info in task_info_lst:
            task_type = task_info["task_type"]
            if task_type not in gl_tasks[from_]:
                gl_tasks[from_][task_type] = {}
            task_id = task_info["task_id"]
            if task_id not in gl_tasks[from_][task_type]:
                gl_tasks[from_][task_type][task_id] = {}
            gl_tasks[from_][task_type][task_id]["params"] = task_info["params"]
            gl_tasks[from_][task_type][task_id]["status"] = task_info["status"]
            gl_tasks[from_][task_type][task_id]["params"]["name"] = get_stock_name(task_info["params"]["symbol"]) or ""
            gl_tasks[from_][task_type][task_id]["status"]["symbol"] = gl_tasks[from_][task_type][task_id]["params"][
                "symbol"
            ]
            gl_tasks[from_][task_type][task_id]["status"]["name"] = gl_tasks[from_][task_type][task_id]["params"][
                "name"
            ]
        save_state()


def get_traders():
    return {k: v for k, v in gl_traders.items() if k in gl_location}


def get_tasks():
    return {k: v for k, v in gl_tasks.items() if k in gl_location}


def display_portfolio(portfolio: DCPortfolio):
    with st.container():
        st.header("账户概览")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric(label="总资产", value=f"{portfolio.asset:.2f}")
            st.metric(label="持仓市值", value=f"{portfolio.market_value:.2f}")
        with col2:
            st.metric(label="总负债", value=f"{portfolio.debt:.2f}")
            st.metric(label="可用现金", value=f"{portfolio.available_cash:.2f}")
        with col3:
            st.metric(label="净资产", value=f"{portfolio.equity:.2f}")
            st.metric(label="冻结资金", value=f"{portfolio.frozen_cash:.2f}")


def display_positions(positions):
    with st.container():
        if not positions:
            return
        st.header("持仓概览")
        data = pd.DataFrame([j for i in positions.values() for j in i.values()])
        data = data.loc[
            data["amount"] > 0,
            [
                "symbol",
                "name",
                "market_value",
                "profit",
                "profit_rate",
                "amount",
                "available",
                "cost_basis",
                "last_price",
                "position_type",
            ],
        ]
        if data.empty:
            return
        data["position_type"] = data["position_type"].apply(lambda x: x.name)
        data["name"] = data["symbol"].apply(get_stock_name)
        data["profit"] = data["amount"] * (data["last_price"] - data["cost_basis"])
        data["profit_rate"] = (data["last_price"] / data["cost_basis"] - 1).apply(lambda x: f"{x:.2%}")
        st.dataframe(data)


def display_params(params):
    with st.container():
        st.header("参数")
        df = pd.DataFrame(params).T
        df["task_id"] = df.index
        st.dataframe(
            df.reset_index(drop=True).reindex(
                columns=["symbol", "name"] + list(df.columns.difference(["symbol", "name"]))
            ),
            hide_index=True,
        )


def display_task_status(status):
    with st.container():
        df = pd.DataFrame(status).T
        df["task_id"] = df.index
        df = df.reset_index(drop=True).reindex(
            columns=["symbol", "name"] + list(df.columns.difference(["symbol", "name"]))
        )
        st.subheader("未开仓")
        st.dataframe(
            df.loc[(df["amount"] == 0) & (df["invalid"] == False)]
            .set_index("task_id", drop=True, append=False)
            .drop(["invalid", "is_wrong"], axis=1),
        )
        st.subheader("已开仓")
        st.dataframe(
            df.loc[(df["amount"] > 0) & (df["invalid"] == False)]
            .set_index("task_id", drop=True, append=False)
            .drop(["invalid", "is_wrong"], axis=1),
        )
        st.subheader("已失效")
        st.dataframe(df.loc[df["invalid"]], hide_index=True)


def display_operate_panel():
    global gl_location, gl_hostname_map, gl_instructor, gl_data_client
    total_service = list(gl_hostname_map.keys())
    if len(total_service) == 0:
        st.error("无可用服务")
        return

    tab1, tab2, tab3 = st.tabs(["新增任务1", "新增任务2", "删除任务"])

    with tab1:
        destination1 = st.selectbox("目标服务", total_service, key="destination1")
        # TODO
        is_support_take_profit = compare_versions(gl_location[gl_hostname_map[destination1]]["version"], "1.9") >= 0
        with st.form("新增任务1", clear_on_submit=True):
            col1, col2 = st.columns(2)
            with col1:
                money1 = st.number_input("资金", step=2500)
                order_type1 = st.selectbox(
                    "订单类型",
                    options=[
                        i
                        for i in st.session_state.order_type_options1
                        if i
                        in (
                            ["信用融资", "信用担保"]
                            if gl_location[gl_hostname_map[destination1]]["is_credit"]
                            else ["普通账户"]
                        )
                    ],
                )
                increase1 = st.number_input(
                    "当日上涨（%）",
                    step=0.1,
                    value=3.0,
                    help="当前价格相比昨收涨幅达到阈值，或相比今日最低涨幅达到2倍阈值，则开仓。-100表示立即开仓，必须在盘中使用",
                )
                stoploss1 = st.number_input("止损（%）", step=0.5, value=7.0)
            with col2:
                symbol1 = st.text_input("标的")
                trade_direction1 = st.selectbox("交易方向", ("只做多", "只做空", "多空双向"))
                rise_or_fall_threshold1 = st.number_input(
                    "涨跌幅阈值（%）", step=0.5, value=8.0, help="触发价格大于涨跌幅阈值时不会开仓"
                )
                stopearn1 = st.number_input("回落止盈（%）", step=0.5, value=11.3)

            is_take_profit = st.checkbox(
                "是否开启1/2主动止盈",
                value=True,
                help="开启后1/2的仓位会在盈利达到回落止盈阈值时主动止盈",
                disabled=not is_support_take_profit,
            )
            is_based_on_his_signals1 = st.checkbox("基于历史信号")
            is_intraday_trade1 = st.checkbox("是否允许日内交易")

            col1, col2 = st.columns(2)
            with col1:
                submit = st.form_submit_button("确认", type="primary")
            with col2:
                st.form_submit_button("清空")

            if submit:
                tmp_symbol = symbol_compelted(symbol1) if len(symbol1) == 6 else symbol1
                if tmp_symbol not in gl_data_client.subscriptions["MINUTE"] and increase1 == -100:
                    st.toast("请先订阅数据", icon="🚨")
                else:
                    if order_type1 == "信用融资":
                        is_credit1, is_financing_first1 = True, True
                    elif order_type1 == "信用担保":
                        is_credit1, is_financing_first1 = True, False
                    elif order_type1 == "普通账户":
                        is_credit1, is_financing_first1 = False, False
                    trade_direction1 = {"只做多": 1, "只做空": -1, "多空双向": 0}[trade_direction1]
                    # 将数据转换为JSON对象
                    data_dict1 = {
                        "action": "ADD",
                        "config": {
                            "task_type": "Task1",
                            "money": money1,
                            "symbol": tmp_symbol,
                            "increase": increase1 / 100,
                            "stoploss": stoploss1 / 100,
                            "stopearn": stopearn1 / 100,
                            "is_based_on_his_signals": is_based_on_his_signals1,
                            "is_credit": is_credit1,
                            "is_financing_first": is_financing_first1,
                            "trade_direction": trade_direction1,
                            "is_intraday_trade": is_intraday_trade1,
                            "rise_or_fall_threshold": rise_or_fall_threshold1 / 100,
                        },
                    }
                    if is_support_take_profit:
                        data_dict1["config"]["is_take_profit"] = is_take_profit

                    if destination1:
                        gl_instructor.publish(
                            data_dict1, CommunicatorMessageType.INSTRUCTION, 2, gl_hostname_map[destination1]
                        )
                    else:
                        gl_instructor.publish(data_dict1, CommunicatorMessageType.INSTRUCTION, 2)

    with tab2:
        destination2 = st.selectbox("目标服务", total_service, key="destination2")
        with st.form("新增任务2", clear_on_submit=True):
            col1, col2 = st.columns(2)
            with col1:
                symbol2 = st.text_input("标的")
            with col2:
                order_type2 = st.selectbox(
                    "订单类型",
                    options=[
                        i
                        for i in st.session_state.order_type_options2
                        if i
                        in (
                            ["信用融资", "信用担保"]
                            if gl_location[gl_hostname_map[destination2]]["is_credit"]
                            else ["普通账户"]
                        )
                    ],
                )
            col1, col2 = st.columns(2)
            with col1:
                holding_direction2 = st.selectbox("持仓方向", ("多仓", "空仓"))
                holding_amount2 = st.number_input("持仓数量", format="%.0f")
                holding_cosis2 = st.number_input("持仓成本", format="%.4f")
                holding_highest2 = st.number_input("最高价", format="%.4f")
                holding_lowest2 = st.number_input("最低价", format="%.4f")
            with col2:
                stop_loss_ratio2 = st.number_input("止损(%)", step=0.5, value=9.0)
                take_profit_threshold2 = st.number_input("止盈阈值(%)", step=0.5, value=10.0)
                take_profit_withdraw12 = st.number_input("小于阈值回落止盈(%)", step=0.5, value=10.0)
                take_profit_withdraw22 = st.number_input("大于阈值回落止盈(%)", step=0.5, value=15.0)
            col1, col2 = st.columns(2)
            with col1:
                submit = st.form_submit_button("确认", type="primary")
            with col2:
                st.form_submit_button("清空")
            if submit:
                if order_type2 == "信用融资":
                    is_credit2, is_financing_first2 = True, True
                elif order_type2 == "信用担保":
                    is_credit2, is_financing_first2 = True, False
                elif order_type2 == "普通账户":
                    is_credit2, is_financing_first2 = False, False
                # 将数据转换为JSON对象
                data_dict2 = {
                    "action": "ADD",
                    "config": {
                        "task_type": "Task2",
                        "symbol": symbol_compelted(symbol2) if len(symbol2) == 6 else symbol2,
                        "is_credit": is_credit2,
                        "is_financing_first": is_financing_first2,
                        "cost_basis": holding_cosis2,
                        "amount": holding_amount2 if holding_direction2 == "多仓" else -holding_amount2,
                        "highest_price": holding_highest2,
                        "lowest_price": holding_lowest2,
                        "stop_loss_ratio": stop_loss_ratio2 / 100,
                        "take_profit_threshold": take_profit_threshold2 / 100,
                        "take_profit_withdraw1": take_profit_withdraw12 / 100,
                        "take_profit_withdraw2": take_profit_withdraw22 / 100,
                    },
                }
                if destination2:
                    gl_instructor.publish(
                        data_dict2, CommunicatorMessageType.INSTRUCTION, 2, gl_hostname_map[destination2]
                    )
                else:
                    gl_instructor.publish(data_dict2, CommunicatorMessageType.INSTRUCTION, 2)

    with tab3:
        with st.form("删除任务", clear_on_submit=True):
            task_id = st.text_input("任务id")
            is_close = st.checkbox("是否平仓")
            submit = st.form_submit_button("确认", type="primary")
            if submit:
                data_dict = {"action": "DELETE", "config": {"task_id": task_id, "is_close": is_close}}
                gl_instructor.publish(data_dict, CommunicatorMessageType.INSTRUCTION, 2)


connection = pika.BlockingConnection(
    pika.ConnectionParameters(host=(config := get_config())["rabbitmq"]["host"], port=config["rabbitmq"]["port"])
)
channel = connection.channel()
channel.exchange_declare(exchange="REPORT", exchange_type="fanout")
result = channel.queue_declare(queue="", exclusive=True, auto_delete=True)
queue_name = result.method.queue
channel.queue_bind(exchange="REPORT", queue=queue_name)

gl_thread_lock = threading.Lock()


def sub_thread():
    global gl_thread_lock
    while True:
        while True:
            msg = channel.basic_get(queue=queue_name, auto_ack=True)
            if msg[2] is not None:
                gl_thread_lock.acquire()
                callback(msg[2])
                gl_thread_lock.release()
            else:
                break
        time.sleep(3)


threading.Thread(target=sub_thread).start()


with st.sidebar:
    display_operate_panel()


@st.fragment(run_every="5s")
def dis(placeholder):
    global gl_thread_lock
    gl_thread_lock.acquire()

    traders_ = get_traders()
    tasks_ = get_tasks()
    all_c_id = set(traders_.keys()).union(set(tasks_.keys()))
    xxx = dict(zip(range(len(all_c_id)), all_c_id))

    if len(xxx) == 0:
        gl_thread_lock.release()
        return

    tabs = placeholder.tabs([f"🔸{gl_location[i]['hostname']}" for i in all_c_id])
    for i, tab in enumerate(tabs):
        communicator_id = xxx[i]
        with tab:
            st.text(
                ", ".join(
                    [f"{k}:{v}" for k, v in gl_location[communicator_id].items() if k in ["is_credit", "version"]]
                )
            )
            trader_snapshot = traders_.get(communicator_id, None)
            if trader_snapshot:
                # portfolio = trader_snapshot.portfolio
                # if portfolio:
                #     display_portfolio(portfolio["data"])
                positions = trader_snapshot.positions
                if positions:
                    display_positions(positions["data"])

            task_of_communicator = tasks_.get(communicator_id, None)
            if task_of_communicator:
                st.header("任务状态")
                for task_type in task_of_communicator:
                    with st.expander(task_type, expanded=True):
                        status = {
                            task_id: task_of_communicator[task_type][task_id]["status"]
                            for task_id in task_of_communicator[task_type]
                        }
                        if status:
                            display_task_status(status)
                        params = {
                            task_id: task_of_communicator[task_type][task_id]["params"]
                            for task_id in task_of_communicator[task_type]
                        }
                        if params:
                            display_params(params)

    gl_thread_lock.release()


placeholder = st.empty()
dis(placeholder)

import pathlib
import sys

sys.path.append(str(pathlib.Path(__file__).parent.parent))

import streamlit as st
from qnt_qds.client import DataClient
from qnt_qds.struct import SubInfo
from qnt_utils.config import get_config

st.set_page_config(page_title="数据订阅", page_icon=None, layout="wide", initial_sidebar_state="auto", menu_items=None)
DEBUG = get_config()["controller"]["settings"]["is_debug"]


gl_data_client = DataClient(
    f"controller@{get_config()['zookeeper']['host']}:{get_config()['zookeeper']['port']}", None, zk_ephemeral=False
)
with st.form("订阅数据", clear_on_submit=True):
    symbol = (
        st.text_area("股票代码", height=500, value=",".join(sorted(gl_data_client.subscriptions["MINUTE"])))
        .replace("\n", "")
        .split(",")
    )  # 将text_input改为text_area，并设置高度为100
    submit = st.form_submit_button("订阅", type="primary")

    if submit:
        tmp = [i for i in symbol if i]
        if len(tmp) > 0:
            gl_data_client.batch_subscribe(
                SubInfo("DEL_SUB", "SNAPSHOT", []),
                SubInfo("DEL_SUB", "MINUTE", []),
                SubInfo("ADD_SUB", "SNAPSHOT", tmp),
                SubInfo("ADD_SUB", "MINUTE", tmp),
            )
        else:
            gl_data_client.batch_subscribe(SubInfo("DEL_SUB", "SNAPSHOT", []), SubInfo("DEL_SUB", "MINUTE", []))

from qnt_research.entity import DCPortfolio, DCPosition


class TraderSnapshot:
    def __init__(self):
        self.positions = {}
        self.portfolio = {}

    def update(self, positions: None | dict = None, portfolio: None | dict = None):
        if positions is not None:
            self.positions.update(self.parse_positions(positions))
        if portfolio is not None:
            self.portfolio.update(self.parse_portfolio(portfolio))

    @staticmethod
    def parse_positions(x):
        x["data"] = {
            symbol: {position_type_str: DCPosition.from_json(v2) for position_type_str, v2 in v1.items()}
            for symbol, v1 in x["data"].items()
        }
        return x

    @staticmethod
    def parse_portfolio(x):
        x["data"] = DCPortfolio.from_json(x["data"])
        return x

import pandas as pd
import streamlit as st
from qnt_utils.config import get_config
from qnt_utils.toolset import decrypt
from risk_control import RiskControl
from sqlalchemy import create_engine

st.set_page_config(page_title="概览", page_icon=None, layout="wide", initial_sidebar_state="auto", menu_items=None)
DEBUG = get_config()["controller"]["settings"]["is_debug"]


@st.fragment(run_every="1s")
def display_trader_log():
    try:
        st.header("交易日志")
        with st.container(height=260):
            with open("log/trader.log", "r") as f:
                for line in f.readlines()[::-1]:
                    st.write(line)
    except:
        pass


@st.fragment(run_every="5s")
def display_risk_control(engine):
    with st.container():
        risk_control = RiskControl(3.6e5)
        st.header("风险控制")
        df = pd.read_sql("select * from portfolio;", engine)
        tmp = df.loc[df["date"] == df["date"].max()]
        df = df.groupby("date").sum()[["equity", "market_value"]]
        for _, row in df.iterrows():
            risk_control.register_portfolio(row["equity"], row["market_value"])
            adjust = risk_control.step()
            if risk_control.iopv > 1.25:
                risk_control = RiskControl(row["equity"])
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric(label="净资产", value=f"{risk_control.last_equity:.2f}")
            st.metric(label="可用资金", value=f"{risk_control.cap:.2f}")
        with col2:
            st.metric(label="持仓市值", value=f"{risk_control.last_market_value:.2f}")
            st.metric(
                label="仓位系数",
                value=f"{risk_control.positions_control:.0%}({risk_control.last_market_value / risk_control.cap * risk_control.positions_control:.0%})",
            )
        with col3:
            st.metric(label="净值", value=f"{risk_control.iopv:.2%}")
            st.metric(label="调仓", value=f"{adjust:.2f}")

        st.header("可用资金")
        col = st.columns(len(tmp))
        for i in range(len(tmp)):
            with col[i]:
                st.metric(
                    label=f"{tmp.iloc[i]['account']} {'信用' if tmp.iloc[i]['is_credit'] else '普通'}",
                    value=f"{tmp.iloc[i]['available_cash']:.0f}",
                )


engine = create_engine(
    f"postgresql+psycopg2://{decrypt(get_config()['database']['username'])}:{decrypt(get_config()['database']['password'])}@{get_config()['database']['host']}:{get_config()['database']['port'] if not DEBUG else 5433}/trader"
)
with st.empty():
    display_risk_control(engine)

with st.empty():
    display_trader_log()

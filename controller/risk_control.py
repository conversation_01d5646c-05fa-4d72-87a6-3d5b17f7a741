import pandas as pd
from sqlalchemy import create_engine


class RiskControl:
    def __init__(self, equity: float):
        self.initial_equity = equity
        self.last_equity = equity
        self.last_market_value = 0
        self.iopv = 1.0
        self.positions_control = 0.8

        self.accounts = []

    def register_portfolio(self, equity: float, market_value: float):
        """记录

        Args:
            equity (float): 权益
            market_value (float): 持仓市值
        """
        self.accounts.append({"equity": equity, "market_value": market_value})

    def step(self):
        equity = sum(account["equity"] for account in self.accounts)
        market_value = sum(account["market_value"] for account in self.accounts)
        iopv = equity / self.last_equity * self.iopv
        self.accounts.clear()

        if iopv < 0.8 and self.iopv > 0.8 and self.positions_control > 0.0:
            self.positions_control = 0.0
        elif iopv < 0.85 and self.iopv > 0.85 and self.positions_control > 0.2:
            self.positions_control = 0.2
        elif iopv < 0.9 and self.iopv > 0.9 and self.positions_control > 0.4:
            self.positions_control = 0.4
        elif iopv < 0.95 and self.iopv > 0.95 and self.positions_control > 0.6:
            self.positions_control = 0.6
        elif iopv > 1.0 and self.iopv < 1.0 and self.positions_control < 0.8:
            self.positions_control = 0.8
        elif iopv > 0.95 and self.iopv < 0.95 and self.positions_control < 0.6:
            self.positions_control = 0.6
        elif iopv > 0.9 and self.iopv < 0.9 and self.positions_control < 0.4:
            self.positions_control = 0.4
        elif iopv > 0.85 and self.iopv < 0.85 and self.positions_control < 0.2:
            self.positions_control = 0.2

        self.last_equity = equity
        self.last_market_value = market_value
        self.iopv = iopv
        return self.initial_equity * self.positions_control - market_value

    @property
    def cap(self):
        return self.initial_equity * self.positions_control


# if __name__ == "__main__":
#     import numpy as np

#     equity = 100000
#     risk_control = RiskControl(equity)
#     for _ in range(500):
#         equity += np.random.randint(-15, 20) / 1000 * risk_control.cap
#         risk_control.register_portfolio(equity, equity)
#         risk_control.step()
#         print(
#             f"equity: {equity:.0f}, iopv: {risk_control.iopv:.3f}, cap: {risk_control.cap:.0f}, positions_control: {risk_control.positions_control}"
#         )

#         if risk_control.iopv > 1.25:
#             risk_control = RiskControl(equity)

if __name__ == "__main__":
    engine = create_engine("postgresql+psycopg2://postgres:believe419@127.0.0.1:5433/trader")
    risk_control = RiskControl(3.8e5)
    df = pd.read_sql("select * from portfolio;", engine)
    df = df.groupby("date").sum()[["equity", "market_value"]]
    for date, row in df.iterrows():
        risk_control.register_portfolio(row["equity"], row["market_value"])
        adjust = risk_control.step()
        print(
            f"净资产: {row['equity']:.0f}, 持仓市值: {row['market_value']:.0f}, \
iopv: {risk_control.iopv:.3f}, 可用资金: {risk_control.cap:.0f}, \
仓位系数: {risk_control.positions_control}, adjust: {adjust:.0f}"
        )

        if risk_control.iopv > 1.25:
            risk_control = RiskControl(row["equity"])

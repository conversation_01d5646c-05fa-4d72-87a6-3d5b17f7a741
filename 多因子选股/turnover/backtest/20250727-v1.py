import datetime
import json
from itertools import product
from typing import Dict, List

import numpy as np
import pandas as pd

gl_all_trade_days = get_all_trade_days().to_pydatetime()

MAX_HOLD_DAYS = 2
NUM = 1
CONTROL_POSITIONS_FILE = None

# SELECT_STOCKS_JSON = ["./C端项目-板块推荐/成果/000852_1225.json","./C端项目-板块推荐/成果/000905_1213.json","./C端项目-板块推荐/成果/000300_1210.json"]
SELECT_STOCKS_JSON = ["./项目四：多因子选股/20250617_direct.json"]
# SELECT_STOCKS_JSON = ["./C端项目-板块推荐/成果/000852.SH_20250529_direct.json"]
# SELECT_STOCKS_JSON = ["./C端项目-板块推荐/成果/000905.SH_20250522.json"]
# SELECT_STOCKS_JSON = ["./C端项目-板块推荐/成果/000300.SH_20250530_direct.json","./C端项目-板块推荐/成果/000852.SH_20250522_direct.json","./C端项目-板块推荐/成果/000905.SH_20250519.json"]

# BENCHMARK = "000300.SH"
# BENCHMARK = "000852.SH"
BENCHMARK = "000985.CSI"

REGULAR_ADJUST_POSTIONS = False
DAY_SHIFT = -1
ORDER_MATCHING_AT_CLOSE = True
TRADING_FREQUENCY = 1


# 股票策略模版
# 初始化函数,全局只运行一次
def init(context):
    set_log_level("info", is_limit=False)
    set_benchmark(BENCHMARK)
    log.info("策略开始运行,初始化函数全局只运行一次")
    # 设置股票每笔交易的手续费为万分之二(手续费在买卖成交后扣除,不包括税费,税费在卖出成交后扣除)
    set_commission(PerShare(type="stock", cost=0.0002))
    # 设置股票交易滑点0.5%,表示买入价为实际价格乘1.005,卖出价为实际价格乘0.995
    set_slippage(PriceSlippage(0.002))
    set_volume_limit(None, None)
    # set_price_limit(daily=None, minute=None)
    enable_open_bar()
    g.signal = parse_json()
    g.weight = get_weight()

    if REGULAR_ADJUST_POSTIONS:
        g.buy_money = context.run_info.stock_starting_cash
    else:
        g.buy_money = context.run_info.stock_starting_cash / (MAX_HOLD_DAYS)

    g.holding_stocks = {}  # 持仓当天算1天，天数>=MAX_HOLD_DAYS时卖出
    g.orders = []

    g.cache = {}
    if ORDER_MATCHING_AT_CLOSE:
        set_execution("close")
    g.count_day = DAY_SHIFT
    g.last_date = get_trade_days(None, context.run_info.end_date, 1)[0]
    g.today_signal = []

    g.high_limit = {}


def before_trading(context):
    g.today_signal = []
    holding = context.portfolio.positions

    ideal_holding_symbols = list(g.holding_stocks.keys())
    td = get_datetime().strftime("%Y%m%d 1500")
    g.cache = {}

    if set(ideal_holding_symbols) != set(holding.keys()):
        log.warn(
            "持仓与理想持仓不一致，理论持有而实际没有：{}，实际持仓：{}".format(
                [i for i in ideal_holding_symbols if i not in holding.keys()],
                [i for i in holding.keys() if i not in ideal_holding_symbols],
            )
        )

    for symbol in ideal_holding_symbols:
        if symbol not in holding.keys():
            g.holding_stocks.pop(symbol)
            log.warn(f"股票{symbol}不再持有，已从持仓中删除")
            continue

        min_created = pd.Timestamp.max
        total_amount = 0
        for order_id in g.holding_stocks[symbol].keys():
            g.holding_stocks[symbol][order_id]["holding_days"] += 1
            g.holding_stocks[symbol][order_id]["status"] = "HOLDING"
            min_created = min(min_created, g.holding_stocks[symbol][order_id]["created"])
            total_amount += g.holding_stocks[symbol][order_id]["amount"]

        g.cache[symbol] = get_price(
            symbol, min_created, td, f"{TRADING_FREQUENCY}m", ["open", "close", "high"], fq="post"
        )
        g.high_limit[symbol] = get_price(
            symbol, None, td, "1d", ["high_limit", "prev_close", "is_paused"], fq="post", skip_paused=False, bar_count=1
        ).iloc[0]

        # 可能会存在分红送股的情况，需要调整理论持股数量
        if total_amount != holding[symbol].amount:
            # log.warn(
            #     f"股票{symbol}持仓数量不等于实际持仓数量，实际持仓数量为{holding[symbol].amount}，理论持仓数量为{total_amount}"
            # )
            mul = holding[symbol].amount / total_amount

            tmp_order_id = list(g.holding_stocks[symbol].keys())
            for order_id in tmp_order_id:
                tmp_amount = g.holding_stocks[symbol][order_id]["amount"] * mul
                if symbol.startswith("68"):
                    if tmp_amount >= 200:
                        tmp_amount = int(tmp_amount)
                    else:
                        tmp_amount = 0
                else:
                    tmp_amount = int((tmp_amount // 100) * 100)

                if tmp_amount == 0:
                    g.holding_stocks[symbol].pop(order_id)
                    if len(g.holding_stocks[symbol]) == 0:
                        g.holding_stocks.pop(symbol)
                    if g.holding_stocks[symbol][order_id]["amount"] != 0:
                        log.warn(
                            f"股票{symbol}持仓数量由{g.holding_stocks[symbol][order_id]['amount']}调整为0，已从持仓中删除"
                        )
                else:
                    if tmp_amount != g.holding_stocks[symbol][order_id]["amount"]:
                        log.warn(
                            f"股票{symbol}持仓数量由{g.holding_stocks[symbol][order_id]['amount']}调整为{tmp_amount}"
                        )
                    g.holding_stocks[symbol][order_id]["amount"] = tmp_amount

    g.count_day += 1


def open_auction(context, bar_dict):
    now = get_datetime()
    positions = context.portfolio.positions
    hold = positions.keys()

    for symbol in [i for i in hold if i not in g.holding_stocks.keys()]:
        log.warn(f"清掉理论不持有的{symbol} {positions[symbol].available_amount}")
        order_target_value(symbol, 0)

    if ((not REGULAR_ADJUST_POSTIONS) or g.count_day % MAX_HOLD_DAYS == 0) and now.date() < g.last_date:
        # 读取交易信号，买入
        g.today_signal = g.signal.get(now.strftime("%Y%m%d 0930"), [])
        buy_stock = [i["symbol"] for i in g.today_signal]
        if not REGULAR_ADJUST_POSTIONS:
            buy_stock = [i for i in buy_stock if len(g.holding_stocks.get(i, [])) <= MAX_HOLD_DAYS]

        if len(buy_stock) > 0:
            # total_buy_money = min(context.portfolio.portfolio_value / MAX_HOLD_DAYS, context.portfolio.available_cash)
            total_buy_money = min(g.buy_money, context.portfolio.available_cash)

            if CONTROL_POSITIONS_FILE is not None:
                total_buy_money = total_buy_money * g.weight.get(now.strftime("%Y%m%d 0930"), 1.0)

            if len(buy_stock) >= NUM:
                buy_money = total_buy_money / len(buy_stock)
                for e, symbol in enumerate(set(buy_stock)):
                    order_value(symbol, buy_money)
            else:
                buy_money = total_buy_money / NUM
                for e, symbol in enumerate(set(buy_stock)):
                    order_value(symbol, buy_money)


# # ## 开盘时运行函数
def handle_bar(context, bar_dict):
    now = get_datetime()
    positions = context.portfolio.positions
    hold = positions.keys()

    for symbol in hold:
        if symbol not in g.holding_stocks:
            continue
        buy_order_id_lst = list(g.holding_stocks[symbol].keys())
        for buy_order_id in buy_order_id_lst:
            if calc_close_signal(g.holding_stocks[symbol][buy_order_id], now):
                sell_order_id = order(symbol, -g.holding_stocks[symbol][buy_order_id]["amount"])
                g.holding_stocks[symbol][sell_order_id] = g.holding_stocks[symbol].pop(buy_order_id)
                g.holding_stocks[symbol][sell_order_id]["status"] = "CLOSING"


def on_order(context, odr):
    # 记录下每一笔成交
    if odr.status in [ORDER_STATUS.FILLED, ORDER_STATUS.CANCELLED]:
        g.orders.append(odr)


def after_trading(context):
    for odr in g.orders:
        if odr.order_type == SIDE.BUY and odr.filled_amount > 0:
            if odr.symbol not in g.holding_stocks:
                g.holding_stocks[odr.symbol] = {}
            cap_map = dict([(i["symbol"], i["cap"]) for i in g.today_signal])
            g.holding_stocks[odr.symbol][odr.order_id] = {
                "symbol": odr.symbol,
                "holding_days": 1,
                "amount": odr.filled_amount,
                "created": odr.created,
                "status": "HOLDING",
                "cap": cap_map[odr.symbol],
            }
        elif (
            odr.order_type == SIDE.SELL
            and odr.filled_amount > 0
            and odr.order_id in g.holding_stocks.get(odr.symbol, {})
        ):
            g.holding_stocks[odr.symbol][odr.order_id]["amount"] -= odr.filled_amount
            if g.holding_stocks[odr.symbol][odr.order_id]["amount"] == 0:
                g.holding_stocks[odr.symbol].pop(odr.order_id)
                if len(g.holding_stocks[odr.symbol]) == 0:
                    g.holding_stocks.pop(odr.symbol)
    g.orders = []


def calc_close_signal(ord, now):
    if g.high_limit[ord["symbol"]]["is_paused"] == 1:
        return False

    if now.time() < datetime.time(9, 31):
        return False
    if ord["status"] != "HOLDING":
        return False
    # 次日才能卖出
    if now.date() <= ord["created"].date():
        return False
    if now.minute % TRADING_FREQUENCY != 0:
        return False
    # 最后一天清仓
    if now.date() == g.last_date and now.time() >= datetime.time(14, 59):
        return True

    price = g.cache[ord["symbol"]].loc[
        (g.cache[ord["symbol"]].index <= now) & (g.cache[ord["symbol"]].index >= now.replace(hour=8))
    ]

    # if price['close'].iat[-1] >= g.high_limit[ord["symbol"]]["high_limit"]:
    #     log.info(f"{ord['symbol']}涨停平仓")
    #     return True

    if price["close"].iat[-1] == g.high_limit[ord["symbol"]]["high_limit"]:
        # log.info(f"{ord['symbol']}涨停平仓")
        if now.time() == datetime.time(15, 0):
            log.info(f"{ord['symbol']}涨停，继续持有")
        return False

    if ord["holding_days"] > MAX_HOLD_DAYS:
        log.info(f"{ord['symbol']}超期")
        return True

    if ord["holding_days"] == MAX_HOLD_DAYS and now.time() >= datetime.time(14, 59):
        return True

    # if price['close'].iat[-1] <= price['high'].max() * (1 - 0.10) and price['close'].iat[-1] <= g.high_limit[ord["symbol"]]["prev_close"]:
    #     log.info(f"{ord['symbol']}回落平仓")
    #     return True

    if (
        price["high"].max() == g.high_limit[ord["symbol"]]["high_limit"]
        and price["close"].iat[-1] < g.high_limit[ord["symbol"]]["high_limit"]
    ):
        # log.info(f"{ord['symbol']}回落平仓")
        return True

    # price = g.cache[ord["symbol"]].loc[
    #     (g.cache[ord["symbol"]].index <= now) & (g.cache[ord["symbol"]].index >= ord["created"])
    # ]
    # open_price = price["open"].iloc[0] * 1.0015
    # last_price = price["close"].iloc[-1]
    # close_price = last_price * 0.9985
    # highest = price["close"].max()

    # if last_price < open_price * 0.9:
    #     log.info(
    #         f"{ord['symbol']}止损，open: {open_price: >7.2f} at {ord['created']}, close: {close_price: >7.2f} at {now}, highest: {highest: >7.2f}, profit: {close_price / open_price - 1: >7.2%}"
    #     )
    #     return True
    return False

    if ord["cap"] == "large":
        p1, p2, p3, p4 = 0.02, 0.05, 0.08, 0.15
    elif ord["cap"] == "mid":
        p1, p2, p3, p4 = 0.03, 0.07, 0.1, 0.14
    elif ord["cap"] == "small":
        p1, p2, p3, p4 = 0.03, 0.07, 0.1, 0.16
    else:
        raise RuntimeError

    if (
        ord["holding_days"] > MAX_HOLD_DAYS
        or (ord["holding_days"] == MAX_HOLD_DAYS and now.time() >= datetime.time(14, 59))
    ) and last_price < open_price * (1 + p1):
        log.info(
            f"{ord['symbol']}到期，open: {open_price: >7.2f} at {ord['created']}, close: {close_price: >7.2f} at {now}, highest: {highest: >7.2f}, profit: {close_price / open_price - 1: >7.2%}"
        )
        return True
    if last_price > open_price * (1 + p2):
        if last_price < highest * (1 - p4):
            log.info(
                f"{ord['symbol']}盈二，open: {open_price: >7.2f} at {ord['created']}, close: {close_price: >7.2f} at {now}, highest: {highest: >7.2f}, profit: {close_price / open_price - 1: >7.2%}"
            )
            return True
    else:
        if last_price < highest * (1 - p3):
            log.info(
                f"{ord['symbol']}盈一，open: {open_price: >7.2f} at {ord['created']}, close: {close_price: >7.2f} at {now}, highest: {highest: >7.2f}, profit: {close_price / open_price - 1: >7.2%}"
            )
            return True
    return False


def get_trade_time(signal_dt: str) -> pd.Timestamp:
    """把信号的产生时间转换成交易时间

    Args:
        signal_dt (str): 信号的产生时间

    Returns:
        pd.Timestamp: 交易执行的时间
    """
    global gl_all_trade_days
    dt_ = pd.Timestamp(signal_dt).to_pydatetime()
    dtt = dt_.time()
    if dt_.replace(hour=0, minute=0) not in gl_all_trade_days or dtt >= datetime.time(15):
        ret = gl_all_trade_days[np.searchsorted(gl_all_trade_days, dt_, "right")].replace(hour=9, minute=30)
    else:
        if dtt < datetime.time(9, 30):
            ret = dt_.replace(hour=9, minute=30)
        elif dtt <= datetime.time(11, 30):
            ret = dt_
        elif dtt < datetime.time(hour=13, minute=1):
            ret = dt_.replace(13, 1)
        else:
            ret = dt_
    return pd.Timestamp(ret)


def calc_trade_day(td, delta_days: int = 0):
    global gl_all_trade_days
    td = pd.Timestamp(td).to_pydatetime()
    if delta_days > 0:
        ret = gl_all_trade_days[np.searchsorted(gl_all_trade_days, td, "right") + delta_days - 1]
    elif delta_days < 0:
        ret = gl_all_trade_days[np.searchsorted(gl_all_trade_days, td, "left") + delta_days]
    else:
        ret = td
    return pd.Timestamp(ret)


def get_index_stocks_periodically(index_code, tmpl_df):
    ret = pd.DataFrame(0, index=tmpl_df.index, columns=tmpl_df.columns)
    a = [f"{i}{j}" for i, j in product(range(2015, 2099), ["{:>02}01".format(k + 1) for k in range(0, 12, 3)])]
    for i_, j_ in zip(a[:-1], a[1:]):
        i = pd.Timestamp(i_).timestamp() * 1e9
        j = pd.Timestamp(j_).timestamp() * 1e9
        if i > ret.index[-1]:
            break
        aa = get_index_stocks(index_code, date=i_)
        ret.loc[(ret.index >= i) & (ret.index < j), ret.columns.intersection(aa)] = 1
    ret = ret.astype(bool)
    return ret


def parse_json() -> Dict[str, List[str]]:
    """json中的时间是信号生成时间，即当日收盘时间，这里会转换成交易时间，即信号时间后最近的执行时间"""
    select_stocks = {}
    for i in SELECT_STOCKS_JSON:
        tmp = json.load(open(i))
        for j in tmp:
            if j not in select_stocks:
                select_stocks[j] = []
            select_stocks[j].extend(tmp[j])
    # idx = set()
    # col = set()
    # for i, j in select_stocks.items():
    #     idx.add(i)
    #     for jj in j:
    #         col.add(jj["symbol"])
    # a = pd.DataFrame("", index=list(idx), columns=list(col))
    # a.index = (pd.to_datetime(a.index) - pd.Timedelta(hours=8)).astype(int)
    # a.sort_index(inplace=True)
    # a[get_index_stocks_periodically("000905.SH", a)] = "mid"
    # a[get_index_stocks_periodically("000300.SH", a)] = "large"
    # a[get_index_stocks_periodically("000852.SH", a)] = "small"
    for i, j in select_stocks.items():
        for jj in j:
            jj["cap"] = "any"
    #         if jj["cap"] == "":
    #             raise RuntimeError(f"{jj['symbol']}没有找到指数")
    return {get_trade_time(i).strftime("%Y%m%d %H%M"): j for i, j in select_stocks.items()}


def write_log(msg):
    """该函数用于将日志消息写入名为 "app.log" 的日志文件中，每条消息后面都会换行"""
    with open("app.log", "a") as f:
        f.write(f"{msg}\n")


def get_weight():
    if CONTROL_POSITIONS_FILE is None:
        return {}
    pred = pd.read_csv(CONTROL_POSITIONS_FILE, index_col=0)
    pred.index = pd.to_datetime(pred.index).tz_localize(None)
    weight = pd.Series(1, index=pred.index)
    weight.loc[pred.mean(axis=1) < -0.0] = 0.5
    return {get_trade_time(i).strftime("%Y%m%d %H%M"): j for i, j in weight.items()}

### @description: 使用新的analyse方法加速
import sys

sys.path.insert(0, "/home/<USER>/work/dev")

import argparse
import pathlib
import pickle
import re
import shutil
import sys
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from functools import lru_cache, reduce
from itertools import product
from pathlib import Path
from typing import Callable, ClassVar, List, Optional

import aichemy.project as yf_prj
import numpy as np
import pandas as pd
import smd_module.factor as smdf
import torch
from aichemy.data_ops import run_construct_dataset
from aichemy.factor_analyse import *
from aichemy.factor_analyse.analyse import FactorAnalyse
from aichemy.ml.experiments_idx import *
from aichemy.project import predict_apply_data, process_train_data, rolling, save_predict_result, train_latest_model
from aichemy.utils import is_notebook, slice_dataset
from autogluon.tabular import TabularDataset, TabularPredictor
from loguru import logger
from mindgo_api import get_price
from sklearn.metrics import mean_squared_error
from smd_module.utils import StockPoolMask, calc_trade_day, get_all_securities_, get_index_stocks_periodically

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm

torch.set_num_threads(6)

interval = 2
gl_log_dir = f"{pathlib.Path(__session__).stem}"
gl_pool = "turnover"
gl_num_groups = 10


gl_ref = {}

gl_logger_id = logger.add(
    f"{gl_log_dir}/log",
    filter=lambda record: "begin" in record["message"]
    or "Finally" in record["message"]
    or "top N" in record["message"],
)

EXCEPT_FACTORS = [
    ("财务因子", "netcashflows_from_opt_act_net_liabilities"),
    ("财务因子", "dividend_rate"),
    ("量价强化学习类", "RL_DAY_MF_ALPHA6"),
    ("量价强化学习类", "RL_DAY_MF_ALPHA11"),
    ("日频量价技术指标类", "DAY_PV_VVR"),
    ("量价强化学习类", "RL_DAY_MF_ALPHA7"),
    ("Tick快照深度模型类", ""),
]

try:
    tmp = "2025-05-31"
    if not pathlib.Path(tmp).exists():
        pickle.dump(
            get_price(
                get_all_securities_("2018-01-01", tmp),
                "2018-01-01",
                tmp,
                "1d",
                ["open", "close", "high_limit", "low", "high", "turnover", "turnover_rate", "avg_price"],
                fq="post",
                skip_paused=True,
                is_panel=True,
            ),
            open(tmp, "wb"),
        )
    tmp = pickle.load(open(tmp, "rb"))

    gl_high_df = tmp["high"]
    gl_high_df.index = (pd.to_datetime(gl_high_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_low_df = tmp["low"]
    gl_low_df.index = (pd.to_datetime(gl_low_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_close_df = tmp["close"]
    gl_close_df.index = (pd.to_datetime(gl_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_open_df = tmp["open"]
    gl_open_df.index = (pd.to_datetime(gl_open_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_turnover_df = tmp["turnover"]
    gl_turnover_df.index = (pd.to_datetime(gl_turnover_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_turnover_rate_df = tmp["turnover_rate"]
    gl_turnover_rate_df.index = (pd.to_datetime(gl_turnover_rate_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_hl = tmp["low"] < tmp["high_limit"]
    gl_hl.index = (pd.to_datetime(gl_hl.index) + pd.Timedelta(hours=7)).astype(int)
    gl_avg_price_df = tmp["avg_price"]
    gl_avg_price_df.index = (pd.to_datetime(gl_avg_price_df.index) + pd.Timedelta(hours=7)).astype(int)

    gl_930_open_df = pickle.load(open("930open", "rb"))
    gl_930_open_df.index = (pd.to_datetime(gl_930_open_df.index) + pd.Timedelta(hours=7)).astype(int)

    # gl_return_df = gl_close_df.shift(-interval) / gl_close_df - 1
    gl_return_df = gl_close_df.shift(-interval) / gl_open_df.shift(-1) - 1
    # gl_return_df = gl_close_df.shift(-interval) / gl_930_open_df.shift(-1) - 1
    gl_alpha_return_df = gl_return_df.sub(gl_return_df.mean(axis=1), axis=0)

except Exception as e:
    logger.exception(e)
    raise e


def func_train(x):
    return x[0]


def func_non_train(x):
    return x[1]


def func_pred(x):
    return x[2]


class FactorSelect:
    factor_result: ClassVar[pd.DataFrame] = pd.DataFrame()

    def __init__(self, pool):
        if FactorSelect.factor_result.empty:
            directory = Path("/home/<USER>/work/因子工厂/turnover_90_2025-07-27/")
            res = []

            # 获取目录下的所有文件和子目录
            for path in directory.iterdir():
                if path.is_file() and {
                    "000852.SH": f"_{interval}_000852.csv",
                    "000905.SH": f"_{interval}_000905.csv",
                    "000300.SH": f"_{interval}_000300.csv",
                    "all": f"_{interval}.csv",
                    "turnover": f"_{interval}_turnover.csv",
                    "turnover_rate": f"_{interval}_turnover_rate.csv",
                }[pool] in str(path):  # 只筛选文件
                    tmp = re.search(r"(\d{8})-(\d{6})", str(path.name))
                    if tmp:
                        dt = tmp.group(1)
                        month = tmp.group(2)
                        d = pd.read_csv(path, index_col=0)
                        d["month"] = month
                        d["dt"] = dt
                        res.append(d)
                    else:
                        raise
            d = pd.concat(res, axis=0)
            d = d.sort_values("month")
            d = d.reset_index()
            d["shift"] = d["f_name"].apply(lambda x: re.search(r"\((\d+)\)", x).group(1))
            d = d.astype({"dt": int})
            FactorSelect.factor_result = d

    def slice(self, end_dt, n):
        return (
            self.factor_result.loc[self.factor_result["dt"] <= int(pd.Timestamp(end_dt).strftime("%Y%m%d"))]
            .groupby("f_name")
            .apply(lambda x: x.nlargest(n, "dt"))
            .reset_index(drop=True)
        )

    @lru_cache(500)
    def _get_factor(self, end_dt, n, ic_threshold, ir_threshold) -> pd.Series:
        tmp = self.slice(end_dt, n)
        cols = list(filter(lambda x: "1_0_ic_mean" in x and "top" not in x, tmp.columns))
        cols = [i for i in cols if "(clip_raw)" in i]
        count_cols = list(map(lambda s: s.replace("ic_mean", "valid_count"), cols))
        sum_cols = list(map(lambda s: f"sum_{s}", cols))

        for col in cols:
            tmp[f"sum_{col}"] = tmp[col.replace("ic_mean", "valid_count")] * tmp[col]
        tmp = tmp.groupby("f_name").apply(lambda x: x[count_cols + sum_cols].sum(axis=0))

        a = tmp[sum_cols]
        a.columns = a.columns.map(lambda x: x[4:])
        b = tmp[count_cols]
        b.columns = b.columns.map(lambda x: x.replace("valid_count", "ic_mean"))

        c = tmp[count_cols] > (np.max(tmp[count_cols]) * 0.8)
        c.columns = c.columns.map(lambda x: x.replace("valid_count", "ic_mean"))

        d = (a / b)[c].abs().max(axis=1)
        d = d.loc[d > ic_threshold]
        e = (a / b)[c].loc[d.index].abs().idxmax(axis=1)
        return e

    def get_factor(self, end_dt, n, ic_threshold, ir_threshold, shift):
        e = self._get_factor(end_dt, n, ic_threshold, ir_threshold)
        ret = {}
        for k, v in e.groupby(e):
            for vv in v.index:
                name1, v2 = vv.split("-")
                tmp = re.search(r"(.+)\((\d+)\)", v2)
                if not tmp:
                    raise
                name2 = tmp.group(1)
                shift_ = int(tmp.group(2))
                if shift_ <= shift and (name1, name2) not in EXCEPT_FACTORS and (name1, "") not in EXCEPT_FACTORS:
                    ret.setdefault(k, {}).setdefault(name1, []).append((name2, shift_))
        return ret


def dp(hxf, factor_name, shift, factor_cache_file, industry_dict_cache_file, start_dt, end_dt, method, stock_mask):
    dd = smdf.get_factor(hxf, factor_name, shift, factor_cache_file, industry_dict_cache_file, start_dt, end_dt)
    dd = dd[stock_mask].dropna(how="all", axis=1)
    dd.replace([np.inf, -np.inf], np.nan, inplace=True)
    factor_name = f"{factor_name}({shift})"

    if "clip" not in method:
        if "(raw)" in method:
            ret = dd
        elif "(log_0)" in method:
            ret = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
        elif "(log_1)" in method:
            ret = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
        elif "(-log_0)" in method:
            dd *= -1
            ret = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
        elif "(-log_1)" in method:
            dd *= -1
            ret = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
        else:
            raise
    else:
        dd = np.clip(
            dd, dd.quantile(0.05, axis=1).to_frame().to_numpy(), dd.quantile(0.95, axis=1).to_frame().to_numpy()
        )
        if "(clip_raw)" in method:
            ret = dd
        elif "(clip_log_0)" in method:
            ret = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
        elif "(clip_log_1)" in method:
            ret = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
        elif "(clip_-log_0)" in method:
            dd *= -1
            ret = np.log(1e-7 + dd.add(dd.min(axis=1).abs(), axis=0))
        elif "(clip_-log_1)" in method:
            dd *= -1
            ret = np.log(1 + dd.add(dd.min(axis=1).abs(), axis=0))
        else:
            raise
    return f"[x]{factor_name}", ret


def construct_dataset(
    t1,
    t2,
    t3,
    factor_mapping,
    factor_cache_file=smdf.DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file=smdf.DEFAULT_INDUSTRY_DICT_CACHE_FILE,
    **kwargs,
):
    start_dt = (pd.Timestamp(t1) - pd.Timedelta(days=20)).strftime("%Y%m%d")
    end_dt = (
        pd.Timestamp(t2).strftime("%Y%m%d")
        if re.match("expire\[\d{4}-\d{2}-\d{2}\]", t3)
        else pd.Timestamp(t3).strftime("%Y%m%d")
    )

    data = {}

    # 构建Y

    yy = slice_dataset(
        # (gl_high_df / 2 + gl_low_df / 2).shift(-interval) / gl_930_open_df.shift(-1) - 1,
        # gl_avg_price_df.shift(-interval) / gl_930_open_df.shift(-1) - 1,
        # np.log(gl_avg_price_df.shift(-interval)) - np.log(gl_930_open_df.shift(-1)),
        np.log(gl_avg_price_df.shift(-interval)) - np.log(gl_open_df.shift(-1)),
        # (gl_high_df / 2 + gl_low_df / 2).shift(-interval) / gl_open_df.shift(-1) - 1,
        f"{start_dt} 0000",
        f"{end_dt} 2359",
        "both",
    )
    data["[y]"] = yy

    if gl_pool == "turnover":
        stock_mask = gl_turnover_df.gt(gl_turnover_df.quantile(0.9, axis=1), axis=0) & np.isfinite(gl_close_df)
    elif gl_pool == "turnover_rate":
        stock_mask = gl_turnover_rate_df.gt(gl_turnover_rate_df.quantile(0.90, axis=1), axis=0) & np.isfinite(
            gl_close_df
        )
    data["[y]"] = data["[y]"][stock_mask].dropna(how="all", axis=1)
    logger.info("Y构建完成")

    # data["[x]jump"] = (np.log(gl_open_df.shift(-1)) - np.log(gl_close_df))[stock_mask].dropna(how="all", axis=1)
    # data["[x]jump"] = ((data["[x]jump"] > -0.04) & (data["[x]jump"] < 0.04) & np.isfinite(gl_close_df)).astype(int)[stock_mask].dropna(how="all", axis=1)
    with tqdm(
        total=sum([len(vv) for v in factor_mapping.values() for vv in v.values()]), desc=f"因子构建 {start_dt}-{end_dt}"
    ) as pbar:
        futures = []
        with ProcessPoolExecutor(max_workers=7) as executor:
            for method, fm in factor_mapping.items():
                for hxf, j in fm.items():
                    for factor_name, shift in j:
                        fut = executor.submit(
                            pj_dp,
                            hxf,
                            factor_name,
                            shift,
                            factor_cache_file,
                            industry_dict_cache_file,
                            start_dt,
                            end_dt,
                            method,
                            stock_mask,
                        )
                        fut.add_done_callback(lambda x: pbar.update(1))
                        futures.append(fut)
            for future in as_completed(futures):
                k, v = future.result()
                data[k] = v

    logger.info("X构建完成")
    yyy = yy[stock_mask]
    if kwargs.get("include", []):
        mask1 = reduce(
            lambda x, y: x | y,
            (
                yyy.ge(yyy.quantile(i / 100, axis=1), axis=0) & yyy.le(yyy.quantile(j / 100, axis=1), axis=0)
                for i, j in kwargs.get("include", [])
            ),
        )
    else:
        mask1 = pd.DataFrame(True, index=yy.index, columns=yy.columns)

    spm = StockPoolMask(interval)
    data["[mask]0"] = spm(stock_mask & mask1, 2, True, True, True, True).fillna(False)
    data["[mask]1"] = spm(stock_mask, 1, True, True, False, False).fillna(False)
    data["[mask]2"] = spm(stock_mask, 1, True, True, False, False).fillna(False)

    return data


def init_exp(sd, md, ed, ic_threshold, ir_threshold, include, **main_kwargs):
    try:
        factor_select = FactorSelect(gl_pool)
        factors = factor_select.get_factor(
            md, main_kwargs["lookback"], ic_threshold, ir_threshold, main_kwargs["shift"]
        )
        if not factors:
            raise pd.errors.EmptyDataError("没有符合条件的因子")

        yf_prj.dump(
            version=gl_log_dir,
            explicit_dir=ed if re.match("expire\[\d{4}-\d{2}-\d{2}\]", ed) else f"{sd}_{md}_{ed}",
            g=globals(),
            t1=sd,
            t2=md,
            t3=ed,
            factors=factors,
            kwargs={
                "num_steps": None,
                "leading_days": 0,
                "idx": False,
                "shuffle": False,
                "drop_threshold_nan_ratio": 0.2,
                "non_training_size": 0.2,
                "cs_x_scaling_method": "cs_zscore",
                "cs_y_scaling_method": "cs_zscore",
                "x_scaling_method": "none",
                "y_scaling_method": "none",
            },
            interval=interval,
            gl_pool=gl_pool,
            pj_construct_dataset=construct_dataset,
            pj_dp=dp,
        )

        log_id = logger.add(pathlib.Path(path, "log"))
        logger.info(
            f"Exp {project_id} begin: start: {sd}, mid: {md}, end: {ed}, pool: {gl_pool}, interval: {interval}, ic_threshold: {ic_threshold}, ir_threshold: {ir_threshold}, include: {include}, main_kwargs: {main_kwargs}"
        )

        data = run_construct_dataset(
            pj_construct_dataset, t1=t1, t2=t2, t3=t3, factor_mapping=factors, **kwargs, include=include, **main_kwargs
        )
        logger.info(f"特征数量为：{len([c for c in data.keys() if c.startswith('[x]')])}")

        global gl_ref
        r_data = process_train_data(
            data,
            start_dt=t1,
            end_dt=t2,
            path=path,
            **kwargs,
            func_train=func_train,
            func_non_train=func_non_train,
            ref=gl_ref,
            enable_log=True,
        )
        return {"log_id": log_id, "r_data": r_data, "data": data}
    except pd.errors.EmptyDataError:
        logger.error("没有符合条件的因子")
        logger.remove(log_id)
        raise
    except Exception as e:
        logger.error("Experiment初始化失败")
        logger.exception(e)
        logger.remove(log_id)
        raise


def test_model(exp, data, vali_t1, vali_t2, epoch, flag, path1, path2, need_analyse=True):
    # exp.load_model(model=pathlib.Path(path2, "checkpoints", f"model_{epoch}.pt"))
    result, true_y = predict_apply_data(
        exp, data, vali_t1, vali_t2, path=path1, enable_log=True, **kwargs, func_pred=func_pred
    )

    y_hat = result.to_numpy().flatten()
    true_y = true_y.to_numpy().flatten()
    isf = np.isfinite(y_hat) & np.isfinite(true_y)
    logger.info(f"{gl_log_dir} {flag} {vali_t1}-{vali_t2}：{np.sqrt(mean_squared_error(y_hat[isf], true_y[isf]))}")

    if not need_analyse:
        save_predict_result([result], file=path2 + f"/{flag}_predict.csv")
        return result, {}
    return analyse(result, vali_t1, vali_t2, flag, path2)


def analyse(result, vali_t1, vali_t2, flag, path2):
    global interval

    save_predict_result([result], file=path2 + f"/{flag}_predict.csv")

    fa = FactorAnalyse(result, gl_return_df)
    ic = fa.ic(1, 0, False, 1, 0)
    ic.plot(title=f"{flag}_{vali_t1}_{vali_t2}", savefig=path2)

    fa = FactorAnalyse(result.apply(lambda x: x.nlargest(25), axis=1), gl_return_df)
    fb = fa.backtest(1, 0, False, False, 1, interval, 0.0)
    fb.plot(title=f"{flag}_{vali_t1}_{vali_t2}", savefig=path2)

    ics = ic.stats
    fbs = fb.stats

    logger.info(
        f"{gl_log_dir}_{flag} {vali_t1}-{vali_t2}： top N% ARR: {fbs['arr'].iloc[-1]:.4f} MDD: {fbs['mdd'].iloc[-1]:.4f}, \
IC: {ics['ic_mean'].iloc[0]:.4f} IR: {ics['ir'].iloc[0]:.4f}"
    )
    return (
        result,
        {"arr": fbs["arr"].iloc[-1], "mdd": fbs["mdd"].iloc[-1], "ic": ics["ic_mean"].iloc[0], "ir": ics["ir"].iloc[0]},
    )


def main(sd, md, ed, ic_threshold, ir_threshold, include, **main_kwargs):
    try:
        exp_dict = init_exp(sd, md, ed, ic_threshold, ir_threshold, include, **main_kwargs)
    except Exception:
        return pd.DataFrame(), {}

    try:
        train_data = exp_dict.pop("r_data")
        train_x, vali_x, train_y, vali_y = train_data[0], train_data[1], train_data[2], train_data[3]

        class DDD:
            def __init__(self, path):
                self.path = path

            def fit(self, train_x, train_y, valid_x, valid_y):
                train_data = TabularDataset(
                    pd.concat([pd.DataFrame(train_x), pd.Series(train_y.flatten()).rename("y")], axis=1)
                )
                valid_data = TabularDataset(
                    pd.concat([pd.DataFrame(valid_x), pd.Series(valid_y.flatten()).rename("y")], axis=1)
                )
                self.predictor = TabularPredictor(
                    label="y",
                    problem_type="regression",
                    path=self.path,
                    log_to_file=True,
                    # eval_metric="mean_absolute_error",
                )
                # self.predictor.fit(
                #     train_data=train_data, tuning_data=valid_data, excluded_model_types=["NN_TORCH", "FASTAI"]
                # )
                # self.predictor.fit_extra(hyperparameters={"NN_TORCH": {}, "FASTAI": {}}, num_gpus=1)
                self.predictor.fit(
                    train_data=train_data,
                    tuning_data=valid_data,
                    excluded_model_types=["NN_TORCH", "FASTAI", "KNN", "RF"],
                    num_cpus=7,
                    # presets="high_quality",
                )
                self.predictor.fit_extra(
                    hyperparameters={"NN_TORCH": {}, "FASTAI": {}},
                    num_gpus=1,  # presets="high_quality"
                )
                self.predictor.fit_extra(
                    hyperparameters={"LR": [{"penalty": "L1"}, {"penalty": "L2"}]},
                    num_cpus=7,  # presets="high_quality"
                )
                self.predictor.fit_weighted_ensemble()
                self.predictor = self.predictor.clone_for_deployment(
                    pathlib.Path(self.path).parent / "autogluon_to_deploy", return_clone=True
                )
                shutil.rmtree(self.path)
                return self.predictor

            def predict(self, x):
                return self.predictor.predict(
                    pd.concat([pd.DataFrame(x), pd.Series([0] * len(x)).rename("y")], axis=1), as_pandas=False
                )

        model = DDD(path + "/autogluon")
        model.fit(train_x, train_y, vali_x, vali_y)
        # model = LinearRegression()
        # model.fit(train_x, train_y)
        if vali_x.size > 0:
            logger.info(
                f"{gl_log_dir} {sd}-{md} train rmse: {np.sqrt(mean_squared_error(model.predict(train_x).flatten(), train_y.flatten()))}"
            )
            logger.info(
                f"{gl_log_dir} {sd}-{md} vali rmse: {np.sqrt(mean_squared_error(model.predict(vali_x).flatten(), vali_y.flatten()))}"
            )

        del train_x, vali_x, train_y, vali_y, train_data

        # test_model(
        #     model,
        #     exp_dict["data"],
        #     sd,
        #     md,
        #     0,
        #     "vali_at_best_metrics",
        #     path,
        #     path,
        #     need_analyse=main_kwargs.get("need_analyse", True),
        # )

        if re.match("expire\[\d{4}-\d{2}-\d{2}\]", ed):
            return pd.DataFrame(), {}
        tt1, tt2 = (pd.Timestamp(md) + pd.Timedelta(days=1)).strftime("%Y%m%d"), ed
        ret = test_model(
            model,
            exp_dict["data"],
            tt1,
            tt2,
            0,
            "test_at_best_metrics",
            path,
            path,
            need_analyse=main_kwargs.get("need_analyse", True),
        )
        exp_dict.pop("data")
        return ret
    except Exception as e:
        logger.error("Experiment运行失败")
        logger.exception(e)
        return pd.DataFrame(), {}
    finally:
        logger.remove(exp_dict["log_id"])


def wrap_main(sd, md, ed, *args, **kwargs):
    global interval
    sd = calc_trade_day(sd, interval).strftime("%Y-%m-%d")
    md = calc_trade_day(md, interval).strftime("%Y-%m-%d")
    tmp = re.search("expire\[(\d{4}-\d{2}-\d{2})\]", ed)
    if tmp:
        ed = calc_trade_day(tmp.group(1), interval).strftime("expire[%Y-%m-%d]")
    else:
        ed = calc_trade_day(ed, interval).strftime("%Y-%m-%d")
    return main(sd, md, ed, *args, **kwargs)


def sub_process(ic, ir, train_length, lookback, test_length, include, shift, n_cores=1):
    global gl_log_dir
    gl_log_dir = str(
        pathlib.Path(
            pathlib.Path(gl_log_dir).parts[0], f"{train_length}_{lookback}_{ic}_{ir}_{test_length}_{include}_{shift}"
        )
    )
    if not (tmp := pathlib.Path(gl_log_dir, "_predict.csv")).exists():
        if tmp.parent.exists():
            for i in tmp.parent.iterdir():
                if i.is_file():
                    i.unlink()
                elif not str(i).startswith("expire"):
                    shutil.rmtree(i)
        rolling(
            end_date="2025-05-31",
            num=int(30 / test_length),
            train_month=train_length,
            test_month=test_length,
            n_cores=n_cores,
            summary=lambda x: analyse(pd.concat([i[0] for i in x], axis=0), "", "", "", gl_log_dir),
            func=wrap_main,
            ic_threshold=ic,
            ir_threshold=ir,
            include=include,
            lookback=lookback,
            need_analyse=True,
            shift=shift,
        )
    # if not any(item.is_dir() and item.name.startswith("expire") for item in pathlib.Path(gl_log_dir).iterdir()):
    #     train_latest_model(
    #         "2025-05-31",
    #         train_length,
    #         test_length,
    #         wrap_main,
    #         ic_threshold=ic,
    #         ir_threshold=ir,
    #         include=include,
    #         lookback=lookback,
    #         need_analyse=False,
    #         shift=shift,
    #     )


if __name__ == "__main__":
    for p1, p2, p3, p4, p5 in product([0.015, 0.01], [24, 36, 48], [24, 48, 72], [3, 6, 2], [0]):
        if p2 > p3:
            continue
        sub_process(p1, 0.0, p2, p3, p4, [], p5, 5)
    # sub_process(0.01, 0.0, 24, 48, 6, [], 0, 5)

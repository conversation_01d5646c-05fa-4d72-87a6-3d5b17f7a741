import asyncio
import json
import pathlib

import aiohttp
import numpy as np
import pandas as pd
import requests
from tqdm import tqdm
import json
import time

import feedparser
import pandas as pd
from sqlalchemy.dialects.postgresql import insert
import requests
from bs4 import BeautifulSoup
from loguru import logger  # 新增: 导入 loguru 库
from sqlalchemy import Column, DateTime, Integer, String, Text, create_engine, BigInteger, Numeric
from sqlalchemy.engine import URL
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy import select, func

Base = declarative_base()

logger.add(pathlib.Path(__file__).parent / "log")


class OkxDailyQuotation(Base):
    __tablename__ = "okx_daily_quotation"
    timestamp = Column(BigInteger, primary_key=True)
    symbol = Column(String(20), primary_key=True)
    open_ = Column("open", Numeric(30, 15))
    high = Column(Numeric(30, 15))
    low = Column(Numeric(30, 15))
    close = Column(Numeric(30, 15))
    volume = Column(Numeric(35, 12))
    volume_ccy = Column(Numeric(35, 12))
    volume_ccy_quote = Column(Numeric(35, 12))


class OkxMinuteQuotation(Base):
    __tablename__ = "okx_minute_quotation"
    timestamp = Column(BigInteger, primary_key=True)
    symbol = Column(String(20), primary_key=True)
    open_ = Column("open", Numeric(30, 15))
    high = Column(Numeric(30, 15))
    low = Column(Numeric(30, 15))
    close = Column(Numeric(30, 15))
    volume = Column(Numeric(35, 12))
    volume_ccy = Column(Numeric(35, 12))
    volume_ccy_quote = Column(Numeric(35, 12))


engine = create_engine(
    url=URL.create(
        drivername="postgresql+psycopg2",
        username="postgres",
        password="believe419",
        # host="host.docker.internal",
        host="localhost",
        port=5432,
        database="quant",
    )
)  # 修改为你的PostgreSQL连接字符串
Base.metadata.create_all(engine)
Session = sessionmaker(bind=engine)

INSTTYPE = ["SPOT", "MARGIN", "SWAP", "FUTURES", "OPTION"]

semaphore = asyncio.Semaphore(30)


def dump_instruments():
    result = []
    for inst_type in INSTTYPE:
        d1 = requests.get(
            "https://www.okx.com/api/v5/public/instruments", params={"instType": inst_type}, proxies=proxies
        )
        res = json.loads(d1.content.decode("utf8"))
        res = pd.DataFrame(res["data"])
        try:
            res["list_datetime"] = pd.to_datetime(res["listTime"].astype(np.int64) * 1e6)
            res["exp_datetime"] = res["expTime"].apply(lambda x: pd.to_datetime(int(x) * 1e6) if x else pd.NaT)
            result.append(res)
        except Exception as e:
            print(e)
            print(res)
    result = pd.concat(result, axis=0).reset_index(drop=True)
    result.to_csv("okx_instruments.csv")


# 每 2 秒允许 20 个请求
RATE_LIMIT = 2
RATE_PERIOD = 0.25

# 初始化令牌桶
bucket = asyncio.Queue(maxsize=RATE_LIMIT)


# 填充令牌的协程
async def fill_bucket():
    while True:
        for _ in range(RATE_LIMIT):
            try:
                bucket.put_nowait(True)
            except asyncio.QueueFull:
                pass
        await asyncio.sleep(RATE_PERIOD)


# 获取令牌
async def get_token():
    await bucket.get()


def parse_price_df(response):
    df = pd.DataFrame(response["data"], columns=["ts", "o", "h", "l", "c", "vol", "volCcy", "volCcyQuote", "confirm"])
    df = df.astype(
        {
            "ts": np.int64,
            "o": np.float64,
            "h": np.float64,
            "l": np.float64,
            "c": np.float64,
            "vol": np.float64,
            "volCcy": np.float64,
            "volCcyQuote": np.float64,
            "confirm": np.int64,
        }
    )
    df.rename(
        columns={
            "ts": "timestamp",
            "o": "open_",
            "h": "high",
            "l": "low",
            "c": "close",
            "vol": "volume",
            "volCcy": "volume_ccy",
            "volCcyQuote": "volume_ccy_quote",
            "confirm": "confirm",
        },
        inplace=True,
    )
    return df


pbar = tqdm(total=0)


async def fetch(session, params):
    async with semaphore:
        for _ in range(5):
            await get_token()  # 获取令牌
            try:
                async with session.get(
                    "https://www.okx.com/api/v5/market/history-candles", params=params, proxy="http://127.0.0.1:10800"
                ) as response:
                    res = await response.json()
                    if res["code"] == "0":
                        res = parse_price_df(res)
                        res["timestamp"] *= 1e6
                        res["symbol"] = params["instId"]
                        res.drop(columns=["confirm"], inplace=True)
                        if not res.empty:
                            with Session() as pg_ss:
                                pg_ss.execute(
                                    insert(OkxDailyQuotation if params["bar"] == "1D" else OkxMinuteQuotation)
                                    .values(res.to_dict(orient="records"))
                                    .on_conflict_do_nothing()
                                )
                                pg_ss.commit()
                        pbar.update(1)
                        return
            except Exception:
                continue
        else:
            logger.warning(f"{params} 请求失败，重试次数已用完")


async def fetch_price(bar):
    token_task = asyncio.create_task(fill_bucket())

    all_instruments = pd.read_csv(
        pathlib.Path(__file__).parent / "okx_instruments.csv",
        index_col=0,
        parse_dates=["list_datetime", "exp_datetime"],
    ).sort_values(by="list_datetime")
    with Session() as pg_ss:
        if bar == "1D":
            result = pg_ss.execute(
                select(OkxDailyQuotation.symbol, func.max(OkxDailyQuotation.timestamp)).group_by(
                    OkxDailyQuotation.symbol
                )
            )
        else:
            result = pg_ss.execute(
                select(OkxMinuteQuotation.symbol, func.max(OkxMinuteQuotation.timestamp)).group_by(
                    OkxMinuteQuotation.symbol
                )
            )
        update_time_mapping = dict(result.fetchall())

    # all_instruments = all_instruments[all_instruments["instId"] == "BTC-USD-SWAP"]
    async with aiohttp.ClientSession() as session:
        tasks = []
        for _, row in all_instruments.iterrows():
            end = max(row["list_datetime"], pd.Timestamp(update_time_mapping.get(row["instId"], 0)))
            while True:
                tasks.append(
                    fetch(
                        session,
                        {"instId": row["instId"], "bar": bar, "limit": 100, "after": int(end.timestamp() * 1e3)},
                    )
                )
                if end > pd.Timestamp.now():
                    break
                end += pd.Timedelta(days=95) if bar == "1D" else pd.Timedelta(minutes=95)
            if bar == "1m":
                pbar.reset(total=len(tasks))
                pbar.set_description(f"{row['instId']}")
                await asyncio.gather(*tasks)
                tasks = []
        if bar == "1D":
            pbar.reset(total=len(tasks))
            await asyncio.gather(*tasks)
        token_task.cancel()


if __name__ == "__main__":
    asyncio.run(fetch_price("1m"))
    # asyncio.run(fetch_price("1D"))
    # print(results)
    # print(results.shape)

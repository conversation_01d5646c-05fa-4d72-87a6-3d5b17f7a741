# content of test_em_trade_api.py
from em_trade_api import EMCreditTradeAPI, EMTradeAPI
from qnt_utils.toolset import decrypt


class TestEMTradeAPI:
    api = EMTradeAPI(
        account="************",
        password=decrypt(
            "encrypted:WHduSzRXSUpHS1Qvbmw0ZWJZWnhKa1pFSGhQcVV6dWxnbllleERjVjJrTy8yVzFpa1BobGd2ZXpNelBwYzhReDZGUmpyV1RvaFRHMzhaaXRNdGN5Q1Awbmt0TGQycTFlYWtHdzZpVG1FUHJIc3lPbVdTdWQ1OEtxTEd0blhNTVM4aFE2U1ZLbVRoSU5MZzB0ZUxPeDUrOHRDSFhwMHdnWVRrV1NLQk5SWHlVPQ=="
        ),
    )
    api_credit = EMCreditTradeAPI(
        account="************",
        password=decrypt(
            "encrypted:WHduSzRXSUpHS1Qvbmw0ZWJZWnhKa1pFSGhQcVV6dWxnbllleERjVjJrTy8yVzFpa1BobGd2ZXpNelBwYzhReDZGUmpyV1RvaFRHMzhaaXRNdGN5Q1Awbmt0TGQycTFlYWtHdzZpVG1FUHJIc3lPbVdTdWQ1OEtxTEd0blhNTVM4aFE2U1ZLbVRoSU5MZzB0ZUxPeDUrOHRDSFhwMHdnWVRrV1NLQk5SWHlVPQ=="
        ),
    )

    def test_portfolio(self):
        assert self.api.portfolio

    def test_positions(self):
        assert self.api.positions

    def test_get_orders(self):
        assert len(self.api.get_orders("2023-11-06", "2023-11-13")) > 0

    def test_get_transactions(self):
        assert len(self.api.get_transactions("2023-08-15", "2023-11-13")) > 0

    def test_order(self):
        assert self.api.order("600000", 6.5, 100, None, 1)

    def test_portfolio_credit(self):
        assert self.api_credit.portfolio

    def test_positions_credit(self):
        assert self.api_credit.positions

    def test_get_orders_credit(self):
        assert len(self.api_credit.get_orders("2023-11-06", "2023-11-13")) > 0

    def test_get_transactions_credit(self):
        assert len(self.api_credit.get_transactions("2023-08-15", "2023-11-13")) > 0

    def test_order_credit(self):
        assert self.api_credit.order("600000", 6.5, 100, None, 1)
        assert self.api_credit.order("600000", 6.5, 100, None, 3)

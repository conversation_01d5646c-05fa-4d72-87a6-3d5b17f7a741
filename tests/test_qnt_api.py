# content of test_qnt_api.py
import pandas as pd
import pytest

from qnt_research.api import *


def test_get_trade_days():
    assert get_trade_days("DCE", "2023-11-17", "2023-11-17") == [
        pd.Timestamp("2023-11-17", tz="Asia/Shanghai")
    ]
    assert get_trade_days("DCE", "2023-11-18", "2023-11-18") == []


@pytest.mark.parametrize(
    "tv, exchange, expected",
    [
        ("ru", "shfe", "RU9999.SHFE"),
        ("FU-1", "SHFE", "FU9999-1.SHFE"),
    ],
)
def test_get_mfut(tv, exchange, expected):
    assert get_mfut(tv, exchange) == expected

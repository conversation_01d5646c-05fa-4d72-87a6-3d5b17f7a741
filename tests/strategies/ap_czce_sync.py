from typing import Optional

import numpy as np
import pandas as pd

from qnt_research.api import set_sql
from qnt_research.market import gl_market_snapshot, gl_trade_parameters
from qnt_research.data_manager.original_data import gl_original_data
from qnt_research.engine import EventDriveEngine
from qnt_research.strategy.base import SyncStrategy
from qnt_research.trader.trader import Trader
from qnt_research.utils.algo import TR, HHVToday, LLVToday, Settle
from qnt_research.virtual_exchange import gl_virtual_exchange
from qnt_utils.enums import BasicData, StrategyMode

pd.set_option("display.max_rows", None)

CALC_SYMBOL = "AP8888.CZCE"
TRADE_SYMBOL = "AP2310.CZCE"


class TestStrategy(SyncStrategy):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def init(self):
        data = self.subscribe(CALC_SYMBOL, 2000, 2, BasicData.MINUTE)
        data.create_series("tr", TR(3))
        data.create_series("hhv_today", HHVToday("high"))
        data.create_series("llv_today", LLVToday("low"))
        data.create_series("llvc_today", LLVToday("close"))
        data.create_series("llvo_today", LLVToday("open"))
        data.create_series("hhvc_today", HHVToday("close"))
        data.create_series("hhvo_today", HHVToday("open"))
        data.create_series("my_settle", Settle())
        data.create_series("cfj1")
        data.create_series("exxn1")
        data.create_series("exxn2")
        data.create_series("fac1_")
        data.create_series("fac2_")
        self.status: int = 0
        self.bkhigh: float = -np.inf
        self.bkhigh_loc: Optional[int] = None
        self.sklow: float = np.inf
        self.sklow_loc: Optional[int] = None
        self.barslast_open: Optional[int] = None

    def calc(self, data):
        positions = self.trader.positions[1]
        portfolio = self.trader.portfolio[1]
        symbol = data.symbol

        n = data.n_trade_day[-1]
        nnn = data.n_natural_day[-1]
        n1 = data.n_trade_day[int(-n - 1)]
        n2 = data.n_trade_day[int(-n - n1 - 1)]
        n3 = data.n_trade_day[int(-n - n1 - n2 - 1)]
        num_bars_oneday = max(n1, n2, n3)

        param1 = int(3.5 * num_bars_oneday)
        ex1 = data.high[-(2 * param1 - 1) :].max() == data.high[-param1] and not data.low[-(2 * param1 - 1) :].min() == data.low[-param1]
        ex2 = data.low[-(2 * param1 - 1) :].min() == data.low[-param1] and not data.high[-(2 * param1 - 1) :].max() == data.high[-param1]
        if ex1:
            data.exxn1.update(data.high[-param1])
        else:
            if data.exxn1.count == 0:
                data.exxn1.update(0)
            else:
                data.exxn1.update(data.exxn1[-1])
        if ex2:
            data.exxn2.update(data.low[-param1])
        else:
            if data.exxn2.count == 0:
                data.exxn2.update(0)
            else:
                data.exxn2.update(data.exxn2[-1])
        if data.exxn1[-1] > data.exxn2[-1]:
            self.status = 0
        else:
            if data.exxn1[-1] != data.exxn1[-2] and data.exxn2[-1] == data.exxn2[-2]:
                self.status = 1
            else:
                self.status = -1
        if self.status == 0:
            loc = (data.close[-1] - data.exxn2[-1]) / (data.exxn1[-1] - data.exxn2[-1]) * 100
        elif self.status == 1:
            loc = 150
        else:
            loc = -50
        if loc > 50 - 20:
            if loc > 100:
                fac1_ = 9 / 10
            else:
                fac1_ = 10 / 10
        else:
            if loc > 0:
                fac1_ = 9 / 10
            else:
                fac1_ = 13 / 10
        if loc < 50 + 20:
            if loc < 0:
                fac2_ = 9 / 10
            else:
                fac2_ = 10 / 10
        else:
            if loc < 100:
                fac2_ = 9 / 10
            else:
                fac2_ = 13 / 10
        data.fac1_.update(fac1_)
        data.fac2_.update(fac2_)
        fac1 = data.fac1_[-n:].max()
        fac2 = data.fac2_[-n:].max()

        atr = data.tr[-int(num_bars_oneday) :].mean()
        hh_today = data.hhv_today[-1]
        hh_yestoday = data.hhv_today[-1 - n]
        ll_today = data.llv_today[-1]
        ll_yestoday = data.llv_today[-1 - n]
        cc_yestoday = data.close[-1 - n]
        oo_today = data.open[-n]

        cfj1 = max(hh_yestoday - cc_yestoday, cc_yestoday - ll_yestoday) * 75 / 100 if n == 1 else 0
        data.cfj1.update(cfj1)
        cfj = data.cfj1[np.argwhere(data.n_trade_day == 1)[-5][0] :].sum() / (4 + 1)

        upperbound = min(max(cc_yestoday + fac1 * cfj, oo_today + 65 / 100 * cfj * fac1), oo_today + 110 / 100 * cfj * fac1)
        lowerbound = max(min(cc_yestoday - fac2 * cfj, oo_today - 65 / 100 * cfj * fac2), oo_today - 110 / 100 * cfj * fac2)

        p9 = 4
        p10 = 50
        p11 = 35
        p12 = 10
        temp_len = int(p10 / 10 * num_bars_oneday)
        temp_hhv = data.high[-temp_len:].max()
        temp_hhv_ref = data.high[-2 * temp_len : -temp_len].max()
        temp_llv = data.low[-temp_len:].min()
        temp_llv_ref = data.low[-2 * temp_len : -temp_len].min()
        temp = (max(temp_hhv, temp_hhv_ref) - min(temp_hhv, temp_hhv_ref) + max(temp_llv, temp_llv_ref) - min(temp_llv, temp_llv_ref)) / (
            min(temp_hhv, temp_hhv_ref) - max(temp_llv, temp_llv_ref)
        )

        if not symbol in positions.keys() or (
            positions[symbol]["LONG"].amount == 0 and positions[symbol]["SHORT"].amount == 0 and portfolio.frozen_cash == 0
        ):
            con1_l = data.close[-1] > upperbound
            con2_l = data.close[-1] >= max(data.close[-n:].max(), data.open[-n:].max())
            con3_l = data.close[-1] < data.my_settle[-n - 1] * (1 + 50 / 1000)
            con4_l = data.close[-1] > data.close[-n - 1] * (1 - 15 / 1000)
            con5_l = (
                temp < 0
                or temp > p9 / 10
                or min(temp_hhv, temp_hhv_ref) / max(temp_llv, temp_llv_ref) > 1 + p11 / 1000
                or data.close[-1] > min(data.close[-n - 1], data.open[-n]) * (1 + p12 / 1000)
            )

            con1_s = data.close[-1] < lowerbound
            con2_s = data.close[-1] <= min(data.close[-n:].min(), data.open[-n:].min())
            con3_s = data.close[-1] > data.my_settle[-n - 1] * (1 - 50 / 1000)
            con4_s = data.close[-1] < data.close[-n - 1] * (1 + 15 / 1000)
            con5_s = (
                temp < 0
                or temp > p9 / 10
                or min(temp_hhv, temp_hhv_ref) / max(temp_llv, temp_llv_ref) > 1 + p11 / 1000
                or data.close[-1] < max(data.close[-n - 1], data.open[-n]) * (1 - p12 / 1000)
            )
            if con1_l and con2_l and con3_l and con4_l and con5_l:
                self.buy_open(symbol, None, self.trader.init_money * 0.3)
                self.bkhigh = -np.inf
                self.barslast_open = data.count
            elif con1_s and con2_s and con3_s and con4_s and con5_s:
                self.sell_open(symbol, None, self.trader.init_money * 0.3)
                self.sklow = np.inf
                self.barslast_open = data.count
        elif positions[symbol]["LONG"].available > 0:
            self.bkhigh = max(self.bkhigh, data.high[-1])
            self.bkhigh_loc = data.count if data.high[-1] == self.bkhigh else self.bkhigh_loc
            xn5 = 8 + (3 if (data.count - self.barslast_open + 1) < abs(np.argwhere(data.n_trade_day == 1)[-1][0] - len(data.n_trade_day)) else 0)
            if data.close[-1] < (positions[symbol]["LONG"].cost_basis - 1) * (1 - 9 / 1000):
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
            elif (
                data.close[-1] < self.bkhigh * (1 - xn5 / 1000)
                and data.close[-1] < (positions[symbol]["LONG"].cost_basis - 1) * (1 + 10 / 1000)
                and data.close[-1] == data.close[-(data.count - self.bkhigh_loc + 1) :].min()
            ):
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
            elif (
                data.close[-1] < self.bkhigh * (1 - 37 / 1000)
                and data.close[-1] >= (positions[symbol]["LONG"].cost_basis - 1) * (1 + 10 / 1000)
                and data.close[-1] == data.close[-(data.count - self.bkhigh_loc + 1) :].min()
            ):
                self.sell_close(symbol, positions[symbol]["LONG"].available, None)
        elif positions[symbol]["SHORT"].available > 0:
            self.sklow = min(self.sklow, data.low[-1])
            self.sklow_loc = data.count if data.low[-1] == self.sklow else self.sklow_loc
            xn5 = 8 + (3 if (data.count - self.barslast_open + 1) < abs(np.argwhere(data.n_trade_day == 1)[-1][0] - len(data.n_trade_day)) else 0)
            if data.close[-1] > (positions[symbol]["SHORT"].cost_basis + 1) * (1 + 9 / 1000):
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)
            elif (
                data.close[-1] > self.sklow * (1 + xn5 / 1000)
                and data.close[-1] > (positions[symbol]["SHORT"].cost_basis + 1) * (1 - 10 / 1000)
                and data.close[-1] == data.close[-(data.count - self.sklow_loc + 1) :].max()
            ):
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)
            elif (
                data.close[-1] > self.sklow * (1 + 37 / 1000)
                and data.close[-1] <= (positions[symbol]["SHORT"].cost_basis + 1) * (1 - 10 / 1000)
                and data.close[-1] == data.close[-(data.count - self.sklow_loc + 1) :].max()
            ):
                self.buy_close(symbol, abs(positions[symbol]["SHORT"].available), None)


if __name__ == "__main__":
    import cProfile
    import io
    import pstats

    # set_sql(host="windows")

    # with cProfile.Profile() as pr:
    aa = EventDriveEngine(StrategyMode.BACKTEST)
    aa.set_env("20221101 2100")

    strategy = TestStrategy()
    trader = Trader(1e7, gl_virtual_exchange)
    # strategy.subscribe(TRADE_SYMBOL, 100, 1, BasicData.MINUTE)
    strategy.register_trader(trader)

    strategy1 = TestStrategy()
    trader1 = Trader(1e7, gl_virtual_exchange)
    strategy1.register_trader(trader1)
    aa.register_strategy(strategy, strategy1)

    aa.run()

    # ps = pstats.Stats(pr, stream=(s := io.StringIO())).sort_stats(pstats.SortKey.CUMULATIVE)
    # ps.print_stats(0.02)
    # print(s.getvalue())

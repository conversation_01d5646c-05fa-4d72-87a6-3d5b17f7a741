import pandas as pd
import datetime

from qnt_research.engine import EventDriveEngine
from qnt_research.strategy.base import BaseStrategy
from qnt_research.trader.trader import Trader
from qnt_research.virtual_exchange import gl_virtual_exchange
from qnt_research.trade_api import TradeAPI
from qnt_utils.enums import BasicData, StrategyMode

pd.set_option("display.max_rows", None)


class TestStrategy(BaseStrategy):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def init(self):
        data = self.subscribe("000001.SZSE", 2000, 1, BasicData.MINUTE)
        # data = self.subscribe("000725.SZSE", 2000, 1, BasicData.MINUTE)
        # data = self.subscribe("600519.SSE", 2000, 1, BasicData.MINUTE)

    def on_data(self, data, **kwargs):
        print(datetime.datetime.now(), data)
        self.trader.order(symbol="000001.SZSE", direction="BUY", offset="OPEN", amount=100, price=12.7)
        print("positions", self.trader.positions)
        print("portfolio", self.trader.portfolio)
        print("\n")

    def on_order(self, msg, **kwargs):
        print("order", datetime.datetime.now(), msg)
        print("\n")


if __name__ == "__main__":
    import cProfile
    import io
    import pstats

    with cProfile.Profile() as pr:
        aa = EventDriveEngine(StrategyMode.TRADE)
        # aa = EventDriveEngine(StrategyMode.BACKTEST)

        # aa.register_original_data("20230414 1458", "20230414 1500", addr={1: {"host": "localhost", "port": 12330}})
        aa.set_env("20230414 1458", "20230414 1500", addr={1: {"host": "*************", "port": 12330}})

        aa._register_trader(Trader(30000, TradeAPI("541300123639", True, "*************", 10000)))
        # aa.register_trader(Trader(1e7, gl_virtual_exchange))

        aa.register_strategy(TestStrategy())
        aa.run()

        # ps = pstats.Stats(pr, stream=(s := io.StringIO())).sort_stats(pstats.SortKey.CUMULATIVE)
        # ps.print_stats(0.02)
        # print(s.getvalue())

import re
import numpy as np
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Side, Border
from openpyxl.worksheet.pagebreak import Break


def check_box_location_mapping(df):
    """检查盒号和存放位置是否1对1对应"""
    print("=== 检查盒号和存放位置的对应关系 ===")

    # 检查盒号对应的存放位置是否唯一
    box_location_mapping = df.groupby("盒号")["存放位置"].nunique()

    # 找出一个盒号对应多个存放位置的情况
    multiple_locations = box_location_mapping[box_location_mapping > 1]
    if not multiple_locations.empty:
        print("❌ 发现盒号对应多个存放位置的问题:")
        for box_id in multiple_locations.index:
            locations = df[df["盒号"] == box_id]["存放位置"].unique()
            print(f"  盒号 '{box_id}' 对应的存放位置: {list(locations)}")
    else:
        print("✅ 所有盒号都只对应一个存放位置")

    # 检查存放位置对应的盒号是否唯一
    location_box_mapping = df.groupby("存放位置")["盒号"].nunique()

    # 找出一个存放位置对应多个盒号的情况
    multiple_boxes = location_box_mapping[location_box_mapping > 1]
    if not multiple_boxes.empty:
        print("❌ 发现存放位置对应多个盒号的问题:")
        for location in multiple_boxes.index:
            boxes = df[df["存放位置"] == location]["盒号"].unique()
            print(f"  存放位置 '{location}' 对应的盒号: {list(boxes)}")
    else:
        print("✅ 所有存放位置都只对应一个盒号")

    # 总结检查结果
    is_one_to_one = multiple_locations.empty and multiple_boxes.empty
    if is_one_to_one:
        print("\n🎉 盒号和存放位置完全1对1对应！")
    else:
        print("\n⚠️  盒号和存放位置不是完全1对1对应，请检查上述问题")

    return is_one_to_one


def fnc(ws, start, df):
    if df["存放位置"].unique().size > 1:
        raise ValueError("该盒号的存放位置不唯一")

    location = df["存放位置"].iloc[0]  # 获取该盒号的存放位置
    print(f"盒号: {box_id}, 存放位置: {location}, 记录数: {len(df)}")
    location = f"杭州市萧山区鸿宁路1379号江苏银行大厦7楼档案中心{location}"

    ws.merge_cells(f"A{start}:E{start}")
    cell = ws[f"A{start}"]
    cell.value = "对公客户信贷档案归档文件目录"
    cell.font = Font(name="方正小标宋简体", size=22)
    cell.alignment = Alignment(horizontal="center", vertical="center")
    start += 1

    ws.merge_cells(f"A{start}:E{start}")
    cell = ws[f"A{start}"]
    cell.value = f"盒号：{box_id}"
    cell.font = Font(name="方正楷体简体", size=12)
    cell.alignment = Alignment(horizontal="left", vertical="center")
    start += 1

    ws.merge_cells(f"A{start}:E{start}")
    cell = ws[f"A{start}"]
    cell.value = f"存放位置：{location}"
    cell.font = Font(name="方正楷体简体", size=12)
    cell.alignment = Alignment(horizontal="left", vertical="center")
    start += 1

    for col, value in enumerate(["序号", "档号", "档案名称", "总页数", "备注"]):
        cell = ws.cell(row=start, column=col + 1, value=value)
        cell.font = Font(name="方正楷体简体", size=12)
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="medium"), bottom=Side(style="thin")
        )
    start += 1

    df = df.reset_index(drop=True)
    for i, j in df.iterrows():
        cell = ws.cell(row=start, column=1, value=str(i + 1))
        cell.font = Font(name="宋体", size=12)
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
        )

        cell = ws.cell(row=start, column=2, value=j["档号"])
        cell.font = Font(name="宋体", size=12)
        cell.alignment = Alignment(horizontal="left", vertical="center")
        cell.border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
        )

        cell = ws.cell(row=start, column=3, value=j["档案名称"])
        cell.font = Font(name="宋体", size=12)
        cell.alignment = Alignment(horizontal="left", vertical="center")
        cell.border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
        )

        cell = ws.cell(row=start, column=4, value=j["总页数"])
        cell.font = Font(name="宋体", size=12)
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
        )

        cell = ws.cell(row=start, column=5, value=j["备注"])
        cell.font = Font(name="宋体", size=12)
        cell.alignment = Alignment(horizontal="left", vertical="center")
        cell.border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
        )
        start += 1

    start += 1
    ws.row_breaks.append(Break(start - 1))
    return start


if __name__ == "__main__":
    # 读取源文件
    print("正在读取源文件.xls...")
    raw_data = pd.read_excel("源文件.xls", sheet_name="Sheet1")
    print(f"成功读取数据，共 {len(raw_data)} 行记录")
    print(f"列名: {list(raw_data.columns)}")

    a = {}
    for i in raw_data["盒号"].unique():
        tmp = re.search(r"放(\d+)-(\d+)", i)
        if not tmp:
            raise Exception(f"盒号格式错误：{i}")
        a.setdefault(int(tmp.group(1)) // 10, {}).setdefault(tmp.group(1), []).append(tmp.group(2))

    # 创建一个新的工作簿和激活的工作表
    wb = Workbook()
    for i, j in a.items():
        i = (i + 1) * 10
        title = f"放{i - 9}-放{i}"
        ws = wb.create_sheet(title=title)

        start = 1
        for id1, id2s in j.items():
            for id2 in np.sort(id2s):
                box_id = f"放{id1}-{id2}"
                df = raw_data[raw_data["盒号"] == box_id]
                start = fnc(ws, start, df)
    wb.remove(wb["Sheet"])

    # 保存Excel文件
    wb.save("结果.xlsx")

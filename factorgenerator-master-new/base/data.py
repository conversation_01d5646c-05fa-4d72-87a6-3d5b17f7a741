from enum import IntEnum
from typing import List, Optional

import numpy as np
import torch


class EnumMock(type):
    def __getitem__(self, name):
        return self(None, name)

    def __getattr__(self, name):
        return self(None, name)

    def __iter__(self):
        for x in self.Enum:
            yield self(None, x.name)


class FeatureType(object, metaclass=EnumMock):
    _ori = None  # 备份原Enum对象
    Enum = IntEnum("Feature", ["open", "close", "high", "low", "volume", "turnover", "open_interest"], start=0)

    @classmethod
    def update(cls, fields=None):
        if cls._ori is None:
            cls._ori = cls.Enum
        cls.Enum = IntEnum("Feature", fields, start=0) if fields else cls._ori

    def __init__(self, value, name=None):
        self.name = name or self.Enum(value).name

    def __getattr__(self, attr):
        return getattr(self.Enum[self.name], attr)

    def __int__(self):
        return int(self.Enum[self.name])


class StockData:
    price_featureset = {"open", "close", "high", "low", "volume", "turnover", "open_interest"}

    def __init__(
        self,
        index,
        start_time,
        end_time,
        freq,
        max_backtrack_days: int = 40,
        max_future_days: int = 40,
        step: int = 0,
        featfile: str = None,
        fields: Optional[List[str]] = None,
        device: torch.device = torch.device("cpu"),
    ) -> None:
        self.freq = freq
        self.device = device
        self.max_backtrack_days = max_backtrack_days
        self.max_future_days = max_future_days
        self.step = step

        self.features = fields = fields or [x.name.lower() for x in FeatureType]
        data = self.prepare_data(index, start_time, end_time, freq, fields, max_backtrack_days, max_future_days)
        self.data = {k: torch.tensor(v, dtype=torch.float, device="cpu") for k, v in data.items()}

    @classmethod
    def prepare_data(
        self, index, start_time, end_time, freq, fields, max_backtrack_days=100, max_future_days=30, featfile=None
    ):
        """
        features: 为字符串fields小写
        """
        from mgquant_mod_mindgo.research.research_api import get_all_securities, get_last_trade_day, get_price

        start_time, end_time = (
            get_last_trade_day(start_time, -max_backtrack_days),
            get_last_trade_day(end_time, max_future_days).replace(hour=16),
        )
        securities, fields_set, data = sorted(index.split(",")), set(fields), {}
        for symbol in securities:
            pfields = list(fields_set & self.price_featureset)
            mdf = get_price(
                symbol,
                start_date=start_time,
                end_date=end_time,
                fre_step=freq,
                fields=pfields,
                skip_paused=False,
                fq="post",
            )
            mdf["date"], mdf["time"] = mdf.index.normalize(), mdf.index.strftime("%H:%M:%S")
            data[symbol] = mdf.set_index(["date", "time"]).to_xarray().to_array().to_numpy().transpose(1, 2, 0)
        return data

    def __iter__(self):
        for symbol, data in self.data.items():
            self.n_primary_bars = data.shape[0] - self.max_backtrack_days - self.max_future_days
            self.n_step = self.step or self.n_primary_bars
            self.n_secondary_bars = data.shape[1]
            self.symbol = symbol
            self.max_backtrack_bars = self.max_backtrack_days * self.n_secondary_bars
            self.max_future_bars = self.max_future_days * self.n_secondary_bars
            yield data.reshape(-1, self.n_features)

    @property
    def n_features(self) -> int:
        return len(self.features)

    @property
    def n_symbols(self) -> int:
        return len(self.data)

from typing import List, Mapping, Optional, Union

import numpy as np
import pandas as pd
import torch

from base.data import StockData
from base.expression import Expression
from base.utils import backtest


class AlphaCalculator:
    def __init__(self, data: StockData, target: Optional[Expression], dim: int = 1):
        self.data = data
        self.dim = dim

        # Target is useless if not set IC as reward
        if target is None:
            self.target_value = None
        else:
            self.target_value = [
                self.normalize(value).to("cpu")
                for value in (target(self.data) if isinstance(target, Expression) else target)
            ]

    def normalize(self, x: torch.Tensor) -> torch.Tensor:
        x = x.copy()
        x[x.isinf()] = torch.nan
        mean = x.nanmean(dim=self.dim, keepdim=True)
        n = (~x.isnan()).sum(dim=self.dim, keepdim=True) - 1
        std = (((x - mean) ** 2).nansum(dim=self.dim, keepdim=True) / n).sqrt()
        x = (x - mean) / std
        x[~x.isfinite()] = 0
        return x

    def pearson(self, x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        x_mean, x_std = x.mean(dim=self.dim, keepdim=True), x.std(dim=self.dim, keepdim=True)
        y_mean, y_std = y.mean(dim=self.dim, keepdim=True), y.std(dim=self.dim, keepdim=True)
        cov = (x * y).mean(dim=self.dim, keepdim=True) - x_mean * y_mean
        std_mul = x_std * y_std
        std_mul[(x_std < 1e-3) | (y_std < 1e-3)] = 1
        corr = cov / std_mul
        return corr

    def cal_alpha(self, expr: Expression):
        for value in expr(self.data):
            yield self.normalize(value)

    def cal_ic(self, value1: torch.Tensor, value2: torch.Tensor) -> torch.Tensor:
        return self.pearson(value1, value2)

    def cal_mutual_ic(self, expr1: Expression, expr2: Expression) -> float:
        ic_sum, ic_count = 0, 0
        for x, y in zip(self.cal_alpha(expr1), self.cal_alpha(expr2)):
            ic = self.cal_ic(x, y)
            ic_sum += ic.sum().item()
            ic_count += ic.numel()
        return ic_sum / ic_count


class IndicatorCalculator(AlphaCalculator):
    def __init__(
        self, data: StockData, target: Optional[Expression], dim: int = 0, cost: float = 0.0, one_way: int = 0
    ):
        self.data = data
        self.dim = dim
        self.cost = cost
        self.one_way = one_way

        # Target is useless if not set IC as reward
        if target is None:
            self.target_value = None
        else:
            self.target_value = [
                value.to("cpu").reshape(-1, 1)
                for value in (target(self.data) if isinstance(target, Expression) else target)
            ]

    def _backtest(self, pos_t: Mapping[str, float], y: Mapping[str, float]) -> Mapping[str, float]:
        return (
            pd.DataFrame(
                {
                    k: backtest(
                        torch.concat(pos_t[k], dim=0),
                        torch.concat(y[k], dim=0),
                        self.cost,
                        self.data.freq,
                        self.data.n_secondary_bars,
                        one_way=self.one_way,
                    )
                    for k in pos_t.keys()
                }
            )
            .median(axis=0)
            .to_dict()
        )

    def make_ensemble_alpha(self, expr_pool: List[Expression], weights: List[float]):
        """输出为 (time, n_stocks, n_strategies)"""
        values = [expr(self.data) for expr in expr_pool]
        for value in values[0]:
            # value (n, )
            position = torch.full(
                size=(len(value), 1, len(values)),
                fill_value=0.0,
                dtype=torch.float64,
                device="cpu",
            )
            position[:, 0, 0] = value * weights[0]
            if len(values) > 1:
                for enm, (iterator, weight) in enumerate(zip(values[1:], weights[1:])):
                    position[:, 0, enm + 1] = next(iterator) * weight
            yield position

    def cal_pool_backtest(self, expr_pool: List[Expression], weights: np.ndarray) -> float:
        if not expr_pool:
            return 0.0
        pos = {}
        ret = {}
        for x, y in zip(self.make_ensemble_alpha(expr_pool, weights), self.target_value):
            pos.setdefault(self.data.symbol).append(x)
            ret.setdefault(self.data.symbol).append(y)
        return self._backtest(pos, ret)["sortino"]

    def calc_single_backtest(self, expr: Expression) -> Mapping[str, float]:
        pos = {}
        ret = {}
        for x, y in zip(expr(self.data), self.target_value):
            # x (n, ) -> (n, 1)
            pos.setdefault(self.data.symbol).append(x.cpu().reshape(-1, 1))
            ret.setdefault(self.data.symbol).append(y)
        return self._backtest(pos, ret)

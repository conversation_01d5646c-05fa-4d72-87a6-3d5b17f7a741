#!/usr/bin/env python3
"""
测试新的接口日志功能：
每条日志都标明是哪个接口的日志
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def main():
    print("=== 测试接口日志功能 ===")
    print("每条日志都会标明调用的接口名称")
    print()
    
    task_file = "./test_interface_logging.json"
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    print("1. 测试 register_task 接口:")
    print("-" * 40)
    task1_id = manager.register_task("测试任务1")
    
    print("\n2. 测试 register_task 接口（带依赖）:")
    print("-" * 40)
    task2_id = manager.register_task("测试任务2", dependencies=[task1_id])
    
    print("\n3. 测试 register_task 接口（无效依赖）:")
    print("-" * 40)
    try:
        manager.register_task("无效任务", dependencies=["invalid-id"])
    except ValueError:
        pass  # 预期的错误
    
    print("\n4. 测试 wait_for_dependencies 接口:")
    print("-" * 40)
    manager.wait_for_dependencies(task1_id)
    
    print("\n5. 测试 start_task 接口:")
    print("-" * 40)
    manager.start_task(task1_id)
    
    print("\n6. 测试 complete_task 接口:")
    print("-" * 40)
    manager.complete_task(task1_id, {"test": "data"})
    
    print("\n7. 测试 update_task_metadata 接口:")
    print("-" * 40)
    manager.update_task_metadata(task1_id, {"updated": True})
    
    print("\n8. 测试 fail_task 接口:")
    print("-" * 40)
    manager.wait_for_dependencies(task2_id)
    manager.start_task(task2_id)
    manager.fail_task(task2_id, "模拟失败")
    
    print("\n9. 测试 clear_all_tasks 接口:")
    print("-" * 40)
    manager.clear_all_tasks()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print()
    print("日志格式说明:")
    print("[时间戳] [TaskManager] [接口名] 级别: 消息")
    print()
    print("示例:")
    print("[2025-09-01 00:19:32,514] [TaskManager] [register_task] INFO: 开始注册任务: 测试任务1")
    print("[2025-09-01 00:19:32,517] [TaskManager] [start_task] INFO: 任务启动成功: 测试任务1")
    print("[2025-09-01 00:19:35,518] [TaskManager] [complete_task] INFO: 任务完成成功: 测试任务1")
    print()
    print(f"完整日志已保存到: {os.path.splitext(task_file)[0]}.log")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试任务B - 依赖任务A完成后才能执行
这个任务会等待任务A（数据下载）完成后再开始执行
"""

import time
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, "../src")

from task_scheduler import TaskManager


def main():
    # 使用相同的任务文件
    task_file = "./test_tasks.json"

    # 创建任务管理器
    manager = TaskManager(task_file)

    # 首先获取任务A的ID（通过名称查找）
    tasks = manager.get_all_tasks()
    task_a_id = None
    for task in tasks:
        if task.name == "数据下载":
            task_a_id = task.id
            break

    if task_a_id is None:
        print("[任务B] 错误: 找不到依赖任务 '数据下载'")
        return

    # 注册任务B，依赖于任务A的ID
    task_id = manager.register_task("数据处理", dependencies=[task_a_id])
    print(f"[任务B] 已注册任务 '数据处理'，ID: {task_id}")
    print(f"[任务B] 依赖任务ID: {task_a_id}")

    # 等待依赖任务完成
    print("[任务B] 等待依赖任务 '数据下载' 完成...")
    manager.wait_for_dependencies(task_id)
    print("[任务B] 依赖任务已完成，开始执行")

    # 开始执行任务
    success = manager.start_task(task_id)
    if not success:
        print("[任务B] 启动任务失败")
        return

    print("[任务B] 正在处理数据...")

    # 模拟任务执行（数据处理需要2秒）
    for i in range(2):
        time.sleep(1)
        print(f"[任务B] 处理进度: {(i + 1) * 50}%")

    # 标记任务完成
    success = manager.complete_task(task_id, metadata={"processed_records": 1000, "processing_time": "2s"})
    if success:
        print("[任务B] 数据处理完成！")
    else:
        print("[任务B] 标记任务完成失败")


if __name__ == "__main__":
    main()

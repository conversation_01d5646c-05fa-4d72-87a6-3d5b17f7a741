#!/usr/bin/env python3
"""
演示新的TaskManager功能：使用task_id作为依赖
展示完整的任务依赖链和日志输出
"""

import os
import sys
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def main():
    print("=== TaskManager 新功能演示 ===")
    print("功能特点:")
    print("1. dependencies 必须使用 task_id（不再支持任务名称）")
    print("2. 注册时验证依赖任务是否存在")
    print("3. 注册成功后显示依赖任务状态")
    print("4. 所有操作都有详细的日志输出")
    print()
    
    task_file = "./demo_task_dependencies.json"
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    print("步骤1: 创建基础任务（无依赖）")
    print("-" * 40)
    task1_id = manager.register_task("数据收集")
    
    print(f"\n步骤2: 创建依赖任务1的任务")
    print("-" * 40)
    task2_id = manager.register_task("数据清洗", dependencies=[task1_id])
    
    print(f"\n步骤3: 创建依赖多个任务的任务")
    print("-" * 40)
    task3_id = manager.register_task("数据验证", dependencies=[task1_id, task2_id])
    
    print(f"\n步骤4: 尝试创建无效依赖的任务（演示错误处理）")
    print("-" * 40)
    try:
        manager.register_task("无效任务", dependencies=["invalid-task-id"])
    except ValueError as e:
        print(f"✓ 正确捕获错误: {e}")
    
    print(f"\n步骤5: 执行任务流程")
    print("-" * 40)
    
    # 执行任务1
    print("\n5.1 执行数据收集任务:")
    manager.wait_for_dependencies(task1_id)
    manager.start_task(task1_id)
    time.sleep(1)  # 模拟执行时间
    manager.complete_task(task1_id, {"records": 10000, "source": "database"})
    
    # 执行任务2
    print("\n5.2 执行数据清洗任务:")
    manager.wait_for_dependencies(task2_id)
    manager.start_task(task2_id)
    time.sleep(1)  # 模拟执行时间
    manager.complete_task(task2_id, {"cleaned_records": 9500, "removed": 500})
    
    # 执行任务3
    print("\n5.3 执行数据验证任务:")
    manager.wait_for_dependencies(task3_id)
    manager.start_task(task3_id)
    time.sleep(1)  # 模拟执行时间
    manager.complete_task(task3_id, {"validation_passed": True, "errors": 0})
    
    print(f"\n步骤6: 查看最终任务状态")
    print("-" * 40)
    tasks = manager.get_all_tasks()
    for task in tasks:
        print(f"任务: {task.name}")
        print(f"  ID: {task.id}")
        print(f"  状态: {task.status.value}")
        print(f"  依赖: {task.dependencies}")
        if task.metadata:
            print(f"  元数据: {task.metadata}")
        print()
    
    print("=" * 50)
    print("演示完成！")
    print()
    print("关键改进:")
    print("✓ 依赖验证：只能使用存在的task_id作为依赖")
    print("✓ 状态显示：注册时显示依赖任务的当前状态")
    print("✓ 详细日志：所有操作都有相应的日志输出")
    print("✓ 错误处理：无效依赖会抛出明确的异常")
    print()
    print(f"任务状态文件: {task_file}")


if __name__ == "__main__":
    main()

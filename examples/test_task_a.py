#!/usr/bin/env python3
"""
测试任务A - 独立任务，不依赖其他任务
这个任务会先执行，为任务B提供依赖
"""

import time
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def main():
    # 使用临时文件存储任务状态
    task_file = "./test_tasks.json"

    # 创建任务管理器
    manager = TaskManager(task_file)

    # 注册任务A（无依赖）
    task_id = manager.register_task("数据下载")
    print(f"[任务A] 已注册任务 '数据下载'，ID: {task_id}")

    # 等待依赖任务完成（此任务无依赖，会立即返回）
    print("[任务A] 检查依赖...")
    manager.wait_for_dependencies(task_id)
    print("[任务A] 依赖检查完成，开始执行")

    # 开始执行任务
    success = manager.start_task(task_id)
    if not success:
        print("[任务A] 启动任务失败")
        return

    print("[任务A] 正在下载数据...")

    # 模拟任务执行（下载数据需要3秒）
    for i in range(3):
        time.sleep(1)
        print(f"[任务A] 下载进度: {(i + 1) * 33}%")

    # 标记任务完成
    success = manager.complete_task(task_id, metadata={"file_size": "100MB", "download_speed": "10MB/s"})
    if success:
        print("[任务A] 数据下载完成！")
    else:
        print("[任务A] 标记任务完成失败")


if __name__ == "__main__":
    main()

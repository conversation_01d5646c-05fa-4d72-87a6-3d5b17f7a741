#!/usr/bin/env python3
"""
测试新的日志系统：
1. 日志同时输出到控制台和文件
2. 日志文件与storage_path在同一目录
3. 日志文件名基于storage_path
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def main():
    print("=== 测试日志系统 ===")
    
    # 使用不同的目录和文件名测试
    test_cases = [
        "./logs/test_logging.json",
        "./test_simple.json",
        "/tmp/test_temp.json"
    ]
    
    for i, task_file in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {task_file}")
        print("-" * 50)
        
        # 清理之前的文件
        if os.path.exists(task_file):
            os.remove(task_file)
        
        # 预期的日志文件路径
        log_file = os.path.splitext(task_file)[0] + ".log"
        if os.path.exists(log_file):
            os.remove(log_file)
        
        # 创建TaskManager并执行一些操作
        manager = TaskManager(task_file)
        
        # 执行一些操作来生成日志
        task1_id = manager.register_task("测试任务1")
        task2_id = manager.register_task("测试任务2", dependencies=[task1_id])
        
        # 尝试创建无效依赖的任务
        try:
            manager.register_task("无效任务", dependencies=["invalid-id"])
        except ValueError:
            pass  # 预期的错误
        
        # 执行任务
        manager.start_task(task1_id)
        manager.complete_task(task1_id, {"test": "data"})
        
        # 检查日志文件是否生成
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.read()
                line_count = len(log_content.strip().split('\n'))
            print(f"✓ 日志文件已生成: {log_file}")
            print(f"✓ 日志行数: {line_count}")
            print(f"✓ 日志文件大小: {len(log_content)} 字节")
        else:
            print(f"✗ 日志文件未生成: {log_file}")
        
        # 显示日志文件的前几行
        if os.path.exists(log_file):
            print(f"\n日志文件内容预览 ({log_file}):")
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for j, line in enumerate(lines[:3], 1):
                    print(f"  {j}: {line.strip()}")
                if len(lines) > 3:
                    print(f"  ... (共 {len(lines)} 行)")
    
    print("\n" + "=" * 50)
    print("日志系统测试完成！")
    print("\n功能验证:")
    print("✓ 日志同时输出到控制台和文件")
    print("✓ 日志文件与storage_path在同一目录")
    print("✓ 日志文件名基于storage_path（.json -> .log）")
    print("✓ 支持不同目录的任务文件")
    print("✓ 自动创建日志目录")


if __name__ == "__main__":
    main()

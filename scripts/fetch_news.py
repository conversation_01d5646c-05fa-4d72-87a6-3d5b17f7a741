import json
import time

import feedparser
import pandas as pd
import requests
from bs4 import BeautifulSoup
from loguru import logger  # 新增: 导入 loguru 库
from sqlalchemy import Column, DateTime, Integer, String, Text, create_engine
from sqlalchemy.engine import URL
from sqlalchemy.orm import declarative_base, sessionmaker

# 新增: 使用 SQLAlchemy ORM 定义 NewsArticle 数据类
Base = declarative_base()


class NewsArticle(Base):
    __tablename__ = "news"
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String, nullable=False)
    source = Column(String, nullable=False)
    link = Column(String, nullable=False, unique=True)
    published_time = Column(DateTime(timezone=True), nullable=False)
    summary = Column(String)
    content = Column(Text)
    importance = Column(Integer, nullable=False, default=0)  # 新增: 添加 importance 字段


# 修改: 初始化 PostgreSQL 数据库连接和创建表
def init_db():
    engine = create_engine(
        url=URL.create(
            drivername="postgresql+psycopg2",
            username="postgres",
            password="believe419",
            host="host.docker.internal",
            # port="5432",
            # host="localhost",
            port=5432,
            database="quant",
        )
    )  # 修改为你的PostgreSQL连接字符串
    Base.metadata.create_all(engine)
    return engine


class NewsSource:
    def __init__(self, links: list[str] | None = None):
        self.seen_entries = set(links or [])

    def get_news_list(self) -> None | list[NewsArticle]: ...


def retry(max_retries=3):
    def decorator(func):
        def wrapper(*args, **kwargs):
            for _ in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Error occurred: {e}. Retrying...")
                    time.sleep(3)
            return None

        return wrapper

    return decorator


class ChinaNews(NewsSource):
    @retry(3)
    def get_news_list(self) -> None | list[NewsArticle]:
        feed = feedparser.parse("https://www.chinanews.com.cn/rss/finance.xml")
        feed.entries.sort(key=lambda entry: entry.published_parsed)
        ret = []
        for entry in feed.entries:
            if entry.link not in self.seen_entries:
                self.seen_entries.add(entry.link)
            else:
                continue
            published_time = (
                pd.Timestamp.fromtimestamp(time.mktime(entry.published_parsed))
                .tz_localize("UTC")
                .tz_convert("Asia/Shanghai")
            )
            article_content = self._fetch_article_content(entry.link)
            news_article = NewsArticle(
                title=entry.title,
                link=entry.link,
                source="chinanews",
                published_time=published_time.to_pydatetime(),
                summary=entry.summary,
                content=article_content,
            )
            ret.append(news_article)
        return ret

    @staticmethod
    @retry(3)
    def _fetch_article_content(url: str) -> None | str:
        response = requests.get(url)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, "html.parser")
        # 假设文章内容在 <div class="content"> 标签中，根据实际情况调整选择器
        content_div = soup.find("div", class_="left_zw")
        if content_div:
            return content_div.get_text(strip=True)
        else:
            return None


class EastMoneyNews(NewsSource):
    @retry(3)
    def get_news_list(self, num=50) -> list[NewsArticle]:
        response = requests.get(
            f"https://np-weblist.eastmoney.com/comm/web/getFastNewsList?client=web&biz=web_724&fastColumn=102&sortEnd=&pageSize={num}&req_trace=1740393257549&_=1740393257550&callback=jQuery18304803562702408326_1740393257416",
            timeout=1,
        )
        response.raise_for_status()
        json_data = json.loads(response.content.decode("utf-8")[41:-1])
        ret = []
        for entry in json_data["data"]["fastNewsList"]:
            link = f"https://finance.eastmoney.com/a/{entry['code']}.html"
            if link not in self.seen_entries:
                self.seen_entries.add(link)
            else:
                continue
            published_time = pd.Timestamp(entry["showTime"], tz="Asia/Shanghai")
            news_article = NewsArticle(
                title=entry["title"],
                link=link,
                source="eastmoney",
                published_time=published_time.to_pydatetime(),
                summary=entry["summary"],
                content=self._fetch_article_content(link),
                importance=1 if entry.get("titleColor", 0) > 0 else 0,
            )
            ret.append(news_article)
        if len(ret) >= num:
            ret.extend(self.get_news_list(200))
        return ret

    @staticmethod
    @retry(3)
    def _fetch_article_content(url: str) -> None | str:
        response = requests.get(url, timeout=1)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, "html.parser")
        content_div = soup.find("div", class_="zwinfos")
        content = content_div.get_text(strip=True) if content_div else None
        return content


def main():
    # 新增: 初始化数据库
    engine = init_db()
    Session = sessionmaker(bind=engine)
    with Session() as session:
        news_source_lst: list[NewsSource] = [
            ChinaNews([link[0] for link in session.query(NewsArticle.link).filter_by(source="chinanews").all()]),
            EastMoneyNews([link[0] for link in session.query(NewsArticle.link).filter_by(source="eastmoney").all()]),
        ]
    interval = 120

    while True:
        for news_source in news_source_lst:
            try:
                news_lst = news_source.get_news_list()
                if news_lst is None:
                    continue
                with Session() as session:
                    for news in news_lst:
                        # 新增: 检查数据库中是否已经存在相同的 link
                        session.flush()
                        existing_article = session.query(NewsArticle).filter_by(link=news.link).first()
                        if existing_article:
                            logger.info(f"Skipping already seen entry: {news.link}")
                            continue

                        logger.info(f"Published: {news.published_time}, Title: {news.title}, Link: {news.link}")

                        # 新增: 将新闻插入到数据库
                        session.add(news)
                        session.commit()

            except Exception as e:
                logger.exception(e)
                continue
            finally:
                # 修改: 使用 loguru 记录等待信息
                logger.info(f"Waiting for {interval} seconds before the next check...\n")

        time.sleep(interval)


if __name__ == "__main__":
    main()

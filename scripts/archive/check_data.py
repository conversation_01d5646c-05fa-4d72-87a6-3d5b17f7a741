import pathlib
import pandas as pd
import datetime


def check_tb_data(data_file1):
    data_file = pathlib.Path(data_file1)
    data = pd.read_csv(data_file, encoding="gbk", header=0)
    data["//时间"] = data["//时间"].apply(lambda x: datetime.datetime.strptime(x, "%Y/%m/%d %H:%M"))
    data2 = data.copy()
    data["last_time"] = data["//时间"].shift(-1)
    data["delta"] = data.apply(lambda x: (x["last_time"] - x["//时间"]).total_seconds() / 60, axis=1)
    data = data.loc[(data["delta"] > 1) & (data["delta"] < 15), :]
    add_data = []
    for _, row_value in data.iterrows():
        for i in range(1, int(row_value["delta"])):
            add_data.append({
                "//时间": (row_value["//时间"] + datetime.timedelta(minutes=i)),
                "开盘价": row_value["收盘价"],
                "最高价": row_value["收盘价"],
                "最低价": row_value["收盘价"],
                "收盘价": row_value["收盘价"],
                "成交量": 0,
                "持仓量": row_value["持仓量"],
            })
    data2 = pd.concat([data2, pd.DataFrame(add_data)], axis=0)
    data2.sort_values("//时间", ascending=True, inplace=True)
    data2["//时间"] = data2["//时间"].apply(lambda x: x.strftime("%Y/%m/%d %H:%M"))
    data2.to_csv(pathlib.Path(data_file.parent, "edit-" + data_file.name), encoding="gbk", header=True, index=False)


if __name__ == "__main__":
    check_tb_data(r"d:\Users\44297\Desktop\ni000.csv")

import sys
import pathlib
import time

import pandas as pd

sys.path.insert(0, str(pathlib.Path().absolute()))
from quant.protocols.enums import *
from quant.protocols.fields import *
from quant.api import *
from quant.utility import Symbol
import os
from quant.dbms.remote_data_handler import tq_formatter
from datetime import date
from contextlib import closing
from quant.dbms.sql_handler import SQLHandlerOfQuotation
import multiprocessing
import pickle


def download(targets):
    from tqsdk import TqApi, TqAuth
    from tqsdk import TqApi, TqAuth, TqSim
    from tqsdk.tools import DataDownloader

    api = TqApi(auth=TqAuth("lijin1201", "lijin1201"))

    download_tasks = {}
    for i in targets:
        download_tasks[i] = DataDownloader(
            api,
            symbol_list=Symbol.qs_to_tq(i),
            dur_sec=60,
            start_dt=date(2015, 1, 1),
            end_dt=date(2022, 12, 31),
            csv_file_name="./scripts/tmp/{}.csv".format(i),
        )
    with closing(api):
        while not all([v.is_finished() for v in download_tasks.values()]):
            api.wait_update()
            res = 0
            for k, v in download_tasks.items():
                res += v.get_progress()
            print("progress: %.2f %%" % (res / len(download_tasks)))


def fnc(target):
    filename = "./scripts/tmp/{}.csv".format(target)
    if pathlib.Path(filename).exists():
        data = pd.read_csv(filename, header=0)
        data.drop("datetime", axis=1, inplace=True)
        data.columns = data.columns.map(lambda x: x.replace(Symbol.qs_to_tq(target) + ".", ""))
        data.rename(columns={"datetime_nano": "datetime"}, inplace=True)
        data["id"] = data.index
        data = tq_formatter(target, data, BasicData.MINUTE)
        with open("./scripts/tmp/{}.pkl".format(target), "wb") as f:
            pickle.dump(data, f)
        os.remove(filename)


def ingest(target):
    filename = "./scripts/tmp/{}.pkl".format(target)
    if pathlib.Path(filename).exists():
        with open(filename, "rb") as f:
            data = pickle.load(f)
        sh = SQLHandlerOfQuotation(
            **{
                "drivername": "postgresql+psycopg2",
                "username": "postgres",
                "password": "believe419",
                "host": "localhost",
                "port": 5432,
                "database": "minute_quotation",
            })
        sh.to_sql(target, BasicData.MINUTE, data)
        os.remove(filename)


if __name__ == "__main__":
    excludes = [
        "B1601.DCE", "B1603.DCE", "BB1602.DCE", "BB1603.DCE", "BB1604.DCE", "BB1605.DCE", "BB1606.DCE", "BB1607.DCE", "BB1608.DCE", "BB1610.DCE",
        "BB1612.DCE", "FB1606.DCE", "FB1607.DCE", "FB1608.DCE", "FB1610.DCE", "FB1612.DCE", "FB1703.DCE", "FB1705.DCE", "FB1704.DCE", "FB1706.DCE",
        "FB1707.DCE", "FU1601.SHFE", "FU1610.SHFE", "FU1707.SHFE", "HC1602.SHFE", "JM1604.DCE", "JR1601.CZCE", "JR1603.CZCE", "JR1605.CZCE",
        "JR1611.CZCE", "JR1707.CZCE", "LR1603.CZCE", "LR1601.CZCE", "LR1703.CZCE", "LR1707.CZCE", "JR1811.CZCE", "JR1903.CZCE", "JR2003.CZCE",
        "LR1803.CZCE", "LR1807.CZCE", "PM1703.CZCE", "RI1603.CZCE", "PM1711.CZCE", "PM1803.CZCE", "PM1807.CZCE", "PM1811.CZCE", "PM1903.CZCE",
        "PM2001.CZCE", "PM2005.CZCE", "RS1608.CZCE", "RI1711.CZCE", "RI1805.CZCE", "RI1907.CZCE", "RI2001.CZCE", "RI2003.CZCE", "RI2007.CZCE",
        "SF1602.CZCE", "SF1603.CZCE", "SF1604.CZCE", "SF1606.CZCE", "SF1607.CZCE", "SF1608.CZCE", "RS1808.CZCE", "SM1602.CZCE", "SM1603.CZCE",
        "SM1604.CZCE", "SM1606.CZCE", "SM1607.CZCE", "SM1608.CZCE", "SM1610.CZCE", "SM1611.CZCE", "SM1703.CZCE", "SN1602.SHFE", "TC1602.CZCE",
        "V1603.DCE", "WR1601.SHFE", "WR1602.SHFE", "WR1603.SHFE", "WR1604.SHFE", "WR1605.SHFE", "WR1608.SHFE", "WR1612.SHFE", "WR1704.SHFE",
        "WR1706.SHFE", "WR1707.SHFE", "RI2107.CZCE", "CY2011.CZCE", "SM2312.CZCE", "JR2303.CZCE", "CY2103.CZCE", "ZC2311.CZCE", "JR2309.CZCE",
        "ZC2303.CZCE", "RI2203.CZCE", "CY2012.CZCE", "RI2011.CZCE", "RI2301.CZCE", "ZC2306.CZCE", "PM2011.CZCE", "RI2103.CZCE", "RI2109.CZCE",
        "PM2205.CZCE", "RI2307.CZCE", "JR2307.CZCE", "PM2301.CZCE", "LR2211.CZCE", "RI2303.CZCE", "WH2311.CZCE", "RI2209.CZCE", "LR2103.CZCE",
        "LR2303.CZCE", "RI2105.CZCE", "ZC2304.CZCE", "LR2101.CZCE", "ZC2305.CZCE", "PM2303.CZCE", "PM2203.CZCE", "LR2311.CZCE", "LR2301.CZCE",
        "RI2305.CZCE", "LR2309.CZCE", "WH2309.CZCE", "RI2211.CZCE", "LR2201.CZCE", "PM2211.CZCE", "RI2309.CZCE", "ZC2302.CZCE", "ZC2307.CZCE",
        "RI2311.CZCE", "LR2105.CZCE", "JR2209.CZCE", "JR2305.CZCE", "RI2205.CZCE", "WH2307.CZCE", "WH2305.CZCE", "LR2111.CZCE", "ZC2312.CZCE",
        "CY2102.CZCE", "ZC2308.CZCE", "PM2311.CZCE", "PM2305.CZCE", "LR2305.CZCE", "ZC2309.CZCE", "PM2309.CZCE", "LR2307.CZCE", "PM2307.CZCE",
        "LR2203.CZCE", "JR2311.CZCE", "CY2312.CZCE", "LR2109.CZCE"
    ]
    t = get_all_futures()
    t = list(zip(t["futures_code"], t["exchange"]))
    for i1, i2 in t:
        targets = [i for i in get_contract_list(i1, i2, start_m=201601) if not i in excludes] + [i1 + "8888." + i2, i1 + "9999." + i2]
        print(i1, i2, "%d个合约待处理" % len(targets))
        download(targets)
        with multiprocessing.Pool(15) as pool:
            pool.map(fnc, targets)
        t1 = time.time()
        with multiprocessing.Pool(20) as pool:
            pool.map(ingest, targets)
        print(time.time() - t1)

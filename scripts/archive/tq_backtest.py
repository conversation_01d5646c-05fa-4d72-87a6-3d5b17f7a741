from datetime import date

import pandas as pd
from tqsdk import BacktestFinished, Tq<PERSON><PERSON>, TqAuth, TqBacktest

from qnt_utils.label import Symbol

symbol_list = [
    "AG2401.SHFE",
    "AG2407.SHFE",
    "SS2311.SHFE",
    "AO2311.SHFE",
    "ZN2401.SHFE",
    "AU9999.SHFE",
    "BC2402.INE",
    "SC2509.INE",
    "AO2401.SHFE",
    "BC2407.INE",
    "ZN2402.SHFE",
    "SS2312.SHFE",
    "SC2506.INE",
    "AG2311.SHFE",
    "AL2406.SHFE",
    "ZN8888.SHFE",
    "SN9999.SHFE",
    "PB2405.SHFE",
    "PB2312.SHFE",
    "AO2407.SHFE",
    "SS2310.SHFE",
    "ZN2407.SHFE",
    "AL2312.SHFE",
    "ZN2312.SHFE",
    "SC9999.INE",
    "BC2403.INE",
    "PB2309.SHFE",
    "AL2403.SHFE",
    "NI2401.SHFE",
    "SC2310.INE",
    "SC2405.INE",
    "AG2309.SHFE",
    "AU2310.SHFE",
    "SS9999.SHFE",
    "ZN9999.SHFE",
    "AO2404.SHFE",
    "CU9999.SHFE",
    "SS8888.SHFE",
    "AG9999.SHFE",
    "AG2406.SHFE",
    "AO2405.SHFE",
    "PB9999.SHFE",
    "AO2402.SHFE",
    "SC2407.INE",
    "SC8888.INE",
    "AL8888.SHFE",
    "ZN2309.SHFE",
    "CU2403.SHFE",
    "CU2309.SHFE",
    "ZN2310.SHFE",
    "AU2404.SHFE",
    "CU2404.SHFE",
    "AO2312.SHFE",
]
api = TqApi(
    backtest=TqBacktest(start_dt=date(2023, 7, 1), end_dt=date(2023, 8, 10)),
    auth=TqAuth("y442974010f", "BeLiEvE4.19"),
)
data = {}
data1 = {}

try:
    for i in symbol_list:
        data[i] = api.get_kline_serial(
            symbol=str(Symbol.qs_to_tq(i)),
            duration_seconds=60,
            data_length=5,
        )
        data1[i] = pd.DataFrame()

    while True:
        api.wait_update()
        for k, v in data.items():
            if api.is_changing(v):
                data1[k] = pd.concat([data1[k], v.iloc[:-1].copy()], axis=0)
                data1[k].drop_duplicates(["id"], inplace=True)
except BacktestFinished as e:
    # 回测结束时会执行这里的代码
    api.close()
    for k, v in data1.items():
        v.reset_index(drop=True).to_csv("./tmp/{}.csv".format(k))

import sys
import pathlib

sys.path.insert(0, str(pathlib.Path().absolute()))

from tqdm import tqdm
from quant.api import get_price, set_sql
from quant.dbms.sql_handler import SQLHandlerOfQuotation
from multiprocessing.dummy import Pool
from itertools import chain

if __name__ == '__main__':
    targets = [
        'A8888.DCE', 'AG8888.SHFE', 'AL8888.SHFE', 'AP8888.CZCE', 'AU8888.SHFE', 'B8888.DCE', 'BB8888.DCE', 'BC8888.INE', 'BU8888.SHFE', 'C8888.DCE',
        'CF8888.CZCE', 'CJ8888.CZCE', 'CS8888.DCE', 'CU8888.SHFE', 'CY8888.CZCE', 'EB8888.DCE', 'EG8888.DCE', 'FB8888.DCE', 'FG8888.CZCE',
        'FU8888.SHFE', 'HC8888.SHFE', 'I8888.DCE', 'J8888.DCE', 'JD8888.DCE', 'JM8888.DCE', 'JR8888.CZCE', 'L8888.DCE', 'LH8888.DCE', 'LR8888.CZCE',
        'LU8888.INE', 'M8888.DCE', 'MA8888.CZCE', 'NI8888.SHFE', 'NR8888.INE', 'OI8888.CZCE', 'P8888.DCE', 'PB8888.SHFE', 'PF8888.CZCE', 'PG8888.DCE',
        'PK8888.CZCE', 'PM8888.CZCE', 'PP8888.DCE', 'RB8888.SHFE', 'RI8888.CZCE', 'RM8888.CZCE', 'RR8888.DCE', 'RS8888.CZCE', 'RU8888.SHFE',
        'SA8888.CZCE', 'SC8888.INE', 'SF8888.CZCE', 'SM8888.CZCE', 'SN8888.SHFE', 'SP8888.SHFE', 'SR8888.CZCE', 'SS8888.SHFE', 'TA8888.CZCE',
        'UR8888.CZCE', 'V8888.DCE', 'WH8888.CZCE', 'WR8888.SHFE', 'Y8888.DCE', 'ZC8888.CZCE', 'ZN8888.SHFE', "IF8888.CFFEX", "IC8888.CFFEX",
        "IH8888.CFFEX"
    ]
    set_sql(host="*************")

    def merge_params(fnc):

        def wrapper(params):
            fnc(*params)

        return wrapper

    @merge_params
    def fnc(symbol, freq):
        if freq == "MINUTE":
            data = get_price(symbol, "20220901 2100", "20221231 1500", "1m")
            sql_handler = SQLHandlerOfQuotation(username="postgres", password="believe419", host="localhost", database="minute_quotation")
            sql_handler.to_sql(symbol, "MINUTE", data)
        elif freq == "DAY":
            data = get_price(symbol, "20220101", "20221231", "1d")
            sql_handler = SQLHandlerOfQuotation(username="postgres", password="believe419", host="localhost", database="daily_quotation")
            sql_handler.to_sql(symbol, "DAY", data)

    p = Pool(10)
    list(tqdm(chain(p.imap(fnc, [(i, "DAY") for i in targets]), p.imap(fnc, [(i, "MINUTE") for i in targets]))))
    p.close()
    p.join()

import sys
import tqdm

sys.path.insert(0, r"E:\code_repositories\quant")
from quant.api import *
from quant.dbms.sql_handler import *

START_DT = "20170101"
END_DT = "20221130"

af = get_all_futures()
af = list(zip(af["futures_code"], af["exchange"]))

def fnc(s1):
    import re
    ss1 = re.findall("[0-9]+", s1)[0]
    return int(ss1[:2]) * 12 + int(ss1[2:])

def roll_yield(futures_code, exchange):
    res = []
    ifut = get_ifut(futures_code, exchange)
    ic = get_index_constituents(ifut, START_DT, END_DT)
    mfut = get_mfut(futures_code, exchange)
    mc = get_mcontracts(mfut, START_DT, END_DT)
    if ic.empty or mc.empty:
        print(futures_code, exchange, "无合约存在")
        return res
    for k, v in tqdm.tqdm(ic[ifut].items(), total=len(ic[ifut])):
        tmp_mc = mc[mfut].loc[k]
        tmp_cl = list(filter(lambda x: fnc(x)>=fnc(tmp_mc), v))    
        data = get_price(tmp_cl, k.replace(hour=15), k.replace(hour=15), "1d", ["close", "open_interest", "volume"])
        for k1, v1 in data.items():
            v1["symbol"] = k1
        data = pd.concat([v1 for _, v1 in data.items()], axis=0)
        if data.empty:
            print(tmp_mc, k, "无数据")
            continue
        data["tmp"] = data["symbol"].apply(fnc)
        data.set_index("symbol", drop=True, append=False, inplace=True)
        data1 = data.loc[(data.index==tmp_mc), :]
        data2 = data.loc[~(data.index==tmp_mc), :]
        if data1.empty:
            print(tmp_mc, k, "主力合约无数据")
            continue
        if data2.empty:
            print(tmp_mc, k, "其他合约无数据")
            continue
        i1 = data1["open_interest"].argmax()
        i2 = data2["open_interest"].argmax()
        res1 = (data2["close"].iloc[i2] / data1["close"].iloc[i1] * 100 - 100) / (data2["tmp"].iloc[i2] - data1["tmp"].iloc[i1])
        res.append({
            "symbol": tmp_mc,
            "datetime": k.timestamp() * 1e9,
            "value": res1
        })
    return res

if __name__ == "__main__":
    pp = []
    from multiprocessing import Pool
    p = Pool(16)
    for futures_code, exchange in af:
        pp.append(p.apply_async(func=roll_yield, args=(futures_code, exchange,)))
    p.close()
    p.join()
    res = []
    for i in pp:
        res.extend(i.get())
    pd.DataFrame(res).to_csv("roll_yield.csv")
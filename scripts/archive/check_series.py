def fnc(x):
    x = x.replace(" ", "")
    x = x.replace("\t", "")
    if ":=" in x:
        return x.split(":=")[0]
    elif ":" in x:
        return x.split(":")[0]
    return None


if __name__ == "__main__":
    lines = []
    while True:
        try:
            lines.append(input('请输入，输入完成后按ctrl+D结束输入：\n'))
        except:
            break
    res = []
    for i in lines:
        x = fnc(i)
        if not x is None:
            res.append(x)
    print(repr(res))

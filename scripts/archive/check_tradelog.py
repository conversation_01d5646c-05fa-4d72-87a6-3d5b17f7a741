from datetime import datetime
import pandas as pd
import pathlib
import datetime

NIGHT_CLOSE_TIME = {"沥青": (23, 0)}


def trans_time(trade_time, minutes, target):
    year = trade_time.year
    month = trade_time.month
    day = trade_time.day
    for i in NIGHT_CLOSE_TIME.keys():
        if i in target:
            night_close_time = NIGHT_CLOSE_TIME[i]
            break
    else:
        night_close_time = None
    if trade_time >= datetime.datetime(year, month, day, 9) and trade_time < datetime.datetime(year, month, day, 10, 15):
        if (trade_time + datetime.timedelta(minutes=minutes)) > datetime.datetime(year, month, day, 10, 15):
            rest = minutes - (datetime.datetime(year, month, day, 10, 15) - trade_time).total_seconds() / 60
            return trans_time(datetime.datetime(year, month, day, 10, 30), rest, target)
    elif trade_time >= datetime.datetime(year, month, day, 10, 30) and trade_time < datetime.datetime(year, month, day, 11, 30):
        if (trade_time + datetime.timedelta(minutes=minutes)) > datetime.datetime(year, month, day, 11, 30):
            rest = minutes - (datetime.datetime(year, month, day, 11, 30) - trade_time).total_seconds() / 60
            return trans_time(datetime.datetime(year, month, day, 13, 30), rest, target)
    elif trade_time >= datetime.datetime(year, month, day, 13, 30) and trade_time < datetime.datetime(year, month, day, 15):
        if (trade_time + datetime.timedelta(minutes=minutes)) > datetime.datetime(year, month, day, 15):
            return datetime.datetime(year, month, day, 15)
    elif trade_time > datetime.datetime(year, month, day, 15) or trade_time < datetime.datetime(year, month, day, 9):
        if night_close_time is None:
            print("无夜盘时间")
            return
        elif night_close_time[0] < 9:
            if trade_time.hour > 15:
                if (trade_time +
                        datetime.timedelta(minutes=minutes)) > datetime.datetime(year, month, day, *night_close_time) + datetime.timedelta(days=1):
                    rest = minutes - (datetime.datetime(year, month, day, *night_close_time) + datetime.timedelta(days=1) -
                                      trade_time).total_seconds() / 60
                    return trans_time(datetime.datetime(year, month, day, 9) + datetime.timedelta(days=1), rest, target)
            else:
                if (trade_time + datetime.timedelta(minutes=minutes)) > datetime.datetime(year, month, day, *night_close_time):
                    rest = minutes - (datetime.datetime(year, month, day, *night_close_time) - trade_time).total_seconds() / 60
                    return trans_time(datetime.datetime(year, month, day, 9), rest, target)
        elif night_close_time[0] > 15:
            if (trade_time + datetime.timedelta(minutes=minutes)) > datetime.datetime(year, month, day, *night_close_time):
                rest = minutes - (datetime.datetime(year, month, day, *night_close_time) - trade_time).total_seconds() / 60
                return trans_time(datetime.datetime(year, month, day, 9) + datetime.timedelta(days=1), rest, target)
    return trade_time + datetime.timedelta(minutes=minutes)


def check_tradelog_with_wh8(wh8_tl: str, tl: str, period: str) -> None:
    """与wh8的交易记录进行核对

    Args:
        wh8_tl (str): wh8交易记录的路径
        tl (str): quant交易记录的路径
        period (str): 分钟数
    """
    try:
        wh8_trade_log_path = pathlib.Path(wh8_tl)
        target = wh8_trade_log_path.name.split(" ")[0]
        wh8_trade_log = pd.read_csv(wh8_trade_log_path, header=0, index_col=0, encoding="gbk")
        wh8_trade_log = wh8_trade_log.iloc[:-1, :]
        wh8_trade_log["时间"] = wh8_trade_log["时间"].apply(func=lambda x: datetime.datetime.strptime(x, "%Y/%m/%d %H:%M:%S"))
        wh8_trade_log["时间1"] = wh8_trade_log["时间"].apply(func=trans_time, args=(
            int(period),
            target,
        ))
        wh8_trade_log.set_index("时间1", drop=True, append=False, inplace=True)
        wh8_trade_log.index.name = None
        print(wh8_trade_log)

        quant_trade_log = pd.read_csv(pathlib.Path(tl), header=0, index_col=0, encoding="utf8")
        quant_trade_log["time"] = quant_trade_log["time"].apply(func=lambda x: datetime.datetime.strptime(x, "%Y-%m-%d %H:%M:%S"))
        quant_trade_log = quant_trade_log.loc[~(quant_trade_log["status"] == "SCRAP"), :]
        quant_trade_log.set_index("time", drop=True, append=False, inplace=True)
        quant_trade_log = quant_trade_log.iloc[:-2, :]
        quant_trade_log.index.name = None
        print(quant_trade_log)

        res = pd.concat([wh8_trade_log, quant_trade_log], axis=1)
        res.drop(
            [
                "合约",
                "信号行",
                "信号消失成本",
                "Unnamed: 17",
                "timing_index",
                "order_id",
                "symbol",
                "status",
                "msg",
                "margin",
                "multiplier",
                "price",
            ],
            axis=1,
            inplace=True,
        )
        res.to_csv("res_compare.csv", encoding="gbk")
    except Exception as e:
        import traceback
        traceback.print_exc()
# import datetime
# import multiprocessing
# import pathlib
# import sys

# sys.path.insert(0, str(pathlib.Path().absolute()))

# import pandas as pd
# import tqdm
# from tqsdk import TqApi, TqAuth

# from quant.api import *
# from quant.dbms.remote_data_handler import tq_formatter
# from quant.dbms.sql_handler import SQLHandlerOfBasicInfo, SQLHandlerOfQuotation
# from quant.protocols.enums import *
# from quant.protocols.enums import Exchange
# from quant.protocols.fields import *
# from quant.utility import to_nstimestamp
# from quant.utils.symbol import Symbol


# # s = SQLHandlerOfBasicInfo(username="postgres", password="believe419")


# def step1():
#     api = TqApi(auth=TqAuth("lijin1201", "lijin1201"))
#     a = api.query_quotes(ins_class="FUTURE")
#     b = api.query_symbol_info(a)
#     # 新增合约导入数据库中
#     print("------------------------------------------------1 更新合约列表---------------------------------------------")
#     c = pd.DataFrame()
#     c["q001v_qnt004"] = b["instrument_id"].apply(Symbol.qs_from_tq)
#     c["futures_code"] = b["product_id"].apply(lambda x: x.upper())
#     c["exchange"] = b["exchange_id"]
#     c["q002s_qnt004"] = 4
#     c["q005d_qnt004"] = b["expire_datetime"].apply(lambda x: pd.Timestamp.fromtimestamp(x, tz="Asia/Shanghai").date())
#     c["q006i_qnt004"] = b["delivery_year"] * 100 + b["delivery_month"]
#     c["q007b_qnt004"] = b["expired"]
#     print(c)
#     api.close()
#     return
#     # print("原先共有{}个合约".format(len(s.get_contract_list(None, None))))
#     # s.contract_info_to_sql(*[i.to_dict() for _, i in c.iterrows()])
#     # print("当前共有{}个合约".format(len(s.get_contract_list(None, None))))

#     # 新增品种导入数据库中
#     print("\n\n------------------------------------------------2 更新品种列表---------------------------------------------")
#     af = get_all_futures()
#     af = list(zip(af["futures_code"], af["exchange"]))
#     bb = b[["price_tick", "volume_multiple", "exchange_id", "product_id"]].drop_duplicates(["exchange_id", "product_id"])
#     bb["product_id"] = bb["product_id"].apply(lambda x: x.upper())
#     bb.rename(
#         columns={
#             "volume_multiple": "contract_multiplier",
#             "price_tick": "minimum_price_change",
#             "exchange_id": "exchange",
#             "product_id": "futures_code",
#         },
#         inplace=True,
#     )
#     # print("原先共有{}个品种".format(len(get_all_futures())))
#     # s.futures_info_to_sql(*[i.to_dict() for _, i in bb.iterrows() if not (i["futures_code"], i["exchange"]) in af])
#     # print("当前共有{}个品种".format(len(get_all_futures())))

#     # 新品种的交易时间导入数据库中
#     print("\n\n------------------------------------------------3 更新品种的交易时间---------------------------------------------")
#     # tt = pd.DataFrame(s.execute("select * from trade_time"))
#     tt = list(zip(tt["futures_code"], tt["exchange"]))
#     bbb = b[["trading_time_day", "trading_time_night", "exchange_id", "product_id"]].drop_duplicates(["exchange_id", "product_id"])
#     bbb["product_id"] = bbb["product_id"].apply(lambda x: x.upper())
#     res = []
#     for i in [i for _, i in bbb.iterrows() if not (i["product_id"], i["exchange_id"]) in tt]:
#         for j in i["trading_time_day"]:
#             res.append(
#                 {
#                     "futures_code": i["product_id"],
#                     "exchange": i["exchange_id"],
#                     "begin": datetime.datetime.strptime(j[0], "%H:%M:%S").time(),
#                     "end": datetime.datetime.strptime(j[1], "%H:%M:%S").time(),
#                     "t_type": 1,
#                 }
#             )
#         if not i["trading_time_night"] is None:
#             for j in i["trading_time_night"]:
#                 tmp = j[1].split(":")
#                 if int(tmp[0]) >= 24:
#                     tmp[0] = str(int(tmp[0]) - 24)
#                 res.append(
#                     {
#                         "futures_code": i["product_id"],
#                         "exchange": i["exchange_id"],
#                         "begin": datetime.datetime.strptime(j[0], "%H:%M:%S").time(),
#                         "end": datetime.datetime.strptime(":".join(tmp), "%H:%M:%S").time(),
#                         "t_type": 2,
#                         "tz": "Asia/Shanghai",
#                     }
#                 )
#     print("trade_time插入{}条".format(len(res)))
#     # s.trade_time_to_sql(*res)

#     print("\n\n------------------------------------------------4 导入主力合约表---------------------------------------------")
#     t = get_all_futures().query('exchange in ["CFFEX","CZCE","DCE","SHFE","INE","GFEX"]')
#     t = list(zip(t["futures_code"], t["exchange"]))
#     conts = api.query_his_cont_quotes(symbol=[Symbol.qs_to_tq(get_mfut(*i)) for i in t], n=10000)
#     conts.set_index("date", drop=True, append=False, inplace=True)
#     res = []
#     for _, i in enumerate(conts.columns):
#         symbol = Symbol.qs_from_tq(i)
#         conts[i] = conts.apply(lambda x: "" if x[i] == "" else Symbol.qs_from_tq(x[i], x.name), axis=1)
#         data = conts[i].loc[conts[i] != ""]
#         for i1, i2 in data.items():
#             res.append({"timestamp": to_nstimestamp(i1.strftime("%Y-%m-%d")), "t_index": symbol, "symbol_list": ",".join([i2])})
#     # s.index_constituent_to_sql(*res)
#     # print(conts)

#     api.close()


# if __name__ == "__main__":
#     step1()

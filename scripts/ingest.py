# @author: <PERSON>
# @description: 把supermind的基础数据和tqsdk的行情数据导入到数据库中
import argparse
import datetime
import multiprocessing
import os
import pathlib
import random
import re
from typing import Mapping

import datetype
import numpy as np
import pandas as pd
from loguru import logger
from sqlalchemy import text
from tqdm import tqdm
from tqsdk import TqApi, TqAuth

from qnt_research.api import *
from qnt_research.dbms.external_data_handler import tq_formatter
from qnt_research.dbms.sql_handler import SQLHandlerOfBasicInfo, SQLHandlerOfQuant, SQLHandlerOrmOfFactor
from qnt_research.dbms.sql_handler.quotation import SQLHandlerOfQuotation
from qnt_utils.config import get_config
from qnt_utils.ctoolset import generate_datetime
from qnt_utils.enums import BasicData
from qnt_utils.label import Symbol
from qnt_utils.toolset import decrypt, to_nstimestamp

args = argparse.ArgumentParser()
args.add_argument("--all", "-a", action="store_true", default=True, help="是否同时导入非行情数据")
args.add_argument("--n_cores", "-n", type=int, default=10, help="下载行情时开启的进程数")
args.add_argument("--only_index", "-i", action="store_true", default=False, help="是否只下载指数行情")
args = args.parse_args()

pd.set_option("display.max_rows", None)
logger.add("tq_ingest.log", level="WARNING")
home = os.getenv("HOME")

gl_sql_config = {
    "username": decrypt(get_config()["database"]["username"]),
    "password": decrypt(get_config()["database"]["password"]),
    "host": get_config()["database"]["host"],
    "port": get_config()["database"]["port"],
}
EXCHANGE_DICT = {
    "212020003": "CZCE",
    "212020004": "DCE",
    "212020001": "CFFEX",
    "212020008": "SHFE",
    "212020019": "GFEX",
    "212001": "SSE",
    "212100": "SZSE",
    "212300": "BSE",
}
try:
    SQL_HANDLERS = {
        "basic_info": SQLHandlerOfBasicInfo(**gl_sql_config),
        "quant": SQLHandlerOfQuant(**gl_sql_config),
        "factor": SQLHandlerOrmOfFactor(**gl_sql_config),
        "quotation": SQLHandlerOfQuotation(**gl_sql_config),
    }
    gl_qcode_series = SQL_HANDLERS["basic_info"].get_qcode_series()
except Exception:
    logger.critical("数据库连接失败")

gl_pg_data_end: Mapping[BasicData, Mapping[str, datetype.NaiveDateTime]] = {
    k: pd.read_sql(
        f"""select 
b.q001v_qnt004 as symbol,
to_timestamp(max(a.timestamp)/1000000000) AT TIME ZONE 'Asia/Shanghai' as datetime
from {v} as a, qnt004 as b 
where a.qcode=b.qcode 
group by b.q001v_qnt004;""",
        con=SQL_HANDLERS["quotation"].connect(),
        index_col="symbol",
    )["datetime"].to_dict()
    for k, v in {
        BasicData.DAY: "chn_futures_daily_quotation",
        BasicData.MINUTE: "chn_futures_minute_quotation",
    }.items()
}


def split_list(input_list, n):
    # 计算每个子列表的大小
    k, m = divmod(len(input_list), n)
    # 使用列表推导式生成 N 个子列表
    ret = [input_list[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n)]
    return [i for i in ret if len(i) > 0]


def quotation_download_process(q1, q2):
    global gl_pg_data_end

    api = TqApi(auth=TqAuth("lijin1108", "lijin1108"))
    sh = SQLHandlerOfQuotation(**{"drivername": "postgresql+psycopg2", **gl_sql_config})
    count = 0
    while q1.qsize() > 0:
        symbol = q1.get()
        if count > 0 and count % 10 == 0:
            api.close()
            api = TqApi(auth=TqAuth("lijin1108", "lijin1108"))
            count = 0
        try:
            for basic_data in [BasicData.DAY, BasicData.MINUTE]:
                try:
                    start_dt = gl_pg_data_end[basic_data].get(symbol.split(".")[0], datetime.datetime.min)

                    # # 专业版更新
                    # data = api.get_kline_data_series(
                    #     symbol=Symbol.qs_to_tq(symbol),
                    #     duration_seconds=60 if basic_data == BasicData.MINUTE else 86400,
                    #     start_dt=start_dt - (pd.Timedelta(days=10) if basic_data == BasicData.MINUTE else pd.Timedelta(days=200)),
                    #     end_dt=datetime.datetime.now(),
                    # )

                    # 免费版更新
                    if (tmp_symbol := Symbol.qs_to_tq(symbol)) is None:
                        continue
                    data = api.get_kline_serial(
                        symbol=tmp_symbol,
                        duration_seconds=60 if basic_data == BasicData.MINUTE else 86400,
                        data_length=10000 if basic_data == BasicData.MINUTE else 100,
                    )

                    # 读取本地数据
                    # data = pd.read_csv(
                    #     "./tmp/{}.csv".format(symbol), header=0, index_col=0
                    # )

                    data = tq_formatter(symbol, data, basic_data)
                    sh.to_sql(symbol, basic_data, data)
                    data_st = generate_datetime(data["date"].iloc[0], data["time"].iloc[0])

                    if start_dt >= data_st:
                        logger.success(
                            "{:<10} {:<13}更新成功. ".format(basic_data.name, symbol)
                            + "历史数据截止{}, 新增数据{}————{}.  剩余{}".format(
                                start_dt,
                                data_st,
                                generate_datetime(data["date"].iloc[-1], data["time"].iloc[-1]),
                                q1.qsize(),
                            ),  # type: ignore
                        )
                    else:
                        logger.warning(
                            "{:<10} {:<13}更新可能不完整".format(basic_data.name, symbol)
                            + "历史数据截止{}, 新增数据{}————{}. ".format(
                                start_dt, data_st, generate_datetime(data["date"].iloc[-1], data["time"].iloc[-1])
                            )
                        )
                    count += 1
                except Exception:
                    logger.error("{}  {}更新失败.   剩余{}".format(basic_data, symbol, q1.qsize()))
                    api.close()
                    q2.put(symbol)
                    api = TqApi(auth=TqAuth("lijin1108", "lijin1108"))
                    count = 0
        except Exception:
            break
    api.close()


def tq_quotation_download():
    print(
        "\n\n------------------------------------------------下载行情数据---------------------------------------------"
    )
    t = get_all_futures()
    t = list(zip(t["futures_code"], t["exchange"]))
    if args.only_index:
        targets = []
    else:
        targets = get_contract_list(
            start_m=int((datetime.datetime.now() - datetime.timedelta(days=90)).strftime("%Y%m"))
        )
    n_cores: int = args.n_cores
    targets.extend(
        [i1 + "8888." + i2 for i1, i2 in t if not "-1" in i1] + [i1 + "9999." + i2 for i1, i2 in t if not "-1" in i1]
    )

    targets = random.sample(targets, len(targets))
    q1 = multiprocessing.Queue()
    q2 = multiprocessing.Queue()
    for i in targets:
        q1.put(i)
    if n_cores > 1:
        p = []
        for i in range(n_cores):
            tmp = multiprocessing.Process(target=quotation_download_process, args=(q1, q2))
            tmp.start()
            p.append(tmp)
        for i in p:
            i.join()
    else:
        quotation_download_process(q1, q2)
    res = set()
    while q2.qsize() > 0:
        res.add(q2.get())
    logger.error(list(res))


def get_exchange(df):
    return (
        "INE"
        if any([i in df["futures_code"] for i in ["SC", "BC", "LU", "NR", "EC"]])
        else EXCHANGE_DICT.get(str(df["exchange"]), str(df["exchange"]))
    )


def ingest_futures_info():
    # 获取本地库里的所有品种信息，并以期货品种代码+交易所代码作为index
    if not (file_path := pathlib.Path(home, "workspace/supermind_data/all_futures.csv")).exists():
        return
    with SQL_HANDLERS["basic_info"].connect() as conn:
        sql = """
        SELECT 
        A.*,
        C.*,
        B.EXCHANGE
        FROM FUTURES_INFO A 
        JOIN FUTURES_DYNAMIC_INFO C ON A.QCODE=C.QCODE
        JOIN EXCHANGE_INFO B ON A.EXCHANGE_QCODE=B.QCODE;
        """
        res = pd.read_sql(sql=text(sql), con=conn).drop(["ctime", "mtime", "qcode", "exchange_qcode"], axis=1)
    res.sort_values(["effective_date"], inplace=True, ascending=True)
    res.drop_duplicates(["futures_code", "exchange"], keep="last", inplace=True)
    res.set_index(["futures_code", "exchange"], drop=True, append=False, inplace=True)
    res.drop(["effective_date"], axis=1, inplace=True)

    # 获取最新的期货品种信息，并以期货品种代码+交易所代码作为index
    raw_data = pd.read_csv(file_path, index_col=0)
    raw_data["exchange"] = raw_data.apply(get_exchange, axis=1)
    raw_data.drop_duplicates(keep="first", inplace=True)
    raw_data["listing_date"] = raw_data["listing_date"].apply(
        lambda x: datetime.datetime.strptime(x, "%Y-%m-%d").date() if not pd.isna(x) else x
    )
    raw_data["delisting_date"] = raw_data["delisting_date"].apply(
        lambda x: datetime.datetime.strptime(x, "%Y-%m-%d").date() if not pd.isna(x) else x
    )
    df = raw_data.set_index(["futures_code", "exchange"], drop=True, append=False)

    # 对已有期货品种信息进行对比，不修改原有信息
    old = df.loc[list(res.index), list(res.columns)].compare(res, result_names=("new", "old"))
    if not old.empty:
        print("\n", old)
        if not input("确认以上期货品种合约细则是否无误, 继续(注: 程序不修改原期货品种)？[y/n]") == "y":
            raise Exception("原期货品种合约细则与最新存在差异，需要手动修改")
    else:
        print("\n", "原期货品种合约细则与最新完全相同")

    # 过滤出新品种信息
    new = df.drop(list(res.index), axis=0)
    if not new.empty:
        print("\n", new)
        if not input("确认以上新期货品种合约细则是否无误, 继续？[y/n]") == "y":
            raise
    else:
        print("\n", "无新期货品种")

    # 插入更新信息
    raw_data.set_index(["futures_code", "exchange"], drop=False, inplace=True)
    raw_data = raw_data.loc[list(new.index), :].reset_index(drop=True)
    if not raw_data.empty:
        raw_data["exchange_qcode"] = raw_data.apply(lambda x: gl_qcode_series["exchange"].loc[(x["exchange"])], axis=1)
        print("\n", "待插入数据如下:\n", raw_data)
        if not input("是否插入？[y/n]") == "y":
            raise
        # TODO 新合约要插入8888和9999，目前手工处理
        SQL_HANDLERS["basic_info"].to_sql("futures_info", raw_data.drop(["exchange"], axis=1))

        # 如果有新品种信息插入，需要更新
        global gl_qcode_series
        gl_qcode_series = SQL_HANDLERS["basic_info"].get_qcode_series()
    return raw_data


def ingest_contract_info():
    """更新contract_info"""
    if not (file_path := pathlib.Path(home, "workspace/supermind_data/all_contracts.csv")).exists():
        return
    # 获取本地库里的所有合约，以合约代码+交易所代码作为index
    with SQL_HANDLERS["basic_info"].connect() as conn:
        sql = """
        SELECT 
        C.EXCHANGE,
        B.FUTURES_CODE,
        A.Q001V_QNT004,--证券代码
        A.Q002S_QNT004,--证券类型, 同FinancialAsset.value
        A.Q003D_QNT004,--上市日期
        A.Q004D_QNT004,--退市日期
        A.Q005D_QNT004,--最后交易日
        A.Q006I_QNT004,--交割月份
        A.Q007B_QNT004--是否退市
        FROM QNT004 A 
        JOIN FUTURES_INFO B ON A.PARENT_QCODE=B.QCODE
        JOIN EXCHANGE_INFO C ON A.EXCHANGE_QCODE=C.QCODE;
        """
        res = pd.read_sql(sql=text(sql), con=conn)
    res = res.set_index(res.apply(lambda x: "{}.{}".format(x["q001v_qnt004"], x["exchange"]), axis=1))

    # 获取最新的合约信息，以合约代码+交易所代码作为index
    raw_data = pd.read_csv(file_path, index_col=0)
    # raw_data = raw_data.query("futures_code!='AO'")
    raw_data["listing_date"] = raw_data["listing_date"].apply(
        lambda x: datetime.datetime.strptime(x, "%Y-%m-%d").date() if not pd.isna(x) else x
    )
    raw_data["delisting_date"] = raw_data["delisting_date"].apply(
        lambda x: datetime.datetime.strptime(x, "%Y-%m-%d").date() if not pd.isna(x) else x
    )
    raw_data["last_trade_day"] = raw_data["last_trade_day"].apply(
        lambda x: datetime.datetime.strptime(x, "%Y-%m-%d").date() if not pd.isna(x) else x
    )
    raw_data["q002s_qnt004"] = raw_data["contract_code"].apply(lambda x: 1 if ("8888" in x or "9999" in x) else 4)
    raw_data.rename(
        columns={
            "contract_code": "q001v_qnt004",
            "listing_date": "q003d_qnt004",
            "delisting_date": "q004d_qnt004",
            "last_trade_day": "q005d_qnt004",
            "delivery_month": "q006i_qnt004",
            "is_listed": "q007b_qnt004",
        },
        inplace=True,
    )
    raw_data["exchange"] = raw_data.apply(get_exchange, axis=1)
    raw_data.drop_duplicates(keep="first", inplace=True)
    # 存在新上新的品种时，可能会出现合约已有，但是品种还没有的情况，所以合约暂时先过滤掉
    raw_data = raw_data.loc[
        raw_data.apply(lambda x: (x["futures_code"], x["exchange"]) in gl_qcode_series["futures"].index, axis=1)
    ]
    df = raw_data.set_index(raw_data.apply(lambda x: "{}.{}".format(x["q001v_qnt004"], x["exchange"]), axis=1))

    # 对已有合约信息进行对比
    it_index = list(res.index.intersection(df.index))
    it_columns = list(res.columns.intersection(df.columns))
    old = df.loc[it_index, it_columns].compare(res.loc[it_index, it_columns], result_names=("new", "old"))
    if not old.empty:
        print("\n", pd.concat([df.loc[old.index, "q001v_qnt004"], old], axis=1))
        if not input("确认以上旧合约是否无误, 继续？[y/n]") == "y":
            raise
    else:
        print("\n", "无旧合约修改")

    # 过滤出新合约信息
    new = df.drop(it_index, axis=0)
    if not new.empty:
        print("\n", new)
        if not input("确认以上新合约是否无误, 继续？[y/n]") == "y":
            raise
    else:
        print("\n", "无新合约")

    raw_data.set_index(raw_data.apply(lambda x: "{}.{}".format(x["q001v_qnt004"], x["exchange"]), axis=1), inplace=True)
    raw_data = raw_data.loc[list(new.index) + list(old.index), :].reset_index(drop=True)

    if not raw_data.empty:
        raw_data["parent_qcode"] = raw_data.apply(
            lambda x: gl_qcode_series["futures"].loc[(x["futures_code"], x["exchange"])], axis=1
        )
        raw_data["exchange_qcode"] = raw_data.apply(lambda x: gl_qcode_series["exchange"].loc[x["exchange"]], axis=1)
        raw_data.drop(["futures_code", "exchange"], axis=1, inplace=True)
        print("\n", "待插入数据如下:\n", raw_data)
        if not input("是否插入？[y/n]") == "y":
            raise
        SQL_HANDLERS["basic_info"].to_sql("qnt004", raw_data)
    return raw_data


def ingest_calendar():
    """更新交易日历"""
    if not (file_path := pathlib.Path(home, "workspace/supermind_data/calendar.csv")).exists():
        return
    data = pd.read_csv(file_path, index_col=0)
    data.rename(
        columns={
            "日期": "q001d_qnt005",
            "交易所": "exchange_qcode",
            "是否开市": "q002b_qnt005",
            "非周末闭市原因": "q003v_qnt005",
            "特殊原因说明": "q004v_qnt005",
        },
        inplace=True,
    )
    data.drop_duplicates(keep="first", inplace=True)
    data["exchange_qcode"] = data["exchange_qcode"].apply(lambda x: gl_qcode_series["exchange"].loc[x])
    data["timestamp"] = data["q001d_qnt005"].apply(lambda x: pd.Timestamp(x, tz="Asia/Shanghai").timestamp() * 1e9)
    SQL_HANDLERS["quant"].to_sql("qnt005", data)


def ingest_workday():
    """更新工作日表"""
    if not (file_path := pathlib.Path(home, "workspace/supermind_data/workday.csv")).exists():
        return
    data = pd.read_csv(file_path, index_col=0)
    data.rename(
        columns={
            "日期": "q001d_qnt007",
            "国家": "q002t_qnt007",
            "是否工作日": "q003b_qnt007",
            "节假日公休说明": "q004t_qnt007",
            "其他公休日说明": "q005t_qnt007",
        },
        inplace=True,
    )
    data.drop_duplicates(keep="first", inplace=True)
    SQL_HANDLERS["quant"].to_sql("qnt007", data)


def ingest_factor():
    if not (file_path := pathlib.Path(home, "workspace/supermind_data/factor.h5")).exists():
        return
    # 获取所有的qsymbol和对应的qcode
    symbol_qcodes = gl_qcode_series["symbol"]
    symbol_qcodes.index = pd.Index(map(lambda x: ".".join(x), symbol_qcodes.index))

    # 获取所有期货品种的加权指数合约和对应的qcode
    t = get_all_futures()[["futures_code", "exchange"]]
    t["ifut"] = t.apply(lambda x: get_ifut(x["futures_code"], x["exchange"]), axis=1)
    t["symbol_qcode"] = t["ifut"].apply(lambda x: symbol_qcodes[x])
    t.set_index("futures_code", inplace=True, drop=True, append=False)
    t = t["symbol_qcode"]

    def process_data(df: pd.DataFrame, table: str, name: str):
        df.index = pd.Index(map(lambda x: to_nstimestamp(f"{x} 1500"), df.index), name="timestamp")
        df.columns = pd.Index(map(lambda x: t[x], df.columns), name="symbol_qcode")
        res = pd.DataFrame(df.stack().rename("value")).reset_index()
        res["name"] = name
        SQL_HANDLERS["factor"].upsert(table, res)

    with pd.HDFStore(file_path, "r") as factor:
        for k in factor.keys():
            table, name = [i for i in k.split("/") if not i == ""]
            process_data(factor[k], table, name)


def ingest_tq_mfut():
    def get_qsymbol(x):
        try:
            return Symbol.qs_from_tq(x["qcode1_qnt006"], x["q001d_qnt006"], True)
        except:
            return None

    api = TqApi(auth=TqAuth("y442974010f", "BeLiEvE4.19"))
    qsymbol_qcode_map = get_all_qcodes()["symbol"].copy()
    qsymbol_qcode_map.index = qsymbol_qcode_map.index.map(lambda x: "{}.{}".format(*x))
    qsymbol_qcode_map = qsymbol_qcode_map.to_dict()

    all_futures = (
        get_all_futures()
        .apply(
            lambda x: "{}.{}".format(
                re.sub("[a-zA-Z]+", lambda match: match.group() + "9999", x["futures_code"]),
                x["exchange"],
            ),
            axis=1,
        )
        .tolist()
    )

    sh = SQL_HANDLERS["basic_info"]
    with sh._engine.connect() as conn:
        conn.execute(text("DELETE FROM qnt006 WHERE q005s_qnt006=2;"))
        conn.commit()

    all_tq_mfut_symbols = [tmp1 for i in all_futures if (tmp1 := Symbol.qs_to_tq(i)) is not None]
    all_tq_mfut_symbols_sublist = split_list(all_tq_mfut_symbols, 10)

    for tq_mfut_symbol in tqdm(all_tq_mfut_symbols, desc="主力合约"):
        try:
            data = api.query_his_cont_quotes(symbol=tq_mfut_symbol, n=12000)
            data.columns = ["q001d_qnt006", "qcode1_qnt006"]

            # 1、先把所有的tqsymbol转成qsymbol
            data = data.loc[data["qcode1_qnt006"] != ""]
            data.loc[:, "qcode1_qnt006"] = data.apply(get_qsymbol, axis=1)

            # 2、把主力合约有变动的日期筛选出来
            data = data.loc[data["qcode1_qnt006"] != data["qcode1_qnt006"].shift(1)].dropna()

            data = data.reset_index(drop=True)
            # 指数合约列
            data["qcode2_qnt006"] = data.apply(
                lambda x: Symbol.qs_from_tq(tq_mfut_symbol, x["q001d_qnt006"], True), axis=1
            )
            # 补上剔除日期
            data["q004d_qnt006"] = data["q001d_qnt006"].shift(-1)
            # 快期标准
            data["q005s_qnt006"] = 2
            # 把合约代码转成qcode
            data["qcode1_qnt006"] = data["qcode1_qnt006"].apply(lambda x: qsymbol_qcode_map.get(x, ""))
            # 把指数合约代码转成qcode
            data["qcode2_qnt006"] = data["qcode2_qnt006"].apply(lambda x: qsymbol_qcode_map[x])
            data = data.loc[data["qcode1_qnt006"] != ""]
            if not (data["q001d_qnt006"].shift(-1).iloc[:-1] == data["q004d_qnt006"].iloc[:-1]).all:
                raise Exception(f"{tq_mfut_symbol}日期不连续")
            sh.to_sql("qnt006", data)
        except Exception as e:
            print(e)
            print(tq_mfut_symbol)
    api.close()


def ingest_fq_factor():
    sh = SQL_HANDLERS["basic_info"]
    with sh._engine.connect() as conn:
        conn.execute(text("DELETE FROM qnt008 WHERE q002s_qnt008=2;"))
        conn.commit()

    st = "20170101"
    ed = pd.Timestamp.now("Asia/Shanghai").strftime("%Y%m%d")
    all_futures = get_all_futures()
    all_futures = list(zip(all_futures["futures_code"], all_futures["exchange"]))
    for futures_code, exchange in tqdm(all_futures, desc="计算复权因子"):
        mfut = get_mfut(futures_code, exchange)

        # 找出需要进行复权的时点
        a = get_fut_mcontract(exchange, futures_code, st, ed, 2)
        b = pd.concat([a.rename("old"), a.shift(-1).rename("new")], axis=1)
        b.index = b.index.map(lambda x: x.replace(hour=15))
        c = b.loc[b["old"] != b["new"]].dropna(axis=0, how="any")

        # 计算复权因子
        b["factor"] = np.nan
        for i, j in c.iterrows():
            d1 = get_last_price(j["old"], i.strftime("%Y%m%d %H%M"))
            d2 = get_last_price(j["new"], i.strftime("%Y%m%d %H%M"))
            if np.isnan(d1) or np.isnan(d2):
                continue
            if d1 == 0 or d2 == 0:
                continue
            b.loc[i, "factor"] = d1 / d2
        factor = b["factor"].dropna()
        factor.index = factor.index.map(to_nstimestamp)

        factor_ = pd.DataFrame()
        factor_["timestamp"] = factor.index
        factor_["qcode_qnt008"] = get_qcode("symbol", mfut)
        factor_["q001n_qnt008"] = factor.values
        factor_["q002s_qnt008"] = 2
        factor_["q003s_qnt008"] = 2

        sh.to_sql("qnt008", factor_)


def ingest():
    ingest_futures_info()
    logger.info("期货品种信息更新完成")
    ingest_contract_info()
    logger.info("合约信息更新完成")
    ingest_calendar()
    logger.info("日历信息更新完成")
    ingest_factor()
    logger.info("因子信息更新完成")
    ingest_workday()
    logger.info("工作日信息更新完成")
    ingest_tq_mfut()
    logger.info("主力合约信息更新完成")


if __name__ == "__main__":
    if args.all:
        ingest()
    tq_quotation_download()
    logger.info("行情数据")
    if args.all:
        ingest_fq_factor()
        logger.info("复权因子计算完成")

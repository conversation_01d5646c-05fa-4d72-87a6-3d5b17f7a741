## Expression

计算系统中所有元素的基类，拥有一些属性来标注元素的特性：

**is_feature**：标注该元素的值类型。`True` 为 Feature，`False` 为 Constant。
**period**：标注该元素影响的时许周期范围，无时序性默认为 `(0, 1)`。
**units**：标注该元素的组成部分，用于拆解公式。

Expression 可以分为两大种类：

---

### Operand 操作数

操作数分为三大种类：

#### Feature 特征

值域:

$$
  x \in \mathbb{R}
$$

示例:

$$
  \text{e.g.}\quad \$open,\ \$close,\ \$high,\ \$low
$$

指开高低收等动态变化的数据，是决定因子的主要因素。

#### Constant 常数

值域:

$$
  x \in \mathbb{R}
$$

示例:

$$
  \text{e.g.}\quad 1.5,\ 0.5,\ 0.01,\ -2.1,\ -5.2
$$

特指恒定不变的数值，作为系数或者与特征共同出现在算符中。

#### Bar 周期

值域:

$$
  x \in \mathbb{Z} \setminus \{0\}
$$

示例:

$$
  \text{e.g.}\quad 2,\ 3,\ 5,\ 10,\ 20,\ 30
$$

仅用于时序类型算符的操作数，表达周期的长度或窗口的长度。正数代表向过去延伸，负数代表向未来延伸。

---

### Operator 算符

存在以下已定义算符类型：

---
#### UnaryOperator 一元算符

数学形式:

$$
  y = U(x)
$$

定义域:

$$
  x \in \{\text{Feature}\}
$$


示例:

$$
  \text{e.g.}\quad \text{Abs},\ \text{Neg},\ \text{Log},\ \text{Exp}
$$

除去常见的数学一元算符，还包含在标的截面进行运算的算符，如 $\text{UnaryMean}(x)$ 代表计算股票截面的均值。

---
#### BinaryOperator 二元算符

数学形式:

$$
  z = B(x, y)
$$

定义域:

$$
  x, y \in \{\text{Feature},\ \text{Constant}\},\quad \text{Feature} \in \{x, y\}
$$

示例:

$$
  \text{e.g.}\quad \text{Add},\ \text{Sub},\ \text{Mul},\ \text{Div}
$$

二元算符要求在 $x, y$ 中至少有一个 Feature 保证计算的有效性。

---
#### TernaryOperator 三元算符

数学形式:

$$
  z = T(c, x, y) = 
  \begin{cases} 
  x & \text{if}\quad t(c) \\ 
  y & \text{otherwise}
  \end{cases}
$$

定义域:

$$
  c \in \{\text{Feature}\},\quad x, y \in \{\text{Feature},\ \text{Constant}\},\quad \text{Feature} \in \{x, y\}
$$

示例:

$$
  \text{e.g.}\quad \text{PositiveCond},\ \text{NegativeCond},\ \text{FiniteCond}
$$

三元算符实际上是计算中的三元操作，表示在 $c$ 满足 $t$ 条件时返回 $x$，否则返回 $y$。例如 $\text{PositiveCond}$ 代表当 $c > 0$ 时返回 $x$。

---
#### RollingOperator 时序窗口算符

数学形式:

$$
  y = R(x, b)
$$

定义域:

$$
  x \in \{\text{Feature}\},\quad b \in \{\text{Bar}\}
$$

示例:

$$
  \text{e.g.}\quad \text{RollingMean},\ \text{RollingMax},\ \text{RollingSkew}
$$

时序窗口算符用于计算窗口值，窗口长度为 $b$。注意：当前元素一定在窗口中。例如 $\text{RollingMean}(x, 2)$ 代表计算今日和昨日 $x$ 的均值，因此 $b=1$ 时无实际意义。

---
#### ShiftingOperator 时序平移算符

数学形式:

$$
  y = S(x, b)
$$

定义域:

$$
  x \in \{\text{Feature}\},\quad b \in \{\text{Bar}\}
$$

示例:

$$
  \text{e.g.}\quad \text{Ref},\ \text{Delta}
$$

时序平移算符用于计算时序平移值，平移长度为 $b$。例如 $\text{Delta}(x, 1)$ 代表计算今日与昨日 $x$ 的差值。

---
#### PairRollingOperator 二元时序窗口算符

数学形式:

$$
  z = P(x, y, b)
$$

定义域:

$$
  x, y \in \{\text{Feature}\},\quad b \in \{\text{Bar}\}
$$

示例:

$$
  \text{e.g.}\quad \text{PairSlope},\ \text{Cov},\ \text{Corr}
$$

用于计算两个特征的滚动统计量，常用于衡量两个分布之间的关联性。

---
#### CompositeOperator 组合算符

数学形式:

$$
  y = C(x_1, x_2, \dots, x_n)
$$

定义域:

$$
  x_i \in \{\text{Feature},\ \text{Constant},\ \text{Bar}\}
$$

示例:

$$
  \text{e.g.}\quad \text{MACD},\ \text{RSV},\ \text{SMA}
$$

组合算符用于定义操作数不在上述分类中的复杂算符。例如：

$$
\text{SMA}(x_i, \alpha, \beta) = \frac{(\beta \alpha) \cdot \text{SMA}(x_{i-1}, \alpha, \beta) + \alpha \cdot x_i}{\beta}
$$

$$
\text{RSV}(b) = \frac{\$close \text{RollingMin}(\$low, b)}{\text{RollingHigh}(\$high, b) \text{RollingMin}(\$low, b)}
$$
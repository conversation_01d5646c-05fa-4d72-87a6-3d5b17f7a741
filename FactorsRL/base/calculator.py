from typing import List, Iterable, Generator

import torch

from base.data import Data
from base.expression import Expression, UnaryZScoreNorm, Corr
from utils.backtest import Report, BackTester


class Calculator:
    def __init__(self, data: Data, target: Expression, dim: int = -2, device: str = "cpu"):
        self.data = data
        self.dim = dim
        self.target = target
        self.value_cache = {}
        self.device = device

    def resample_indexes(self):
        self.data.resample()
        self.clear_cache([])

    def clear_cache(self, pool: List[Expression]):
        self.value_cache = {k: v for k, v in self.value_cache.items() if k in pool}

    def refresh_cache(self, expr_pool: List[Expression]):
        expr_pool += [self.target]
        self.clear_cache(expr_pool)
        for expr in expr_pool:
            if expr in self.value_cache:
                continue
            self.value_cache[expr] = [value.to(self.device) for value in self.cal_value(expr)]

    def normalize(self, x: torch.Tensor) -> torch.Tensor:
        x = x.to(self.data.device)
        x = x.reshape(-1, self.data.n_secondary_bars, self.data.n_symbols)
        x = x.transpose(self.dim, -1)
        x = UnaryZScoreNorm.apply(x)
        x = torch.where(x.isfinite(), x, 0)
        x = x.transpose(self.dim, -1)
        return x

    def pearson(self, x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        x = x.transpose(self.dim, -1)
        y = y.transpose(self.dim, -1)
        corr = Corr.apply(x, y)
        corr = torch.where(corr.isfinite(), corr, 0)
        return corr

    def cal_ic(self, xs: Iterable[torch.Tensor], ys: Iterable[torch.Tensor]) -> torch.Tensor:
        ic_sum, ic_count = 0, 0
        for x, y in zip(xs, ys):
            ic = self.pearson(x, y)
            ic_sum += ic.sum()
            ic_count += ic.numel()
        return ic_sum / ic_count

    def cal_value(self, expr: Expression) -> Generator[torch.Tensor, None, None]:
        if expr in self.value_cache:
            for value in self.value_cache[expr]:
                yield value.to(self.data.device)
        else:
            for value in expr(self.data):
                yield self.normalize(value)

    def cal_pool_value(self, expr_pool: List[Expression], weights: torch.Tensor) -> Generator[torch.Tensor, None, None]:
        value_iters = [self.cal_value(expr) for expr in expr_pool]
        for value in value_iters[0]:
            value *= weights[0]
            for weight, value_iter in zip(weights[1:], value_iters[1:]):
                value += weight * next(value_iter)
            yield value

    def cal_single_ic(self, expr: Expression) -> torch.Tensor:
        return self.cal_ic(self.cal_value(expr), self.cal_value(self.target))

    def cal_mutual_ic(self, expr_x: Expression, expr_y: Expression) -> torch.Tensor:
        return self.cal_ic(self.cal_value(expr_x), self.cal_value(expr_y))

    def cal_pool_ic(self, expr_pool: List[Expression], weights: torch.Tensor) -> torch.Tensor:
        if not expr_pool:
            return torch.tensor(0.0, device=self.device)
        return self.cal_ic(self.cal_pool_value(expr_pool, weights), self.cal_value(self.target))


class CustomCalculator(Calculator):
    def __init__(self, *args, backtester: BackTester):
        super().__init__(*args)
        self.backtester = backtester
        self.raw_value_cache = {}

    def clear_cache(self, pool: List[Expression]):
        super().clear_cache(pool)
        self.raw_value_cache = {k: v for k, v in self.raw_value_cache.items() if k in pool}

    def refresh_cache(self, expr_pool: List[Expression]):
        expr_pool += [self.target]
        self.clear_cache(expr_pool)
        for expr in expr_pool:
            if expr in self.value_cache and expr in self.raw_value_cache:
                continue
            value_cache, raw_value_cache = [], []
            for value in self.get_value(expr):
                raw_value_cache.append(value.to(self.device))
                value = self.normalize(value)
                value_cache.append(value.to(self.device))
            self.value_cache[expr] = value_cache
            self.raw_value_cache[expr] = raw_value_cache

    def get_value(self, expr: Expression):
        if expr in self.raw_value_cache:
            for value in self.raw_value_cache[expr]:
                yield value.to(self.data.device)
        else:
            for value in expr(self.data):
                yield value

    def cal_ret(self, xs: Iterable[torch.Tensor]) -> Report:
        positions, returns = [], []
        for x, y in zip(xs, self.get_value(self.target)):
            positions.append(x.to(y.device))
            returns.append(y)
        positions, returns = torch.concat(positions, dim=0), torch.concat(returns, dim=0)
        return self.backtester(positions, returns, self.data.n_secondary_bars)

    def cal_pool_value(self, expr_pool: List[Expression], weights: torch.Tensor) -> Generator[torch.Tensor, None, None]:
        value_iters = [self.get_value(expr) for expr in expr_pool]
        for value in value_iters[0]:
            values = [value.to(self.device) * weights[0]]
            for weight, value_iter in zip(weights[1:], value_iters[1:]):
                values.append(next(value_iter).to(self.device) * weight)
            yield torch.stack(values, dim=-1)

    def cal_pool_ret(self, expr_pool: List[Expression], weights: torch.Tensor) -> Report:
        if not expr_pool:
            return Report()
        return self.cal_ret(self.cal_pool_value(expr_pool, weights))

    def cal_single_ret(self, expr: Expression) -> Report:
        return self.cal_ret(self.get_value(expr))

    def calc_finite_ratio(self, expr: Expression) -> float:
        count = 0
        finite_count = 0
        for value in self.get_value(expr):
            count += value.numel()
            finite_count += value.isfinite().sum().item()
        return finite_count / count

from enum import IntEnum
from typing import Generator, List, Optional, Sequence, Set, Tuple, Union

import pandas as pd
import torch

from base.data import Data


class SlotType(IntEnum):
    NotDefine = 0
    Feature = 1
    Constant = 2
    Bar = 3


class Expression:
    value: Union[str, float, int]

    def __init__(self, *args): ...

    def __call__(self, *args, **kwargs):
        raise NotImplementedError

    def __repr__(self):
        return str(self)

    def __hash__(self):
        return hash(str(self))

    def __add__(self, other):
        return Add(self, other)

    def __radd__(self, other):
        return Add(other, self)

    def __sub__(self, other):
        return Sub(self, other)

    def __rsub__(self, other):
        return Sub(other, self)

    def __mul__(self, other):
        return Mul(self, other)

    def __rmul__(self, other):
        return Mul(other, self)

    def __truediv__(self, other):
        return Div(self, other)

    def __rtruediv__(self, other):
        return Div(other, self)

    def __neg__(self):
        return Neg(self)

    def __pow__(self, other):
        return Pow(self, other)

    def __rpow__(self, other):
        return Pow(other, self)

    def __eq__(self, other: "Expression"):
        return str(self) == str(other)

    @property
    def is_feature(self) -> bool:
        return False

    @property
    def period(self):
        return slice(0, 1)

    @property
    def slot_type(self) -> SlotType:
        return SlotType.NotDefine

    @property
    def units(self):
        return []

    def get_value(self, data: Data, is_df: bool = False):
        result = []
        if not is_df:
            for value in self(data):
                result.append(value.cpu())
            return torch.concat(result, dim=0)
        else:
            for value, d, t, s in zip(self(data), Feature("date")(data), Feature("time")(data), Feature("symbol")(data)):
                isf = torch.isfinite(d) & torch.isfinite(t) & torch.isfinite(s)
                d = data.dts.categories[d[isf].long()]
                t = data.dts.categories[t[isf].long()]
                dt = pd.to_datetime(list(map(lambda x: f'{x[0]} {x[1]}', zip(d, t))))
                s = data.dts.categories[s[isf].long()]
                result.append(pd.Series(value[isf].cpu(), index=pd.MultiIndex.from_tuples(zip(dt, s))))
            return pd.concat(result, axis=0).unstack()


class Operand(Expression):
    def __init__(self, value):
        super().__init__()
        self.value = value

    @property
    def units(self):
        return [self.value]


class Feature(Operand):
    value: str

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        return data(period, self.value)

    def __str__(self):
        return f"${self.value}"

    @property
    def is_feature(self):
        return True

    @property
    def slot_type(self) -> SlotType:
        return SlotType.Feature


class Constant(Operand):
    value: float

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        return data(period, float(self.value))

    def __str__(self):
        return str(self.value)

    @property
    def slot_type(self) -> SlotType:
        return SlotType.Constant


class Bar(Operand):
    value: int

    def __str__(self):
        return str(self.value)

    @property
    def slot_type(self) -> SlotType:
        return SlotType.Bar


class Operator(Expression):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return []

    @classmethod
    def n_args(cls) -> int:
        if not cls.slots():
            return 0
        return len(cls.slots()[0])

    @classmethod
    def check_slots(cls, args: List[Expression]) -> bool:
        arg_slots = [arg.slot_type for arg in args]
        for slots in cls.slots():
            if arg_slots == slots[: len(arg_slots)]:
                return True
        return False

    @classmethod
    def check_args(cls, args: List[Expression]) -> bool:
        if not cls.check_slots(args):
            return False
        return cls.check_args_value(args)

    @classmethod
    def check_args_value(cls, args: Sequence[Optional[Expression]]) -> bool:
        for i, j in enumerate(cls.lazy_check(args)):
            if not all(j):
                return False
            if i >= len(args) - 1:
                break
        return True

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)

    @classmethod
    def require(cls) -> Set[str]:
        return set()

    @property
    def slot_type(self) -> SlotType:
        if self.is_feature:
            return SlotType.Feature
        else:
            return SlotType.Constant


"""
Unary Operators
"""


class UnaryOperator(Operator):
    def __init__(self, feature: Expression):
        super().__init__()
        self.feature = feature

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature]]

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        for value in self.feature(data, period):
            yield self.apply(value)

    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.feature})"

    @property
    def is_feature(self):
        return self.feature.is_feature

    @property
    def units(self):
        return [*self.feature.units, type(self)]


"""
Arithmetic Unary Operators
"""


class Neg(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.neg()


class Abs(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.abs()


class Inv(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.reciprocal()


class Sign(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.sign()


class Sin(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.sin()


class Cos(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.cos()


class Tan(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.tan()


class Asin(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.asin()


class Acos(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.acos()


class Atan(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.atan()


class Sqrt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.sqrt()


class UnsignedSqrt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sqrt.apply(Abs.apply(value))


class SignedSqrt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sign.apply(value) * UnsignedSqrt.apply(value)


class Square(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.square()


class Curt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.pow(1 / 3)


class UnsignedCurt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Curt.apply(Abs.apply(value))


class SignedCurt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sign.apply(value) * UnsignedCurt.apply(value)


class Cube(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.pow(3)


class Log(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.log()


class UnsignedLog(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Log.apply(Abs.apply(value))


class SignedLog(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sign.apply(value) * UnsignedLog.apply(value)


class Exp(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.exp()


"""
Non-Linear Unary Operators
"""


class TanH(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.tanh()


class Sigmoid(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.sigmoid()


class ReLU(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.relu()


class GeLU(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return torch.nn.functional.gelu(value)


"""
Statistic Unary Operators
"""


class UnaryCount(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingCount.apply(value).unsqueeze(-1).expand_as(value)


class UnarySum(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingSum.apply(value).unsqueeze(-1).expand_as(value)


class UnaryProd(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingProd.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMean(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMean.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMed(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMed.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMad(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMad.apply(value).unsqueeze(-1).expand_as(value)


class UnaryVar(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingVar.apply(value).unsqueeze(-1).expand_as(value)


class UnaryStd(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingStd.apply(value).unsqueeze(-1).expand_as(value)


class UnaryIncv(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingIncv.apply(value).unsqueeze(-1).expand_as(value)


class UnarySkew(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingSkew.apply(value).unsqueeze(-1).expand_as(value)


class UnaryKurt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingKurt.apply(value).unsqueeze(-1).expand_as(value)


class UnaryRank(UnaryOperator):
    """The largest value corresponds to 1, and the smallest value corresponds to 0."""

    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        count = UnaryCount.apply(value)
        greater, equal = torch.zeros_like(value), torch.zeros_like(value)
        for i in range(value.shape[-1]):
            greater[..., i] = (value[..., i:i + 1] > value).sum(dim=-1)
            equal[..., i] = (value[..., i:i + 1] == value).sum(dim=-1)
        rank = greater + (equal - 1) / 2
        return FiniteCond.apply(value, rank / (count - 1), torch.nan)


class UnaryDescendRank(UnaryOperator):
    """The largest value corresponds to 0, and the smallest value corresponds to 1."""

    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return 1 - UnaryRank.apply(value)


class UnaryMax(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMax.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMin(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMin.apply(value).unsqueeze(-1).expand_as(value)


"""
Unary Norm Operators 
"""


class UnaryCentral(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value - value.nanmean(dim=-1, keepdim=True)


class UnaryZScoreNorm(UnaryCentral):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return UnaryCentral.apply(value) / UnaryStd.apply(value)


class UnaryL1Norm(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value / UnarySum.apply(value)


class UnaryL2Norm(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value / Sqrt.apply(UnarySum.apply(value**2))


class UnaryMinMaxNorm(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        min_value = UnaryMin.apply(value)
        return (value - min_value) / (UnaryMax.apply(value) - min_value)


class UnarySoftmax(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.softmax(dim=-1)


"""
Binary Operators
"""


class BinaryOperator(Operator):
    def __init__(self, left: Union[Expression, float], right: Union[Expression, float]):
        super().__init__()
        self.left = left if isinstance(left, Expression) else Constant(left)
        self.right = right if isinstance(right, Expression) else Constant(right)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [
            [SlotType.Feature, SlotType.Constant],
            [SlotType.Constant, SlotType.Feature],
            [SlotType.Feature, SlotType.Feature],
        ]

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        for left, right in zip(self.left(data, period), self.right(data, period)):
            yield self.apply(left, right)

    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.left},{self.right})"

    @property
    def is_feature(self):
        return self.left.is_feature or self.right.is_feature

    @property
    def units(self):
        return [*self.left.units, *self.right.units, type(self)]


"""
Arithmetic Binary Operators
"""


class Add(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left + right


class Sub(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left - right


class Mul(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left * right


class Div(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left / right


class Pow(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left.pow(right)


class UnsignedPow(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Pow.apply(Abs.apply(left), right)


class SignedPow(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Sign.apply(left) * UnsignedPow.apply(left, right)


class BinaryLog(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Log.apply(left) / Log.apply(right)


class UnsignedBinaryLog(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Log.apply(Abs.apply(left)) / Log.apply(Abs.apply(right))


class SignedBinaryLog(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Sign.apply(left) * UnsignedBinaryLog.apply(left, right)


"""
Compare Binary Operator
"""


class BinaryMax(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left.max(right)


class BinaryMin(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left.min(right)


"""
Ternary Operators
"""


class TernaryOperator(Operator):
    def __init__(self, condition: Expression, left: Union[Expression, float], right: Union[Expression, float]):
        super().__init__()
        self.condition = condition
        self.left = left if isinstance(left, Expression) else Constant(left)
        self.right = right if isinstance(right, Expression) else Constant(right)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [
            [SlotType.Feature, SlotType.Feature, SlotType.Constant],
            [SlotType.Feature, SlotType.Constant, SlotType.Feature],
            [SlotType.Feature, SlotType.Feature, SlotType.Feature],
        ]

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        for cond, left, right in zip(self.condition(data, period), self.left(data, period), self.right(data, period)):
            yield self.apply(cond, left, right)

    @staticmethod
    def apply(
        cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]
    ) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.condition},{self.left},{self.right})"

    @property
    def is_feature(self):
        return self.condition.is_feature or self.left.is_feature or self.right.is_feature

    @property
    def units(self):
        return [*self.condition.units, *self.left.units, *self.right.units, type(self)]


class PositiveCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond > 0, left, right)


class NonNegativeCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond >= 0, left, right)


class NegativeCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond < 0, left, right)


class NonPositiveCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond <= 0, left, right)


class FiniteCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond.isfinite(), left, right)


"""
Rolling Operators
"""


class RollingOperator(Operator):
    def __init__(self, feature: Expression, bar: Union[int, Bar]):
        super().__init__()
        self.feature = feature
        self.bar = bar if isinstance(bar, Bar) else Bar(bar)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (args[1].value >= 2,)

    def _expand_period(self, period: slice = slice(0, 1)):
        if self.bar.value > 0:
            return slice(period.start - self.bar.value + 1, period.stop)
        else:
            return slice(period.start, period.stop - self.bar.value - 1)

    def _unfold_value(self, value: torch.Tensor):
        return value.unfold(0, abs(self.bar.value), 1)

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        period = self._expand_period(period)
        for value in self.feature(data, period):
            value = self._unfold_value(value)
            yield self.apply(value, self.bar.value)

    @staticmethod
    def apply(value: torch.Tensor, bar: int) -> torch.Tensor:
        return value

    def __str__(self):
        return f"{type(self).__name__}({self.feature},{self.bar})"

    @property
    def is_feature(self):
        return self.feature.is_feature

    @property
    def period(self):
        return self._expand_period(self.feature.period)

    @property
    def units(self):
        return [*self.feature.units, *self.bar.units, type(self)]


"""
Statistic Rolling Operator
"""


class RollingCount(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, 1.0, 0.0).sum(dim=-1)


class RollingSum(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value.nansum(dim=-1)


class RollingProd(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, 1.0).prod(dim=-1)


class RollingMean(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value.nanmean(dim=-1)


class RollingMed(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value.nanmedian(dim=-1)[0]


class RollingMad(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return RollingMean.apply(Abs.apply(UnaryCentral.apply(value)))


class RollingVar(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return RollingSum.apply(UnaryCentral.apply(value) ** 2) / (RollingCount.apply(value) - 1)


class RollingStd(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Sqrt.apply(RollingVar.apply(value))


class RollingIncv(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return RollingMean.apply(value) / RollingStd.apply(value)


class RollingSkew(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        top = RollingMean.apply(UnaryCentral.apply(value) ** 3)
        bottom = RollingMean.apply(UnaryCentral.apply(value) ** 2) ** 1.5
        return top / bottom


class RollingKurt(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        top = RollingMean.apply(UnaryCentral.apply(value) ** 4)
        bottom = RollingVar.apply(value) ** 2
        return top / bottom - 3


class RollingMax(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, -torch.inf).amax(dim=-1)


class RollingMin(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, torch.inf).amin(dim=-1)


class Argmax(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, -torch.inf).argmax(dim=-1).to(value.dtype)


class Argmin(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, torch.inf).argmin(dim=-1).to(value.dtype)


class ArgmaxArgmin(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Argmax.apply(value) - Argmin.apply(value)


"""
Rolling Norm Operators
"""


class RollingRank(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: int) -> torch.Tensor:
        value_ = torch.concat([value[0, :, :].transpose(0, 1), value[1:, :, -1]], dim=0)
        count = value_.isfinite().to(dtype=value.dtype).unfold(0, bar, 1).sum(dim=-1)
        if bar < 0:
            dim = 0
        else:
            dim = -1
        base_value = value[..., dim].unsqueeze(-1)
        greater = (base_value > value).sum(dim=-1, dtype=torch.uint8).to(dtype=value.dtype)
        equal = (base_value == value).sum(dim=-1, dtype=torch.uint8).to(dtype=value.dtype)
        rank = greater + (equal - 1) / 2
        return FiniteCond.apply(value[..., dim], rank / (count - 1), torch.nan)


class RollingDescendRank(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: int) -> torch.Tensor:
        return 1 - RollingRank.apply(value, bar)


class RollingCentral(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: int) -> torch.Tensor:
        if bar < 0:
            return UnaryCentral.apply(value)[..., 0]
        else:
            return UnaryCentral.apply(value)[..., -1]


class RollingZScoreNorm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryZScoreNorm.apply(value)[..., 0]
        else:
            return UnaryZScoreNorm.apply(value)[..., -1]


class RollingL1Norm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryL1Norm.apply(value)[..., 0]
        else:
            return UnaryL1Norm.apply(value)[..., -1]


class RollingL2Norm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryL2Norm.apply(value)[..., 0]
        else:
            return UnaryL2Norm.apply(value)[..., -1]


class RollingMinMaxNorm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryMinMaxNorm.apply(value)[..., 0]
        else:
            return UnaryMinMaxNorm.apply(value)[..., -1]


class RollingSoftmax(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnarySoftmax.apply(value)[..., 0]
        else:
            return UnarySoftmax.apply(value)[..., -1]


"""
Weighted Rolling Operator
"""


class DecayLinear(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        weights = torch.arange(value.shape[-1], 0, -1, device=value.device, dtype=value.dtype).expand_as(value)
        weights /= RollingSum.apply(FiniteCond.apply(value, weights, 0))
        return RollingSum.apply(value * weights)


class DescendDecayLinear(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        weights = torch.arange(value.shape[-1], device=value.device, dtype=value.dtype).expand_as(value) + 1
        weights /= RollingSum.apply(FiniteCond.apply(value, weights, 0))
        return RollingSum.apply(value * weights)


"""
Shifting Operator
"""


class ShiftingOperator(RollingOperator):
    def _expand_period(self, period: slice = slice(0, 1)):
        if self.bar.value > 0:
            return slice(period.start - self.bar.value, period.stop)
        else:
            return slice(period.start, period.stop - self.bar.value)

    def _unfold_value(self, value: torch.Tensor):
        return value.unfold(0, abs(self.bar.value) + 1, 1)


class Ref(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return value[..., -1]
        else:
            return value[..., 0]


class Delta(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value[..., -1] - value[..., 0]


class Ratio(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value[..., -1] / value[..., 0]


class DeltaRatio(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Ratio.apply(value) - 1


"""
Statistic Rolling Operator
"""


class PairRollingOperator(Operator):
    def __init__(self, left: Expression, right: Expression, bar: Union[int, Bar]):
        super().__init__()
        self.left = left
        self.right = right
        self.bar = bar if isinstance(bar, Bar) else Bar(bar)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Feature, SlotType.Bar]]

    def _expand_period(self, period: slice = slice(0, 1)):
        if self.bar.value > 0:
            return slice(period.start - self.bar.value + 1, period.stop)
        else:
            return slice(period.start, period.stop - self.bar.value - 1)

    def _unfold_value(self, value: torch.Tensor):
        return value.unfold(0, abs(self.bar.value), 1)

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        period = self._expand_period(period)
        for left, right in zip(self.left(data, period), self.right(data, period)):
            left = self._unfold_value(left)
            right = self._unfold_value(right)
            yield self.apply(left, right, self.bar.value)

    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.left},{self.right},{self.bar})"

    @property
    def is_feature(self):
        return self.left.is_feature or self.right.is_feature

    @property
    def period(self):
        left_period = self._expand_period(self.left.period)
        right_period = self._expand_period(self.right.period)
        return slice(min(left_period.start, right_period.start), max(left_period.stop, right_period.stop))

    @property
    def units(self):
        return [*self.left.units, *self.right.units, *self.bar.units, type(self)]


class Cov(PairRollingOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        top = RollingSum.apply(UnaryCentral.apply(left) * UnaryCentral.apply(right))
        bottom = UnaryCount.apply(top) - 1
        return top / bottom


class Corr(PairRollingOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Cov.apply(left, right) / (RollingStd.apply(left) * RollingStd.apply(right))


class PairSlope(PairRollingOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        left_central = UnaryCentral.apply(left)
        top = RollingSum.apply(left_central * UnaryCentral.apply(right))
        bottom = RollingSum.apply(left_central**2)
        return top / bottom


class CompositeOperator(Operator):
    features: List[Union[Operator, Feature]]
    formulas: List[Union[Operator, Feature]]
    args: List[Union[int, float]]

    def __init__(self, *args):
        super().__init__()
        self.features, self.args = [], []
        for arg in args:
            if isinstance(arg, (Feature, Operator)):
                self.features.append(arg)
            elif isinstance(arg, Operand):
                self.args.append(arg.value)
            else:
                self.args.append(arg)
        self.formulas = self.features

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        last_values: Optional[List[torch.Tensor]] = None
        for values in zip(*[formula(data, period) for formula in self.formulas]):
            last_values = self.apply(list(values), self.args, last_values)
            if isinstance(last_values, torch.Tensor):
                last_values = [last_values]
            yield last_values[0]

    def __str__(self):
        return f"{type(self).__name__}({','.join(str(e) for e in self.features + self.args)})"

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> Union[List[torch.Tensor], torch.Tensor]: ...

    @property
    def is_feature(self) -> bool:
        return any(feature.is_feature for feature in self.formulas)


class SMA(CompositeOperator):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar, SlotType.Constant]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (True,)
        yield args[2].value > 0, args[1].value > args[2].value

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        value, (alpha, beta) = values[0], args
        if last_values:
            x_last = last_values[0][-1]
        else:
            x_last = value[0]
        for i, x in enumerate(value):
            value[i] = x_last = (x_last * (beta - alpha) + x * alpha) / beta
        return value


class EMA(CompositeOperator):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (args[1].value >= 2,)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return SMA.apply(values, [args[0] + 1, 2], last_values)


class KAMA(CompositeOperator):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Constant]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (0 < args[1].value < 1,)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        value, args = values[0], args[0]
        if last_values:
            x_last = last_values[0][-1]
        else:
            x_last = value[0]
        for i, x in enumerate(value):
            value[i] = x_last = x_last * (1 - args) + args * x
        return value


class RSI(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingOperator(DeltaRatio(Feature("close"), 1), self.args[0])]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingOperator.check_args_value([None, args[0]]),)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"close"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        value = values[0]
        value = torch.concat([value[0, :, :].transpose(0, 1), value[1:, :, -1]], dim=0)

        pos_value = PositiveCond.apply(value, value, 0)
        up_average = pos_value.unfold(0, args[0], 1).sum(dim=-1) / (value > 0).to(dtype=value.dtype).unfold(
            0, args[0], 1
        ).sum(dim=-1)

        neg_value = NegativeCond.apply(value, Abs.apply(value), 0)
        down_average = neg_value.unfold(0, args[0], 1).sum(dim=-1) / (value < 0).to(dtype=value.dtype).unfold(
            0, args[0], 1
        ).sum(dim=-1)
        rs = up_average / down_average
        return rs / (rs + 1)


class RSV(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            Feature("close"),
            RollingMax(Feature("high"), self.args[0]),
            RollingMin(Feature("low"), self.args[0]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"close", "high", "low"}

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingMax.check_args_value([None, args[0]]) and RollingMin.check_args_value([None, args[0]]),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        close, high, low = values
        return (close - low) / (high - low)


class KDJ(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RSV(self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar, SlotType.Bar, SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return RSV.require()

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RSV.check_args_value([args[0]]),)
        yield (SMA.check_args_value([None, args[1], Constant(1)]),)
        yield (SMA.check_args_value([None, args[2], Constant(1)]),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> List[torch.Tensor]:
        k = SMA.apply(values, [args[1], 1], [last_values[1]] if last_values else None)
        d = SMA.apply([k], [args[2], 1], [last_values[2]] if last_values else None)
        return [3 * k - 2 * d, k, d]


class MACD(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [EMA(Feature("close"), self.args[0]), EMA(Feature("close"), self.args[1])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar, SlotType.Bar, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (EMA.check_args_value([None, args[0]]),)
        yield (EMA.check_args_value([None, args[1]]),)
        yield (EMA.check_args_value([None, args[2]]),)

    @classmethod
    def require(cls) -> Set[str]:
        return {"close"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> List[torch.Tensor]:
        dif = values[0] - values[1]
        dea = EMA.apply([dif], [args[2]], [last_values[1]] if last_values else None)
        return [2 * (dif - dea), dea]


class WR(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            Feature("close"),
            RollingMax(Feature("high"), self.args[0]),
            RollingMin(Feature("low"), self.args[0]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingMax.check_args_value([None, args[0]]) and RollingMin.check_args_value([None, args[0]]),)

    @classmethod
    def require(cls) -> Set[str]:
        return {"close", "high", "low"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        close, high, low = values
        return (high - close) / (high - low)


class ATR(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingMean(Feature("tr"), self.args[0])]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingMean.check_args_value([None, args[0]]),)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"tr"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0]


class MFI(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            RollingSum(PositiveCond(Feature("raw_money_flow"), Feature("raw_money_flow"), 0), self.args[0]),
            RollingSum(NegativeCond(Feature("raw_money_flow"), Feature("raw_money_flow"), 0), self.args[0]),
        ]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingSum.check_args_value([None, args[0]]),)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"raw_money_flow"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return 100 - (100 / (1 + values[0] / values[1]))


class ADOSC(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingMean(Feature("ad"), self.args[0]) - RollingMean(Feature("ad"), self.args[1])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar, SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"ad"}

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingMean.check_args_value([None, args[0]]),)
        yield args[0].value < args[1].value, RollingMean.check_args_value([None, args[1]])

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0]


class DEMA(CompositeOperator):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [EMA(self.features[0], self.args[0])]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (EMA.check_args_value([None, args[1]]),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> List[torch.Tensor]:
        value, bar = values[0], args[0]
        ema_ema = EMA.apply([value], [bar], [last_values[1]] if last_values else None)
        return [2 * value - ema_ema, ema_ema]


class APO(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            RollingMean(self.features[0], self.args[0]) - RollingMean(self.features[0], self.args[1]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (RollingMean.check_args_value(args[:2]),)
        yield (RollingMean.check_args_value([args[0], args[2]]), args[1].value < args[2].value)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0]


"""
以下指标来源于：https://www.nesc.cn/timerfiles/upload/report/2023/11/14/15813304.pdf
"""


class AROON(CompositeOperator):
    """
    AROON 意为“黎明的第一缕光”。阿隆指标（AROON）是Tushar Chande于1995
    年发明的，它通过计算自价格达到近期最高值和最低值以来所经过的期间数，用来
    揭示一个新趋势的早期开始。AROON指标由两个独立测量的指标表AROON_Up和
    AROON_Down组成，一般认为当AROON_Up超过70时，表示市场处于多头强势，
    当AROON_Down 超过 70 时，市场处于空头强势。当两者中的下行线上穿上行线
    时，表示原有趋势逐渐减弱，预计趋势开始反转。
    周期参数：N，阈值参数：H
    AROONUp = (N-N 日最高价距当前天数)/N×100
    AROONDown = (N-N 日最低价距当前天数)/N×100
    AROON = AROONUp - AROONDown
    AROONUP大于H（默认70），并且AROON大于0，产生买入信号；
    AROONUP大于H（默认70），并且AROON大于0，产生买入信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            1 + Argmax(Feature("high"), self.args[0]),
            1 + Argmin(Feature("low"), self.args[0]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingOperator.check_args_value([None, args[0]]),)

    @classmethod
    def require(cls) -> Set[str]:
        return {"high", "low"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0] / args[0] * 100 - values[1] / args[0] * 100


class DPO(CompositeOperator):
    """
    DPO由Walt Bressert 用于辅助判断股价的短期趋势。它的计算方法是通过消除股价
    的前期长期趋势，只留下短线价格波动，形成区间震荡线，更容易识别小周期的超
    买/超卖水平。当 DPO区间震荡线向上时，表示股价处于上升趋势。当DPO区间震
    荡线向下时，表示股价处于下降趋势。
    周期参数：N
    DPO=CLOSE-REF(MA(CLOSE, N), N/2+1)
    当DPO>0时，代表股价短期向上，产生买入信号；
    当DPO<0时，代表股价短期向下，产生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [self.features[0] - Ref(RollingMean(self.features[0], self.args[0]), int(self.args[0] / 2) + 1)]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (RollingMean.check_args_value(args),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0]


class MOM(CompositeOperator):
    """
    动量指标利用的时物理学的动力学原理，通过衡量价格的动量和趋势，以判断未来
    价格的变化。当MOM为正时，说明最近价格处于上涨趋势，当MOM为负时，说明最近价格处于下跌趋势。动量指标有两种使用逻辑，一种是利用动量的持续性，另外一种是利用动量的反转性，在短期MOM一般表现为反转效应和在长期表现为动量效应。
    周期参数：N
    MOM = CLOSE - REF(CLOSE, N)
    当MOM>0时，动量为正，产生买入信号；
    当MOM<0时，动量为负，产生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [self.features[0] - Ref(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0]


class BIAS(CompositeOperator):
    """
    乖离率（BIAS）是测量股价偏离均线大小程度的指标，通过百分比的形式来表示股价与平均移动线之间的差距，反映一定时期内股价与其移动平均数偏离程度的指标。
    当BIAS 大于0时，表示股价高于均线，市场上涨力量较强，当BIAS小于0时，表示股价低于均线，说明市场下跌力量较强。当股价偏离均线过大时，表示股价处于超买状态/超卖状态，未来可能会发生均值回归的情况。
    周期参数：N，阈值参数：H, L
    BIAS(N)=(CLOSE-MA(CLOSE, N))/MA(CLOSE, N)×100
    当BIAS大于阈值H时，预计上升趋势较强，产生买入信号；
    当BIAS小于阈值L时，预计上升趋势较强，产生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [self.features[0], RollingMean(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (RollingMean.check_args_value(args),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return (values[0] - values[1]) / values[1]


class ROC(CompositeOperator):
    """
    ROC指标（变动率指标）是一种基于动量的技术指标，衡量当前价格与一定天数前
    价格之间变化的百分比。ROC指标围绕零轴上下波动，如果价格变化向上，指标会
    移动到零轴之上；如果价格变动向下，则指标会移动到零轴以下。指标逐渐远离零
    轴表示动量增加，而向零轴移动，则表示动量减弱。此外，ROC指标超出常态范围
    时，可以用来识别超买和超卖情况。
    周期参数：N
    ROC=100×(CLOSE-REF(CLOSE, N)) /REF(CLOSE, N)
    ROC>0 产生买入信号，ROC<0产生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [self.features[0], Ref(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return (values[0] - values[1]) / values[1]


class CCI(CompositeOperator):
    """
    CCI 指标用来衡量典型价格（最高价、最低价和收盘价的均值）与其一段时间的移
    动平均的偏离程度。CCI被称为“短线指标之王”，在大部分时间内CCI在-100到
    +100 之间范围内波动，在剩下20%-30%时间内，CCI会超出这个范围。当CCI超
    出100时被认为上涨趋势继续延续的信号，当CCI小于-100时，表明下跌趋势将延
    续。
    周期参数：N，阈值参数：H, L
    TYP=(HIGH+LOW+CLOSE)/3
    TYP_MA=MA(TYP, N)
    MD=MA(ABS(TYP_MA-TYP), N)
    CCI=(TYP-MA)/(0.015MD)
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            Feature("tp"),
            RollingMean(Feature("tp"), self.args[0]),
            RollingMean(Abs(RollingMean(Feature("tp"), self.args[0]) - Feature("tp")), self.args[0]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingOperator.check_args_value([None, args[0]]),)

    @classmethod
    def require(cls) -> Set[str]:
        return {"tp"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return (values[0] - values[1]) / (0.015 * values[2])


class FI(CompositeOperator):
    """
    强力指数（FI）把市场上每一次的变化力量划分为三个因素：方向、距离以及成交
    量，其中方向和距离有价格涨跌方向和幅度决定，价格变化幅度越大，成交量越大，
    在该方向的力量越大。
    周期参数：N
    QL = (CLOSE – REF(CLOSE, 1))×VOLUME
    FI = EMA(QL, N)
    当FI>0，市场多头强劲，产生买入信号；当FI<0 时，市场空头强劲，产生卖出信
    号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [EMA((Feature("close") - Ref(Feature("close"), 1)) * Feature("volume"), self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (EMA.check_args_value([None, args[0]]),)

    @classmethod
    def require(cls) -> Set[str]:
        return {"close", "volume"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0]


class CMO(CompositeOperator):
    """
    CMO是一种“净”动量震荡指标，将“动能与震荡结合，可协助辨是股市动量的极
    端位置，找出超买超卖区。以CMO的正负值判断动量是在"上涨"或"下跌"。
    周期参数：N
    D = CLOSE-REF(CLOSE,1)
    SU = IF(D>0, D, 0)
    SD = IF(D<=0, 0, -D)
    SUM1 = SUM(SU, N)
    SUM2 = SUM(SD, N)
    CMO = ((SUM1-SUM2) / (SUM1+SUM2)) × 100
    CMO>0，产生买入信号；CMO<0，产生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingOperator(Feature("close") - Ref(Feature("close"), 1), self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)

    @classmethod
    def require(cls) -> Set[str]:
        return {"close"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        value = values[0]
        su = PositiveCond.apply(value, value, 0)
        sd = NonPositiveCond.apply(value, 0, -value)
        sum1 = RollingSum.apply(su)
        sum2 = RollingSum.apply(sd)
        return (sum1 - sum2) / (sum1 + sum2)


class UO(CompositeOperator):
    """
    终极震荡指标是一种动量指标，旨在衡量资产在多个时间范围内的价格动量。它使
    用三个不同的时期（7、14 和 28）来确定短期、中期和长期市场趋势的动量，然后
    生成三者的加权平均值。最短的时间范围在计算中具有最大的权重，而最长的时间
    范围具有最小的权重。通过使用三个不同时间框架的加权平均值，该指标的波动性
    较小，这有助于它避免依赖单一时间框架的其他震荡指标的陷阱。
    周期参数：N1, N2, N3
    TH = MAX(HIGH, REF(CLOSE,1))
    TL = MIN(LOW, REF(CLOSE,1))
    TR = TH-TL
    XR = CLOSE-TL
    XRM = SUM(XR, N1)/SUM(TR, N1)
    XRN = SUM(XR, N2)/SUM(TR, N2)
    XRO = SUM(XR, N3)/SUM(TR, N3)
    UOS = 100×(XRM×N2×N3+XRN×N1×N3+XRO×N1×N2)/(N1×N2+ N2×N3+ N1×N3)
    UO>70，产生买入信号；UO<50，产生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            RollingSum(
                BinaryMax(Feature("high"), Ref(Feature("close"), 1))
                - BinaryMin(Feature("low"), Ref(Feature("close"), 1)),
                self.args[0],
            ),
            RollingSum(Feature("close") - BinaryMin(Feature("low"), Ref(Feature("close"), 1)), self.args[0]),
            RollingSum(
                BinaryMax(Feature("high"), Ref(Feature("close"), 1))
                - BinaryMin(Feature("low"), Ref(Feature("close"), 1)),
                self.args[1],
            ),
            RollingSum(Feature("close") - BinaryMin(Feature("low"), Ref(Feature("close"), 1)), self.args[1]),
            RollingSum(
                BinaryMax(Feature("high"), Ref(Feature("close"), 1))
                - BinaryMin(Feature("low"), Ref(Feature("close"), 1)),
                self.args[2],
            ),
            RollingSum(Feature("close") - BinaryMin(Feature("low"), Ref(Feature("close"), 1)), self.args[2]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar, SlotType.Bar, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (RollingOperator.check_args_value([None, args[0]]),)
        yield (RollingOperator.check_args_value([None, args[1]]), args[0].value < args[1].value)
        yield (RollingOperator.check_args_value([None, args[2]]), args[1].value < args[2].value)

    @classmethod
    def require(cls) -> Set[str]:
        return {"high", "low", "close"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        xrm = values[0] / values[1]
        xrn = values[2] / values[3]
        xro = values[4] / values[5]
        n1, n2, n3 = args
        return (xrm * n2 * n3 + xrn * n1 * n3 + xro * n1 * n2) / (n1 * n2 + n1 * n3 + n2 * n3)


class TripleEMA(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [EMA(EMA(EMA(self.features[0], self.args[0]), self.args[0]), self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (EMA.check_args_value(args),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return values[0]


class TRIX(CompositeOperator):
    """
    TRIX 指标是根据移动平均线理论，对一条移动平均线进行三次平滑处理，再根据
    这条移动平均线的变动情况来预测股价的长期走势.是一种研究股价趋势的长期技
    术分析工具。
    当TRIX线从下向上突破TRMA线，形成“金叉”时，预示着股价开始进入强势拉
    升阶段，发出买入信号；当TRIX线向下跌破MATRIX线（一般在高位）形成死叉
    时，股价上涨行情终结，发出卖出信号。
    周期参数：N1, N2
    TRIPLE_EMA = EMA(EMA(EMA(CLOSE, N1), N1), N1)
    TRIX = (TRIPLE_EMA-REF(TRIPLE_EMA, 1))/REF(TRIPLE_EMA, 1)
    TRIXMA = MA(TRIX, N2)
    TRIX 上穿TRIXMA，产生买入信号；TRIX下穿TRIXMA，产生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [ShiftingOperator(TripleEMA(self.features[0], self.args[0]), 1)]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (TripleEMA.check_args_value(args),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        value = values[0]
        return (value[..., -1] - value[..., 0]) / value[..., 0]


class POS(CompositeOperator):
    """
    POS 指标衡量当前的N天收益率在过去N天的收益率中位置。当位置指标上穿一
    定阈值时，意味着上涨趋势劲头正强劲，未来大概继续上涨；当位置指标下穿一定
    阈值时，意味着下跌趋势正强劲，未来大概率继续下跌。
    周期参数：N，阈值参数：H, L
    PC = (CLOSE-REF(CLOSE, N))/REF(CLOSE, N)
    POS = (PC-MIN(PC, N))/(MAX(PC,N)-MIN(PC, N))
    当 POS 上穿H（默认80）时，产生买入信号；当 POS下穿L（默认20）时，产
    生卖出信号。
    """

    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            RollingOperator((self.features[0] - Ref(self.features[0], 1)) / Ref(self.features[0], 1), self.args[0])
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def lazy_check(cls, args: Sequence[Optional[Expression]]) -> Generator[Tuple[bool, ...], None, None]:
        yield (True,)
        yield (RollingOperator.check_args_value(args),)

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        min_pc = RollingMin.apply(values[0])
        return (values[0][..., -1] - min_pc) / (RollingMax.apply(values[0]) - min_pc)

import itertools
from typing import List, Mapping, Optional, Sequence, Tuple, Union

import cvxpy as cp
import torch

from base.calculator import Calculator, CustomCalculator
from base.expression import (
    CompositeOperator,
    Expression,
    NegativeCond,
    PositiveCond,
    RollingDescendRank,
    RollingMean,
    RollingOperator,
    RollingRank, FiniteCond,
    RollingStd,
    Sign, SlotType
)


class QuantileSignalConverter(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingRank(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar, SlotType.Constant]]

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return Sign.apply(Sign.apply(values[0] - args[1]) + Sign.apply(values[0] - (1 - args[1])))


class ReverseQuantileSignalConverter(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingDescendRank(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar, SlotType.Constant]]

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        return Sign.apply(Sign.apply(values[0] - args[1]) + Sign.apply(values[0] - (1 - args[1])))


class BOLLSignalConverter(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingOperator(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar, SlotType.Constant]]

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        value = values[0][..., -1]
        mean = RollingMean.apply(values[0])
        std = RollingStd.apply(values[0])
        constant = args[1]
        return PositiveCond.apply(
            value - (mean + constant * std), 1.0, NegativeCond.apply(value - (mean - constant * std), -1.0, 0.0)
        )


class MaxMinNormSignalConverter(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingOperator(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar, SlotType.Constant]]

    @staticmethod
    def apply(values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        value = values[0][..., -1]
        value_ = torch.concat([values[0][0, :, :].transpose(0, 1), values[0][1:, :, -1]], dim=0)
        min_value = FiniteCond.apply(value_, value_, torch.inf).unfold(0, args[0], 1).amin(dim=-1)
        max_value = FiniteCond.apply(value_, value_, -torch.inf).unfold(0, args[0], 1).amax(dim=-1)
        value = (value-min_value)/(max_value-min_value)
        value = FiniteCond.apply(value, value, torch.nan)
        return torch.where(value > 1-args[1], 1.0, torch.where(value < args[1], -1.0, 0.0))


class ReverseMaxMinNormSignalConverter(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingOperator(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar, SlotType.Constant]]

    @staticmethod
    def apply(values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        value = values[0][..., -1]
        value_ = torch.concat([values[0][0, :, :].transpose(0, 1), values[0][1:, :, -1]], dim=0)
        min_value = FiniteCond.apply(value_, value_, torch.inf).unfold(0, args[0], 1).amin(dim=-1)
        max_value = FiniteCond.apply(value_, value_, -torch.inf).unfold(0, args[0], 1).amax(dim=-1)
        value = (value-min_value)/(max_value-min_value)
        value = FiniteCond.apply(value, value, torch.nan)
        return torch.where(value > 1-args[1], -1.0, torch.where(value < args[1], 1.0, 0.0))


class CrossSignalConverter(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [self.features[0], RollingMean(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @staticmethod
    def apply(values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        return torch.where(values[0] > values[1], 1.0, 0.0)


class ReverseCrossSignalConverter(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [self.features[0], RollingMean(self.features[0], self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @staticmethod
    def apply(values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        return torch.where(values[0] < values[1], 1.0, 0.0)


class Pool:
    size: int
    expr_pool: List[Optional[Expression]]
    single_ics: torch.Tensor
    mutual_ics: torch.Tensor
    weights: torch.Tensor

    repeat_count: int = 0
    evaluate_count: int = 0

    train_ic: float

    def __init__(self, pool_size: int, calculator: Calculator, **kwargs):
        self.pool_size = pool_size
        self.calculator = calculator

        self.lr = kwargs.get("lr", 5e-4)
        self.epochs = kwargs.get("epochs", 512)
        self.max_epochs = kwargs.get("max_epochs", 8096)
        self.epsilon = kwargs.get("epsilon", 1e-6)

        self.ic_single_threshold = kwargs.get("ic_single_threshold", 0.0)
        self.ic_mutual_threshold = kwargs.get("ic_mutual_threshold", 0.85)
        self.l1_alpha = kwargs.get("l1_alpha", 5e-3)
        self.device = kwargs.get("device", "cpu")

        self.convex_limit = kwargs.get("convex", {}).get("limit", 3.0)
        self.convex_base = kwargs.get("convex", {}).get("base", 2.0)
        self.grad_limit = kwargs.get("grad", {}).get("limit", 5.0)
        self.grad_error = kwargs.get("grad", {}).get("error", 50.0)
        self.weight_base = kwargs.get("weight", {}).get("base", 0.1)

        self.reset_repeat = kwargs.get("reset", {}).get("repeat", float("inf"))
        self.reset_evaluate = kwargs.get("reset", {}).get("evaluate", float("inf"))

        self.best_ic = -float("inf")
        self.expr_set = set()
        self.reset_pool()

    def reset_pool(self):
        self.size = 0
        self.expr_pool = [None] * (self.pool_size + 1)
        self.single_ics = torch.zeros([self.pool_size + 1], device=self.device)
        self.mutual_ics = torch.ones([self.pool_size + 1, self.pool_size + 1], device=self.device)
        self.weights = torch.zeros([self.pool_size + 1], device=self.device)
        self.train_ic = -float("inf")

    def cal_ics(self, expr: Expression) -> Tuple[torch.Tensor, Optional[List[torch.Tensor]]]:
        single_ic = self.calculator.cal_single_ic(expr)
        if single_ic.abs() < self.ic_single_threshold:
            return single_ic, None
        mutual_ics = []
        for existed_expr in self.expr_pool[: self.size]:
            mutual_ic = self.calculator.cal_mutual_ic(expr, existed_expr)
            if mutual_ic.abs() > self.ic_mutual_threshold:
                return single_ic, None
            mutual_ics.append(mutual_ic)
        return single_ic, mutual_ics

    def check_reset(self):
        self.evaluate_count += 1
        if self.repeat_count > self.reset_repeat:
            self.reset_pool()
            self.repeat_count = 0
            return True
        if self.evaluate_count > self.evaluate_count:
            self.reset_pool()
            self.evaluate_count = 0
            return True
        return False

    def evaluate(self, expr: Expression) -> float:
        self.expr_set.add(str(expr))
        self.calculator.refresh_cache(self.expr_pool[: self.size] + [expr])
        single_ic, mutual_ic = self.cal_ics(expr)
        if mutual_ic is None:
            return 0.0
        self.add(expr, single_ic, mutual_ic)
        self.optimize()
        self.train_ic = self.calculator.cal_pool_ic(self.expr_pool[: self.size], self.weights[: self.size]).item()
        self.best_ic = max(self.train_ic, self.best_ic)
        if self.check_reset():
            return 0.0
        return self.train_ic

    def convex_optimize(self):
        weights = self.weights[: self.size]
        weight_limit = self.weight_base / self.size
        if weights.abs().mean() <= weight_limit * self.convex_limit:
            return weights
        p = self.mutual_ics[: self.size, : self.size]
        q = self.single_ics[: self.size]
        rand = torch.rand(self.size, device=self.device)
        threshold = q.abs() / q.abs().mean() * weight_limit * self.convex_limit * rand
        best_weights = q.sign() * weight_limit * self.convex_base * rand
        p, q, threshold = p.numpy(), q.numpy(), threshold.numpy()
        x = cp.Variable(self.size)
        objective = cp.Minimize(0.5 * cp.quad_form(x, p) - q.T @ x)
        constraints = [x <= threshold, x >= -threshold]
        try:
            cp.Problem(objective, constraints).solve()
            if x.value is not None:
                best_weights = torch.tensor(x.value, device=self.device)
        except cp.DCPError:
            pass
        return best_weights

    def grad_optimize(self, convex_weights):
        weights = self.weights[: self.size].detach().clone().requires_grad_(True)
        weight_limit = self.weight_base / self.size
        single_ics = self.single_ics[: self.size]
        mutual_ics = self.mutual_ics[: self.size, : self.size]

        optimizer = torch.optim.Adam([weights], lr=self.lr)

        epochs, best_weights, best_loss = 0, convex_weights, float("inf")
        for _ in range(self.max_epochs):
            single_ic_sum = (weights * single_ics).sum()
            mutual_ic_sum = (torch.outer(weights, weights) * mutual_ics).sum()
            loss_ic = mutual_ic_sum - 2 * single_ic_sum + 1
            loss_l1 = torch.norm(weights, p=1)
            loss = loss_ic + self.l1_alpha * loss_l1

            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            if loss_ic < best_loss and weights.abs().mean() < weight_limit * self.grad_limit:
                best_weights, best_ic = weights.detach(), loss_ic.item()
            epochs = 0 if loss_ic - best_loss > self.epsilon else epochs + 1

            if epochs == self.epochs or weights.abs().mean() > weight_limit * self.grad_error:
                break
        return best_weights

    def optimize_weights(self):
        convex_weights = self.convex_optimize()
        weights = self.grad_optimize(convex_weights)
        return weights

    def optimize(self):
        if self.size <= 1:
            return
        weights = self.optimize_weights()
        min_idx = int(weights.abs().argmin())
        if min_idx == self.pool_size:
            self.repeat_count += 1
        else:
            self.weights[: self.size] = weights
            self.repeat_count = 0
        self.pop(min_idx)

    def init_weight(self):
        min_weight = self.weight_base
        if self.size:
            rank = int(self.single_ics[: self.size + 1].argsort()[self.size])
            rank = min(rank, self.size - 1)
            min_weight = max(self.weights[: self.size].sort()[0][rank].item(), min_weight)
        self.weights[self.size] = self.single_ics[self.size].sign() * min_weight

    def add(self, expr: Expression, single_ic: torch.Tensor, mutual_ics: List[torch.Tensor]):
        self.expr_pool[self.size] = expr
        self.single_ics[self.size] = single_ic
        for i, mutual_ic in enumerate(mutual_ics):
            self.mutual_ics[i][self.size] = self.mutual_ics[self.size][i] = mutual_ic
        self.init_weight()
        self.size += 1

    def pop(self, min_idx):
        if self.size <= self.pool_size:
            return
        self.size = self.pool_size
        if min_idx == self.pool_size:
            return
        self.swap(min_idx, self.pool_size)

    def swap(self, i: int, j: int):
        self.expr_pool[i], self.expr_pool[j] = self.expr_pool[j], self.expr_pool[i]
        self.single_ics[i], self.single_ics[j] = self.single_ics[j], self.single_ics[i]
        self.mutual_ics[:, [i, j]] = self.mutual_ics[:, [j, i]]
        self.mutual_ics[[j, i], :] = self.mutual_ics[[i, j], :]
        self.weights[i], self.weights[j] = self.weights[j], self.weights[i]


class CustomPool(Pool):
    calculator: CustomCalculator

    param_pool: List[Optional[Tuple[int, float]]]
    signal_pool: List[Optional[Expression]]
    single_rets: torch.Tensor

    train_ret: float

    def __init__(self, *args, target: str, signal_converter: Mapping[str, Sequence[Sequence]], **kwargs):
        super().__init__(*args, **kwargs)
        self.target = target
        self.signal_converter = signal_converter

        self.custom_condition = kwargs.get("custom_condition", {})
        self.signal_ic_mutual_threshold = kwargs.get("signal_ic_mutual_threshold", 0.9)
        self.reward_scale = kwargs.get("reward_scale", 0.05)
        self.finite_value_threshold = kwargs.get("finite_value_threshold", 0.5)

        self.best_ret = -float("inf")

    @staticmethod
    def build_signal(type_: str, expr: Expression, params: Sequence[Sequence]):
        converter = {k.__name__: k for k in [
            QuantileSignalConverter,
            ReverseQuantileSignalConverter,
            # BOLLSignalConverter,
            MaxMinNormSignalConverter,
            ReverseMaxMinNormSignalConverter,
            CrossSignalConverter,
            ReverseCrossSignalConverter,
        ]}[type_]
        for i in itertools.product(*params):
            yield converter(expr, *i), (type_, *i)

    def reset_pool(self):
        super().reset_pool()
        self.param_pool = [None] * (self.pool_size + 1)
        self.signal_pool = [None] * (self.pool_size + 1)
        self.single_rets = torch.zeros_like(self.single_ics)
        self.train_ret = -float("inf")

    def cal_signal_ics(self, signal: Expression) -> Optional[List[torch.Tensor]]:
        mutual_ics = []
        for existed_signal in self.signal_pool[: self.size]:
            mutual_ic = self.calculator.cal_mutual_ic(signal, existed_signal)
            if mutual_ic.abs() > self.signal_ic_mutual_threshold:
                return None
            mutual_ics.append(mutual_ic)
        return mutual_ics

    def cal_rets(
        self, expr: Expression
    ) -> Tuple[Optional[Expression], Optional[Tuple], float, Optional[torch.Tensor], Optional[List[torch.Tensor]]]:
        best_signal, best_param, best_ret = None, None, -float("inf")

        if self.calculator.calc_finite_ratio(expr) < self.finite_value_threshold:
            return best_signal, best_param, best_ret, None, None

        single_ic, mutual_ics = self.cal_ics(expr)
        if mutual_ics is None:
            return best_signal, best_param, best_ret, single_ic, mutual_ics

        for signal, param in itertools.chain(
            *(self.build_signal(type_, expr, params) for type_, params in self.signal_converter.items())
        ):
            if self.cal_signal_ics(signal) is None:
                continue
            report = self.calculator.cal_single_ret(signal)
            if not all(eval('{}'.format(report[key]) + condition) for key, condition in self.custom_condition.items()):
                continue
            if report[self.target] > best_ret:
                best_signal, best_param, best_ret = signal, param, report[self.target]
        return best_signal, best_param, best_ret, single_ic, mutual_ics

    def init_weight(self):
        min_weight = self.weight_base
        if self.size:
            rank = int(self.single_rets[: self.size + 1].argsort()[self.size])
            rank = min(rank, self.size - 1)
            min_weight = max(self.weights[: self.size].sort()[0][rank].item(), min_weight)
        self.weights[self.size] = self.single_rets[self.size].sign() * min_weight

    def evaluate(self, expr: Expression) -> float:
        self.expr_set.add(str(expr))
        self.calculator.refresh_cache(self.expr_pool[: self.size] + [expr])
        best_signal, best_param, best_ret, single_ic, mutual_ics = self.cal_rets(expr)
        if best_signal is None:
            return -1.0
        self.add(expr, single_ic, mutual_ics, signal=best_signal, param=best_param, single_ret=best_ret)
        self.optimize()
        report = self.calculator.cal_pool_ret(self.signal_pool[: self.size], self.weights[: self.size])
        self.train_ret = report[self.target]
        self.best_ret = max(self.best_ret, self.train_ret)
        if self.check_reset():
            return -1.0
        return self.train_ret

    def enum_optimize(self):
        weights = torch.full_like(self.weights, 1 / self.pool_size)
        worst_idx, worst_ret = 0, float("inf")
        for i in range(self.pool_size + 1):
            signals = [j for j in self.signal_pool[:i] + self.signal_pool[i + 1:] if j is not None]
            report = self.calculator.cal_pool_ret(signals, weights[: self.pool_size])
            ret = report[self.target]
            if ret < worst_ret:
                worst_idx, worst_ret = i, ret
        weights[worst_idx] = 0.0

        return weights

    def optimize_weights(self):
        if self.size < self.pool_size + 1:
            return torch.full_like(self.weights[: self.size], 1 / self.size)
        else:
            return self.enum_optimize()

    def add(self, *args, signal: Expression, param: Tuple[int, float], single_ret: float):
        self.signal_pool[self.size] = signal
        self.param_pool[self.size] = param
        self.single_rets[self.size] = single_ret
        super().add(*args)

    def swap(self, i: int, j: int):
        super().swap(i, j)
        self.signal_pool[i], self.signal_pool[j] = self.signal_pool[j], self.signal_pool[i]
        self.param_pool[i], self.param_pool[j] = self.param_pool[j], self.param_pool[i]
        self.single_rets[i], self.single_rets[j] = self.single_rets[j], self.single_rets[i]

import json
import pathlib

import torch
from base.calculator import CustomCalculator
from base.data import Data
from base.pool import CustomPool
from env.callback import CustomCallback
from env.environment import CustomEnv
from env.utils import reseed_everything
from loguru import logger
from sb3_contrib import MaskablePPO
from utils.argparser import parse_args
from utils.backtest import BackTester

torch.cuda.set_per_process_memory_fraction(0.8, device="cuda:0")

config = {
    "name": "default7",
    "max_expr_length": 30,
    "operators": [
        "Abs",
        "Sign",
        "Neg",
        "UnsignedSqrt",
        "SignedSqrt",
        "Square",
        "Curt",
        "Cube",
        "Inv",
        "UnsignedLog",
        "SignedLog",
        "Add",
        "Sub",
        "Mul",
        "Div",
        "Pow",
        "BinaryMax",
        "BinaryMin",
        "UnsignedBinaryLog",
        "BinaryLog",
        "Ref",
        "RollingMean",
        "RollingSum",
        "RollingStd",
        "RollingVar",
        "RollingMax",
        "RollingMin",
        "RollingMed",
        "RollingMad",
        "Delta",
        "Ratio",
        "Argmax",
        "Argmin",
        "Sin",
        "Cos",
        "Tan",
        "Asin",
        "Acos",
        "Atan",
        "SMA",
        "EMA",
        "KAMA",
        "RSI",
        "KDJ",
        "MACD",
        "WR",
        "ATR",
        "MFI",
        "ADOSC",
        "DEMA",
        "APO",
        "AROON",
        "DPO",
        "MOM",
        "BIAS",
        "ROC",
        "CCI",
        "FI",
        "CMO",
        "UO",
        "TripleEMA",
        "TRIX",
        "POS",
    ],
    "features": [
        "OPEN",
        "HIGH",
        "LOW",
        "CLOSE",
        "VOLUME",
        "TURNOVER",
        "TURNOVER_RATE",
        "QUOTE_RATE",
        "AMP_RATE",
        "AVG_PRICE",
        "TP",
        "OBV",
        "MP",
        "WC",
        "CFJ",
    ],
    "constants": [10, 5, 3, 2, 1, 0.5, 0.1, 0.01],
    "bars": [1, 2, 3, 5, 10, 15, 30, 60],
    "device": "cpu" if not torch.cuda.is_available() else "cuda",
    "seed": None,
    "steps": 2 ** 40,
    "data_config": {
        "index": "all",
        "train_interval": ("2020-01-01 00:00:00", "2023-07-01 00:00:00"),
        "test_interval": ("2023-07-01 00:00:00", "2024-12-31 00:00:00"),
        "freq": "1d",
        "step": 5,
        "sample_rate": 0.2,
    },
    "pool_config": {
        "pool_size": 20,
        "target": "sortino",
        "signal_converter": {
            "QuantileSignalConverter": [[20, 60, 120, 250], [0.1, 0.2, 0.3, 0.05]],
            "ReverseQuantileSignalConverter": [[20, 60, 120, 250], [0.1, 0.2, 0.3, 0.05]],
            "MaxMinNormSignalConverter": [[20, 60, 120, 250], [0.1, 0.2, 0.3, 0.05]],
            "ReverseMaxMinNormSignalConverter": [[20, 60, 120, 250], [0.1, 0.2, 0.3, 0.05]],
            "CrossSignalConverter": [[5, 10, 20, 60]],
            "ReverseCrossSignalConverter": [[5, 10, 20, 60]],
        },
        "signal_backward_bars": 250,
        "repeat": 800,
        "finite_value_threshold": 0.75,
        "ic_single_threshold": -float("inf"),
        "ic_mutual_threshold": 0.7,
        "custom_condition": {"count_per_year": ">10", "total_win_rate": ">0.6", "mdd": "<0.65", "positive_return_ratio": ">0.4"},
        "signal_ic_mutual_threshold": 0.9,
        "cost": 1e-3,
        "one_way": 1,
    },
}

if __name__ == "__main__":
    name = config["name"]
    max_expr_length = config["max_expr_length"]
    operators = config["operators"]
    features = config["features"]
    constants = config["constants"]
    bars = config["bars"]
    device = config["device"]
    seed = config["seed"]

    (save_path := pathlib.Path(name)).mkdir(parents=True, exist_ok=True)
    json.dump(config, open(save_path / "config.json", "w"))
    logger.add(save_path / "log")

    if seed:
        reseed_everything(seed)
    tokens, return_expr, backward_bars, forward_bars, required_features = parse_args(
        operators, features, constants, bars, "DeltaRatio($close, -1)", max_expr_length
    )
    backward_bars += config["pool_config"]["signal_backward_bars"]
    backtester = BackTester(cost=config["pool_config"]["cost"], one_way=config["pool_config"]["one_way"])

    train_data = Data(
        index=config["data_config"]["index"],
        features=required_features,
        start_time=config["data_config"]["train_interval"][0],
        end_time=config["data_config"]["train_interval"][1],
        freq=config["data_config"]["freq"],
        backward_bars=backward_bars,
        forward_bars=forward_bars,
        step=config["data_config"]["step"],
        sample_rate=config["data_config"]["sample_rate"],
        device=device,
    )
    train_calculator = CustomCalculator(train_data, return_expr, 1, backtester=backtester)
    test_data = Data(
        index=config["data_config"]["index"],
        features=required_features,
        start_time=config["data_config"]["test_interval"][0],
        end_time=config["data_config"]["test_interval"][1],
        freq=config["data_config"]["freq"],
        backward_bars=backward_bars,
        forward_bars=forward_bars,
        step=config["data_config"]["step"],
        sample_rate=config["data_config"]["sample_rate"],
        device=device,
    )
    test_calculator = CustomCalculator(test_data, return_expr, 1, backtester=backtester)

    pool = CustomPool(
        pool_size=config["pool_config"]["pool_size"],
        calculator=train_calculator,
        target=config["pool_config"]["target"],
        signal_converter=config["pool_config"]["signal_converter"],
        repeat=config["pool_config"]["repeat"],
        ic_single_threshold=config["pool_config"]["ic_single_threshold"],
        ic_mutual_threshold=config["pool_config"]["ic_mutual_threshold"],
        custom_condition=config["pool_config"]["custom_condition"],
        signal_ic_mutual_threshold=config["pool_config"]["signal_ic_mutual_threshold"],
    )
    env = CustomEnv(pool, tokens, max_expr_length)
    model = MaskablePPO(
        "MlpPolicy",
        env,
        n_steps=128,
        gamma=1,
        ent_coef=0.01,
        batch_size=64,
        learning_rate=5e-5,
        n_epochs=4,
        tensorboard_log=str(save_path / "logs"),
        device=device,
        verbose=1,
    )
    callback = CustomCallback(str(save_path / "out"), pool, test_calculator)
    try:
        model.learn(total_timesteps=config["steps"], callback=callback, reset_num_timesteps=False, tb_log_name=name)
    except Exception as e:
        logger.exception(e)

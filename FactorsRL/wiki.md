- Expression
	- Operand
		- Feature
		- Constant
		- Bar
	- Operator
		- UnaryOperator
			- Neg
			```python
			Class Neg:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.neg()
			
			```
			- Abs
			```python
			Class Abs:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.abs()
			
			```
			- Inv
			```python
			Class Inv:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.reciprocal()
			
			```
			- Sign
			```python
			Class Sign:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.sign()
			
			```
			- Sqrt
			```python
			Class Sqrt:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.sqrt()
			
			```
			- UnsignedSqrt
			```python
			Class UnsignedSqrt:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return Sqrt.apply(Abs.apply(value))
			
			```
			- SignedSqrt
			```python
			Class SignedSqrt:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return Sign.apply(value) * UnsignedSqrt.apply(value)
			
			```
			- Square
			```python
			Class Square:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.square()
			
			```
			- Curt
			```python
			Class Curt:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.pow(1 / 3)
			
			```
			- UnsignedCurt
			```python
			Class UnsignedCurt:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return Curt.apply(Abs.apply(value))
			
			```
			- SignedCurt
			```python
			Class SignedCurt:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return Sign.apply(value) * UnsignedCurt.apply(value)
			
			```
			- Cube
			```python
			Class Cube:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.pow(3)
			
			```
			- Log
			```python
			Class Log:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.log()
			
			```
			- UnsignedLog
			```python
			Class UnsignedLog:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return Log.apply(Abs.apply(value))
			
			```
			- SignedLog
			```python
			Class SignedLog:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return Sign.apply(value) * UnsignedLog.apply(value)
			
			```
			- Exp
			```python
			Class Exp:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.exp()
			
			```
			- TanH
			```python
			Class TanH:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.tanh()
			
			```
			- Sigmoid
			```python
			Class Sigmoid:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.sigmoid()
			
			```
			- ReLU
			```python
			Class ReLU:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.relu()
			
			```
			- GeLU
			```python
			Class GeLU:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return torch.nn.functional.gelu(value)
			
			```
			- UnaryCount
			```python
			Class UnaryCount:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingCount.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnarySum
			```python
			Class UnarySum:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingSum.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryProd
			```python
			Class UnaryProd:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingProd.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryMean
			```python
			Class UnaryMean:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingMean.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryMed
			```python
			Class UnaryMed:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingMed.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryMad
			```python
			Class UnaryMad:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingMad.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryVar
			```python
			Class UnaryVar:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingVar.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryStd
			```python
			Class UnaryStd:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingStd.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryIncv
			```python
			Class UnaryIncv:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingIncv.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnarySkew
			```python
			Class UnarySkew:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingSkew.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryKurt
			```python
			Class UnaryKurt:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingKurt.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryRank
			```python
			Class UnaryRank:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        count = UnaryCount.apply(value)
			        value = FiniteCond.apply(value, value, torch.inf).unsqueeze(-1)
			        lesser = (value < value.transpose(-1, -2)).sum(dim=-1)
			        equal = (value == value.transpose(-1, -2)).sum(dim=-1)
			        rank = lesser + (equal - 1) / 2
			        return rank / (count - 1)
			
			```
			- UnaryDescendRank
			```python
			Class UnaryDescendRank:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        count = UnaryCount.apply(value)
			        value = FiniteCond.apply(value, value, torch.inf).unsqueeze(-1)
			        greater = (value > value.transpose(-1, -2)).sum(dim=-1)
			        equal = (value == value.transpose(-1, -2)).sum(dim=-1)
			        rank = greater + (equal - 1) / 2
			        return rank / (count - 1)
			
			```
			- UnaryMax
			```python
			Class UnaryMax:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingMax.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryMin
			```python
			Class UnaryMin:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return RollingMin.apply(value).unsqueeze(-1).expand_as(value)
			
			```
			- UnaryCentral
			```python
			Class UnaryCentral:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value - value.nanmean(dim=-1, keepdim=True)
			
			```
			- UnaryZScoreNorm
			```python
			Class UnaryZScoreNorm:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return UnaryCentral.apply(value) / UnaryStd.apply(value)
			
			```
			- UnaryL1Norm
			```python
			Class UnaryL1Norm:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value / UnarySum.apply(value)
			
			```
			- UnaryL2Norm
			```python
			Class UnaryL2Norm:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value / Sqrt.apply(UnarySum.apply(value ** 2))
			
			```
			- UnaryMinMaxNorm
			```python
			Class UnaryMinMaxNorm:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        min_value = UnaryMin.apply(value)
			        return (value - min_value) / (UnaryMax.apply(value) - min_value)
			
			```
			- UnarySoftmax
			```python
			Class UnarySoftmax:
			    @staticmethod
			    def apply(value: torch.Tensor) -> torch.Tensor:
			        return value.softmax(dim=-1)
			
			```
		- BinaryOperator
			- Add
			```python
			Class Add:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return left + right
			
			```
			- Sub
			```python
			Class Sub:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return left - right
			
			```
			- Mul
			```python
			Class Mul:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return left * right
			
			```
			- Div
			```python
			Class Div:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return left / right
			
			```
			- Pow
			```python
			Class Pow:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return left.pow(right)
			
			```
			- UnsignedPow
			```python
			Class UnsignedPow:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return Pow.apply(Abs.apply(left), right)
			
			```
			- SignedPow
			```python
			Class SignedPow:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return Sign.apply(left) * UnsignedPow.apply(left, right)
			
			```
			- BinaryLog
			```python
			Class BinaryLog:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return Log.apply(left) / Log.apply(right)
			
			```
			- UnsignedBinaryLog
			```python
			Class UnsignedBinaryLog:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return Log.apply(Abs.apply(left)) / Log.apply(Abs.apply(right))
			
			```
			- SignedBinaryLog
			```python
			Class SignedBinaryLog:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return Sign.apply(left) * UnsignedBinaryLog.apply(left, right)
			
			```
			- BinaryMax
			```python
			Class BinaryMax:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return left.max(right)
			
			```
			- BinaryMin
			```python
			Class BinaryMin:
			    @staticmethod
			    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return left.min(right)
			
			```
		- TernaryOperator
			- PositiveCond
			```python
			Class PositiveCond:
			    @staticmethod
			    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return torch.where(cond > 0, left, right)
			
			```
			- NegativeCond
			```python
			Class NegativeCond:
			    @staticmethod
			    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return torch.where(cond < 0, left, right)
			
			```
			- FiniteCond
			```python
			Class FiniteCond:
			    @staticmethod
			    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
			        return torch.where(cond.isfinite(), left, right)
			
			```
		- RollingOperator
			- RollingCount
			```python
			Class RollingCount:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return FiniteCond.apply(value, 1., 0.).sum(dim=-1)
			
			```
			- RollingSum
			```python
			Class RollingSum:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return value.nansum(dim=-1)
			
			```
			- RollingProd
			```python
			Class RollingProd:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return FiniteCond.apply(value, value, 1.).prod(dim=-1)
			
			```
			- RollingMean
			```python
			Class RollingMean:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return value.nanmean(dim=-1)
			
			```
			- RollingMed
			```python
			Class RollingMed:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return value.nanmedian(dim=-1)[0]
			
			```
			- RollingMad
			```python
			Class RollingMad:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return RollingMean.apply(Abs.apply(UnaryCentral.apply(value)))
			
			```
			- RollingVar
			```python
			Class RollingVar:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return RollingSum.apply(UnaryCentral.apply(value) ** 2) / (RollingCount.apply(value) - 1)
			
			```
			- RollingStd
			```python
			Class RollingStd:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return Sqrt.apply(RollingVar.apply(value))
			
			```
			- RollingIncv
			```python
			Class RollingIncv:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return RollingMean.apply(value) / RollingStd.apply(value)
			
			```
			- RollingSkew
			```python
			Class RollingSkew:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        top = RollingMean.apply(UnaryCentral.apply(value) ** 3)
			        bottom = RollingMean.apply(UnaryCentral.apply(value) ** 2) ** 1.5
			        return top / bottom
			
			```
			- RollingKurt
			```python
			Class RollingKurt:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        top = RollingMean.apply(UnaryCentral.apply(value) ** 4)
			        bottom = RollingVar.apply(value) ** 2
			        return top / bottom - 3
			
			```
			- RollingMax
			```python
			Class RollingMax:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return FiniteCond.apply(value, value, -torch.inf).amax(dim=-1)
			
			```
			- RollingMin
			```python
			Class RollingMin:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return FiniteCond.apply(value, value, torch.inf).amin(dim=-1)
			
			```
			- Argmax
			```python
			Class Argmax:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return FiniteCond.apply(value, value, -torch.inf).argmax(dim=-1).to(value.dtype)
			
			```
			- Argmin
			```python
			Class Argmin:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return FiniteCond.apply(value, value, torch.inf).argmin(dim=-1).to(value.dtype)
			
			```
			- ArgmaxArgmin
			```python
			Class ArgmaxArgmin:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return Argmax.apply(value) - Argmin.apply(value)
			
			```
			- RollingRank
			```python
			Class RollingRank:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        if bar < 0:
			            return UnaryRank.apply(value)[..., 0]
			        else:
			            return UnaryRank.apply(value)[..., -1]
			
			```
			- RollingDescendRank
			```python
			Class RollingDescendRank:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        if bar < 0:
			            return UnaryDescendRank.apply(value)[..., 0]
			        else:
			            return UnaryDescendRank.apply(value)[..., -1]
			
			```
			- RollingCentral
			```python
			Class RollingCentral:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: int) -> torch.Tensor:
			        if bar < 0:
			            return UnaryCentral.apply(value)[..., 0]
			        else:
			            return UnaryCentral.apply(value)[..., -1]
			
			```
			- RollingZScoreNorm
			```python
			Class RollingZScoreNorm:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        if bar < 0:
			            return UnaryZScoreNorm.apply(value)[..., 0]
			        else:
			            return UnaryZScoreNorm.apply(value)[..., -1]
			
			```
			- RollingL1Norm
			```python
			Class RollingL1Norm:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        if bar < 0:
			            return UnaryL1Norm.apply(value)[..., 0]
			        else:
			            return UnaryL1Norm.apply(value)[..., -1]
			
			```
			- RollingL2Norm
			```python
			Class RollingL2Norm:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        if bar < 0:
			            return UnaryL2Norm.apply(value)[..., 0]
			        else:
			            return UnaryL2Norm.apply(value)[..., -1]
			
			```
			- RollingMinMaxNorm
			```python
			Class RollingMinMaxNorm:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        if bar < 0:
			            return UnaryMinMaxNorm.apply(value)[..., 0]
			        else:
			            return UnaryMinMaxNorm.apply(value)[..., -1]
			
			```
			- RollingSoftmax
			```python
			Class RollingSoftmax:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        if bar < 0:
			            return UnarySoftmax.apply(value)[..., 0]
			        else:
			            return UnarySoftmax.apply(value)[..., -1]
			
			```
			- DecayLinear
			```python
			Class DecayLinear:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        weights = torch.arange(value.shape[-1], 0, -1, device=value.device, dtype=value.dtype).expand_as(value)
			        weights /= RollingSum.apply(FiniteCond.apply(value, weights, 0))
			        return RollingSum.apply(value * weights)
			
			```
			- DescendDecayLinear
			```python
			Class DescendDecayLinear:
			    @staticmethod
			    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        weights = torch.arange(value.shape[-1], device=value.device, dtype=value.dtype).expand_as(value) + 1
			        weights /= RollingSum.apply(FiniteCond.apply(value, weights, 0))
			        return RollingSum.apply(value * weights)
			
			```
			- ShiftingOperator
				- Ref
				```python
				Class Ref:
				    @staticmethod
				    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
				        if bar < 0:
				            return value[..., -1]
				        else:
				            return value[..., 0]
				
				```
				- Delta
				```python
				Class Delta:
				    @staticmethod
				    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
				        return value[..., -1] - value[..., 0]
				
				```
				- Ratio
				```python
				Class Ratio:
				    @staticmethod
				    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
				        return value[..., -1] / value[..., 0]
				
				```
				- DeltaRatio
				```python
				Class DeltaRatio:
				    @staticmethod
				    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
				        return Ratio.apply(value) - 1
				
				```
		- PairRollingOperator
			- Cov
			```python
			Class Cov:
			    @staticmethod
			    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        top = RollingSum.apply(UnaryCentral.apply(left) * UnaryCentral.apply(right))
			        bottom = UnaryCount.apply(top) - 1
			        return top / bottom
			
			```
			- Corr
			```python
			Class Corr:
			    @staticmethod
			    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        return Cov.apply(left, right) / (RollingStd.apply(left) * RollingStd.apply(right))
			
			```
			- PairSlope
			```python
			Class PairSlope:
			    @staticmethod
			    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
			        left_central = UnaryCentral.apply(left)
			        top = RollingSum.apply(left_central * UnaryCentral.apply(right))
			        bottom = RollingSum.apply(left_central ** 2)
			        return top / bottom
			
			```
		- CompositeOperator
			- SMA
			```python
			Class SMA:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> torch.Tensor:
			        value, (alpha, beta) = values[0], args
			        if last_values:
			            x_last = last_values[0][-1]
			        else:
			            x_last = value[0]
			        for i, x in enumerate(value):
			            value[i] = x_last = (x_last * (beta - alpha) + x * alpha) / beta
			        return value
			
			```
			- EMA
			```python
			Class EMA:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> torch.Tensor:
			        return SMA.apply(values, [args[0], 1], last_values)
			
			```
			- RSI
			```python
			Class RSI:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> torch.Tensor:
			        value = values[0]
			        up_average = RollingMean.apply(PositiveCond.apply(value, value, torch.nan))
			        down_average = RollingMean.apply(NegativeCond.apply(value, Abs.apply(value), torch.nan))
			        rs = up_average / down_average
			        return rs / (rs + 1)
			
			```
			- RSV
			```python
			Class RSV:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> torch.Tensor:
			        close, high, low = values
			        return (close - low) / (high - low)
			
			```
			- MACD
			```python
			Class MACD:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> List[torch.Tensor]:
			        ema_values = [
			            EMA.apply(values, [2 / (bar + 1)], [last_values[i + 1]] if last_values else None)
			            for i, bar in enumerate(args)
			        ]
			        dif = ema_values[0] - ema_values[1]
			        dea = ema_values[2]
			        return [dif - dea, *ema_values]
			
			```
			- KDJ
			```python
			Class KDJ:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> List[torch.Tensor]:
			        k = SMA.apply(values, [args[1]], [last_values[1]] if last_values else None)
			        d = SMA.apply([k], [args[2]], [last_values[2]] if last_values else None)
			        return [3 * k - 2 * d, k, d]
			
			```
			- WR
			```python
			Class WR:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> torch.Tensor:
			        close, high, low = values
			        return (high - close) / (high - low)
			
			```
			- ATR
			```python
			Class ATR:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> torch.Tensor:
			        return values[0]
			
			```
			- MFI
			```python
			Class MFI:
			    @staticmethod
			    def apply(
			            values: List[torch.Tensor], args: List[Union[int, float]], last_values: Optional[List[torch.Tensor]] = None
			    ) -> torch.Tensor:
			        return values[0]
			
			```
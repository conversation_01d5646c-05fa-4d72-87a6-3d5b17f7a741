import json
import pathlib
from dataclasses import asdict
import re

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
from mindgo_api import get_price

from .backtest import backtest


def get_indicators(*paths):
    def fnc(a, b):
        b = re.search(r'^\s*([^,\s]+)\s*,\s*([^,\s]+)\s*(?:,\s*([^,\s]+)\s*)?$', b).groups()
        return f"{b[0]}({a},{','.join(i for i in b[1:] if i)})"

    expressions = []
    for path in paths:
        with open(pathlib.Path(path) / "steps.jsonl", "r") as f:
            for line in f:
                # 解析 JSON
                data = json.loads(line)

                # 获取表达式数量
                n_expr = data.get("metric/size", 0)

                # 遍历所有表达式
                for i in range(n_expr):
                    key_formula = f"expr formula/{i}"
                    key_param = f"expr param/{i}"
                    key_train_ret = f"expr train ret/{i}"
                    key_test_ret = f"expr test ret/{i}"
                    key_weight = f"expr weight/{i}"

                    expr_info = {
                        "index": i,
                        "formula": data.get(key_formula),
                        "param": ",".join(str(kk) for kk in data.get(key_param, [])),
                        "train_ret": data.get(key_train_ret),
                        "test_ret": data.get(key_test_ret),
                        "weight": data.get(key_weight),
                    }
                    expr_info['signal'] = fnc(expr_info['formula'], expr_info['param'])
                    expressions.append(expr_info)
    if expressions:
        return (
            pd.DataFrame(expressions)
            .drop_duplicates(["formula", "param"])
            .sort_values("train_ret", ascending=False)
            .reset_index(drop=True)
            .drop(["index", "weight"], axis=1)
        )
    else:
        return pd.DataFrame()


def plot(position: pd.Series, one_way):

    position = position.shift(1).fillna(0)
    symbol = position.name
    if one_way == 1:
        position = pd.Series(np.where(position > 0, position, 0), index=position.index).rename(symbol)
    elif one_way == -1:
        position = pd.Series(np.where(position < 0, position, 0), index=position.index).rename(symbol)
    data = get_price(symbol, position.index[0], position.index[-1], "1d", ["close"], fq="post")["close"]
    ddd = pd.concat([data, position], axis=1)
    plt.figure(figsize=(15, 5))
    for i in range(1, len(ddd)):
        if ddd[symbol].iat[i - 1] == 1:
            plt.plot(ddd.index[i - 1: i + 1], ddd["close"][i - 1: i + 1], color="red")
        elif ddd[symbol].iat[i - 1] == -1:
            plt.plot(ddd.index[i - 1: i + 1], ddd["close"][i - 1: i + 1], color="green")
        else:
            plt.plot(ddd.index[i - 1: i + 1], ddd["close"][i - 1: i + 1], color="black")
    ((data / data.shift(1) - 1) * position).cumsum().plot(secondary_y=True, linestyle="--")
    plt.show()


def backtest_per_symbol(x1: pd.DataFrame, y: pd.DataFrame, cost: float, one_way=1):
    tmp = {}
    for i in x1.columns.intersection(y.columns):
        tmp[i] = asdict(backtest(torch.from_numpy(x1[[i]].to_numpy()),
                        torch.from_numpy(y[[i]].to_numpy()), cost, one_way=one_way))
    return pd.DataFrame(tmp).T.sort_values('total_win_rate', ascending=False).query('count>0')

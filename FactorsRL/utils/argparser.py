from typing import List

from base.tokens import BarToken, ConstantToken, FeatureToken, OperatorToken, PAD_TOKEN, SEP_TOKEN
from base.tree import ExpressionParser


def parse_args(
    operators: List[str], features: List[str], constants: List[float], bars: List[int], expr_str, max_expr_length: int
):
    parser = ExpressionParser()
    expr = parser.parse(expr_str)
    tokens = [PAD_TOKEN, SEP_TOKEN]
    for i, feature in enumerate(features):
        features[i] = feature.lower()
        tokens.append(FeatureToken(feature))
    for constant in constants:
        tokens.append(ConstantToken(float(constant)))
        tokens.append(ConstantToken(float(-constant)))
    for bar in bars:
        if bar < 1:
            continue
        tokens.append(BarToken(int(bar)))
    specifyed_features = set()
    for operator in operators:
        operator = parser.operators[operator]
        specifyed_features.update(operator.require())
        tokens.append(OperatorToken(operator))
    if specifyed_features.difference(features):
        print(
            "Operators require these features which are not in features tokens:",
            specifyed_features.difference(features),
        )
    specifyed_features.update(features)
    backward_bars = max_expr_length // 2 * max(bars)
    forward_bars = expr.period.stop
    return tokens, expr, backward_bars, forward_bars, list(specifyed_features)

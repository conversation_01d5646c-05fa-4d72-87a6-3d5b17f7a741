{"cells": [{"cell_type": "code", "execution_count": 1, "id": "10775d87-e905-4ad5-93c1-473e124fb5dc", "metadata": {"execution": {"iopub.execute_input": "2025-07-31T08:59:54.952523Z", "iopub.status.busy": "2025-07-31T08:59:54.952246Z", "iopub.status.idle": "2025-07-31T08:59:54.966548Z", "shell.execute_reply": "2025-07-31T08:59:54.965866Z", "shell.execute_reply.started": "2025-07-31T08:59:54.952500Z"}}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "id": "d4c963e4-0dda-48dd-aca0-52438fd74b7d", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T02:30:23.190080Z", "iopub.status.busy": "2025-08-02T02:30:23.189892Z", "iopub.status.idle": "2025-08-02T02:30:28.958946Z", "shell.execute_reply": "2025-08-02T02:30:28.958228Z", "shell.execute_reply.started": "2025-08-02T02:30:23.190064Z"}}, "outputs": [], "source": ["from dual_thrust import *"]}, {"cell_type": "code", "execution_count": 5, "id": "df1d55bc-c33c-447e-a9bb-4317d71214c2", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T02:31:59.504540Z", "iopub.status.busy": "2025-08-02T02:31:59.504149Z", "iopub.status.idle": "2025-08-02T02:32:00.746056Z", "shell.execute_reply": "2025-08-02T02:32:00.745387Z", "shell.execute_reply.started": "2025-08-02T02:31:59.504520Z"}}, "outputs": [], "source": ["from stable_baselines3.ppo import PPO\n", "\n", "model=PPO.load('dt_test1/110592_steps/model.zip')"]}, {"cell_type": "code", "execution_count": 2, "id": "8062e0ec-284a-4b6c-8f07-c017a21d74d3", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T02:30:28.960039Z", "iopub.status.busy": "2025-08-02T02:30:28.959667Z", "iopub.status.idle": "2025-08-02T02:30:39.906064Z", "shell.execute_reply": "2025-08-02T02:30:39.905269Z", "shell.execute_reply.started": "2025-08-02T02:30:28.960018Z"}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "afcdff678e2941d5b5dbd5c68add3826", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-02 10:30:39.902\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mrl_strategy.env\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m284\u001b[0m - \u001b[1m初始化完成，采样日期数量为：777，采样标的数量为：1，训练区间为：2022-01-04~2024-04-02，样本量为：544，验证区间为：2024-04-03~2025-03-21\u001b[0m\n"]}], "source": ["t1, t2 = \"2022-01-01\", \"2025-05-01\"\n", "\n", "env = DTEnv(\n", "    feature_length=256,\n", "    observe_features=features,\n", "    feature_freq=5,\n", "    evaluate_days=1,\n", "    action_freq=5,\n", "    test_size=0.3,\n", "    symbols=[\"SA9999.CZCE\"],\n", "    # symbols=[\"SA9999.CZCE\", \"SC9999.INE\", \"JM9999.DCE\", \"NI9999.SHFE\"],\n", "    start_dt=t1,\n", "    end_dt=t2,\n", "    # specify_instrument_info={\"SA9999.CZCE\": {\"commission_ratio\": 1e-2}},\n", "    n_core=10,\n", ")"]}, {"cell_type": "code", "execution_count": 33, "id": "941cda74-d76c-444e-8572-2b45ca608c67", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T07:08:39.428618Z", "iopub.status.busy": "2025-08-02T07:08:39.428153Z", "iopub.status.idle": "2025-08-02T07:08:47.245632Z", "shell.execute_reply": "2025-08-02T07:08:47.244968Z", "shell.execute_reply.started": "2025-08-02T07:08:39.428599Z"}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54fe310d55484a019801aed8b63e8479", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-02 15:08:47.240\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mrl_strategy.env\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m284\u001b[0m - \u001b[1m初始化完成，采样日期数量为：136，采样标的数量为：1，训练区间为：None~None，样本量为：0，验证区间为：2025-01-02~2025-07-25\u001b[0m\n"]}], "source": ["test_env = env.create_test_env(\n", "    symbols=[\"AP9999.CZCE\"],\n", "    # symbols=[\"SA9999.CZCE\", \"SC9999.INE\", \"JM9999.DCE\", \"NI9999.SHFE\"],\n", "    start_dt='20250101',\n", "    end_dt='20250731',\n", ")"]}, {"cell_type": "code", "execution_count": 34, "id": "b9af0106-af2c-4610-b0d9-256fc4a5bfb3", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T07:08:59.144229Z", "iopub.status.busy": "2025-08-02T07:08:59.143774Z", "iopub.status.idle": "2025-08-02T07:09:55.826543Z", "shell.execute_reply": "2025-08-02T07:09:55.825774Z", "shell.execute_reply.started": "2025-08-02T07:08:59.144208Z"}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-01-03 11:21:00 base: 5111.2753636779125, lower: 4926.11436010784, price: 4914.909982474467, fall: -3.84%, action: [3 0 1 3 2]\n", "2025-01-03 11:21:00 - SELL - AP9999.CZCE price: 4914.909982474467, vol: -2.034625259802904, cash: 19990.0\n", "2025-01-03 14:43:00 - BUY - AP9999.CZCE price: 4954.616217644282, vol: 2.034625259802904, cash: 9899.13190364277\n", "2025-01-13 11:24:00 base: 4810.229907935866, upper: 4889.259754554768, price: 4922.129297959888, rise: 2.33%, action: [3 0 1 3 2]\n", "2025-01-13 11:24:00 - BUY - AP9999.CZCE price: 4922.129297959888, vol: 2.0316410631766164, cash: -10.0\n", "2025-01-13 15:00:00 - SELL - AP9999.CZCE price: 4937.289860479272, vol: -2.0316410631766164, cash: 10010.77002053388\n", "2025-01-24 14:08:00 base: 4887.476583629868, upper: 4957.182684299349, price: 4972.6645063578335, rise: 1.74%, action: [3 0 1 3 2]\n", "2025-01-24 14:08:00 - BUY - AP9999.CZCE price: 4972.6645063578335, vol: 2.0109943044044964, cash: -10.0\n", "2025-01-24 15:00:00 - SELL - AP9999.CZCE price: 4974.108369454918, vol: -2.0109943044044964, cash: 9982.900696864112\n", "2025-01-27 09:15:00 base: 4993.600521265554, upper: 5067.302513056215, price: 5065.793676119762, rise: 1.45%, action: [3 0 1 3 2]\n", "2025-01-27 09:15:00 - BUY - AP9999.CZCE price: 5065.793676119762, vol: 1.9740243364312628, cash: -10.0\n", "2025-01-27 15:00:00 - SELL - AP9999.CZCE price: 5158.200914333148, vol: -1.9740243364312628, cash: 10162.23172295853\n", "2025-02-05 14:41:00 base: 5160.366708978774, lower: 4895.56221697354, price: 4893.252036018205, fall: -5.18%, action: [3 0 1 3 2]\n", "2025-02-05 14:41:00 - SELL - AP9999.CZCE price: 4893.252036018205, vol: -2.0436306829061923, cash: 19990.0\n", "2025-02-05 15:00:00 - BUY - AP9999.CZCE price: 4901.193283052168, vol: 2.0436306829061923, cash: 9963.754794924755\n", "2025-02-12 11:16:00 base: 5089.617417221651, upper: 5170.423215449966, price: 5180.5807923379525, rise: 1.79%, action: [3 0 1 3 2]\n", "2025-02-12 11:16:00 - BUY - AP9999.CZCE price: 5180.5807923379525, vol: 1.9302855028899344, cash: -10.0\n", "2025-02-12 15:00:00 - SELL - AP9999.CZCE price: 5152.425461944811, vol: -1.9302855028899344, cash: 9925.706521739128\n", "2025-02-26 14:15:00 base: 5137.264899425428, upper: 5245.745943567103, price: 5248.442357900908, rise: 2.16%, action: [3 0 1 3 2]\n", "2025-02-26 14:15:00 - BUY - AP9999.CZCE price: 5248.442357900908, vol: 1.9053272034027744, cash: -10.0\n", "2025-02-26 15:00:00 - SELL - AP9999.CZCE price: 5240.501110866945, vol: -1.9053272034027744, cash: 9964.884456671252\n", "2025-03-06 14:56:00 base: 5180.5807923379525, upper: 5262.422562338425, price: 5260.715194226123, rise: 1.55%, action: [3 0 1 3 2]\n", "2025-03-06 14:56:00 - BUY - AP9999.CZCE price: 5260.715194226123, vol: 1.9008822243362387, cash: -10.0\n", "2025-03-06 15:00:00 - SELL - AP9999.CZCE price: 5260.715194226123, vol: -1.9008822243362387, cash: 9980.0\n", "2025-03-10 10:46:00 base: 5197.907149502962, upper: 5275.901024349706, price: 5282.373140682385, rise: 1.63%, action: [3 0 1 3 2]\n", "2025-03-10 10:46:00 - BUY - AP9999.CZCE price: 5282.373140682385, vol: 1.8930885292795094, cash: -10.0\n", "2025-03-10 15:00:00 - SELL - AP9999.CZCE price: 5326.410965143452, vol: -1.8930885292795094, cash: 10063.284132841329\n", "2025-03-26 09:44:00 base: 5472.963069497494, upper: 5558.504738684244, price: 5587.750185715684, rise: 2.10%, action: [3 0 1 3 2]\n", "2025-03-26 09:44:00 - BUY - AP9999.CZCE price: 5587.750185715684, vol: 1.7896290398886527, cash: -10.0\n", "2025-03-26 15:00:00 - SELL - AP9999.CZCE price: 5581.974733327348, vol: -1.7896290398886527, cash: 9969.674418604653\n", "2025-04-07 10:02:00 base: 5574.033486293385, upper: 5647.291490181692, price: 5649.114367341761, rise: 1.35%, action: [3 0 1 3 2]\n", "2025-04-07 10:02:00 - BUY - AP9999.CZCE price: 5649.114367341761, vol: 1.7701889800304371, cash: -10.0\n", "2025-04-07 15:00:00 - SELL - AP9999.CZCE price: 5693.87412335137, vol: -1.7701889800304371, cash: 10059.153993610224\n", "2025-04-21 13:32:00 base: 5662.508089027874, upper: 5777.0839093169525, price: 5812.333205780363, rise: 2.65%, action: [3 0 1 3 2]\n", "2025-04-21 13:32:00 - BUY - AP9999.CZCE price: 5812.333205780363, vol: 1.7204794780270003, cash: -10.0\n", "2025-04-21 15:00:00 - SELL - AP9999.CZCE price: 5818.20870055497, vol: -1.7204794780270003, cash: 9990.098559514785\n", "2025-05-16 10:03:00 base: 5674.993515423915, upper: 5726.936561415679, price: 5744.765015872378, rise: 1.23%, action: [3 0 1 3 2]\n", "2025-05-16 10:03:00 - BUY - AP9999.CZCE price: 5744.765015872378, vol: 1.7407152376765123, cash: -10.0\n", "2025-05-16 15:00:00 - SELL - AP9999.CZCE price: 5699.22993136917, vol: -1.7407152376765123, cash: 9900.815648171822\n", "2025-06-04 14:18:00 base: 5632.396178308011, upper: 5684.790902960573, price: 5691.885562900911, rise: 1.06%, action: [3 0 1 3 2]\n", "2025-06-04 14:18:00 - BUY - AP9999.CZCE price: 5691.885562900911, vol: 1.7568870437555717, cash: -10.0\n", "2025-06-04 15:00:00 - SELL - AP9999.CZCE price: 5691.885562900911, vol: -1.7568870437555717, cash: 9980.0\n", "2025-07-09 09:17:00 base: 5635.333925695315, upper: 5687.578090794277, price: 5689.682252360433, rise: 0.96%, action: [3 0 1 3 2]\n", "2025-07-09 09:17:00 - BUY - AP9999.CZCE price: 5689.682252360433, vol: 1.7575673924236066, cash: -10.0\n", "2025-07-09 15:00:00 - SELL - AP9999.CZCE price: 5686.74450497313, vol: -1.7575673924236066, cash: 9974.841874273912\n", "2025-07-21 14:19:00 base: 5801.316653077974, upper: 5839.257660585001, price: 5839.507369112922, rise: 0.66%, action: [3 0 1 3 2]\n", "2025-07-21 14:19:00 - BUY - AP9999.CZCE price: 5839.507369112922, vol: 1.7124732221237178, cash: -10.0\n", "2025-07-21 15:00:00 - SELL - AP9999.CZCE price: 5818.943137401796, vol: -1.7124732221237178, cash: 9944.819519557286\n"]}], "source": ["result = test_env.test_model(model)"]}, {"cell_type": "code", "execution_count": 35, "id": "2f35c408-de44-4e81-bdca-8ab089440033", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T07:28:55.347493Z", "iopub.status.busy": "2025-08-02T07:28:55.347126Z", "iopub.status.idle": "2025-08-02T07:28:55.351596Z", "shell.execute_reply": "2025-08-02T07:28:55.350818Z", "shell.execute_reply.started": "2025-08-02T07:28:55.347476Z"}}, "outputs": [], "source": ["result = [i for i in result if i]"]}, {"cell_type": "code", "execution_count": 36, "id": "e811276a-3b6c-45be-b0b1-6ca3cc2d1250", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T07:28:55.718326Z", "iopub.status.busy": "2025-08-02T07:28:55.717914Z", "iopub.status.idle": "2025-08-02T07:28:55.722031Z", "shell.execute_reply": "2025-08-02T07:28:55.721389Z", "shell.execute_reply.started": "2025-08-02T07:28:55.718305Z"}, "scrolled": true}, "outputs": [], "source": ["a = {i['end_time']:i['raw_profit_rate'] for i in result}"]}, {"cell_type": "code", "execution_count": 37, "id": "1e589c48-b38f-4396-9bef-9e25717067d3", "metadata": {"execution": {"iopub.execute_input": "2025-08-02T07:28:56.237543Z", "iopub.status.busy": "2025-08-02T07:28:56.237077Z", "iopub.status.idle": "2025-08-02T07:28:56.397960Z", "shell.execute_reply": "2025-08-02T07:28:56.397384Z", "shell.execute_reply.started": "2025-08-02T07:28:56.237521Z"}}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.Series(a).cumsum().plot(figsize=(18,5))"]}, {"cell_type": "code", "execution_count": null, "id": "2a4fff69-9dff-4578-a567-4a1aee5cac2f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cce07709-5435-4de9-a1e3-f6f2dfc3c134", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}
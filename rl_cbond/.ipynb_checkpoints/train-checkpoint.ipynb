{"cells": [{"cell_type": "code", "execution_count": null, "id": "fe1b64c4-7948-4fc8-b235-92c29aa7bf68", "metadata": {"scrolled": true}, "outputs": [], "source": ["%run dual_thrust.py"]}, {"cell_type": "code", "execution_count": null, "id": "2a4fff69-9dff-4578-a567-4a1aee5cac2f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}
import pickle
import warnings
from dataclasses import dataclass
from typing import Class<PERSON><PERSON>, Dict, List, Tuple, Type

import datetype
import gymnasium as gym
import matplotlib.pyplot as plt
import mplfinance as mpf
import numpy as np
import numpy.typing as npt
import pandas as pd
from aichemy.ml.exp_base import MetricsTracker
from aichemy.utils import rolling_window
from rl_strategy.base import BackTest, InstrumentDataHandler, InstrumentInfo
from rl_strategy.callback import Callback
from rl_strategy.env import BackTestEngine, StrategyEnv
from rl_strategy.model.transformer import Transformer
from rl_strategy.utils import RunEnv, run_env, time_cos_encoding, time_sin_encoding
from stable_baselines3.ppo import PPO

if run_env == RunEnv.SUPERMIND:
    from mindgo_api import get_price  # type: ignore

warnings.filterwarnings("ignore")


class DTEnv(StrategyEnv):
    def _bind_data_handler_type(self) -> Type[InstrumentDataHandler]:
        return InstrumentDataHandlerForDT

    def _define_action_space(self) -> gym.Space:
        return gym.spaces.MultiDiscrete([5, 2, 2, 5, 3])
        # return gym.spaces.MultiDiscrete([10, 10, 10, 2, 2])

    def _define_observation_space(self) -> gym.Space:
        return gym.spaces.Dict(
            {
                "price": gym.spaces.Box(
                    low=-np.inf, high=np.inf, shape=(self.feature_length, len(self.observe_features) - 6)
                ),
                "time": gym.spaces.Box(low=-np.inf, high=np.inf, shape=(self.feature_length, 6)),
                "other": gym.spaces.Box(low=-np.inf, high=np.inf, shape=(10,)),
            }
        )

    def _define_record(self):
        best_reward_mean = -np.inf
        best_reward_median = -np.inf
        best_raw_profit_rate_mean = -np.inf
        best_raw_profit_rate_median = -np.inf
        best_raw_profit_rate_sum = -np.inf

        def fnc(infos_: List[Dict[str, float]]):
            nonlocal \
                best_reward_mean, \
                best_reward_median, \
                best_raw_profit_rate_mean, \
                best_raw_profit_rate_median, \
                best_raw_profit_rate_sum

            infos = pd.DataFrame(infos_)
            infos = infos.loc[(infos["count"] != 0) & (~pd.isna(infos["count"]))]
            res = {
                "reward_mean": infos["reward"].mean(),
                "reward_median": infos["reward"].median(),
                "raw_profit_rate_mean": infos["raw_profit_rate"].mean(),
                "raw_profit_rate_median": infos["raw_profit_rate"].median(),
                "raw_profit_rate_sum": infos["raw_profit_rate"].sum(),
                "count": infos["count"].sum(),
                "win_rate": (infos["raw_profit_rate"] > 0).mean(),
            }
            flag = 0
            if res["reward_mean"] > best_reward_mean:
                flag = 1
                best_reward_mean = res["reward_mean"]
            if res["reward_median"] > best_reward_median:
                flag = 1
                best_reward_median = res["reward_median"]
            if res["raw_profit_rate_mean"] > best_raw_profit_rate_mean:
                flag = 1
                best_raw_profit_rate_mean = res["raw_profit_rate_mean"]
            if res["raw_profit_rate_median"] > best_raw_profit_rate_median:
                flag = 1
                best_raw_profit_rate_median = res["raw_profit_rate_median"]
            if res["raw_profit_rate_sum"] > best_raw_profit_rate_sum:
                flag = 1
                best_raw_profit_rate_sum = res["raw_profit_rate_sum"]
            return bool(flag) and res["count"] > 0, res

        return fnc

    def on_rollout_start(self):
        DTBackTest.cache.clear()


@dataclass
class DTStrategyParams:
    trade_value: float
    init_cash: float


class InstrumentDataHandlerForDT(InstrumentDataHandler):
    dt_params: DTStrategyParams

    # 策略数据
    price: npt.NDArray[np.float64]
    open_: npt.NDArray[np.float64]
    timestamp: npt.NDArray[np.int64]
    tr_daily: pd.Series
    std_daily: pd.Series
    rsmp_mdata: pd.DataFrame

    def __init__(self, symbol, start_dt, end_dt, instrument_info, **config):
        super().__init__(
            symbol,
            start_dt,
            end_dt,
            required=["minute", "daily"],
            forward_days=config.get("forward_days", 60),
            instrument_info=instrument_info,
        )

        self.dt_params = DTStrategyParams(
            trade_value=config.get("trade_value", 10000.0), init_cash=config.get("init_cash", 10000.0)
        )

        self.timestamp = self.minute_price_df.index.astype(np.int64).values
        self.price = self.minute_price_df["close"].bfill().to_numpy()
        self.open_ = self.minute_price_df["open"].to_numpy()
        self.high = self.minute_price_df["high"].to_numpy()
        self.low = self.minute_price_df["low"].to_numpy()
        self.tr_daily = np.maximum(
            np.maximum(
                np.abs(self.daily_price_df["high"] - self.daily_price_df["close"].shift(1)),
                np.abs(self.daily_price_df["low"] - self.daily_price_df["close"].shift(1)),
            ),
            np.abs(self.daily_price_df["high"] - self.daily_price_df["low"]),
        )
        self.std_daily = self.daily_price_df["close"].pct_change().rolling(20).std()
        rsmp_values = self.minute_price_df.resample(f"{config['feature_freq']}min", closed="right", label="right").agg(
            {
                "open": "first",  # 开盘价取第一个值
                "high": "max",  # 最高价取最大值
                "low": "min",  # 最低价取最小值
                "close": "last",  # 收盘价取最后一个值
                "volume": "sum",  # 成交量求和
                "open_interest": "sum",  # 成交量求和
                "sin_h": "last",
                "cos_h": "last",
                "sin_m": "last",
                "cos_m": "last",
                "sin_d": "last",
                "cos_d": "last",
            }
        )
        self.rsmp_mdata = rsmp_values.loc[rsmp_values.index.isin(self.minute_price_df.index)]
        # from quant_factors import generate_quant_factors

        # self.rsmp_mdata = generate_quant_factors(self.rsmp_mdata, 22)

    def _preprocess_minute_data(self):
        tmp = self.minute_price_df.index.hour
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        hmax = 24
        self.minute_price_df["sin_h"] = tmp.apply(lambda x: time_sin_encoding(x, hmax))
        self.minute_price_df["cos_h"] = tmp.apply(lambda x: time_cos_encoding(x, hmax))

        tmp = self.minute_price_df.index.minute
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        mmax = 60
        self.minute_price_df["sin_m"] = tmp.apply(lambda x: time_sin_encoding(x, mmax))
        self.minute_price_df["cos_m"] = tmp.apply(lambda x: time_cos_encoding(x, mmax))

        tmp = self.minute_price_df.index.tz_localize("Asia/Shanghai").day_of_week
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        dmax = 7
        self.minute_price_df["sin_d"] = tmp.apply(lambda x: time_sin_encoding(x, dmax))
        self.minute_price_df["cos_d"] = tmp.apply(lambda x: time_cos_encoding(x, dmax))

        self.minute_price_df.loc[self.minute_price_df["close"] == 0, "close"] = np.nan
        self.minute_price_df["close"] = self.minute_price_df["close"].ffill()  # 用前一个有效值填充

    def _preprocess_daily_data(self):
        tmp = self.daily_price_df.index.tz_localize("Asia/Shanghai").day_of_week
        tmp = pd.Series(tmp, index=self.daily_price_df.index).rank(method="dense") - 1
        dmax = tmp.max() + 1
        self.daily_price_df["sin_d"] = tmp.apply(lambda x: time_sin_encoding(x, dmax))
        self.daily_price_df["cos_d"] = tmp.apply(lambda x: time_cos_encoding(x, dmax))

    def _time2trade_day(self) -> Tuple[pd.DatetimeIndex, np.ndarray]:
        b: pd.DatetimeIndex = self.minute_price_df.index  # type: ignore
        d = b.indexer_between_time("09:00:00", "15:00:00")
        c = pd.Series([pd.NaT] * len(b))
        c[d] = b.date[d]
        return b, pd.DatetimeIndex(c.bfill()).date  # type: ignore

    @staticmethod
    def check(engine: BackTestEngine):
        tmp = []
        for data_handler in engine.data_handlers.values():
            tmp.append(pd.Series(1, index=data_handler.total_trade_dates, name=data_handler.symbol))
        if len(tmp) == 0:
            raise RuntimeError("No data found")
        elif len(tmp) == 1:
            tmp = tmp[0].to_frame().sort_index()
        else:
            tmp = pd.concat(tmp, axis=1).sort_index()
        df1 = tmp.rolling(22, min_periods=1).sum().shift(1) == 22
        return df1

    def _bind_backtest_type(self):
        return DTBackTest


class DTBackTest(BackTest):
    data_handler: InstrumentDataHandlerForDT
    price: npt.NDArray[np.float64]
    timestamp: npt.NDArray[np.int64]
    tr: npt.NDArray[np.float64]

    instrument_info: InstrumentInfo
    dt_params: DTStrategyParams

    base_price: float
    prev_close: float
    atr: Tuple[float, float]
    holding: float
    cash: float

    init_idx: int

    cache: ClassVar[Dict[str, List]] = {}

    # baseline: ClassVar = pickle.load(open("baseline_SA9999.CZCE", "rb"))

    def __init__(
        self,
        data_handler: InstrumentDataHandlerForDT,
        begin_time: datetype.NaiveDateTime,
        end_time: datetype.NaiveDateTime,
        feature_length: int,
        observe_features: List[str],
        feature_freq: int,
        evaluate_days: int,
        action_freq: int,
    ):
        super().__init__(
            data_handler,
            begin_time,
            end_time,
            feature_length,
            observe_features,
            feature_freq,
            evaluate_days,
            action_freq,
        )

        idx1: int = data_handler.minute_price_df.index.get_loc(begin_time)  # type: ignore
        idx2: int = data_handler.minute_price_df.index.get_loc(end_time) + 1  # type: ignore

        self.timestamp = data_handler.timestamp[idx1:idx2]
        self.price = data_handler.price[idx1:idx2]
        self.high = data_handler.high[idx1:idx2]
        self.low = data_handler.low[idx1:idx2]
        tmp: int = data_handler.tr_daily.index.get_loc(end_time.normalize().replace(hour=15))
        self.tr = data_handler.tr_daily.iloc[tmp - 20 : tmp].to_numpy()
        self.std = data_handler.std_daily.iat[tmp]

        self.instrument_info = data_handler.instrument_info
        self.dt_params = data_handler.dt_params

        self.base_price = data_handler.open_[idx1]
        self.prev_close = data_handler.price[idx1 - 1]
        self.holding = 0.0
        self.cash = self.dt_params.init_cash

        self.result = np.full((len(self.price), 13), 0.0, dtype=np.float64)
        self.init_idx = idx1
        self.idx = 0

        self.atr = np.inf, np.inf

    def get_observation(self):
        now = self.data_handler.minute_price_df.index[self.init_idx + self.idx]
        bar_end = np.searchsorted(self.data_handler.rsmp_mdata.index, now, "right")
        values = self.data_handler.rsmp_mdata.iloc[bar_end - self.feature_length : bar_end][self.observe_features]

        tmp = np.searchsorted(self.data_handler.minute_price_df.index, now, "right")
        last_bar = self.data_handler.minute_price_df.iloc[tmp - 2]
        current_data = self.data_handler.minute_price_df.iloc[tmp - 1]
        states = (
            # *((0, 0, 1) if self.holding == 0 else ((0, 1, 0) if self.holding > 0 else (1, 0, 0))),
            *(
                (current_data[["open", "high", "low", "close"]] - values[["open", "high", "low", "close"]].mean(axis=0))
                / values[["open", "high", "low", "close"]].std(axis=0)
                * self.feature_freq
            ),
            current_data.at["sin_h"],
            current_data.at["cos_h"],
            current_data.at["sin_m"],
            current_data.at["cos_m"],
            current_data.at["sin_d"],
            current_data.at["cos_d"],
            *(
                (
                    current_data[["volume", "open_interest"]] * self.feature_freq
                    - (values[["volume", "open_interest"]].mean(axis=0))
                )
                / values[["volume", "open_interest"]].std(axis=0)
            ),
        )
        values = values.values
        price = values[:, :-6]
        price[:, :4] /= self.base_price
        price = (price - np.nanmean(price, axis=0)) / np.nanstd(price, axis=0)
        return {
            "price": price,
            "time": values[:, -6:],
            "other": (self.tr[-10:] / self.base_price).flatten(),
            # "other": np.array(states),
        }

    def _step(self, action=None):
        def fnc(x1, x2):
            x1min = (x1 + 1) ** 3
            x1max = (x1 + 2) ** 3
            return x1min + (x1max - x1min) / 5 * x2

        # action = np.array([0, 0, 0, 0, 2])
        # self.actions.append(action)
        if action is not None:
            action1, action2, action3, action4, action5 = action
            # print(f"{pd.Timestamp(self.timestamp[self.idx])} action: {action}")
            tmp = self.tr[-action1 - 1 :].mean()
            self.atr = tmp * (fnc(action2, action4) + 3) / 10, tmp * (fnc(action3, action4) + 3) / 10
            # self.atr = tmp * 21 / 10, tmp * 6 / 10

        i = self.idx

        upper = self.base_price + self.atr[0]
        lower = self.base_price - self.atr[1]

        volume = 0
        if i == self.price.shape[0] - 1:
            if self.holding != 0:
                volume = -self.holding
        else:
            if self.holding == 0 and np.abs(self.result[:, 5]).sum() == 0:
                if i > 2 and (self.high / 2 + self.low / 2 >= upper)[i - 2 : i + 1].all():
                    print(
                        f"{pd.Timestamp(self.timestamp[i])} base: {self.base_price}, upper: {upper}, price: {self.price[i]}, rise: {self.price[i] / self.base_price - 1:.2%}, action: {self.actions[-1]}"
                    )
                    volume = self.dt_params.trade_value / self.price[i]
                    self.hc = self.price[i]
                    self.cost = self.price[i]
                elif i > 2 and (self.high / 2 + self.low / 2 <= lower)[i - 2 : i + 1].all():
                    print(
                        f"{pd.Timestamp(self.timestamp[i])} base: {self.base_price}, lower: {lower}, price: {self.price[i]}, fall: {self.price[i] / self.base_price - 1:.2%}, action: {self.actions[-1]}"
                    )
                    volume = -self.dt_params.trade_value / self.price[i]
                    self.lc = self.price[i]
                    self.cost = self.price[i]
            elif self.holding > 0:
                self.hc = max(self.hc, self.price[i])
                if self.high[i] / 2 + self.low[i] / 2 < self.hc * (
                    1 - (8 + (25 - 8) / 4 * self.actions[-1][-1]) / 1000
                ):
                    volume = -self.holding
            elif self.holding < 0:
                self.lc = min(self.lc, self.price[i])
                if self.high[i] / 2 + self.low[i] / 2 > self.lc * (
                    1 + (8 + (25 - 8) / 4 * self.actions[-1][-1]) / 1000
                ):
                    volume = -self.holding

        commission = 0
        trade_price = 0
        amount = 0

        if volume > 0:
            trade_price = self.price[i]
            amount = volume * trade_price
            self.holding += volume
            self.cash = self.cash - amount - self.instrument_info.commission_ratio * amount
            commission = self.instrument_info.commission_ratio * amount
            print(
                f"{pd.Timestamp(self.timestamp[i])} - BUY - {self.data_handler.symbol} price: {self.price[i]}, vol: {volume}, cash: {self.cash}"
            )
        elif volume < 0:
            trade_price = self.price[i]
            amount = -volume * trade_price
            self.holding += volume
            self.cash = self.cash + amount - self.instrument_info.commission_ratio * amount
            commission = self.instrument_info.commission_ratio * amount
            print(
                f"{pd.Timestamp(self.timestamp[i])} - SELL - {self.data_handler.symbol} price: {self.price[i]}, vol: {volume}, cash: {self.cash}"
            )

        self.result[i, 0] = self.timestamp[i]  # 时间戳
        self.result[i, 1] = self.holding  # 持股数量
        self.result[i, 2] = self.cash  # 现金
        self.result[i, 3] = self.holding * self.price[i]  # 持仓市值
        self.result[i, 4] = self.price[i]  # 成交价
        self.result[i, 5] = volume  # 成交量
        self.result[i, 6] = amount  # 成交额
        self.result[i, 7] = commission  # 交易成本：手续费、印花税

        self.idx += 1

        if np.sum(np.abs(self.result[:, 5])) != 0:
            return False
        else:
            return True

    def get_reward(self) -> Tuple[float, Dict]:
        if not self.is_done():
            return 0.0, {}

        init, final = self.dt_params.init_cash, self.result[-1, 2] + self.result[-1, 3]
        count = 0 if init == final else 1
        raw_profit_rate = final / init - 1

        # clip_raw_profit_rate = raw_profit_rate
        # if count:
        #     self.cache.setdefault(self.data_handler.symbol, []).append(raw_profit_rate)
        #     if len(self.cache[self.data_handler.symbol]) > 20:
        #         t1, t2 = np.percentile(self.cache[self.data_handler.symbol], [15, 85])
        #         clip_raw_profit_rate = np.clip(raw_profit_rate, t1, t2)

        # profit_rate = clip_raw_profit_rate + (count - 1) * 1e-4 * 6 * 5 * 0
        reward = raw_profit_rate
        # reward = profit_rate
        # reward = profit_rate / self.std
        return reward, {
            "symbol": self.data_handler.symbol,
            "begin_time": self.begin_time,
            "end_time": self.end_time,
            "reward": reward,
            "init": init,
            "final": final,
            # "actions": self.actions,
            "details": self.result[self.result[:, 5] != 0, :],
            "count": count,
            "profit_rate": 0,
            "raw_profit_rate": raw_profit_rate,
        }


def analyse_result(res, plot_every=True):
    if len(res[1]) == 0:
        print("没有交易记录")
        return

    print(f"累积超额收益：{res[0]}")
    a = res[1]
    b = pd.DataFrame([{"yield": i["details"]["yield_"], "base_yield": i["details"]["base_yield_"]} for i in a])
    b = b.cumsum()
    b.plot()
    (b["yield"] - b["base_yield"]).plot(linestyle="--")
    plt.show()

    if not plot_every:
        return
    test_symbol = a[1]["details"]["symbol"]
    for test in a:
        begin_time = test["begin_time"]
        end_time = test["end_time"]

        data = get_price(test_symbol, begin_time, end_time, "1m", ["open", "high", "low", "close"], fq="post")

        signal = pd.DataFrame(test["details"]["trades"])
        signal["dt"] = pd.to_datetime(signal["dt"])

        buy_signal = signal[["dt", "touch"]].loc[signal["side"] == 1].set_index("dt")["touch"]
        buy_signal.index.rename(None, inplace=True)
        buy_signal = buy_signal.reindex(index=data.index)
        sell_signal = signal[["dt", "touch"]].loc[signal["side"] == -1].set_index("dt")["touch"]
        sell_signal.index.rename(None, inplace=True)
        sell_signal = sell_signal.reindex(index=data.index)

        # 绘制K线图
        apds = []
        if np.isfinite(buy_signal).sum() > 0:
            apds.append(
                mpf.make_addplot(buy_signal, scatter=True, markersize=100, marker="^", color="red", label="Buy")
            )
        if np.isfinite(sell_signal).sum() > 0:
            apds.append(
                mpf.make_addplot(sell_signal, scatter=True, markersize=100, marker="v", color="green", label="Sell")
            )
        apds.append(mpf.make_addplot(test["details"]["adjusted_base_price"], color="grey", label="adjusted_base_price"))
        apds.append(mpf.make_addplot(pd.Series([test["details"]["init_price"]] * len(data), index=data.index)))

        # 绘制K线图并添加买卖点
        mpf.plot(
            data,
            type="candle",
            addplot=apds,
            volume=False,
            figsize=(18, 5),
            style="yahoo",
            datetime_format="%Y-%m-%d %H:%M",  # 设置x轴时间格式为年-月-日 时:分
        )
        print("收益{:.4%}, 基准收益{:.4%}".format(test["details"]["yield_"], test["details"]["base_yield_"]))

        # 展示图表
        plt.show()


features = [
    "open",
    "high",
    "low",
    "close",
    "volume",
    "open_interest",
    "sin_h",
    "cos_h",
    "sin_m",
    "cos_m",
    "sin_d",
    "cos_d",
]


def train(t1, t2):
    # version = f"checkpoints/{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}_simple_量价因子_收益reward_90D_10D_0.5/{t1}_{t2}"
    version = "dt_test3"
    print(t1, t2)

    env = DTEnv(
        feature_length=256,
        observe_features=features,
        feature_freq=5,
        evaluate_days=1,
        action_freq=5,
        test_size=0.3,
        symbols=["SA9999.CZCE", "JD9999.DCE"],
        # symbols=["SA9999.CZCE", "SC9999.INE", "JM9999.DCE", "NI9999.SHFE"],
        start_dt=t1,
        end_dt=t2,
        # specify_instrument_info={"SA9999.CZCE": {"commission_ratio": 1e-2}},
        n_core=10,
    )
    # env.engine.post_init(normalize_window=252 * 16)
    callback = Callback(env, version)
    model = PPO(
        "MultiInputPolicy",
        env,
        n_steps=2048,
        # n_steps=env.engine.num_samples,
        learning_rate=1e-5,
        gamma=1.0,
        ent_coef=0.1,
        batch_size=512,
        # batch_size=env.engine.num_samples,
        n_epochs=2,
        policy_kwargs=dict(features_extractor_class=Transformer, features_extractor_kwargs=dict()),
        tensorboard_log="/workspace/tb_log_dir",
        verbose=1,
    )
    model.learn(env.engine.num_samples * 20000, reset_num_timesteps=False, callback=callback, tb_log_name=version)


if __name__ == "__main__":
    train("2022-01-01", "2025-05-01")

import warnings

import numpy as np
import pandas as pd

warnings.filterwarnings("ignore")


def calculate_sma(data, window=14):
    data[f"sma_{window}"] = data["close"].rolling(window=window).mean()
    return data


def calculate_ema(data, window=14):
    data[f"ema_{window}"] = data["close"].ewm(span=window, adjust=False).mean()
    return data


def calculate_rsi(data, window=14):
    delta = data["close"].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    # 添加一个小的常数以避免除以零
    rs = gain / (loss + 1e-10)
    data["rsi"] = 100 - (100 / (1 + rs))
    return data


def calculate_macd(data, short_window=12, long_window=26, signal_window=9):
    short_ema = data["close"].ewm(span=short_window, adjust=False).mean()
    long_ema = data["close"].ewm(span=long_window, adjust=False).mean()
    data["macd"] = short_ema - long_ema
    data["macd_signal"] = data["macd"].ewm(span=signal_window, adjust=False).mean()
    return data


def calculate_bollinger_bands(data, window=20, num_std=2):
    data["bollinger_mid"] = data["close"].rolling(window=window).mean()
    data["bollinger_std"] = data["close"].rolling(window=window).std()
    data["bollinger_upper"] = data["bollinger_mid"] + (data["bollinger_std"] * num_std)
    data["bollinger_lower"] = data["bollinger_mid"] - (data["bollinger_std"] * num_std)
    return data


def calculate_atr(data, window=14):
    high_low = data["high"] - data["low"]
    high_close = np.abs(data["high"] - data["close"].shift())
    low_close = np.abs(data["low"] - data["close"].shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = np.max(ranges, axis=1)
    data["atr"] = true_range.rolling(window=window).mean()
    return data


def calculate_volatility(data, window=20):
    data["volatility"] = data["close"].pct_change().rolling(window=window).std()
    return data


def calculate_kdj(data, rsv_window=9, k_window=3, d_window=3):
    low_min = data["low"].rolling(window=rsv_window).min()
    high_max = data["high"].rolling(window=rsv_window).max()
    data["rsv"] = (data["close"] - low_min) / (high_max - low_min + 1e-10) * 100
    data["k"] = data["rsv"].rolling(window=k_window).mean()
    data["d"] = data["k"].rolling(window=d_window).mean()
    data["j"] = 3 * data["k"] - 2 * data["d"]
    return data


def calculate_roc(data, window=12):
    data["roc"] = data["close"].pct_change(periods=window)
    return data


def calculate_obv(data):
    data["obv"] = np.where(
        data["close"] > data["close"].shift(1),
        data["volume"],
        np.where(data["close"] < data["close"].shift(1), -data["volume"], 0),
    ).cumsum()
    return data


def calculate_cci(data, window=20):
    typical_price = (data["high"] + data["low"] + data["close"]) / 3
    data["cci"] = (typical_price - typical_price.rolling(window=window).mean()) / (
        0.015 * typical_price.rolling(window=window).std() + 1e-10
    )
    return data


def calculate_dmi(data, window=14):
    data["tr"] = np.maximum(
        np.maximum(data["high"] - data["low"], np.abs(data["high"] - data["close"].shift(1))),
        np.abs(data["low"] - data["close"].shift(1)),
    )
    data["tr_sum"] = data["tr"].rolling(window=window).sum()
    data["up_move"] = data["high"] - data["high"].shift(1)
    data["down_move"] = data["low"].shift(1) - data["low"]
    data["pos_dm"] = np.where((data["up_move"] > data["down_move"]) & (data["up_move"] > 0), data["up_move"], 0)
    data["neg_dm"] = np.where((data["down_move"] > data["up_move"]) & (data["down_move"] > 0), data["down_move"], 0)
    data["pos_dm_sum"] = data["pos_dm"].rolling(window=window).sum()
    data["neg_dm_sum"] = data["neg_dm"].rolling(window=window).sum()
    data["pos_di"] = (data["pos_dm_sum"] / (data["tr_sum"] + 1e-10)) * 100
    data["neg_di"] = (data["neg_dm_sum"] / (data["tr_sum"] + 1e-10)) * 100
    data["dx"] = np.abs(data["pos_di"] - data["neg_di"]) / (data["pos_di"] + data["neg_di"] + 1e-10) * 100
    data["adx"] = data["dx"].rolling(window=window).mean()
    return data


def calculate_bollinger_bandwidth(data, window=20, num_std=2):
    data = calculate_bollinger_bands(data, window, num_std)
    data["bollinger_bandwidth"] = (data["bollinger_upper"] - data["bollinger_lower"]) / data["bollinger_mid"]
    return data


def calculate_rsi_momentum(data, window=14):
    data = calculate_rsi(data, window)
    data["rsi_momentum"] = data["rsi"].diff()
    return data


def calculate_macd_momentum(data, short_window=12, long_window=26, signal_window=9):
    data = calculate_macd(data, short_window, long_window, signal_window)
    data["macd_momentum"] = data["macd"].diff()
    return data


def calculate_price_momentum(data, window=12):
    data["price_momentum"] = data["close"].diff(periods=window)
    return data


def calculate_price_change_rate(data):
    data["price_change_rate"] = data["close"].pct_change()
    return data


def calculate_price_change_amount(data):
    data["price_change_amount"] = data["close"].diff()
    return data


def calculate_price_up_down_days(data, window):
    data["price_up_days"] = (data["price_change_amount"] > 0).rolling(window=window).sum()
    data["price_down_days"] = (data["price_change_amount"] < 0).rolling(window=window).sum()
    return data


def calculate_price_up_down_amplitude(data, window):
    data["price_up_amplitude"] = (
        data["price_change_amount"].where(data["price_change_amount"] > 0, 0).rolling(window=window).sum()
    )
    data["price_down_amplitude"] = (
        data["price_change_amount"].where(data["price_change_amount"] < 0, 0).rolling(window=window).sum()
    )
    return data


def calculate_price_up_down_ratio(data, window):
    data["price_up_ratio"] = data["price_up_days"] / window
    data["price_down_ratio"] = data["price_down_days"] / window
    return data


def calculate_price_up_down_strength(data):
    data["price_up_strength"] = data["price_up_amplitude"] / (data["price_up_days"] + 1e-10)
    data["price_down_strength"] = data["price_down_amplitude"] / (data["price_down_days"] + 1e-10)
    return data


def calculate_price_up_down_speed(data, window):
    data["price_up_speed"] = data["price_up_amplitude"] / window
    data["price_down_speed"] = data["price_down_amplitude"] / window
    return data


def calculate_price_up_down_acceleration(data):
    data["price_up_acceleration"] = data["price_up_speed"].diff()
    data["price_down_acceleration"] = data["price_down_speed"].diff()
    return data


def generate_quant_factors(data, window):
    # 确保输入数据的时间周期为15分钟
    data = calculate_sma(data, 14)
    data = calculate_ema(data, 14)
    data = calculate_rsi(data, 14)
    data = calculate_macd(data, 12, 26, 9)
    data = calculate_bollinger_bands(data, 20, 2)
    data = calculate_atr(data, 14)
    data = calculate_volatility(data, 20)
    data = calculate_kdj(data, 9, 3, 3)
    data = calculate_roc(data, 12)
    data = calculate_obv(data)
    data = calculate_cci(data, 20)
    data = calculate_dmi(data, 14)
    data = calculate_bollinger_bandwidth(data, 20, 2)
    data = calculate_rsi_momentum(data, 14)
    data = calculate_macd_momentum(data, 12, 26, 9)
    data = calculate_price_momentum(data, 12)
    data = calculate_price_change_rate(data)
    data = calculate_price_change_amount(data)
    data = calculate_price_up_down_days(data, window)
    data = calculate_price_up_down_amplitude(data, window)
    data = calculate_price_up_down_ratio(data, window)
    data = calculate_price_up_down_strength(data)
    data = calculate_price_up_down_speed(data, window)
    data = calculate_price_up_down_acceleration(data)
    return data

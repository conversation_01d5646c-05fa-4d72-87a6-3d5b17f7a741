from dataclasses import dataclass
from typing import ClassV<PERSON>, Dict, List, Tuple, Type

import datetype
import gymnasium as gym
import matplotlib.pyplot as plt
import mplfinance as mpf
import numpy as np
import numpy.typing as npt
import pandas as pd
from sklearn.preprocessing import StandardScaler

from ..base import BackTest, InstrumentDataHandler, InstrumentInfo
from ..env import BackTestEngine, StrategyEnv
from ..quant_factors import generate_quant_factors
from ..utils import RunEnv, run_env, time_cos_encoding, time_sin_encoding

if run_env == RunEnv.SUPERMIND:
    from mindgo_api import get_price  # type: ignore


class SimpleEnv(StrategyEnv):
    def _bind_data_handler_type(self) -> Type[InstrumentDataHandler]:
        return InstrumentDataHandlerForSimple

    def _define_action_space(self) -> gym.Space:
        return gym.spaces.Discrete(3)

    def _define_observation_space(self) -> gym.Space:
        return gym.spaces.Dict(
            {
                "price": gym.spaces.Box(
                    low=-np.inf, high=np.inf, shape=(self.feature_length, len(self.observe_features) - 6)
                ),
                "time": gym.spaces.Box(low=-np.inf, high=np.inf, shape=(self.feature_length, 6)),
                "other": gym.spaces.Box(low=-np.inf, high=np.inf, shape=(3,)),
            }
        )

    def _define_record(self):
        best_reward_mean = -np.inf
        best_reward_median = -np.inf
        best_raw_profit_rate_mean = -np.inf
        best_raw_profit_rate_median = -np.inf
        best_raw_profit_rate_sum = -np.inf

        def fnc(infos_: List[Dict[str, float]]):
            nonlocal \
                best_reward_mean, \
                best_reward_median, \
                best_raw_profit_rate_mean, \
                best_raw_profit_rate_median, \
                best_raw_profit_rate_sum
            infos = pd.DataFrame(infos_)
            infos = infos.loc[infos["count"] != 0]
            res = {
                "reward_mean": infos["reward"].mean(),
                "reward_median": infos["reward"].median(),
                "raw_profit_rate_mean": infos["raw_profit_rate"].mean(),
                "raw_profit_rate_median": infos["raw_profit_rate"].median(),
                "raw_profit_rate_sum": infos["raw_profit_rate"].sum(),
                "count": infos["count"].sum(),
            }
            flag = 0
            if res["reward_mean"] > best_reward_mean:
                flag = 1
                best_reward_mean = res["reward_mean"]
            if res["reward_median"] > best_reward_median:
                flag = 1
                best_reward_median = res["reward_median"]
            if res["raw_profit_rate_mean"] > best_raw_profit_rate_mean:
                flag = 1
                best_raw_profit_rate_mean = res["raw_profit_rate_mean"]
            if res["raw_profit_rate_median"] > best_raw_profit_rate_median:
                flag = 1
                best_raw_profit_rate_median = res["raw_profit_rate_median"]
            if res["raw_profit_rate_sum"] > best_raw_profit_rate_sum:
                flag = 1
                best_raw_profit_rate_sum = res["raw_profit_rate_sum"]
            return bool(flag) and res["count"] > 0, res

        return fnc

    # def on_rollout_start(self):
    #     DTBackTest.cache.clear()


@dataclass
class SimpleStrategyParams:
    trade_value: float
    init_cash: float


class InstrumentDataHandlerForSimple(InstrumentDataHandler):
    simple_params: SimpleStrategyParams

    # 策略数据
    open_: npt.NDArray[np.float64]
    features_data: pd.DataFrame

    def __init__(self, symbol, start_dt, end_dt, **config):
        super().__init__(
            symbol,
            start_dt,
            end_dt,
            required=["minute", "daily"],
            forward_days=config.get("forward_days", 60),
            instrument_info=config.get("instrument_info", None),
        )

        self.simple_params = SimpleStrategyParams(
            trade_value=config.get("trade_value", 10000.0), init_cash=config.get("init_cash", 10000.0)
        )

        self.open_ = self.minute_price_df["open"].to_numpy()

        tmp = self.daily_price_df["close"] / self.daily_price_df["open"] - 1
        d1 = tmp.rolling(252).quantile(0.8)
        d2 = tmp.rolling(252).quantile(0.2)
        self.target = tmp.where((tmp > d1) | (tmp < d2), 0.0).abs()

        agg = {
            "open": "first",  # 开盘价取第一个值
            "high": "max",  # 最高价取最大值
            "low": "min",  # 最低价取最小值
            "close": "last",  # 收盘价取最后一个值
            "volume": "sum",  # 成交量求和
            "sin_h": "last",
            "cos_h": "last",
            "sin_m": "last",
            "cos_m": "last",
            "sin_d": "last",
            "cos_d": "last",
        }
        if "open_interest" in self.minute_price_df.columns:
            agg.update({"open_interest": "last"})
        if "turnover" in self.minute_price_df.columns:
            agg.update({"turnover": "sum"})

        rmp_values = self.minute_price_df.resample(f"{config['feature_freq']}min", closed="right", label="right").agg(
            agg
        )
        self.features_data = rmp_values.loc[rmp_values.index.isin(self.minute_price_df.index)]
        self.features_data = generate_quant_factors(self.features_data, 22)

    def post_init(self, **kwargs):
        scaler = StandardScaler()
        scaler.fit(self.features_data.loc[self.features_data.index <= kwargs["mid_dt"]])
        self.features_data = pd.DataFrame(
            scaler.transform(self.features_data), index=self.features_data.index, columns=self.features_data.columns
        )

    def _preprocess_minute_data(self):
        tmp = self.minute_price_df.index.hour
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        hmax = 24
        self.minute_price_df["sin_h"] = tmp.apply(lambda x: time_sin_encoding(x, hmax))
        self.minute_price_df["cos_h"] = tmp.apply(lambda x: time_cos_encoding(x, hmax))

        tmp = self.minute_price_df.index.minute
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        mmax = 60
        self.minute_price_df["sin_m"] = tmp.apply(lambda x: time_sin_encoding(x, mmax))
        self.minute_price_df["cos_m"] = tmp.apply(lambda x: time_cos_encoding(x, mmax))

        tmp = self.minute_price_df.index.tz_localize("Asia/Shanghai").day_of_week
        tmp = pd.Series(tmp, index=self.minute_price_df.index).rank(method="dense") - 1
        dmax = 7
        self.minute_price_df["sin_d"] = tmp.apply(lambda x: time_sin_encoding(x, dmax))
        self.minute_price_df["cos_d"] = tmp.apply(lambda x: time_cos_encoding(x, dmax))

        self.minute_price_df.loc[self.minute_price_df["close"] == 0, "close"] = np.nan
        self.minute_price_df["close"] = self.minute_price_df["close"].ffill()  # 用前一个有效值填充

    def _preprocess_daily_data(self):
        tmp = self.daily_price_df.index.tz_localize("Asia/Shanghai").day_of_week
        tmp = pd.Series(tmp, index=self.daily_price_df.index).rank(method="dense") - 1
        dmax = 7
        self.daily_price_df["sin_d"] = tmp.apply(lambda x: time_sin_encoding(x, dmax))
        self.daily_price_df["cos_d"] = tmp.apply(lambda x: time_cos_encoding(x, dmax))

    def _bind_backtest_type(self) -> Type["BackTest"]:
        return SimpleBackTest

    def _time2trade_day(self) -> Tuple[pd.DatetimeIndex, np.ndarray]:
        b: pd.DatetimeIndex = self.minute_price_df.index  # type: ignore
        d = b.indexer_between_time("09:00:00", "15:00:00")
        c = pd.Series([pd.NaT] * len(b))
        c[d] = b.date[d]
        return b, pd.DatetimeIndex(c.bfill()).date  # type: ignore

    @staticmethod
    def check(engine: BackTestEngine):
        tmp = []
        for data_handler in engine.data_handlers.values():
            tmp.append(pd.Series(1, index=data_handler.trade_dates_of_sampling, name=data_handler.symbol))
        if len(tmp) == 0:
            raise RuntimeError("No data found")
        elif len(tmp) == 1:
            tmp = tmp[0].to_frame().sort_index()
        else:
            tmp = pd.concat(tmp, axis=1).sort_index()
        df1 = tmp.rolling(22, min_periods=1).sum().shift(1) == 22
        return df1


class SimpleBackTest(BackTest):
    last_price: npt.NDArray[np.float64]
    timestamp: npt.NDArray[np.int64]
    tr: npt.NDArray[np.float64]

    instrument_info: InstrumentInfo
    simple_params: SimpleStrategyParams

    base_price: float
    prev_close: float
    atr: Tuple[float, float]
    holding: float
    cash: float

    init_idx: int

    cache: ClassVar[Dict[str, List]] = {}

    # baseline: ClassVar = pickle.load(open("baseline_SA9999.CZCE", "rb"))

    def __init__(
        self,
        data_handler: InstrumentDataHandlerForSimple,
        begin_time: datetype.NaiveDateTime,
        end_time: datetype.NaiveDateTime,
        feature_length,
        observe_features,
        feature_freq,
        evaluate_days,
        action_freq,
    ):
        super().__init__(
            data_handler,
            begin_time,
            end_time,
            feature_length,
            observe_features,
            feature_freq,
            evaluate_days,
            action_freq,
        )
        idx1: int = data_handler.minute_price_df.index.get_loc(begin_time)  # type: ignore
        idx2: int = data_handler.minute_price_df.index.get_loc(end_time) + 1  # type: ignore

        self.last_price = data_handler.last_price[idx1:idx2]
        self.timestamp = data_handler.timestamp[idx1:idx2]
        self.target = data_handler.target.iat[data_handler.target.index.get_loc(end_time.normalize().replace(hour=15))]

        self.instrument_info = data_handler.instrument_info
        self.simple_params = data_handler.simple_params

        self.base_price = data_handler.open_[idx1]
        self.prev_close = data_handler.last_price[idx1 - 1]
        self.holding = 0.0
        self.cash = self.simple_params.init_cash

        self.result = np.full((len(self.last_price), 13), 0.0, dtype=np.float64)
        self.init_idx = idx1
        self.idx = 0

    def get_observation(self):
        now = self.data_handler.minute_price_df.index[self.init_idx + self.idx]
        bar_end = np.searchsorted(self.data_handler.features_data.index, now, "right")
        values = self.data_handler.features_data.iloc[bar_end - self.feature_length : bar_end][self.observe_features]

        states = (0, 0, 1) if self.holding == 0 else ((0, 1, 0) if self.holding > 0 else (1, 0, 0))

        # tmp = np.searchsorted(self.data_handler.minute_price_df.index, now, "right")
        # last_bar = self.data_handler.minute_price_df.iloc[tmp - 2]
        # current_data = self.data_handler.minute_price_df.iloc[tmp - 1]
        # states = (
        #     *(
        #         (current_data[["open", "high", "low", "close"]] - values[["open", "high", "low", "close"]].mean(axis=0))
        #         / values[["open", "high", "low", "close"]].std(axis=0)
        #         * self.feature_freq
        #     ),
        #     current_data.at["sin_h"],
        #     current_data.at["cos_h"],
        #     current_data.at["sin_m"],
        #     current_data.at["cos_m"],
        #     current_data.at["sin_d"],
        #     current_data.at["cos_d"],
        #     *(
        #         (
        #             current_data[["volume", "open_interest"]] * self.feature_freq
        #             - (values[["volume", "open_interest"]].mean(axis=0))
        #         )
        #         / values[["volume", "open_interest"]].std(axis=0)
        #     ),
        # )

        values = values.values
        return {"price": values[:, :-6], "time": values[:, -6:], "other": np.array(states)}

    def _step(self, action=-1):
        action -= 1
        i = self.idx

        order_vol = 0
        if i == self.last_price.shape[0] - 1:
            if self.holding > 0:
                order_vol = -self.holding
                # print(
                #     "{} - SELL CLOSE - price: {}, vol: {}".format(
                #         pd.Timestamp(self.timestamp[i]), self.price[i], order_vol
                #     )
                # )
            elif self.holding < 0:
                order_vol = -self.holding
                # print(
                #     "{} - BUY CLOSE - price: {}, vol: {}".format(
                #         pd.Timestamp(self.timestamp[i]), self.price[i], order_vol
                #     )
                # )
        else:
            if self.holding == 0 and self.result[:, 5].sum() == 0:
                if action == -1:
                    order_vol = self.simple_params.trade_value / self.last_price[i]
                    self.hc = self.last_price[i]
                    self.cost = self.last_price[i]
                    # print(
                    #     "{} - BUY OPEN - price: {}, vol: {}".format(
                    #         pd.Timestamp(self.timestamp[i]), self.price[i], order_vol
                    #     )
                    # )
                elif action == 1:
                    order_vol = -self.simple_params.trade_value / self.last_price[i]
                    self.lc = self.last_price[i]
                    self.cost = self.last_price[i]
                    # print(
                    #     "{} - SELL OPEN - price: {}, vol: {}".format(
                    #         pd.Timestamp(self.timestamp[i]), self.price[i], order_vol
                    #     )
                    # )

            elif self.holding > 0:
                self.hc = max(self.hc, self.last_price[i])
                if self.last_price[i] < self.hc * 0.99:
                    order_vol = -self.holding
                    # print(
                    #     "{} - SELL CLOSE - price: {}, vol: {}".format(
                    #         pd.Timestamp(self.timestamp[i]), self.price[i], order_vol
                    #     )
                    # )

            elif self.holding < 0:
                self.lc = min(self.lc, self.last_price[i])
                if self.last_price[i] > self.lc * 1.01:
                    order_vol = -self.holding
                    # print(
                    #     "{} - BUY CLOSE - price: {}, vol: {}".format(
                    #         pd.Timestamp(self.timestamp[i]), self.price[i], order_vol
                    #     )
                    # )

        commission = 0
        trade_price = 0
        amount = 0

        if order_vol > 0:
            trade_price = self.last_price[i]
            amount = order_vol * trade_price
            self.holding += order_vol
            commission = self.instrument_info.commission_ratio * amount
            self.cash = self.cash - amount - commission
        elif order_vol < 0:
            trade_price = self.last_price[i]
            amount = -order_vol * trade_price
            self.holding += order_vol
            commission = self.instrument_info.commission_ratio * amount
            self.cash = self.cash + amount - commission

        self.result[i, 0] = self.timestamp[i]  # 时间戳
        self.result[i, 1] = self.holding  # 持股数量
        self.result[i, 2] = self.cash  # 现金
        self.result[i, 3] = self.holding * self.last_price[i]  # 持仓市值
        self.result[i, 4] = self.last_price[i]  # 成交价
        self.result[i, 5] = order_vol  # 成交量
        self.result[i, 6] = amount  # 成交额
        self.result[i, 7] = commission  # 交易成本：手续费、印花税

        self.idx += 1

    def get_reward(self) -> Tuple[float, Dict]:
        if not self.is_done():
            return 0.0, {}

        init, final = self.simple_params.init_cash, self.result[-1, 2] + self.result[-1, 3]
        count = 0 if init == final else 1
        raw_profit_rate = final / init - 1

        # clip_raw_profit_rate = raw_profit_rate
        # if count:
        #     self.cache.setdefault(self.data_handler.symbol, []).append(raw_profit_rate)
        #     if len(self.cache[self.data_handler.symbol]) > 20:
        #         t1, t2 = np.percentile(self.cache[self.data_handler.symbol], [15, 85])
        #         clip_raw_profit_rate = np.clip(raw_profit_rate, t1, t2)

        # profit_rate = clip_raw_profit_rate + (count - 1) * 1e-4 * 6 * 5 * 0
        reward = raw_profit_rate - self.target
        # reward = np.sign(raw_profit_rate) * np.power(raw_profit_rate, 2) - self.target**2
        # reward = profit_rate
        # reward = profit_rate / self.std
        # print("{} - {}, raw_profit_rate: {}".format(self.begin_time, self.end_time, raw_profit_rate))
        return reward, {
            "symbol": self.data_handler.symbol,
            "begin_time": self.begin_time,
            "end_time": self.end_time,
            "reward": reward,
            "init": init,
            "final": final,
            "actions": self.actions,
            "count": count,
            "profit_rate": 0,
            "raw_profit_rate": raw_profit_rate,
        }


def analyse_result(res, plot_every=True):
    if len(res[1]) == 0:
        print("没有交易记录")
        return

    print(f"累积超额收益：{res[0]}")
    a = res[1]
    b = pd.DataFrame([{"yield": i["details"]["yield_"], "base_yield": i["details"]["base_yield_"]} for i in a])
    b = b.cumsum()
    b.plot()
    (b["yield"] - b["base_yield"]).plot(linestyle="--")
    plt.show()

    if not plot_every:
        return
    test_symbol = a[1]["details"]["symbol"]
    for test in a:
        begin_time = test["begin_time"]
        end_time = test["end_time"]

        data = get_price(test_symbol, begin_time, end_time, "1m", ["open", "high", "low", "close"], fq="post")

        signal = pd.DataFrame(test["details"]["trades"])
        signal["dt"] = pd.to_datetime(signal["dt"])

        buy_signal = signal[["dt", "touch"]].loc[signal["side"] == 1].set_index("dt")["touch"]
        buy_signal.index.rename(None, inplace=True)
        buy_signal = buy_signal.reindex(index=data.index)
        sell_signal = signal[["dt", "touch"]].loc[signal["side"] == -1].set_index("dt")["touch"]
        sell_signal.index.rename(None, inplace=True)
        sell_signal = sell_signal.reindex(index=data.index)

        # 绘制K线图
        apds = []
        if np.isfinite(buy_signal).sum() > 0:
            apds.append(
                mpf.make_addplot(buy_signal, scatter=True, markersize=100, marker="^", color="red", label="Buy")
            )
        if np.isfinite(sell_signal).sum() > 0:
            apds.append(
                mpf.make_addplot(sell_signal, scatter=True, markersize=100, marker="v", color="green", label="Sell")
            )
        apds.append(mpf.make_addplot(test["details"]["adjusted_base_price"], color="grey", label="adjusted_base_price"))
        apds.append(mpf.make_addplot(pd.Series([test["details"]["init_price"]] * len(data), index=data.index)))

        # 绘制K线图并添加买卖点
        mpf.plot(
            data,
            type="candle",
            addplot=apds,
            volume=False,
            figsize=(18, 5),
            style="yahoo",
            datetime_format="%Y-%m-%d %H:%M",  # 设置x轴时间格式为年-月-日 时:分
        )
        print("收益{:.4%}, 基准收益{:.4%}".format(test["details"]["yield_"], test["details"]["base_yield_"]))

        # 展示图表
        plt.show()

import warnings

import pandas as pd
from stable_baselines3.ppo import PPO

from aichemy.ml.exp_base import MetricsTracker
from aichemy.utils import rolling_window
from rl_strategy.callback import Callback
from rl_strategy.model.transformer import Transformer
from simple_cbond import SimpleEnv
# from rl_strategy.strategies.simple_cbond import SimpleEnv

# 忽略所有警告
warnings.filterwarnings("ignore")

features = [
    "open",
    "high",
    "low",
    "close",
    "volume",
    "open_interest",
    "sin_h",
    "cos_h",
    "sin_m",
    "cos_m",
    "sin_d",
    "cos_d",
]


def train(
    t1,
    t2,
):
    # version = f"checkpoints/{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}_simple_量价因子_收益reward_90D_10D_0.5/{t1}_{t2}"
    version = ""
    print(t1, t2)

    env = SimpleEnv(
        256,
        features,
        15,
        1,
        15,
        0.5,
        ["SA9999.CZCE"],
        t1,
        t2,
        specify_instrument_info={"SA9999.CZCE": {"commission_ratio": 1e-2}},
        # n_core=1,
    )
    env.engine.post_init(normalize_window=252 * 16)
    callback = Callback(env, version)
    model = PPO(
        "MultiInputPolicy",
        env,
        n_steps=160,
        # n_steps=env.engine.num_samples,
        learning_rate=1e-5,
        gamma=1.0,
        ent_coef=0.1,
        batch_size=env.engine.num_samples,
        n_epochs=2,
        policy_kwargs=dict(features_extractor_class=Transformer, features_extractor_kwargs=dict()),
        # tensorboard_log="/workspace/tb_log_dir",
        verbose=1,
    )
    model.learn(
        env.engine.num_samples * 2,
        reset_num_timesteps=False,
        callback=callback,
        # tb_log_name=version,
    )


if __name__ == "__main__":
    train("2023-01-01", "2024-01-01")

    # res = []
    # for e, (t1, t2, t3) in enumerate(rolling_window("2021-01-01", "2025-01-01", "90D", "10D")):
    #     t1 = t1.strftime("%Y-%m-%d")
    #     t2 = t2.strftime("%Y-%m-%d")
    #     t3 = t3.strftime("%Y-%m-%d")
    #     train(t1, t2, t3)

    #     # mt = MetricsTracker.load(version)
    #     # mt = pd.DataFrame(mt.vali_evl_series.tolist())
    #     # num_timesteps = mt["num_timesteps"].iat[(mt["raw_profit_rate_sum"]).idxmax()]
    #     # test_env = env.create_test_env(["SA9999.CZCE"], (pd.Timestamp(t2) + pd.Timedelta("1D")).strftime("%Y-%m-%d"), t3)
    #     # res.append(test_env.test_model(PPO.load(f"{version}/{num_timesteps}_steps/model.zip")))

    #     break

    #     if e >= 10:
    #         break

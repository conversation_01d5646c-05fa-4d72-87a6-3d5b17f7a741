[build-system]
requires = ["setuptools", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[project]
name = "quant"
authors = [{ name = "<PERSON>" }]
requires-python = ">=3.10"
dependencies = [
    "dataclasses_json",
    "ddddocr",
    "lxml",
    "pillow",
    "requests",
    "pandas",
    "sqlalchemy",
    "grpcio",
    "numpy",
    "protobuf ~= 4.25",
    "cython",
    "matplotlib",
    "scikit_learn",
    "tqsdk",
    "tomli",
    "psycopg2",
    "tqdm",
    "asyncpg",
    "scipy",
    "redis",
    "pika",
    "loguru",
    "tables",
    "schedule",
    "kazoo",
    "datetype",
]
dynamic = ["version"]

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.package-data]
"*" = [
    "*.proto",
    "*.pyi",
    "*.dll",
    "*.so",
    "*.so.*",
    "*.csv",
    "*.json",
    "*.onnx",
    "*.pxd",
]

[tool.setuptools_scm]

import os
import pickle
import re
from typing import Optional

import torch

# from hxcelery.hxcrontab.util import call_algo_api
from mgquant_mod_mindgo.utils.recorder import log
from sb3_contrib.ppo_mask import MaskablePPO

import config
from base import expression
from base.calculator import IndicatorCalculator
from base.data import FeatureType, StockData
from base.pool import IndicatorPool
from model import models
from model.callback import AlphaCallback
from model.utils import reseed_everything
from model.wrapper import build_env


class FactorTrain:
    def train(
        self,
        name,
        target,
        index="000300.SH",
        freq="1d",
        model="LSTMSharedExtractor",
        device="cuda",
        train_stime=None,
        train_etime=None,
        valid_stime=None,
        valid_etime=None,
        test_stime=None,
        test_etime=None,
        dim: int = 1,
        seed: int = 82,
        pool_size: int = 10,
        max_expr_length: int = 20,
        steps: int = 2048 * 512,
        featfile: Optional[str] = None,
        cost: float = 1.5e-3,
        one_way: int = 0,
        data_kwargs: dict = dict(),
        pool_kwargs: dict = dict(),
        model_kwargs: dict = dict(),
        config_kwargs: dict = dict(),
        config_operators: list = [],
        config_features: list = [],
        config_deltabars: list = [],
        config_constants: list = [],
    ):
        self.name = name
        reseed_everything(seed)
        device = torch.device(device)
        self.save_path = os.path.join("out", name)
        os.makedirs(self.save_path, exist_ok=True)
        self.steps = steps
        self.logf_path = os.path.join(self.save_path, "train.log")
        log.set_tofile(self.logf_path)

        tgflds = list(set(re.findall(r"\b({})\b".format("|".join(x.lower() for x in config.features)), target.lower())))
        expression.update(tgflds)  # 先使用target用到的features先加载tgtsor值(支持target不在config_features中情况)
        scope = dict()
        exec("from base.expression import *", scope)
        ecode = target.rsplit("\n", 1)
        ecode, eline = ecode if len(ecode) == 2 else ("", ecode[-1])
        exec(ecode, scope)
        target = eval(eline, scope)

        def get_calcu_data(s, e):
            d = StockData(index, s, e, freq, featfile=featfile, fields=config_features, device=device, **data_kwargs)
            t = StockData(index, s, e, freq, fields=tgflds, device=device, **data_kwargs)
            return d, target(t.init_tsor(align=d))  # 将target使用data的index对齐

        log.info("开始数据提取({})...".format(StockData.__module__.replace("base.data_", "")))

        global data_train, data_test
        data_train = get_calcu_data(train_stime, train_etime)
        log.info("训练数据完成...")
        # data_valid = data_train  # TODO: 暂未使用评估数据
        # data_valid = get_calcu_data(valid_stime, valid_etime)
        log.info("评估数据跳过...")
        data_test = [get_calcu_data(s, e) for s, e in zip(test_stime.split("|"), test_etime.split("|"))]
        log.info("测试数据完成...")
        self.calculator_train = IndicatorCalculator(*data_train, dim=dim, cost=cost, one_way=one_way)
        # self.calculator_valid = IndicatorCalculator(*data_valid, dim=dim, cost=cost)
        self.calculator_test = [IndicatorCalculator(*x, dim=dim, cost=cost, one_way=one_way) for x in data_test]

        config.__dict__.update(config_kwargs)
        if config_operators:
            config.OPERATORS = [getattr(expression, x) for x in config_operators]
        if config_features:
            expression.update(config_features)  # 动态FeatureType设置
            config.FEATURES = [FeatureType[x] for x in config_features]
        else:
            expression.update(config.features)
        if config_deltabars:
            config.DELTA_BARS = config_deltabars
        if config_constants:
            config.CONSTANTS = config_constants
        config.update()

        checkpoint_path = os.path.join(self.save_path, "checkpoint")
        if os.path.exists(checkpoint_path):
            log.info("恢复", checkpoint_path, "继续运行")
            pool = pickle.load(open(os.path.join(checkpoint_path, "pool.pkl"), "rb"))
            env = build_env(pool=pool, max_expr_length=max_expr_length, device=device, verbose=True)
            self.model = MaskablePPO.load(os.path.join(checkpoint_path, "model.zip"), env=env, device=device)
            # self.model.set_env(env)
        else:
            pool = IndicatorPool(
                pool_size=pool_size,
                max_expr_length=max_expr_length,
                calculator=self.calculator_train,
                device=device,
                **pool_kwargs,
            )
            env = build_env(pool=pool, max_expr_length=max_expr_length, device=device, verbose=True)
            self.model = MaskablePPO(
                "MlpPolicy",
                env,
                policy_kwargs=dict(
                    features_extractor_class=getattr(models, model),
                    features_extractor_kwargs=dict(pool_size=pool_size),  # v1版本不需要
                ),
                **{
                    "gamma": 1.0,
                    "ent_coef": 0.01,
                    "batch_size": 128,
                    "learning_rate": 5e-5,
                    "n_epochs": 4,
                    **model_kwargs,
                },
                tensorboard_log="logs",
                device=device,
                verbose=1,
            )

        callback = AlphaCallback(save_path=self.save_path, valid_calculator=None, test_calculator=self.calculator_test)

        self.model.learn(
            total_timesteps=self.steps, callback=callback, reset_num_timesteps=False, tb_log_name=self.name
        )


FactorTrain().train(**kwargs)

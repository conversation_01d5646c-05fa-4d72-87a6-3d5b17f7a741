{"cells": [{"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["import torch\n", "import pickle\n", "import os\n", "import re\n", "\n", "import config\n", "from base import expression\n", "from base.data import FeatureType, StockData\n", "from base.pool import AlphaPool\n", "from model import models\n", "from model.callback import AlphaCallback\n", "from model.utils import reseed_everything\n", "from model.wrapper import build_env\n", "from base.calculator import AlphaCalculator\n", "from sb3_contrib.ppo_mask import MaskablePPO\n", "# from hxcelery.hxcrontab.util import call_algo_api\n", "from mgquant_mod_mindgo.utils.recorder import log\n", "\n", "class FactorTrain:\n", "    def train(\n", "        self, name, target, index='000300.SH', freq='1d', model='LSTMSharedExtractor', device='cuda',\n", "        train_stime=None, train_etime=None, valid_stime=None, valid_etime=None, test_stime=None, test_etime=None,\n", "        dim: int = 1, seed: int = 82, pool_size: int = 10, max_expr_length: int = 20, steps: int = 2048 * 512, featfile: str = None,\n", "        data_kwargs: dict = dict(), pool_kwargs: dict = dict(), model_kwargs: dict = dict(), config_kwargs: dict=dict(),\n", "        config_operators: list = [], config_features: list = [], config_deltabars: list = [], config_constants: list = [],\n", "    ):\n", "        self.name = name\n", "        reseed_everything(seed)\n", "        device = torch.device(device)\n", "        self.save_path = os.path.join('out', name)\n", "        self.steps = steps\n", "        self.logf_path = os.path.join(self.save_path, 'train.log')\n", "        log.set_tofile(self.logf_path)\n", "\n", "        tgflds = list(set(re.findall(r'\\b({})\\b'.format('|'.join(x.lower() for x in config.features)), target.lower())))\n", "        expression.update(tgflds)  # 先使用target用到的features先加载tgtsor值(支持target不在config_features中情况)\n", "        scope = dict()\n", "        exec('from base.expression import *', scope)\n", "        ecode = target.rsplit('\\n', 1)\n", "        ecode, eline = ecode if len(ecode)==2 else ('', ecode[-1])\n", "        exec(ecode, scope)\n", "        target = eval(eline, scope)\n", "        def get_calcu_data(s, e):\n", "            d = StockData(index, s, e, freq, featfile=featfile, fields=config_features, device=device, **data_kwargs)\n", "            t = StockData(index, s, e, freq, fields=tgflds, device=device, **data_kwargs)\n", "            return d, target(t.init_tsor(align=d))  # 将target使用data的index对齐\n", "        log.info('开始数据提取({})...'.format(StockData.__module__.replace('base.data_', '')))\n", "        data_train = get_calcu_data(train_stime, train_etime)\n", "        log.info('训练数据完成...')\n", "        data_valid = data_train  # TODO: 暂未使用评估数据\n", "        # data_valid = get_calcu_data(valid_stime, valid_etime)\n", "        log.info('评估数据跳过...')\n", "        data_test = [get_calcu_data(s, e) for s, e in zip(test_stime.split('|'), test_etime.split('|'))]\n", "        log.info('测试数据完成...')\n", "        self.calculator_train = AlphaCalculator(*data_train, dim=dim)\n", "        self.calculator_valid = AlphaCalculator(*data_valid, dim=dim)\n", "        self.calculator_test = [AlphaCalculator(*x, dim=dim) for x in data_test]\n", "\n", "        config.__dict__.update(config_kwargs)\n", "        if config_operators:\n", "            config.OPERATORS = [getattr(expression, x) for x in config_operators]\n", "        if config_features:\n", "            expression.update(config_features)  # 动态FeatureType设置\n", "            config.FEATURES = [FeatureType[x] for x in config_features]\n", "        else:\n", "            expression.update(config.features)\n", "        if config_deltabars:\n", "            config.DELTA_BARS = config_deltabars\n", "        if config_constants:\n", "            config.CONSTANTS = config_constants\n", "        config.update()\n", "\n", "        checkpoint_path = os.path.join(self.save_path, 'checkpoint')\n", "        if os.path.exists(checkpoint_path):\n", "            log.info('恢复', checkpoint_path, '继续运行')\n", "            pool = pickle.load(open(os.path.join(checkpoint_path, \"pool.pkl\"), \"rb\"))\n", "            env = build_env(pool=pool, max_expr_length=max_expr_length, device=device, verbose=True)\n", "            self.model = MaskablePPO.load(os.path.join(checkpoint_path, f\"model.zip\"), env=env, device=device)\n", "            # self.model.set_env(env)\n", "        else:\n", "            pool = AlphaPool(pool_size=pool_size, max_expr_length=max_expr_length, calculator=self.calculator_train, device=device, **pool_kwargs)\n", "            env = build_env(pool=pool, max_expr_length=max_expr_length, device=device, verbose=True)\n", "            self.model = MaskablePPO(\n", "                'MlpPolicy',\n", "                env,\n", "                policy_kwargs=dict(\n", "                    features_extractor_class=getattr(models, model),\n", "                    features_extractor_kwargs=dict(pool_size=pool_size),  # v1版本不需要\n", "                ),\n", "                **{'gamma': 1., 'ent_coef':0.01, 'batch_size':128, 'learning_rate':5e-5, 'n_epochs':4, **model_kwargs},\n", "                tensorboard_log='logs',\n", "                device=device,\n", "                verbose=1,\n", "            )\n", "\n", "        callback = AlphaCallback(\n", "            save_path=self.save_path,\n", "            valid_calculator=self.calculator_valid,\n", "            test_calculator=self.calculator_test,\n", "        )\n", "\n", "        self.model.learn(\n", "            total_timesteps=self.steps,\n", "            callback=callback,\n", "            reset_num_timesteps=False,\n", "            tb_log_name=self.name,\n", "        )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}
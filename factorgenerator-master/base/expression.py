from typing import Type, Union

import torch

from base.data import FeatureType, StockData


class Expression:
    def __init__(self, *args, **kwargs): ...

    def __call__(self, *args, **kwargs) -> torch.Tensor: ...

    def __repr__(self) -> str:
        return str(self)

    def __add__(self, other: Union["Expression", float]) -> "Add":
        if isinstance(other, Expression):
            return Add(self, other)
        else:
            return Add(self, Constant(other))

    def __radd__(self, other: float) -> "Add":
        return Add(Constant(other), self)

    def __sub__(self, other: Union["Expression", float]) -> "Sub":
        if isinstance(other, Expression):
            return Sub(self, other)
        else:
            return Sub(self, Constant(other))

    def __rsub__(self, other: float) -> "Sub":
        return Sub(Constant(other), self)

    def __mul__(self, other: Union["Expression", float]) -> "Mul":
        if isinstance(other, Expression):
            return Mul(self, other)
        else:
            return Mul(self, Constant(other))

    def __rmul__(self, other: float) -> "Mul":
        return Mul(Constant(other), self)

    def __truediv__(self, other: Union["Expression", float]) -> "Div":
        if isinstance(other, Expression):
            return Div(self, other)
        else:
            return Div(self, Constant(other))

    def __rtruediv__(self, other: float) -> "Div":
        return Div(Constant(other), self)

    def __pow__(self, other: Union["Expression", float]) -> "Pow":
        if isinstance(other, Expression):
            return Pow(self, other)
        else:
            return Pow(self, Constant(other))

    def __rpow__(self, other: float) -> "Pow":
        return Pow(Constant(other), self)

    def __pos__(self) -> "Expression":
        return self

    def __neg__(self) -> "Sub":
        return Sub(Constant(0), self)

    def __abs__(self) -> "Abs":
        return Abs(self)

    @property
    def is_feature(self):
        raise NotImplementedError


class Feature(Expression):
    def __init__(self, feature: FeatureType):
        super().__init__()
        self.feature = feature

    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        assert period.step == 1 or period.step is None
        if period.start < -data.max_backtrack_bars or period.stop - 1 > data.max_future_bars:
            raise IndexError("Out of data range")

        start = period.start + data.max_backtrack_bars
        stop = period.stop + data.max_backtrack_bars - 1

        for i in data.sample_indexes:
            step = min(data.n_step, data.n_primary_bars - i)
            s = start + i * data.n_secondary_bars
            e = stop + (i + step) * data.n_secondary_bars
            yield data.data[s:e, int(self.feature), :].to(data.device)

    def __str__(self) -> str:
        return "$" + self.feature.name.lower()

    @property
    def is_feature(self):
        return True


class Constant(Expression):
    def __init__(self, value: float):
        super().__init__()
        self.value = value

    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        assert period.step == 1 or period.step is None
        if period.start < -data.max_backtrack_bars or period.stop - 1 > data.max_future_bars:
            raise IndexError("Out of data range")
        dtype = data.data.dtype

        interval = period.stop - period.start - 1

        for i in data.sample_indexes:
            step = min(data.n_step, data.n_primary_bars - i)
            size = (data.n_secondary_bars * step + interval, data.n_stocks)
            yield torch.full(size=size, fill_value=self.value, dtype=dtype, device=data.device)

    def __str__(self) -> str:
        return f"Constant({str(self.value)})"

    @property
    def is_feature(self):
        return False


class Bar(Expression):
    def __init__(self, value: int):
        super().__init__()
        self.value = value

    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        raise NotImplementedError

    def __str__(self) -> str:
        return f"Bar({str(self.value)})"

    @property
    def is_feature(self):
        return False


# feature name
for x in list(FeatureType):
    globals()[x.name.lower()] = Feature(x)


# Operator base classes


class Operator(Expression):
    @classmethod
    def n_args(cls) -> int: ...

    @classmethod
    def category_type(cls) -> Type["Operator"]: ...


class UnaryOperator(Operator):
    def __init__(self, operand: Union[Expression, float]):
        super().__init__()
        self.operand = operand if isinstance(operand, Expression) else Constant(operand)

    @classmethod
    def n_args(cls) -> int:
        return 1

    @classmethod
    def category_type(cls) -> Type["Operator"]:
        return UnaryOperator

    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        for value in self.operand(data, period):
            yield self.apply(value)

    def apply(self, operand: torch.Tensor) -> torch.Tensor: ...

    def __str__(self) -> str:
        return f"{type(self).__name__}({self.operand})"

    @property
    def is_feature(self):
        return self.operand.is_feature


class BinaryOperator(Operator):
    def __init__(self, left: Union[Expression, float], right: Union[Expression, float]):
        super().__init__()
        self.left = left if isinstance(left, Expression) else Constant(left)
        self.right = right if isinstance(right, Expression) else Constant(right)

    @classmethod
    def n_args(cls) -> int:
        return 2

    @classmethod
    def category_type(cls) -> Type["Operator"]:
        return BinaryOperator

    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        for left, right in zip(self.left(data, period), self.right(data, period)):
            yield self.apply(left, right)

    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor: ...

    def __str__(self) -> str:
        return f"{type(self).__name__}({self.left},{self.right})"

    @property
    def is_feature(self):
        return self.left.is_feature or self.right.is_feature


class RollingOperator(Operator):
    def __init__(self, operand: Union[Expression, float], bar: Union[int, Bar]):
        super().__init__()
        self.operand = operand if isinstance(operand, Expression) else Constant(operand)
        if isinstance(bar, Bar):
            bar = bar.value
        self.bar = bar

    @classmethod
    def n_args(cls) -> int:
        return 2

    @classmethod
    def category_type(cls) -> Type["Operator"]:
        return RollingOperator

    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        start = period.start - self.bar + 1
        stop = period.stop
        for value in self.operand(data, slice(start, stop)):
            value = value.unfold(0, self.bar, 1)
            yield self.apply(value)

    def apply(self, operand: torch.Tensor) -> torch.Tensor: ...

    def __str__(self) -> str:
        return f"{type(self).__name__}({self.operand},{self.bar})"

    @property
    def is_feature(self):
        return self.operand.is_feature


class PairRollingOperator(Operator):
    def __init__(self, left: Expression, right: Expression, bar: Union[int, Bar]):
        super().__init__()
        self.left = left
        self.right = right
        if isinstance(bar, Bar):
            bar = bar.value
        self.bar = bar

    @classmethod
    def n_args(cls) -> int:
        return 3

    @classmethod
    def category_type(cls) -> Type["Operator"]:
        return PairRollingOperator

    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        start = period.start - self.bar + 1
        stop = period.stop
        for left, right in zip(self.left(data, slice(start, stop)), self.right(data, slice(start, stop))):
            left = left.unfold(0, self.bar, 1)
            right = right.unfold(0, self.bar, 1)
            yield self.apply(left, right)

    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor: ...

    def __str__(self) -> str:
        return f"{type(self).__name__}({self.left},{self.right},{self.bar})"

    @property
    def is_feature(self):
        return self.left.is_feature or self.right.is_feature


class Neg(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return -operand


class Sqrt(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.sqrt()


class AbsSqrt(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.abs().sqrt()


class SignedSqrt(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.sign() * operand.abs().sqrt()


class Square(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.square()


class Curt(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.sign() * operand.abs().pow(1 / 3)


class Cube(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.pow(3)


class Abs(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.abs()


class Sin(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.sin()


class Cos(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.cos()


class Tan(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.tan()


class Asin(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.asin()


class Acos(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.acos()


class Atan(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.atan()


class Inv(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.reciprocal()


class Sign(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.sign()


class Log(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.log()


class AbsLog(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.abs().log()


class SignedLog(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.sign() * operand.abs().log()


class UnaryMean(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        result = operand.nanmean(dim=-1, keepdim=True)
        return result.expand_as(operand)


class UnarySum(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        result = operand.nansum(dim=-1, keepdim=True)
        return result.expand_as(operand)


class UnaryStd(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        mean = operand.nanmean(dim=-1, keepdim=True)
        n = operand.isfinite().sum(dim=-1, keepdim=True) - 1
        var = (operand - mean).pow(2).nansum(dim=-1, keepdim=True) / n
        std = var.sqrt()
        return std.expand_as(operand)


class Scale(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        sum_result = operand.nansum(dim=-1, keepdim=True).expand_as(operand)
        return operand / sum_result


class UnaryMad(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        mean_result = operand.nanmean(dim=-1, keepdim=True)
        return operand - mean_result


class UnaryRank(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        finite_mask = operand.isfinite()
        n = finite_mask.sum(dim=1, keepdim=True)
        rank = operand.argsort().argsort() / n
        rank[~finite_mask] = torch.nan
        return rank


class Tanh(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.tanh()


class Sigmoid(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.sigmoid()


class Gelu(UnaryOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return torch.nn.functional.gelu(operand)


class Add(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left + right


class Sub(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left - right


class Mul(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left * right


class Div(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left / right


class Pow(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left.sign() * left.abs().pow(right)


class Greater(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left.max(right)

    @property
    def is_feature(self):
        return self.left.is_feature and self.right.is_feature


class Less(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left.min(right)

    @property
    def is_feature(self):
        return self.left.is_feature and self.right.is_feature


class LogAB(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return torch.log(left) / torch.log(right)

    @property
    def is_feature(self):
        return self.left.is_feature and self.right.is_feature


class AbsLogAB(BinaryOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        return left.abs().log() / right.abs().log()

    @property
    def is_feature(self):
        return self.left.is_feature and self.right.is_feature


class Ref(RollingOperator):
    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        start = period.start - self.bar
        stop = period.stop - self.bar
        return self.operand(data, slice(start, stop))

    def apply(self, operand: torch.Tensor) -> torch.Tensor: ...


class Mean(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.nanmean(dim=-1)


class Sum(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.nansum(dim=-1)


class Std(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        mean = operand.nanmean(dim=-1, keepdim=True)
        n = operand.isfinite().sum(dim=-1) - 1
        var = (operand - mean).pow(2).nansum(dim=-1) / n
        std = var.sqrt()
        return std


class Var(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        mean = operand.nanmean(dim=-1, keepdim=True)
        n = operand.isfinite().sum(dim=-1) - 1
        var = (operand - mean).pow(2).nansum(dim=-1) / n
        return var


class Skew(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        central = operand - operand.nanmean(dim=-1, keepdim=True)
        m3 = (central**3).nanmean(dim=-1)
        m2 = (central**2).nanmean(dim=-1)
        return m3 / m2**1.5


class Kurt(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        central = operand - operand.nanmean(dim=-1, keepdim=True)
        m4 = (central**4).nanmean(dim=-1)
        mean = operand.nanmean(dim=-1, keepdim=True)
        n = operand.isfinite().sum(dim=-1) - 1
        var = (operand - mean).pow(2).nansum(dim=-1) / n
        return m4 / var**2 - 3


class Max(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        operand[~operand.isfinite()] = torch.finfo(operand.dtype).min
        return operand.max(dim=-1)[0]


class Min(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        operand[~operand.isfinite()] = torch.finfo(operand.dtype).max
        return operand.min(dim=-1)[0]


class Med(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        return operand.nanmedian(dim=-1)[0]


class Mad(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        central = operand - operand.nanmean(dim=-1, keepdim=True)
        return central.abs().nanmean(dim=-1)


class RollRank(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        n = operand.shape[-1]
        last = operand[:, :, -1, None]
        left = (last > operand).count_nonzero(dim=-1)
        right = (last >= operand).count_nonzero(dim=-1)
        result = (right + left + (right > left)) / (2 * n)
        return result


class RollRank1(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        # (时间, 股票, bar)
        # n = operand.shape[-1]
        n = torch.isfinite(operand).sum(dim=-1)
        last = operand[:, :, [-1]]
        left = (operand < last).sum(dim=-1)  # (时间, 股票)
        right = (operand > last).sum(dim=-1)  # (时间, 股票)
        result = ((left + 1 + n - right) / 2 - 1) / (n - 1)
        return result


class Delta(RollingOperator):
    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        start = period.start - self.bar
        stop = period.stop
        for value in self.operand(data, slice(start, stop)):
            yield value[self.bar :] - value[: -self.bar]

    def apply(self, operand: torch.Tensor) -> torch.Tensor: ...


class DeltaPerc(RollingOperator):
    def __call__(self, data: StockData, period: slice = slice(0, 1)) -> torch.Tensor:
        start = period.start - self.bar
        stop = period.stop
        for value in self.operand(data, slice(start, stop)):
            yield (value[self.bar :] - value[: -self.bar]) / value[: -self.bar]

    def apply(self, operand: torch.Tensor) -> torch.Tensor: ...


class Argmax(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        operand[~operand.isfinite()] = torch.finfo(operand.dtype).min
        return operand.argmax(dim=-1).float()


class Argmin(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        operand[~operand.isfinite()] = torch.finfo(operand.dtype).max
        return operand.argmin(dim=-1).float()


class ArgmaxArgmin(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        min_value = torch.finfo(operand.dtype).min
        max_value = torch.finfo(operand.dtype).max
        operand[~operand.isfinite()] = min_value
        left = operand.argmax(dim=-1).float()
        operand[operand == min_value] = max_value
        right = operand.argmin(dim=-1).float()
        return left - right


class Incv(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        mean = operand.nanmean(dim=-1, keepdim=True)
        n = operand.isfinite().sum(dim=-1) - 1
        var = (operand - mean).pow(2).nansum(dim=-1) / n
        std = var.sqrt()
        return mean.squeeze(-1) / std


class MaxMinNorm(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        min_value = torch.finfo(operand.dtype).min
        operand[~operand.isfinite()] = min_value
        max_value = operand.max(dim=-1)[0]
        operand[operand == min_value] = torch.finfo(operand.dtype).max
        min_value = operand.min(dim=-1)[0]
        return (operand[..., -1] - min_value) / (max_value - min_value)


class Prod(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        operand[~operand.isfinite()] = 1
        return operand.prod(dim=-1)


class DecayLinear(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        weights = torch.arange(self.bar, 0, -1, device=operand.device)
        return (weights / weights.sum() * operand).nansum(dim=-1)


class DescendDecayLinear(RollingOperator):
    def apply(self, operand: torch.Tensor) -> torch.Tensor:
        weights = torch.arange(self.bar, device=operand.device) + 1
        return (weights / weights.sum() * operand).nansum(dim=-1)


class Cov(PairRollingOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        n = left.shape[-1]
        c_left = left - left.nanmean(dim=-1, keepdim=True)
        c_right = right - right.nanmean(dim=-1, keepdim=True)
        return (c_left * c_right).nansum(dim=-1) / (n - 1)


class Corr(PairRollingOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        c_left = left - left.nanmean(dim=-1, keepdim=True)
        c_right = right - right.nanmean(dim=-1, keepdim=True)
        cov = (c_left * c_right).nansum(dim=-1)
        var_left = (c_left**2).nansum(dim=-1)
        var_right = (c_right**2).nansum(dim=-1)
        bottom = (var_left * var_right).sqrt()
        bottom[(var_left < 1e-6) | (var_right < 1e-6)] = 1
        return cov / bottom


class PairSlope(PairRollingOperator):
    def apply(self, left: torch.Tensor, right: torch.Tensor) -> torch.Tensor:
        left_mean = left.nanmean(dim=-1, keepdim=True)
        right_mean = right.nanmean(dim=-1, keepdim=True)
        return ((left - left_mean) * (right - right_mean)).nansum(dim=-1) / (left - left_mean).pow(2).nansum(dim=-1)


def update(features):
    FeatureType.update(features)
    for x in list(FeatureType):
        globals()[x.name.lower()] = Feature(x)

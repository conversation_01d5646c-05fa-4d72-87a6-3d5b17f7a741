from enum import IntEnum
from typing import Type
from base.data import FeatureType
from base.expression import Operator


class SpecialTokenType(IntEnum):
    BEG = 0
    SEP = 1


class Token:
    def __repr__(self):
        return str(self)


class ConstantToken(Token):
    def __init__(self, constant: float) -> None:
        self.constant = constant

    def __str__(self):
        return str(self.constant)


class BarToken(Token):
    def __init__(self, bar: int) -> None:
        self.bar = bar

    def __str__(self):
        return str(self.bar)


class FeatureToken(Token):
    def __init__(self, feature: FeatureType) -> None:
        self.feature = feature

    def __str__(self):
        return '$' + self.feature.name.lower()


class OperatorToken(Token):
    def __init__(self, operator: Type[Operator]) -> None:
        self.operator = operator

    def __str__(self):
        return self.operator.__name__


class SpecialToken(Token):
    def __init__(self, indicator: SpecialTokenType) -> None:
        self.indicator = indicator

    def __str__(self):
        return self.indicator.name


BEG_TOKEN = SpecialToken(SpecialTokenType.BEG)
SEP_TOKEN = SpecialToken(SpecialTokenType.SEP)
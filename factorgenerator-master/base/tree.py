from typing import List, Type

from base.expression import (
    Expression,
    Constant,
    Bar,
    Feature,
    Operator,
    UnaryOperator,
    BinaryOperator,
    RollingOperator,
    PairRollingOperator,
)
from base.tokens import Token, OperatorToken, ConstantToken, BarToken, FeatureToken


class ExpressionBuilder:
    stack: List[Expression]

    def __init__(self):
        self.stack = []

    def get_tree(self) -> Expression:
        if len(self.stack) == 1:
            return self.stack[0]
        else:
            raise ValueError(f"Expected only one tree, got {len(self.stack)}")

    def add_token(self, token: Token):
        if not self.validate(token):
            raise ValueError(f"Token {token} not allowed here, stack: {self.stack}.")
        if isinstance(token, OperatorToken):
            n_args: int = token.operator.n_args()
            children = []
            for _ in range(n_args):
                children.append(self.stack.pop())
            self.stack.append(token.operator(*reversed(children)))
        elif isinstance(token, ConstantToken):
            self.stack.append(Constant(token.constant))
        elif isinstance(token, BarToken):
            self.stack.append(Bar(token.bar))
        elif isinstance(token, FeatureToken):
            self.stack.append(Feature(token.feature))
        else:
            assert False

    def is_valid(self) -> bool:
        return len(self.stack) == 1 and self.stack[0].is_feature

    def validate(self, token: Token) -> bool:
        if isinstance(token, OperatorToken):
            return self.validate_operator(token.operator)
        elif isinstance(token, BarToken):
            return self.validate_delta()
        elif isinstance(token, ConstantToken):
            return self.validate_const()
        elif isinstance(token, FeatureToken):
            return self.validate_feature()
        else:
            assert False

    def validate_operator(self, operator: Type[Operator]) -> bool:
        if len(self.stack) < operator.n_args():
            return False

        if issubclass(operator, UnaryOperator):
            if not self.stack[-1].is_feature:
                return False
        elif issubclass(operator, BinaryOperator):
            if not self.stack[-1].is_feature and not self.stack[-2].is_feature:
                return False
            if isinstance(self.stack[-1], Bar) or isinstance(self.stack[-2], Bar):
                return False
        elif issubclass(operator, RollingOperator):
            if not isinstance(self.stack[-1], Bar):
                return False
            if not self.stack[-2].is_feature:
                return False
        elif issubclass(operator, PairRollingOperator):
            if not isinstance(self.stack[-1], Bar):
                return False
            if not self.stack[-2].is_feature or not self.stack[-3].is_feature:
                return False
        else:
            assert False
        return True

    def validate_delta(self) -> bool:
        return len(self.stack) > 0 and self.stack[-1].is_feature

    def validate_const(self) -> bool:
        return len(self.stack) == 0 or self.stack[-1].is_feature

    def validate_feature(self) -> bool:
        return not (len(self.stack) >= 1 and isinstance(self.stack[-1], Bar))


if __name__ == "__main__":
    from base.data import FeatureType
    from base.expression import Abs, Ref, Div, Add, RollRank

    tokens = [
        FeatureToken(FeatureType.LOW),
        OperatorToken(Abs),
        BarToken(-10),
        OperatorToken(Ref),
        FeatureToken(FeatureType.HIGH),
        FeatureToken(FeatureType.CLOSE),
        OperatorToken(Div),
        OperatorToken(Add),
    ]
    Operator(RollRank)
    BarToken(-10)

    builder = ExpressionBuilder()
    for e in tokens:
        builder.add_token(e)

    print(f"res: {str(builder.get_tree())}")
    print(f"ref: Add(Ref(Abs($low),-10),Div($high,$close))")

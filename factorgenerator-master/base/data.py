from typing import List, Optional
from enum import IntEnum

import os
import json
import torch
import math
import random
import numpy as np
import pandas as pd


class EnumMock(type):
    def __getitem__(self, name):
        return self(None, name)

    def __getattr__(self, name):
        return self(None, name)

    def __iter__(self):
        for x in self.Enum:
            yield self(None, x.name)


class FeatureType(object, metaclass=EnumMock):
    _ori = None  # 备份原Enum对象
    Enum = IntEnum(
        "Feature",
        [
            "OPEN",
            "CLOSE",
            "HIGH",
            "LOW",
            "VOLUME",
            "TURNOVER",
            "turnover_rate",
            "quote_rate",
            "amp_rate",
            "avg_price",
            "prev_close",
            "act_buy",
            "act_sell",
            "dde_l",
            "net_flow_rate",
            "l_net_value",  # 资金流
            "fin_buy_value",
            "sec_sell_value",
            "fin_value",
            "sec_value",  # mtss
            "pe_mrq",
            "pb_mrq",
            "ps_mrq",
            "total_mv",
            "float_mv",  # ashare
            "thsindex1",
            "thsindex2_a",
            "thsindex2_b",
            "thsindex2_c",
            "thsindex3",
            "thsindex4",
            "thsindex4j",  # thsindex
            "thsindex5_a",
            "thsindex5_b",
            "thsindex5_c",
            "thsindex6",
            "thsindex6j",
            "thsindex7",
            "thsindex7j",
            "focus_sticky_1d",
            "focus_sticky_3d",
            "focus_sticky_7d",
            "focus_sticky_30d",
            "heat_1d",
            "heat_7d",
            "heat_30d",
            "heat_focus_1d",
            "heat_focus_7d",
            "heat_focus_30d",
            "heat_search_1d",
            "heat_search_7d",
            "heat_search_30d",
            "heat_zxstk",
            "heat_zxroc_1d",
            "heat_zxroc_7d",
            "heat_zxroc_30d",
        ],
        start=0,
    )

    @classmethod
    def update(cls, fields=None):
        if cls._ori is None:
            cls._ori = cls.Enum
        cls.Enum = IntEnum("Feature", fields, start=0) if fields else cls._ori

    def __init__(self, value, name=None):
        self.name = name or self.Enum(value).name

    def __getattr__(self, attr):
        return getattr(self.Enum[self.name], attr)

    def __int__(self):
        return int(self.Enum[self.name])


class DataSrc:
    def __init__(self, mdf, freq):
        self.mdf = mdf  # 多索引dataframe数据
        self.freq = freq

    @property
    def index(self):
        return self.mdf.index

    def reindex(self, align):
        self.mdf = self.mdf.reindex(align.index)

    @property
    def ndarr(self):
        mdf = self.mdf
        mdf["date"] = mdf.index.get_level_values("major").normalize()  # 拆分date和time
        mdf["time"] = mdf.index.get_level_values("major").strftime("%H:%M:%S")
        mdf["symbol"] = mdf.index.get_level_values("minor")  # 将3维扩成4维
        mdf = mdf.set_index(["date", "time", "symbol"]).to_xarray()
        self.date_arr, self.time_arr, self.code_arr = mdf.date.values, mdf.time.values, mdf.symbol.values
        if self.freq.endswith("d"):
            values = mdf.to_array().to_numpy().transpose(2, 1, 0, 3)  # time, date, field, symbol
        else:
            values = mdf.to_array().to_numpy().transpose(1, 2, 0, 3)  # date, time, field, symbol
        return values

    @property
    def tsarr(self):
        return torch.tensor(self.ndarr, dtype=torch.float, device="cpu")  # MAN: 数据可选强制放cpu上(防止显存不足)


class MmapSrc(DataSrc):
    def __init__(self, mdf, freq, start_date=None, end_date=None):
        self.mdf = mdf  # 内存文件所在目录
        with open(os.path.join(mdf, "config.json"), "r") as f:
            self.config = json.loads(f.read())
        assert "date" in self.config, "配置缺少date属性"
        assert "time" in self.config, "配置缺少time属性"
        assert "symbol" in self.config, "配置缺少symbol属性"
        assert "shape" in self.config, "配置缺少shape属性"
        assert "fields" in self.config, "配置缺少fields属性"
        self.freq = freq
        self.si = start_date and np.searchsorted(self.config["date"], start_date.strftime("%Y-%m-%d"), side="left")
        self.ei = end_date and np.searchsorted(self.config["date"], end_date.strftime("%Y-%m-%d"), side="right")

    @classmethod
    def init_data(cls, mdf, fields, date, time, symbol):
        """初始化共享内存传入四个维度列表
        mdf: 文件夹路径
        date: 日期列表
        time: 时间列表
        fields: 字段列表
        symbol: 股票列表
        """
        dshape = (len(date), len(time), len(fields), len(symbol))
        with open(os.path.join(mdf, "config.json"), "w") as f:
            f.write(
                json.dumps(
                    {
                        "date": date,
                        "time": time,
                        "symbol": symbol,
                        "shape": dshape,
                        "fields": fields,
                    }
                )
            )
        marr = np.memmap(os.path.join(mdf, "data"), dtype=float, mode="w+", shape=dshape)
        marr.flush()
        return marr

    @classmethod
    def update_data(cls, mdf, ddf):
        """更新共享内存数据
        mdf: 文件夹路径
        ddf: index为['date', 'symbol']其中date为pd.Timestamp格式, columns为fields的dataframe格式
        """
        with open(os.path.join(mdf, "config.json"), "r") as f:
            config = json.loads(f.read())
        date = {k: i for i, k in enumerate(config["date"])}
        time = {k: i for i, k in enumerate(config["time"])}
        fields = {k: i for i, k in enumerate(config["fields"])}
        symbol = {k: i for i, k in enumerate(config["symbol"])}
        marr = np.memmap(os.path.join(mdf, "data"), dtype=float, mode="w+", shape=tuple(config["shape"]))
        for i in ddf.to_records():
            d, t, s = i[0].strftime("%Y-%m-%d"), i[0].strftime("%H:%M:%S"), i[1]
            for k in ddf.columns:
                marr[date[d], time[t], fields[k], symbol[s]] = i[k]
        marr.flush()
        return marr

    def mock_df(self):
        return pd.DataFrame(columns=self.config["fields"])

    @property
    def index(self):
        pass

    @property
    def date_arr(self):
        return np.array(self.config["date"][self.si : self.ei], dtype="datetime64[s]")

    @property
    def time_arr(self):
        return np.array(self.config["time"], dtype="<U10")

    @property
    def code_arr(self):
        return np.array(self.config["symbol"], dtype="<U10")

    @property
    def ndarr(self):
        values = np.memmap(
            os.path.join(self.mdf, "data"),
            dtype=self.config.get("dtype", float),
            mode="r",
            shape=tuple(self.config["shape"]),
        )
        values = values.transpose(*self.config.get("transpose", (0, 1, 2, 3)))[self.si : self.ei]
        return values.transpose(1, 0, 2, 3) if self.freq.endswith("d") else values

    @property
    def tsarr(self):
        return torch.from_numpy(self.ndarr)


class StockData:
    price_featureset = {
        "open",
        "close",
        "high",
        "low",
        "volume",
        "turnover",
        "turnover_rate",
        "quote_rate",
        "amp_rate",
        "avg_price",
        "prev_close",
    }
    money_featureset = {"act_buy", "act_sell", "dde_l", "net_flow_rate", "l_net_value"}
    mtss_featureset = {"fin_buy_value", "sec_sell_value", "fin_value", "sec_value"}
    ashare_featureset = {"pe_mrq", "pb_mrq", "ps_mrq", "total_mv", "float_mv"}
    thsindex_featureset = {
        "thsindex1",
        "thsindex2_a",
        "thsindex2_b",
        "thsindex2_c",
        "thsindex3",
        "thsindex4",
        "thsindex4j",
        "thsindex5_a",
        "thsindex5_b",
        "thsindex5_c",
        "thsindex6",
        "thsindex6j",
        "thsindex7",
        "thsindex7j",
        "focus_sticky_1d",
        "focus_sticky_3d",
        "focus_sticky_7d",
        "focus_sticky_30d",
        "heat_1d",
        "heat_7d",
        "heat_30d",
        "heat_focus_1d",
        "heat_focus_7d",
        "heat_focus_30d",
        "heat_search_1d",
        "heat_search_7d",
        "heat_search_30d",
        "heat_zxstk",
        "heat_zxroc_1d",
        "heat_zxroc_7d",
        "heat_zxroc_30d",
    }

    def __init__(
        self,
        index,
        start_time,
        end_time,
        freq,
        max_backtrack_bars: int = 200,
        max_future_bars: int = 40,
        step: int = 0,
        sample_rate: float = 1.0,
        featfile: str = None,
        fields: Optional[List[str]] = None,
        device: torch.device = torch.device("cpu"),
    ) -> None:
        self.freq = freq
        self.device = device
        self.max_backtrack_bars = max_backtrack_bars
        self.max_future_bars = max_future_bars
        self.step = step
        self.sample_rate = sample_rate

        self.features = [x.name.lower() for x in FeatureType]
        fields = [x.lower() for x in (fields or self.features)]
        try:
            if not featfile:
                mdf = self.prepare_data(index, start_time, end_time, freq, fields, max_backtrack_bars, max_future_bars)
            elif featfile.startswith("+"):
                mdf = self.prepare_data(
                    index, start_time, end_time, freq, fields, max_backtrack_bars, max_future_bars, featfile.lstrip("+")
                )
            else:
                mdf = self.read_featfile(
                    featfile, start_time, end_time, freq, fields, max_backtrack_bars, max_future_bars
                )
        except (ImportError, AttributeError):
            mdf = self.dbfile_data(index, start_time, end_time, freq, fields, max_backtrack_bars, max_future_bars)
        self.datasrc = mdf if isinstance(mdf, DataSrc) else DataSrc(mdf, freq)
        self.init_tsor()

    def init_tsor(self, align=None):
        """将df转为tensor格式
        align: StockData 以该索引对齐
        """
        if align and isinstance(align.datasrc, MmapSrc):
            self.datasrc = align.datasrc
            print("内存映射模式不做target对齐,直接改用features对应的前i个字段!")
        else:
            align and self.datasrc.reindex(align.datasrc)
        self.data = self.datasrc.tsarr
        self.dates, self.times, self.stock_ids = self.datasrc.date_arr, self.datasrc.time_arr, self.datasrc.code_arr
        self.n_primary_bars, self.n_secondary_bars = self.data.shape[0], self.data.shape[1]
        if self.freq.endswith("d"):
            self.n_secondary_bars -= self.max_backtrack_bars + self.max_future_bars
            self.n_step = self.n_primary_bars
            self.sample_rate = 1.0
        else:
            assert (self.max_backtrack_bars + self.max_future_bars) % self.n_secondary_bars == 0, (
                f"回看和后看bar总和需为{self.n_secondary_bars}倍数"
            )
            self.n_primary_bars -= (self.max_backtrack_bars + self.max_future_bars) // self.n_secondary_bars
            self.n_step = self.step or self.n_primary_bars
        self.data = self.data.view(-1, self.data.shape[-2], self.data.shape[-1])
        self.resample()
        return self

    def resample(self, values=None):
        involved_indexes = list(range(math.ceil(self.n_primary_bars / self.n_step)))
        if self.sample_rate < 1:
            involved_indexes = random.sample(involved_indexes, int(len(involved_indexes) * self.sample_rate))
        self.sample_indexes = []
        sample_values = []
        for i, n in enumerate(range(0, self.n_primary_bars, self.n_step)):
            if values:
                if i not in involved_indexes:
                    continue
                sample_values.append(values[i])
                self.sample_indexes.append(n)
            else:
                self.sample_indexes.append(n)
        return sample_values

    @classmethod
    def read_featfile(
        cls,
        f,
        start_time=None,
        end_time=None,
        freq=None,
        fields=None,
        max_backtrack_bars=100,
        max_future_bars=30,
        limit=None,
    ):
        if start_time and end_time and freq:
            from mindgo_api import get_last_trade_day, get_price

            calendar = get_price(
                "000300.SH",
                get_last_trade_day(start_time, -max_backtrack_bars - 1),
                get_last_trade_day(end_time, max_future_bars + 1),
                freq,
                ["open"],
            ).index
            max_date = get_last_trade_day()  # 跑训练只需要上个交易日
            start_index = max(calendar.get_indexer([start_time], method="bfill") - max_backtrack_bars, 0)
            end_index = min(calendar.get_indexer([end_time], method="ffill") + max_future_bars, len(calendar) - 1)
            start_time, end_time = calendar[start_index].normalize(), min(calendar[end_index], max_date)
        where = 'date>="{}" and date<="{}"'.format(start_time, end_time) if start_time and end_time else None
        if f.endswith(".csv"):
            df = pd.read_csv(f, parse_dates=["date"])
            df = df.query(where) if where else df
        elif f.endswith(".pkl"):
            df = pd.read_pickle(f)
            df = df.query(where) if where else df
        elif f.endswith(".dat"):
            df = MmapSrc(f, freq, start_time, end_time)
            fldset = set(df.config["fields"])
            assert not set(fields or fldset) ^ fldset, "共享内存方式不支持选择features字段"
            return df.mock_df() if limit else df
        else:
            df = pd.read_hdf(f, key="all", where=where)
        df = df.rename(columns={"date": "major", "symbol": "minor"}).set_index(["major", "minor"])
        df = df[fields] if fields else df
        return df.head(limit) if limit else df

    @classmethod
    def get_open_api(cls):
        from mindgo_api import get_open_api

        if not hasattr(cls, "_open_api"):
            cls._open_api = get_open_api("share:research")
        return cls._open_api

    @classmethod
    def dbfile_data(self, index, start_time, end_time, freq, fields, max_backtrack_bars=100, max_future_bars=30):
        """
        本地加载数据
        """
        dbfile = f"dbfile/data-{freq}.h5"
        calendar = pd.DatetimeIndex(pd.read_hdf(dbfile, key="trade_date").major)
        max_date = pd.Timestamp.now().normalize() - pd.Timedelta(days=1)  # 跑训练只需要上个交易日
        start_index = max(calendar.get_indexer([start_time], method="bfill")[0] - max_backtrack_bars, 0)
        end_index = min(calendar.get_indexer([end_time], method="ffill")[0] + max_future_bars, len(calendar) - 1)
        start_time, end_time = calendar[start_index].normalize(), min(calendar[end_index], max_date)
        where = 'major>="{}" and major<="{}"'.format(start_time, end_time)
        mdf = pd.read_hdf(dbfile, key="all", where=where)
        if index != "all":
            securities = sorted({s for x in index.split(",") for s in pd.read_hdf(dbfile, key=x, where=where)["minor"]})
            mdf = mdf[mdf.index.get_level_values("minor").isin(securities)]
        return mdf[fields].fillna(0 if freq.endswith("m") else np.nan)

    @classmethod
    def prepare_data(
        self, index, start_time, end_time, freq, fields, max_backtrack_bars=100, max_future_bars=30, featfile=None
    ):
        """
        features: 为字符串fields小写
        """
        from mgquant_mod_mindgo.research.research_api import get_all_securities
        from mindgo_api import (
            get_last_trade_day,
            get_price,
            get_money_flow_step,
            get_mtss,
            query,
            asharevalue,
            get_normal_query,
        )

        open_api = self.get_open_api()
        calendar = get_price(
            "000300.SH",
            get_last_trade_day(start_time, -max_backtrack_bars - 1),
            get_last_trade_day(end_time, max_future_bars + 1),
            freq,
            ["open"],
        ).index
        max_date = get_last_trade_day()  # 跑训练只需要上个交易日
        start_index = max(calendar.get_indexer([start_time], method="bfill")[0] - max_backtrack_bars, 0)
        end_index = min(calendar.get_indexer([end_time], method="ffill")[0] + max_future_bars, len(calendar) - 1)
        start_time, end_time = calendar[start_index].normalize(), min(calendar[end_index], max_date)
        if index == "all":
            securities = sorted(get_all_securities("stock", "nat").query("end_date>'2005'").index.tolist())
        else:
            securities = sorted(
                {s for x in index.split(",") for s in open_api.range_index_stocks(x, start_time, end_time)["symbol"]}
            )
        fields_set = set(fields)
        pfields = list(fields_set & self.price_featureset)
        mdf = get_price(
            securities,
            start_date=start_time,
            end_date=end_time,
            fre_step=freq,
            fields=pfields,
            skip_paused=False,
            fq="post",
            is_panel=True,
        ).to_frame()
        pfields = list(fields_set & self.money_featureset)
        if pfields:
            money_features = [
                "act_buy_xl",
                "act_buy_l",
                "act_buy_m",
                "act_sell_xl",
                "act_sell_l",
                "act_sell_m",
                "dde_l",
                "net_flow_rate",
                "l_net_value",
            ]
            tdf = get_money_flow_step(
                securities,
                start_date=start_time,
                end_date=end_time,
                fre_step=freq,
                fields=money_features,
                is_panel=True,
            ).to_frame()
            tdf["act_buy"] = tdf["act_buy_xl"] + tdf["act_buy_l"]
            tdf["act_sell"] = tdf["act_sell_xl"] + tdf["act_sell_l"]
            mdf = mdf.join(tdf[pfields], how="left")
        pfields = list(fields_set & self.mtss_featureset)
        if pfields:
            tdf = (
                get_mtss(securities, start_date=start_time, end_date=end_time, fields=pfields, is_panel=True)
                .to_frame()
                .fillna(0)
            )
            mdf = mdf.join(tdf, how="left")
        pfields = list(fields_set & self.ashare_featureset)
        if pfields:
            q = (
                query(
                    asharevalue.date,
                    asharevalue.symbol,
                    asharevalue.pe_mrq,
                    asharevalue.pb_mrq,
                    asharevalue.ps_mrq,
                    asharevalue.total_mv,
                    asharevalue.float_mv,
                )
                .filter(asharevalue.date >= start_time, asharevalue.date <= end_time)
                .order_by(asharevalue.date)
            )
            tdf = get_normal_query(q)
            tdf.columns = [x[12:] for x in tdf.columns]
            tdf["date"] = pd.to_datetime(tdf["date"])
            tdf = tdf.rename(columns={"date": "major", "symbol": "minor"}).set_index(["major", "minor"])
            mdf = mdf.join(tdf[pfields], how="left")
        pfields = list(fields_set & self.thsindex_featureset)
        if pfields:
            tdf = open_api.get_hxfactor_thsindex(
                start_time, end_time, pfields, securities if len(securities) < 1000 else None
            )
            tdf = tdf.rename(columns={"date": "major", "symbol": "minor"}).set_index(["major", "minor"])
            mdf = mdf.join(tdf, how="left")
        pfields = list(fields_set & set("ff_" + self.read_featfile(featfile, limit=1).columns)) if featfile else []
        if pfields:
            tdf = self.read_featfile(
                featfile, start_time, end_time, fields=[x.replace("ff_", "", 1) for x in pfields]
            ).set_axis(pfields, axis=1)
            mdf = mdf.join(tdf, how="left")
        return mdf[fields].fillna(0 if freq.endswith("m") else np.nan)

    @property
    def n_features(self) -> int:
        return len(self.features)

    @property
    def n_stocks(self) -> int:
        return self.data.shape[-1]

    @property
    def n_bars(self) -> int:
        return self.n_primary_bars * self.n_secondary_bars

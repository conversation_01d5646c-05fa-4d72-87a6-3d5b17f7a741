from itertools import product
from typing import List, Optional, Tuple, MutableMapping, Iterable

import cvxpy as cp
import numpy as np
import torch

from base.calculator import AlphaCalculator, IndicatorCalculator
from base.expression import Constant, Expression, RollRank1, Sign, Sub, Mean


class AlphaPool:
    MIN_REWARD = 0.0
    INVALID_EXPR_PUNISH = -1.0

    def __init__(
        self,
        pool_size: int,
        max_expr_length: int,
        calculator: AlphaCalculator,
        learning_rate: float = 5e-4,
        n_epochs: int = 512,
        max_epochs: int = 8096,
        ic_single_threshold: float = 0,
        ic_mutual_threshold: float = 0.85,
        l1_alpha: float = 5e-3,
        weight_cvx: float = 3.0,
        weight_rand: float = 2.0,
        weight_up_limit: float = 5.0,
        weight_error: float = 50.0,
        repeat_threshold: int = float("inf"),
        device: torch.device = torch.device("cpu"),
    ):
        self.pool_size = pool_size
        self.max_expr_length = max_expr_length
        self.calculator = calculator
        self.learning_rate = learning_rate
        self.n_epochs = n_epochs
        self.max_epochs = max_epochs
        self.device = device

        self.size: int = 0
        self.tokens_pool: List[List[int]] = [[0] * (self.max_expr_length + 1) for _ in range(self.pool_size + 1)]
        self.expr_pool: List[Optional[Expression]] = [None for _ in range(self.pool_size + 1)]
        self.single_ics: np.ndarray = np.zeros(self.pool_size + 1)
        self.mutual_ics: np.ndarray = np.identity(self.pool_size + 1)
        self.weights: np.ndarray = np.zeros(self.pool_size + 1)

        self.best_ic: float = -1.0
        self.repeat_count = 0

        self.repeat_threshold = repeat_threshold

        self.ic_train = None

        self.ic_single_threshold = ic_single_threshold
        self.ic_mutual_threshold = ic_mutual_threshold
        self.l1_alpha = l1_alpha
        self.weight_cvx = weight_cvx
        self.weight_rand = weight_rand
        self.weight_up_limit = weight_up_limit
        self.weight_error = weight_error

        self.eval_count = 0
        self.exprs = set()

    def reset(self):
        self.size: int = 0
        self.tokens_pool: List[List[int]] = [[0] * (self.max_expr_length + 1) for _ in range(self.pool_size + 1)]
        self.expr_pool: List[Optional[Expression]] = [None for _ in range(self.pool_size + 1)]
        self.single_ics: np.ndarray = np.zeros(self.pool_size + 1)
        self.mutual_ics: np.ndarray = np.identity(self.pool_size + 1)
        self.weights: np.ndarray = np.zeros(self.pool_size + 1)

        self.best_ic: float = -1.0
        self.repeat_count = 0

    def to_dict(self, **kwargs) -> dict:
        return {
            "expr_pool": [str(expr) for expr in self.expr_pool[: self.size]],
            "weights": list(self.weights[: self.size]),
            "ics_train": list(self.single_ics[: self.size]),
            "ic_test": kwargs["ic_test"],
            "ic_train": self.ic_train,
        }

    def evaluate(self, tokens: List[int], expr: Expression) -> float:
        # TODO: embed expr in a Rank func
        self.exprs.add(str(expr))
        ic_single, ic_mutual = self.cal_ics(expr)
        if ic_mutual is None:
            return self.MIN_REWARD

        self.add(tokens, expr, ic_single, ic_mutual)
        if self.size > 1:
            # TODO: replace to Exhaustive check
            new_weights = self.optimize()
            worst_idx = np.argmin(np.abs(new_weights))
            if worst_idx != self.pool_size:
                self.weights[: self.size] = new_weights
                self.repeat_count = 0
            else:
                self.repeat_count += 1
                if self.repeat_count > self.repeat_threshold:
                    self.reset()
                    return self.MIN_REWARD

            self.pop()

        self.ic_train = self.evaluate_ensemble()
        if self.ic_train > self.best_ic:
            self.best_ic = self.ic_train
        self.eval_count += 1
        return self.ic_train

    def optimize(self) -> np.ndarray:
        ics_ret = torch.from_numpy(self.single_ics[: self.size]).to(self.device)
        ics_mut = torch.from_numpy(self.mutual_ics[: self.size, : self.size]).to(self.device)
        weights = torch.from_numpy(self.weights[: self.size]).to(self.device).requires_grad_()
        optim = torch.optim.Adam([weights], lr=self.learning_rate)

        loss_ic_min = float("inf")
        best_weights = weights.cpu().detach().numpy()
        iter_count = 0

        weight_limit = 0.1 / self.size
        if np.mean(np.abs(best_weights)) > weight_limit * self.weight_cvx:
            p = self.mutual_ics[: self.size, : self.size]
            q = self.single_ics[: self.size]
            try:
                x = cp.Variable(self.size)
                thresh = np.abs(q) / np.mean(np.abs(q)) * np.random.rand(q.shape[0]) * weight_limit * self.weight_cvx
                prob = cp.Problem(cp.Minimize((1 / 2) * cp.quad_form(x, p) - q.T @ x), [x <= thresh, x >= -thresh])
                prob.solve()
                if x.value is not None:
                    best_weights = x.value
                else:
                    best_weights = np.sign(q) * np.random.rand(q.shape[0]) * weight_limit * self.weight_rand
            except:
                best_weights = np.sign(q) * np.random.rand(q.shape[0]) * weight_limit * self.weight_rand

        for it in range(self.max_epochs):
            ret_ic_sum = (weights * ics_ret).sum()
            mut_ic_sum = (torch.outer(weights, weights) * ics_mut).sum()
            loss_ic = mut_ic_sum - 2 * ret_ic_sum + 1
            loss_ic_curr = loss_ic.item()

            if loss_ic_curr < loss_ic_min and weights.abs().mean().item() < weight_limit * self.weight_up_limit:
                best_weights = weights.cpu().detach().numpy()
                loss_ic_min = loss_ic_curr

            loss_l1 = torch.norm(weights, p=1)
            loss = loss_ic + self.l1_alpha * loss_l1

            optim.zero_grad()
            loss.backward()
            optim.step()

            if loss_ic_min - loss_ic_curr > 1e-6:
                iter_count = 0
            else:
                iter_count += 1

            if iter_count >= self.n_epochs or weights.abs().mean().item() > weight_limit * self.weight_error:
                break
        return best_weights

    def test_ensemble(self, calculator: AlphaCalculator) -> float:
        ic = calculator.cal_pool_ic_ret(self.expr_pool[: self.size], self.weights[: self.size])
        return ic

    def evaluate_ensemble(self) -> float:
        ic = self.calculator.cal_pool_ic_ret(self.expr_pool[: self.size], self.weights[: self.size])
        return ic

    def cal_ics(self, expr: Expression) -> Tuple[float, Optional[List[float]]]:
        single_ic = self.calculator.cal_single_ic_ret(expr)
        if abs(single_ic) < self.ic_single_threshold:
            return single_ic, None

        mutual_ics = []
        for i in range(self.size):
            mutual_ic = self.calculator.cal_mutual_ic(expr, self.expr_pool[i])
            if abs(mutual_ic) > self.ic_mutual_threshold:
                return single_ic, None
            mutual_ics.append(mutual_ic)

        return single_ic, mutual_ics

    def add(self, tokens: List[int], expr: Expression, ic_ret: float, ic_mut: List[float]):
        self.pop()
        n = self.size
        self.expr_pool[n] = expr
        self.tokens_pool[n] = tokens + [0] * (self.max_expr_length - len(tokens) + 1)
        self.single_ics[n] = ic_ret
        for i in range(n):
            self.mutual_ics[i][n] = self.mutual_ics[n][i] = ic_mut[i]
        if n > 0:
            rank = min(np.argsort(self.single_ics[: n + 1])[n], n - 1)
            if rank < 0:
                min_weight = 0.1 / self.pool_size
            else:
                min_weight = max(np.sort(self.weights[:n])[rank], 0.1 / self.pool_size)
        else:
            min_weight = 0.1 / self.pool_size
        self.weights[n] = np.sign(ic_ret) * min_weight
        self.size += 1

    def pop(self):
        if self.size <= self.pool_size:
            return
        idx = np.argmin(np.abs(self.weights))
        self.swap(idx, self.pool_size)
        self.size = self.pool_size

    def swap(self, i, j):
        if i == j:
            return
        self.expr_pool[i], self.expr_pool[j] = self.expr_pool[j], self.expr_pool[i]
        self.tokens_pool[i], self.tokens_pool[j] = self.tokens_pool[j], self.tokens_pool[i]
        self.single_ics[i], self.single_ics[j] = self.single_ics[j], self.single_ics[i]
        self.mutual_ics[:, [i, j]] = self.mutual_ics[:, [j, i]]
        self.mutual_ics[[i, j], :] = self.mutual_ics[[j, i], :]
        self.weights[i], self.weights[j] = self.weights[j], self.weights[i]


class IndicatorPool(AlphaPool):
    MIN_REWARD = -1.0
    INVALID_EXPR_PUNISH = -2.0

    calculator: IndicatorCalculator
    single_backtest_threshold: float
    expr_indicator_mapping: MutableMapping[Expression, Expression]
    quantile_bars: Iterable[int]
    signal_thresholds: Iterable[float]

    def __init__(
        self,
        pool_size: int,
        max_expr_length: int,
        calculator: IndicatorCalculator,
        single_backtest_threshold: float = 0,
        count_threshold: int = 20,
        expr_ic_mutual_threshold: float = 0.85,
        indicator_ic_mutual_threshold: float = 0.85,
        quantile_bars: Iterable[int] = [10, 20, 50],
        signal_thresholds: Iterable[float] = [0.1, 0.2, 0.3],
        repeat_threshold: int = float("inf"),
        device: torch.device = torch.device("cpu"),
    ):
        super().__init__(
            pool_size=pool_size,
            max_expr_length=max_expr_length,
            calculator=calculator,
            ic_mutual_threshold=expr_ic_mutual_threshold,
            repeat_threshold=repeat_threshold,
            device=device,
        )
        self.single_backtest_threshold = single_backtest_threshold
        self.count_threshold = count_threshold
        self.indicator_ic_mutual_threshold = indicator_ic_mutual_threshold
        self.quantile_bars = quantile_bars
        self.signal_thresholds = signal_thresholds

        self.best_ic = -float("inf")
        self.expr_indicator_mapping = {}

    def search_params(self, expr: Expression) -> Tuple[Optional[Expression], Optional[float], Optional[List[float]]]:
        mutual_ics = []
        for i in range(self.size):
            mutual_ic = self.calculator.cal_mutual_ic(expr, self.expr_pool[i])
            if abs(mutual_ic) > self.ic_mutual_threshold:
                return None, None, None
            mutual_ics.append(mutual_ic)

        a, b = [], []
        for i, j in product(self.quantile_bars, self.signal_thresholds):
            expr_ = RollRank1(expr, i)
            expr_ = Sign(Sign(Sub(expr_, Constant(j))) + Sign(Sub(expr_, Constant(1 - j))))
            # expr_ = Mean(expr_, 2)
            # expr_ = Sign(Sign(Sub(expr_, Constant(-0.6))) + Sign(Sub(expr_, Constant(0.6))))
            single = self.calculator.calc_single_backtest(expr_)
            if (
                single["total_win_rate"] < self.single_backtest_threshold
                or single["count"] / single["days"] * 252 < self.count_threshold
            ):
                continue

            for k in range(self.size):
                mic = self.calculator.cal_mutual_ic(expr_, self.expr_indicator_mapping[self.expr_pool[k]])
                if abs(mic) > self.indicator_ic_mutual_threshold:
                    break
            else:
                a.append(expr_)
                b.append(single["sortino"])

        if len(a) == 0:
            return None, None, None
        idx = np.argmax(b)
        return a[idx], b[idx], mutual_ics

    def evaluate(self, tokens: List[int], expr: Expression) -> float:
        indicator, bck_performance, ic_mutual = self.search_params(expr)
        if indicator is None:
            return self.MIN_REWARD

        self.exprs.add(str(expr))
        self.expr_indicator_mapping[expr] = indicator
        self.add(tokens, expr, bck_performance, ic_mutual)

        new_weights = self.optimize()
        worst_idx = np.argmin(np.abs(new_weights))
        if worst_idx != self.pool_size:
            self.weights[: self.size] = new_weights
            self.repeat_count = 0
        else:
            self.repeat_count += 1
            if self.repeat_count > self.repeat_threshold:
                self.reset()
                return self.MIN_REWARD

        self.pop()

        self.ic_train = self.evaluate_ensemble()
        if self.ic_train > self.best_ic:
            self.best_ic = self.ic_train
        self.eval_count += 1
        return np.tanh(self.ic_train * 0.05)

    def test_ensemble(self, calculator: IndicatorCalculator) -> float:
        performance = calculator.cal_pool_backtest(
            [self.expr_indicator_mapping[expr] for expr in self.expr_pool[: self.size]], self.weights[: self.size]
        )
        return performance

    def evaluate_ensemble(self) -> float:
        performance = self.calculator.cal_pool_backtest(
            [self.expr_indicator_mapping[expr] for expr in self.expr_pool[: self.size]], self.weights[: self.size]
        )
        return performance

    def to_dict(self, **kwargs) -> dict:
        res = super().to_dict(**kwargs)
        res.update({"indicator_pool": [str(self.expr_indicator_mapping[expr]) for expr in self.expr_pool[: self.size]]})
        res.update(
            {
                "ics_test": [
                    [
                        i.calc_single_backtest(self.expr_indicator_mapping[expr])["total_win_rate"]
                        for expr in self.expr_pool[: self.size]
                    ]
                    for i in kwargs["test_calculator"]
                ]
            }
        )
        return res

    def optimize(self) -> np.ndarray:
        if self.size <= self.pool_size:
            res = np.zeros(self.size, dtype=np.float64)
            res[: self.size] = 1 / self.size
            return res
        elif self.size == self.pool_size + 1:
            res = np.zeros(self.pool_size + 1, dtype=np.float64)
            for i in range(self.pool_size + 1):
                res[i] = self.calculator.cal_pool_backtest(
                    [self.expr_indicator_mapping[expr] for enu, expr in enumerate(self.expr_pool) if enu != i],
                    np.full(self.pool_size, 1 / self.pool_size, dtype=np.float64),
                )
            idx = np.argmin(res)
            res.fill(1 / self.pool_size)
            res[idx] = 0.0
            return res
        else:
            raise

import json

import pathlib
from datetime import datetime
from functools import reduce
from itertools import product
from typing import Mapping, Optional

import matplotlib.pyplot as plt
import numba as nb
import numpy as np
import pandas as pd
import torch
from mindgo_api import get_price


def expr_cal(expr_str, data):
    scope = dict()
    exec("from base.expression import *", scope)
    ecode = expr_str.rsplit("\n", 1)
    ecode, eline = ecode if len(ecode) == 2 else ("", ecode[-1])
    exec(ecode, scope)
    target = eval(eline, scope)
    # print(target)
    out = target(data)

    times = data.times
    date_strs = data.dates.astype("datetime64[D]").astype(str)
    combinations = list(product(date_strs, times))

    # 将每个组合转换为完整的datetime字符串并创建datetime对象
    datetime_objects = [datetime.strptime(date + " " + time, "%Y-%m-%d %H:%M:%S") for date, time in combinations]

    # 如果需要，可以将datetime对象转换回numpy的datetime64类型
    datetime_array = np.array(datetime_objects, dtype="datetime64[s]")

    if data.device == "cuda":
        rtn = (
            pd.DataFrame(
                np.vstack([x.cpu().numpy() for x in list(out)]),
                index=datetime_array[data.max_backtrack_bars : -data.max_future_bars],
                columns=data.stock_ids,
            )
            .stack()
            .reset_index()
            .rename(columns={"level_0": "trade_date", "level_1": "ts_code", 0: "fct"})
        )
    else:
        rtn = (
            pd.DataFrame(
                np.vstack([x.numpy() for x in list(out)]),
                index=datetime_array[data.max_backtrack_bars : -data.max_future_bars],
                columns=data.stock_ids,
            )
            .stack()
            .reset_index()
            .rename(columns={"level_0": "trade_date", "level_1": "ts_code", 0: "fct"})
        )

    return rtn


def get_indicators(path):
    res = []
    with open(pathlib.Path(path) / "steps.txt", "r") as f:
        for i in f:
            res.append(json.loads(i))
    result = {
        "indicator": reduce(lambda x, y: x + y, [i["indicator_pool"] for i in res]),
        "expr": reduce(lambda x, y: x + y, [i["expr_pool"] for i in res]),
        "ic_train": reduce(lambda x, y: x + y, [i["ics_train"] for i in res]),
    }
    result.update(
        {f"ic_test_{i}": reduce(lambda x, y: x + y, j) for i, j in enumerate(zip(*[i["ics_test"] for i in res]))}
    )
    return pd.DataFrame(result).drop_duplicates().sort_values("ic_train", ascending=False).reset_index(drop=True)


def plot(position: pd.Series, one_way):
    symbol = position.name
    if one_way == 1:
        position = pd.Series(np.where(position > 0, position, 0), index=position.index).rename(symbol)
    elif one_way == -1:
        position = pd.Series(np.where(position < 0, position, 0), index=position.index).rename(symbol)
    data = get_price(symbol, position.index[0], position.index[-1], "1d", ["close"], fq="post")["close"]
    ddd = pd.concat([data, position], axis=1)
    plt.figure(figsize=(15, 5))
    for i in range(1, len(ddd)):
        if ddd[symbol].iat[i - 1] == 1:
            plt.plot(ddd.index[i - 1 : i + 1], ddd["close"][i - 1 : i + 1], color="red")
        elif ddd[symbol].iat[i - 1] == -1:
            plt.plot(ddd.index[i - 1 : i + 1], ddd["close"][i - 1 : i + 1], color="green")
        else:
            plt.plot(ddd.index[i - 1 : i + 1], ddd["close"][i - 1 : i + 1], color="black")
    ((data.shift(-1) / data - 1) * position).cumsum().plot(secondary_y=True, linestyle="--")
    plt.show()


@nb.njit
def fnc(pos_t, y):
    # stop_loss = torch.full_like(pos_t, fill_value=0.0, dtype=torch.double, device=pos_t.device)
    stop_loss = np.full_like(pos_t, fill_value=0.0, dtype=np.float64)
    n_times, n_stocks, n_strategies = pos_t.shape
    for j in range(n_stocks):
        for k in range(n_strategies):
            profit = 0.0
            pos_type = np.sign(pos_t[0, j, k])
            for i in range(1, n_times):
                if np.sign(pos_t[i, j, k]) != pos_type:
                    pos_type = np.sign(pos_t[i, j, k])
                    profit = 0.0
                else:
                    if pos_type != 0:
                        profit += y[i - 1, j, k] * pos_type
                        if profit <= -0.1:
                            stop_loss[i, j, k] = np.abs(pos_t[i, j, k])
                            profit = 0.0
    return stop_loss


@nb.njit
def numpy_ffill(x):
    """
    Numpy version of pandas ffill
    """
    n_times, n_stocks, n_strategies = x.shape
    for j in range(n_stocks):
        for k in range(n_strategies):
            last = 0.0
            for i in range(0, n_times):
                if np.isfinite(x[i, j, k]):
                    last = x[i, j, k]
                x[i, j, k] = last
    return x


@nb.njit
def calc_win_rate(buy_open, sell_close, sell_open, buy_close, ret):
    n_times, n_stocks, n_strategies = buy_open.shape
    win = np.zeros((n_stocks, n_strategies), dtype=np.float64)
    loss = np.zeros((n_stocks, n_strategies), dtype=np.float64)

    sell_close_ = sell_close.copy().astype(np.float64)
    sell_close_[-1, ...] = np.inf
    buy_close_ = buy_close.copy().astype(np.float64)
    buy_close_[-1, ...] = np.inf

    for j in range(n_stocks):
        for k in range(n_strategies):
            for i in range(0, n_times):
                if buy_open[i, j, k] > 0.0:
                    pos1 = buy_open[i, j, k]
                    tmp_ret = 0.0
                    for ii in range(i + 1, n_times):
                        if sell_close_[ii, j, k] > 0.0:
                            tmp = np.minimum(pos1, sell_close_[ii, j, k])
                            pos1 -= tmp
                            sell_close_[ii, j, k] -= tmp
                            tmp_ret += tmp * ret[i:ii, j, k].sum()
                            if pos1 == 0.0:
                                break
                    if tmp_ret > 0:
                        win[j, k] += 1
                    else:
                        loss[j, k] += 1
                if sell_open[i, j, k] > 0.0:
                    pos1 = sell_open[i, j, k]
                    tmp_ret = 0.0
                    for ii in range(i + 1, n_times):
                        if buy_close_[ii, j, k] > 0.0:
                            tmp = np.minimum(pos1, buy_close_[ii, j, k])
                            pos1 -= tmp
                            buy_close_[ii, j, k] -= tmp
                            tmp_ret -= tmp * ret[i:ii, j, k].sum()
                            if pos1 == 0.0:
                                break
                    if tmp_ret > 0:
                        win[j, k] += 1
                    else:
                        loss[j, k] += 1
    return win, loss


def backtest(
    pos_t_tsr: torch.Tensor,
    return_tadd1_tsr: torch.Tensor,
    cost: float,
    freq: str = "1d",
    n_secondary_bars: Optional[int] = None,
    one_way: int = 0,
) -> Mapping[str, float]:
    pos_t = pos_t_tsr.double().cpu().numpy()
    if one_way == 1:
        pos_t = np.where(pos_t > 0, pos_t, 0)
    elif one_way == -1:
        pos_t = np.where(pos_t < 0, pos_t, 0)

    return_tadd1 = return_tadd1_tsr.double().cpu().numpy()
    if pos_t.ndim == 2:
        pos_t = pos_t[..., None]

    pos_t[np.isinf(pos_t)] = np.nan
    pos_t = numpy_ffill(pos_t)
    pos_t_1 = np.full_like(pos_t, fill_value=0.0, dtype=np.float64)
    pos_t_1[1:] = pos_t[:-1]

    return_tadd1[~np.isfinite(return_tadd1)] = 0
    return_tadd1 = np.repeat(return_tadd1[..., None], repeats=pos_t.shape[2], axis=2)
    # return_tadd1 = torch.repeat_interleave(return_tadd1[..., None], repeats=pos_t.size(2), dim=2)

    ret_ts_ = pos_t * return_tadd1  # (time, n_stocks, n_strategies)
    ret_ts: np.ndarray = np.zeros_like(ret_ts_)
    ret_ts[1:] = ret_ts_[:-1]

    count = np.full_like(pos_t, fill_value=0.0, dtype=np.float64)

    # 买开
    tmp = (pos_t > 0) & (pos_t > pos_t_1)
    count += tmp
    buy_open = np.where(tmp, pos_t - np.maximum(np.zeros_like(pos_t_1), pos_t_1), 0.0)
    ret_ts -= cost * buy_open

    # 卖平
    tmp = (pos_t_1 > 0) & (pos_t < pos_t_1)
    sell_close = np.where(tmp, pos_t_1 - np.maximum(np.zeros_like(pos_t), pos_t), 0.0)
    ret_ts -= cost * sell_close

    # 卖开
    tmp = (pos_t < 0) & (pos_t < pos_t_1)
    count += tmp
    sell_open = np.where(tmp, np.minimum(np.zeros_like(pos_t_1), pos_t_1) - pos_t, 0.0)
    ret_ts -= cost * sell_open

    # 买平
    tmp = (pos_t_1 < 0) & (pos_t > pos_t_1)
    buy_close = np.where(tmp, np.minimum(np.zeros_like(pos_t), pos_t) - pos_t_1, 0.0)
    ret_ts -= cost * buy_close

    win_count, loss_count = calc_win_rate(buy_open, sell_close, buy_close, sell_open, return_tadd1)

    # stop_loss = torch.from_numpy(fnc(pos_t.cpu().numpy(), y.cpu().numpy())).to(ret_ts.device)
    # ret_ts = ret_ts - stop_loss * 2 * cost

    ret_ts = ret_ts.sum(axis=-1, keepdims=False)  # (time, n_stocks)
    if not freq.endswith("d"):
        if n_secondary_bars is None:
            raise ValueError("n_secondary_bars must be None if freq is not end with 'd'")
        else:
            ret_ts = ret_ts.reshape(-1, n_secondary_bars, ret_ts.shape[1]).sum(
                axis=1, keepdims=False
            )  # (n_days, n_stocks)

    cum_ret_ts = ret_ts.cumsum(axis=0)
    mdd = (np.maximum.accumulate(cum_ret_ts, axis=0) - cum_ret_ts).max(axis=0)

    res = {}

    res["count"] = np.median(count.sum(axis=0).mean(axis=-1))
    res["total_count"] = np.sum(count.sum(axis=0).mean(axis=-1))
    res["symbol_count"] = np.sum(count.sum(axis=0).mean(axis=-1) > 0)
    t1 = np.sum(loss_count)
    t2 = np.sum(win_count)
    res["total_win_rate"] = t2 / (t1 + t2) if t1 + t2 > 0 else np.nan
    t1 = np.sum(loss_count, axis=-1)
    t2 = np.sum(win_count, axis=-1)
    res["median_win_rate"] = np.nanmedian(np.nan_to_num(t2 / (t1 + t2), nan=np.nan, posinf=np.nan, neginf=np.nan))

    res["days"] = ret_ts.shape[0]
    res["mdd"] = np.median(mdd)

    # calc sharpe
    mean_ret = ret_ts.mean(axis=0) * 252
    res["positive_return_ratio"] = (mean_ret > 0).mean()
    std_ret = ret_ts.std(axis=0) * np.sqrt(252) + 1e-10
    res["sharpe"] = np.median((mean_ret - 0.02) / std_ret)
    res["arr"] = np.median(mean_ret)

    # calc sortino
    mean_ret = ret_ts.mean(axis=0) * 252 - 0.02
    tmp = np.where(ret_ts < (0.02 / 252), ret_ts, np.full_like(ret_ts, fill_value=np.nan))
    std_ret = np.nansum(np.power(tmp - (0.02 / 252), 2), axis=0) / ret_ts.shape[0] * np.sqrt(252) + 1e-10
    res["sortino"] = np.median(mean_ret / std_ret)

    return res


def test(res, cost, one_way):
    a = {}
    for symbol in res.columns:
        position = res[symbol]
        a1 = get_price(
            position.name, position.index[0], position.index[-1], "1d", ["close"], skip_paused=False, fq="post"
        )["close"]
        a1 = (a1.shift(-1) / a1 - 1).reindex(index=res.index, fill_value=0)
        tmp = backtest(torch.tensor(res[symbol]).reshape(-1, 1), torch.tensor(a1).reshape(-1, 1), cost, one_way=one_way)
        a[symbol] = tmp
    return pd.DataFrame(a).T


def test1(res, cost, one_way):
    a1 = get_price(
        res.columns.tolist(), res.index[0], res.index[-1], "1d", ["close"], skip_paused=False, fq="post", is_panel=True
    )["close"]
    a1 = (a1.shift(-1) / a1 - 1).reindex(index=res.index, fill_value=0)
    return backtest(torch.from_numpy(res.values), torch.from_numpy(a1.to_numpy()), cost, one_way=one_way)


def parse_expr(text):
    if "(" not in text:
        return text, []
    a = np.array([ord(i) for i in text])
    b = np.full_like(a, 0.0)
    b[np.argwhere(a == ord("(")).flatten()] = 1
    b[np.argwhere(a == ord(")")).flatten()] = -1
    b = b.cumsum()

    c = np.argwhere((a == ord(",")) & (b == 1)).flatten()

    s = np.argwhere(b == 1)[0][0]
    e = np.argwhere(b[s:] == 0)[0][0] + s

    unit = []
    if len(c):
        for i in range(len(c)):
            if i == 0:
                unit.append(text[s + 1 : c[i]])
            else:
                unit.append(text[c[i - 1] + 1 : c[i]])
            if i == len(c) - 1:
                unit.append(text[c[i] + 1 : e])
    else:
        unit.append(text[s + 1 : e])
    return text[:s], unit


def print_expr(text, i=0):
    a, b = parse_expr(text)
    c = []
    if b:
        for bb in b:
            i = print_expr(bb, i)
            c.append(f"F{i - 1}")
    if c:
        if a == "Mul":
            print(f"F{i}:={'*'.join(c)};".upper())
        elif a == "Add":
            print(f"F{i}:={'+'.join(c)};".upper())
        elif a == "Sub":
            print(f"F{i}:={'-'.join(c)};".upper())
        elif a == "Div":
            print(f"F{i}:={'/'.join(c)};".upper())
        elif a == "Constant":
            if len(c) > 1:
                raise
            print(f"F{i}:={c[0]};".upper())
        else:
            print(f"F{i}:={a}({','.join(c)});".upper())
    else:
        print(f"F{i}:={a};".upper())
    return i + 1

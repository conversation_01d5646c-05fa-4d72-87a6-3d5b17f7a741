from base.expression import *

try:
    from mgquant_mod_mindgo.utils.recorder import log
except (ImportError, AttributeError):
    import logging as log

    log.basicConfig(level=log.INFO)
    log.info = lambda *args: log.root._log(log.INFO, " ".join([str(x) for x in args]), ())

SIGNIFICANT_THRESHOLD = 1e-4

OPERATORS = [
    # Unary
    Abs,
    Log,
    Sign,
    Neg,
    Sqrt,
    AbsSqrt,
    SignedSqrt,
    Square,
    Curt,
    Cube,
    Inv,
    AbsLog,
    SignedLog,
    UnaryMean,
    UnarySum,
    UnaryStd,
    Scale,
    UnaryMad,
    UnaryRank,
    Tanh,
    Sigmoid,
    Gelu,
    Sin,
    Cos,
    Tan,
    Asin,
    Acos,
    Atan,
    # Binary
    Add,
    Sub,
    Mul,
    Div,
    Pow,
    Greater,
    Less,
    LogAB,
    AbsLogAB,
    # Rolling
    Ref,
    Mean,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,  # <PERSON>,
    <PERSON><PERSON>ank,
    Delta,
    DeltaPerc,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>v,
    MaxMinNorm,
    Prod,
    DecayLinear,
    DescendDecayLinear,
    # Pair rolling
    Cov,
    Corr,
    PairSlope,
]
operators = [x.__name__ for x in OPERATORS]

FEATURES = list(FeatureType)
features = [x.name for x in FEATURES]

DELTA_BARS = [1, 2, 3, 5, 10, 20, 30]

CONSTANTS = [-20.0, -10.0, -5.0, -2.0, -1.0, -0.5, -0.1, -0.01, 0.01, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0]


def update():
    # 配置校验
    global DELTA_BARS, CONSTANTS
    if not any([issubclass(x, RollingOperator) for x in OPERATORS]):
        log.warn("未选择任何Rolling的OPERATORS，DELTA_BARS不生效。")
        DELTA_BARS = []
    if not any([issubclass(x, BinaryOperator) for x in OPERATORS]):
        log.warn("未选择任何Binary的OPERATORS，CONSTANTS不生效。")
        CONSTANTS = []
    global SIZE_OPERATOR, SIZE_FEATURE, SIZE_DELTA_BAR, SIZE_CONSTANT, SIZE_SEP, SIZE_BEG
    SIZE_OPERATOR = len(OPERATORS)
    SIZE_FEATURE = len(FEATURES)
    SIZE_DELTA_BAR = len(DELTA_BARS)
    SIZE_CONSTANT = len(CONSTANTS)
    SIZE_SEP = 1
    SIZE_BEG = 1

    global SIZE_ALL, SIZE_ACTION
    SIZE_ALL = SIZE_SEP + SIZE_OPERATOR + SIZE_FEATURE + SIZE_DELTA_BAR + SIZE_CONSTANT + SIZE_BEG
    SIZE_ACTION = SIZE_ALL - SIZE_BEG

    global OFFSET_OPERATOR, OFFSET_FEATURE, OFFSET_DELTA_BAR, OFFSET_CONSTANT, OFFSET_SEP, OFFSET_BEG
    OFFSET_OPERATOR = 1  # padding_idx
    OFFSET_FEATURE = OFFSET_OPERATOR + SIZE_OPERATOR
    OFFSET_DELTA_BAR = OFFSET_FEATURE + SIZE_FEATURE
    OFFSET_CONSTANT = OFFSET_DELTA_BAR + SIZE_DELTA_BAR
    OFFSET_SEP = OFFSET_CONSTANT + SIZE_CONSTANT
    OFFSET_BEG = OFFSET_SEP + SIZE_BEG


update()  # 联动更新

from torch import nn


class FeedForward(nn.Module):
    def __init__(self, hidden_size, ff_size, dropout_prob):
        super(FeedForward, self).__init__()

        self.fc1 = nn.Linear(hidden_size, ff_size, bias=False)
        self.fc2 = nn.Linear(ff_size, hidden_size, bias=False)

        self.norm = nn.LayerNorm(hidden_size)
        self.activation = nn.GELU()
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x):
        out = self.fc1(x)
        out = self.activation(out)
        out = self.dropout(out)
        out = self.fc2(out)
        out = self.dropout(out)
        out = self.norm(out + x)
        return out
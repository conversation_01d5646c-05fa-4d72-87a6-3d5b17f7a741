import math

import torch
from torch import nn


class MultiHeadAttention(nn.Module):
    def __init__(self, hidden_size, heads, dropout_prob):
        super(MultiHeadAttention, self).__init__()
        self.hidden_size = hidden_size
        self.heads = heads
        self.head_dim = hidden_size // heads

        self.scale = 1 / math.sqrt(self.head_dim)

        self.fc_q = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_k = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_v = nn.Linear(self.hidden_size, self.hidden_size)
        self.fc_out = nn.Linear(heads * self.head_dim, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.attention_dropout = nn.Dropout(dropout_prob)
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x, cross_x=None, mask=None):
        batch_size, seq_len, _ = x.shape
        query = self.fc_q(x) * self.scale
        query = query.view(batch_size, seq_len, self.heads, self.head_dim).transpose(1, 2).contiguous()

        if cross_x is not None:
            key = self.fc_k(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(cross_x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
        else:
            key = self.fc_k(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()
            value = self.fc_v(x).view(batch_size, -1, self.heads, self.head_dim).transpose(1, 2).contiguous()

        prob_shape = (batch_size * self.heads, -1, self.head_dim)
        query, key, value = query.view(*prob_shape), key.view(*prob_shape), value.view(*prob_shape)

        attention = query @ key.transpose(1, 2)

        if mask is not None:
            attention = attention.view(batch_size, self.heads, seq_len, seq_len)
            attention = attention + mask
            attention = attention.view(batch_size * self.heads, seq_len, seq_len)

        attention = torch.softmax(attention, dim=-1)
        attention = self.attention_dropout(attention)

        out = attention @ value
        out = out.view(batch_size, self.heads, seq_len, self.head_dim).transpose(1, 2).contiguous()
        out = out.view(batch_size, seq_len, self.hidden_size)

        out = self.fc_out(out)

        out = self.dropout(out)
        out = self.norm(out + x)
        return out
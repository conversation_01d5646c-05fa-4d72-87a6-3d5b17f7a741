import torch
from torch import nn


class LinearPositionEmbedding(nn.Module):
    def __init__(self, embed_size, embed_length, pool_size, hidden_size, dropout_prob):
        super(LinearPositionEmbedding, self).__init__()
        self.input_embed = nn.Embedding(embed_size, hidden_size)
        self.position_embed = nn.Embedding(embed_length, hidden_size)
        self.weight_embed = nn.Linear(pool_size, hidden_size)

        self.norm = nn.LayerNorm(hidden_size)
        self.dropout = nn.Dropout(dropout_prob)

    def forward(self, x, positions, weights):
        out = self.input_embed(x)
        position_out = self.position_embed(positions)
        weight_out = self.weight_embed(weights)[:, None, :].expand_as(position_out)
        out += position_out + weight_out
        out = self.norm(out)
        out = self.dropout(out)
        return out
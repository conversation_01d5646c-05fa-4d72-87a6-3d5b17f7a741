from torch import nn

from model.layers.attention import MultiHeadAttention
from model.layers.feed_forward import FeedForward


class EncoderLayer(nn.Module):
    def __init__(self, hidden_size, heads, ff_size, dropout_prob):
        super(EncoderLayer, self).__init__()
        self.attention = MultiHeadAttention(hidden_size, heads, dropout_prob)
        self.feed_forward = FeedForward(hidden_size, ff_size, dropout_prob)

    def forward(self, x, mask=None):
        out = self.attention(x, mask=mask)
        out = self.feed_forward(out)
        return out
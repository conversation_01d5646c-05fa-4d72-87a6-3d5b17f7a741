from typing import Tuple

import gymnasium as gym
import numpy as np

import config
from base.data import FeatureType
from base.pool import AlphaPool
from base.tokens import Token, SpecialTokenType, OperatorToken, FeatureToken, BarToken, ConstantToken, SpecialToken
from model.environment import AlphaEnv


class AlphaEnvWrapper(gym.Wrapper):
    last_sequence_state: np.ndarray
    state: np.ndarray
    env: AlphaEnv
    action_space: gym.spaces.Discrete
    observation_space: gym.spaces.Box
    counter: int

    def __init__(self, env: AlphaEnv):
        super().__init__(env)
        self.action_space = gym.spaces.Discrete(config.SIZE_ACTION)
        self.observation_space = gym.spaces.Box(low=0, high=config.SIZE_ACTION,
                                                shape=((self.env.pool.pool_size + 1) * (
                                                        self.env.max_expr_length + 1) + self.env.pool.pool_size,))

    def reset(self, **kwargs) -> <PERSON><PERSON>[np.ndarray, dict]:
        self.counter = 1
        self.last_sequence_state = np.zeros(self.env.max_expr_length + 1)
        self.last_sequence_state[0] = config.OFFSET_BEG
        self.env.reset()
        self.state = np.concatenate(
            (*self.env.pool.tokens_pool[:self.env.pool.pool_size], self.last_sequence_state,
             self.env.pool.weights[:self.env.pool.pool_size]))
        return self.state, {}

    def step(self, action: int):
        action = action + 1  # padding idx
        _, reward, done, truncated, info = self.env.step((self.action(action), action))
        if not done:
            self.last_sequence_state[self.counter] = action
            self.counter += 1
        self.state = np.concatenate(
            (*self.env.pool.tokens_pool[:self.env.pool.pool_size], self.last_sequence_state,
             self.env.pool.weights[:self.env.pool.pool_size]))
        return self.state, self.reward(reward), done, truncated, info

    @staticmethod
    def action(action: int) -> Token:
        if action < config.OFFSET_OPERATOR:
            raise ValueError
        elif action < config.OFFSET_FEATURE:
            return OperatorToken(config.OPERATORS[action - config.OFFSET_OPERATOR])
        elif action < config.OFFSET_DELTA_BAR:
            return FeatureToken(config.FEATURES[action - config.OFFSET_FEATURE])
        elif action < config.OFFSET_CONSTANT:
            return BarToken(config.DELTA_BARS[action - config.OFFSET_DELTA_BAR])
        elif action < config.OFFSET_SEP:
            return ConstantToken(config.CONSTANTS[action - config.OFFSET_CONSTANT])
        elif action == config.OFFSET_SEP:
            return SpecialToken(SpecialTokenType.SEP)
        else:
            assert False

    @staticmethod
    def reward(reward: float) -> float:
        return reward

    def action_masks(self) -> np.ndarray:
        res = np.zeros(config.SIZE_ACTION, dtype=bool)
        valid = self.env.valid_action_types()
        for i in range(config.OFFSET_OPERATOR, config.OFFSET_OPERATOR + config.SIZE_OPERATOR):
            if valid['operator'][config.OPERATORS[i - config.OFFSET_OPERATOR].category_type()]:
                res[i - 1] = True
        if valid['select'][1]:  # FEATURE
            for i in range(config.OFFSET_FEATURE, config.OFFSET_FEATURE + config.SIZE_FEATURE):
                res[i - 1] = True
        if valid['select'][2]:  # CONSTANT
            for i in range(config.OFFSET_CONSTANT, config.OFFSET_CONSTANT + config.SIZE_CONSTANT):
                res[i - 1] = True
        if valid['select'][3]:  # DELTA_TIME
            for i in range(config.OFFSET_DELTA_BAR, config.OFFSET_DELTA_BAR + config.SIZE_DELTA_BAR):
                res[i - 1] = True
        if valid['select'][4]:  # SEP
            res[config.OFFSET_SEP - 1] = True
        return res


def build_env(pool: AlphaPool, max_expr_length: int, **kwargs):
    return AlphaEnvWrapper(AlphaEnv(pool=pool, max_expr_length=max_expr_length, **kwargs))
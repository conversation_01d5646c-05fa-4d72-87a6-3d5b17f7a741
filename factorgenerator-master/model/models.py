import gymnasium as gym
import torch
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
from torch import nn

from model.layers.embedding import LinearPositionEmbedding
from model.layers.encoder import EncoderLayer


class LSTMSharedExtractor(BaseFeaturesExtractor):
    def __init__(
        self,
        observation_space: gym.Space,
        pool_size: int,
        num_layers: int = 2,
        hidden_size: int = 128,
        dropout: float = 0.1,
        padding_idx: int = 0,
    ):
        super().__init__(observation_space, hidden_size)

        assert isinstance(observation_space, gym.spaces.Box)
        actions_num = int(observation_space.high[0])
        states_num = observation_space.shape[0] - pool_size
        tokens_num = actions_num + 1  # BEG

        self.pool_size = pool_size
        self.hidden_size = hidden_size
        self.padding_idx = padding_idx

        self.token_embed = nn.Embedding(tokens_num + 1, hidden_size, padding_idx=padding_idx)  # padding_idx
        self.position_embed = nn.Embedding(states_num + 1, hidden_size, padding_idx=padding_idx)  # padding_idx
        self.weight_embed = nn.Linear(pool_size, hidden_size)

        self.lstm = nn.LSTM(
            input_size=hidden_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            batch_first=True,
        )

    def build_input(self, x):
        batch_size, seq_len = x.shape[0], x.shape[1]
        x = x.long()
        positions = torch.arange(1, seq_len + 1, dtype=torch.long).expand(batch_size, -1).to(x.device)
        positions = positions.where(x.not_equal(self.padding_idx), self.padding_idx)
        return x, positions

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        weights, x = x[:, -self.pool_size :], x[:, : -self.pool_size]
        x, positions = self.build_input(x)
        out = self.token_embed(x)
        position_embed = self.position_embed(positions)
        weight_embed = self.weight_embed(weights)[:, None, :].expand_as(position_embed)
        out += position_embed + weight_embed
        out, _ = self.lstm(out)  # can switch to last_hidden_state
        out = out.mean(dim=1)  # for output seq
        return out


class EncoderSharedExtractor(BaseFeaturesExtractor):
    def __init__(
        self,
        observation_space: gym.Space,
        pool_size: int,
        hidden_size=128,
        ff_size=256,
        num_layers=2,
        heads=4,
        dropout_prob=0.1,
        padding_idx=0,
    ):
        super(EncoderSharedExtractor, self).__init__(observation_space, hidden_size)

        assert isinstance(observation_space, gym.spaces.Box)
        actions_num = int(observation_space.high[0])
        states_num = observation_space.shape[0] - pool_size
        tokens_num = actions_num + 1  # BEG

        self.pool_size = pool_size
        self.padding_idx = padding_idx
        self.embedding = LinearPositionEmbedding(tokens_num + 1, states_num + 1, pool_size, hidden_size, dropout_prob)
        self.layers = nn.ModuleList(
            [EncoderLayer(hidden_size, heads, ff_size, dropout_prob) for _ in range(num_layers)]
        )

    def build_input(self, x):
        batch_size, seq_len = x.shape[0], x.shape[1]
        x = x.long()
        positions = torch.arange(1, seq_len + 1, dtype=torch.long).expand(batch_size, -1).to(x.device)
        mask = torch.full((batch_size, seq_len), -torch.inf).to(x.device)
        pad_idx = x.not_equal(self.padding_idx)
        positions = positions.where(pad_idx, self.padding_idx)
        mask = mask.where(~pad_idx, 0)  # TODO: seems strange
        mask = mask[:, None, None, :]
        return x, positions, mask

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        weights, x = x[:, -self.pool_size :], x[:, : -self.pool_size]
        x, positions, mask = self.build_input(x)
        out = self.embedding(x, positions, weights)
        for layer in self.layers:
            out = layer(out, mask)

        out = out.mean(dim=1)
        return out

import math
from typing import List, Optional, Tuple

import gymnasium as gym
import numpy as np
import torch

import config
from base.expression import BinaryOperator, Expression, PairRollingOperator, RollingOperator, UnaryOperator
from base.pool import AlphaPool, IndicatorCalculator
from base.tokens import BEG_TOKEN, SpecialToken, SpecialTokenType, Token
from base.tree import ExpressionBuilder
from model.utils import reseed_everything


class AlphaEnv(gym.Env):
    pool: AlphaPool
    actions: List[int]
    tokens: List[Token]
    builder: ExpressionBuilder
    verbose: bool

    counter: int
    state: np.ndarray

    def __init__(
        self, pool: AlphaPool, max_expr_length: int, device: torch.device = torch.device("cpu"), verbose: bool = False
    ):
        super().__init__()

        self.pool = pool
        self.max_expr_length = max_expr_length
        self.verbose = verbose
        self.device = device

        self.eval_count = 0

    def reset(
        self, *, seed: Optional[int] = None, return_info: bool = False, options: Optional[dict] = None
    ) -> Tuple[List[Token], dict]:
        reseed_everything(seed)

        self.actions = [config.OFFSET_BEG]
        self.tokens = [BEG_TOKEN]
        self.builder = ExpressionBuilder()
        return self.tokens, self.valid_action_types()

    def step(self, action: Tuple[Token, int]) -> Tuple[List[Token], float, bool, bool, dict]:
        action, action_id = action
        if isinstance(action, SpecialToken) and action.indicator == SpecialTokenType.SEP:
            reward = self.evaluate()
            done = True
        elif len(self.tokens) < self.max_expr_length:
            self.actions.append(action_id)
            self.tokens.append(action)
            self.builder.add_token(action)
            done = False
            reward = 0.0
        else:
            done = True
            reward = self.evaluate() if self.builder.is_valid() else self.pool.INVALID_EXPR_PUNISH

        if math.isnan(reward):
            reward = self.pool.MIN_REWARD

        return self.tokens, reward, done, False, self.valid_action_types()

    def evaluate(self):
        expr: Expression = self.builder.get_tree()
        if self.verbose:
            print(expr)
        try:
            ret = self.pool.evaluate(self.actions, expr)
            self.eval_count += 1
            return ret
        except IndexError:
            return self.pool.MIN_REWARD

    def valid_action_types(self) -> dict:
        valid_operator_unary = self.builder.validate_operator(UnaryOperator)
        valid_operator_binary = self.builder.validate_operator(BinaryOperator)
        valid_operator_rolling = self.builder.validate_operator(RollingOperator)
        valid_operator_pair_rolling = self.builder.validate_operator(PairRollingOperator)

        valid_operator = (
            valid_operator_unary or valid_operator_binary or valid_operator_rolling or valid_operator_pair_rolling
        )
        valid_delta = self.builder.validate_delta()
        valid_const = self.builder.validate_const()
        valid_feature = self.builder.validate_feature()
        valid_stop = self.builder.is_valid()

        ret = {
            "select": [valid_operator, valid_feature, valid_const, valid_delta, valid_stop],
            "operator": {
                UnaryOperator: valid_operator_unary,
                BinaryOperator: valid_operator_binary,
                RollingOperator: valid_operator_rolling,
                PairRollingOperator: valid_operator_pair_rolling,
            },
        }
        return ret

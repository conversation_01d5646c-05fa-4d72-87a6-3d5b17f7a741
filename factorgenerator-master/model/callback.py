import io
import json
import os
import pickle
import sys
from typing import Optional, Sequence

import numpy as np
from stable_baselines3.common.callbacks import BaseCallback

import config
from base.calculator import AlphaCalculator
from base.pool import AlphaPool
from config import log
from model.environment import AlphaEnv


class AlphaCallback(BaseCallback):
    def __init__(
        self,
        save_path: str,
        valid_calculator: Optional[AlphaCalculator],
        test_calculator: Sequence[AlphaCalculator],
        name_prefix: str = "",
        verbose: int = 0,
    ):
        super().__init__(verbose)
        self.save_path = save_path
        self.name_prefix = name_prefix

        self.valid_calculator = valid_calculator
        self.test_calculator = test_calculator

        self.stdout = sys.stdout = io.StringIO()  # 收集终端输出
        self.stdout_si = 0

    def _stdout2file(self) -> None:
        self.stdout.seek(self.stdout_si)
        msg = self.stdout.read()
        self.stdout_si += len(msg)
        log.info("\n" + msg)  # 增量输出

    def _init_callback(self) -> None:
        os.makedirs(self.save_path, exist_ok=True)
        with open(os.path.join(self.save_path, "steps.txt"), "w") as f:
            f.write("")

    def _on_step(self) -> bool:
        # log.info('运行到', self.num_timesteps)
        return True

    def _on_rollout_start(self) -> None:
        self._stdout2file()

    def _on_rollout_end(self) -> None:
        assert self.logger is not None
        self.logger.record("pool/size", self.pool.size)
        self.logger.record(
            "pool/significant", (np.abs(self.pool.weights[: self.pool.size]) > config.SIGNIFICANT_THRESHOLD).sum()
        )
        self.logger.record("pool/weight_l1", np.abs(self.pool.weights[: self.pool.size]).sum())
        self.logger.record("pool/best_ic", self.pool.best_ic)
        self.logger.record("pool/eval_count", self.pool.eval_count)
        if self.pool.size:
            ic_test = np.mean([self.pool.test_ensemble(x) for x in self.test_calculator])
            self.logger.record("test/ic", ic_test)
            self.save_checkpoint(ic_test)

    def save_checkpoint(self, ic_test):
        # path = os.path.join(self.save_path, self.name_prefix, 'checkpoint')
        # if not os.path.exists(path):
        #     os.makedirs(path)

        # self.model.save(os.path.join(path, "model"))
        # pickle.dump(self.model.policy, open(os.path.join(path, "policy.pkl"), "wb"))
        # pickle.dump(self.model.env, open(os.path.join(path, "env.pkl"), "wb"))
        # if self.verbose > 1:
        #     print(f'Saving model checkpoint to {path}')
        with open(os.path.join(self.save_path, "steps.txt"), "a") as f:
            f.write(
                json.dumps(
                    {
                        **self.pool.to_dict(ic_test=ic_test, test_calculator=self.test_calculator),
                        "steps": self.num_timesteps,
                    }
                )
                + "\n"
            )
        with open(os.path.join(self.save_path, "exprs.txt"), "w") as f:
            f.write("\n".join(self.pool.exprs))
        log.info("完成checkpoint", self.num_timesteps)

    def _on_training_end(self) -> None:
        self._stdout2file()
        log.info("训练完成")

    @property
    def pool(self) -> AlphaPool:
        return self.env_core.pool

    @property
    def env_core(self) -> AlphaEnv:
        return self.training_env.envs[0].unwrapped

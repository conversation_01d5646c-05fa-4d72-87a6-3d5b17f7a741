import numpy as np
import pandas as pd
from quant.protocols.enums import BasicData, FinancialAsset, Mode
from quant.protocols.request import *
from quant.research.strategies.indicator_parse import *
from quant.research.strategies.strategy import *

np.seterr(divide='ignore', invalid='ignore')
pd.set_option("display.max_rows", None)


class TestStrategy(IndicatorStrategy):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._series.extend([
            'N', 'N_1', 'N_2', 'N_3', 'HH_TODAY', 'HH_YESTODAY', 'LL_TODAY', 'LL_YESTODAY', 'CC_YESTODAY', 'OO_TODAY', 'CFJ1', 'CFJ1_', 'CFJ2', 'CFJ',
            'UPPERBOUND_BREAK', 'LOWERBOUND_BREAK', 'IFDO', 'BARSBP1', 'BARSSP1', 'CONTROL_B', 'CONTROL_S', 'TEMP', 'LONG1', 'SHORT1'
        ])

    def calc(self):
        self.logger.logger.info("%d %09d open: %d, high: %d, low: %d, close: %d", self.data.date[-1], self.data.time[-1], self.data.open[-1],
                                self.data.high[-1], self.data.low[-1], self.data.close[-1])
        code = """
N:=self.data.n_trade_day[-1];
N_1:=REF(N,N.value);
N_2:=REF(N_1,N.value);
N_3:=REF(N_2,N.value);
ONEDAY=MAX1(N_1,N_2,N_3).value;

HH_TODAY:=HHV(HIGH,N.value);
HH_YESTODAY:=REF(HH_TODAY,N.value);
LL_TODAY:=LLV(LOW,N.value);
LL_YESTODAY:=REF(LL_TODAY,N.value);
CC_YESTODAY:=REF(C,N.value);
OO_TODAY:=REF(O,N.value-1);

CFJ1:=IF(N==1,MAX(HH_YESTODAY-CC_YESTODAY,CC_YESTODAY-LL_YESTODAY)*35/100,0);
CFJ1_:=IF(N==1,REF(CFJ1,SUMBARS(N==1,2).value-1),0);
CFJ2:=IF(N==1,MIN(0,(ABS(CC_YESTODAY-REF(O,SUMBARS(N==1,2).value-1))+ABS(REF(CC_YESTODAY,N.value)-REF(O,SUMBARS(N==1,3).value-1))-CC_YESTODAY*25/1000))*15/100,0);
# CFJ:=MIN(SUM(ABS(CFJ1+CFJ1+CFJ2),SUMBARS(N==1,4))/4,45/100*HHV(REF(ABS(C-OO_TODAY),N),SUMBARS(N==1,80)));
CFJ:=SUM(ABS(CFJ1+CFJ1+CFJ2),SUMBARS(N==1,4).value)/4;

if OO_TODAY>=CC_YESTODAY then
	UPPERBOUND_BREAK:=MIN(MAX(CC_YESTODAY+CFJ,OO_TODAY+50/100*CFJ),MAX(CC_YESTODAY,LL_TODAY)+90/100*CFJ);
	LOWERBOUND_BREAK:=MAX(CC_YESTODAY-CFJ,OO_TODAY-115/100*CFJ);
else
	UPPERBOUND_BREAK:=MIN(CC_YESTODAY+CFJ,OO_TODAY+115/100*CFJ);
	LOWERBOUND_BREAK:=MAX(MIN(CC_YESTODAY-CFJ,OO_TODAY-50/100*CFJ),MIN(CC_YESTODAY,HH_TODAY)-90/100*CFJ);
IFDO:=COUNTSIG(BK,N)+COUNTSIG(SK,N)<2;
BARSBP1:=BARSLAST(SKVOL==0 && REF(SKVOL,1)>0)+1;
BARSSP1:=BARSLAST(BKVOL==0 && REF(BKVOL,1)>0)+1;

CONTROL_B:=IF(BARSBP1<SUMBARS(N==1,1) && REF(C,BARSBP1.value)>SKPRICE,C>MIN(REF(C,N.value),REF(O,N.value-1))*(1+10/1000) || C>LLV(L,BARSSK.value+1)*(1+20/1000),1)
&& IF(BARSBK<N && REF(C,BARSSP1.value)<BKPRICE*(1-5/1000),C>BKPRICE*(1+12/1000),1)
&& IF(BARSBK<SUMBARS(N==1,2) && BARSBK>=N && REF(C,BARSSP1.value)<BKPRICE*(1-4/1000),C>LLV(L,BARSBK.value+1)*(1+30/1000) || C>BKPRICE*(1+10/1000) || C>MIN(REF(C,N.value),REF(O,N.value-1))*(1+20/1000),1);

CONTROL_S:=IF(BARSSP1<SUMBARS(N==1,1) && REF(C,BARSSP1.value)<BKPRICE,C<MAX(REF(C,N.value),REF(O,N.value-1))*(1-10/1000) || C<HHV(H,BARSBK.value+1)*(1-20/1000),1)
&& IF(BARSSK<N && REF(C,BARSBP1.value)>SKPRICE*(1+5/1000),C<SKPRICE*(1-12/1000),1)
&& IF(BARSSK<SUMBARS(N==1,2) && BARSSK>=N && REF(C,BARSBP1.value)>SKPRICE*(1+4/1000),C<HHV(H,BARSSK.value+1)*(1-30/1000) || C<SKPRICE*(1-10/1000) || C<MAX(REF(C,N.value),REF(O,N.value-1))*(1-20/1000),1);

P9=4;
P10=45;
P11=55;
P12=20;
TEMP:=(MAX(HHV(H,P10/10*ONEDAY),REF(HHV(H,P10/10*ONEDAY),P10/10*ONEDAY))-MIN(HHV(H,P10/10*ONEDAY),REF(HHV(H,P10/10*ONEDAY),P10/10*ONEDAY))+MAX(LLV(L,P10/10*ONEDAY),REF(LLV(L,P10/10*ONEDAY),P10/10*ONEDAY))-MIN(LLV(L,P10/10*ONEDAY),REF(LLV(L,P10/10*ONEDAY),P10/10*ONEDAY)))/(MIN(HHV(H,P10/10*ONEDAY),REF(HHV(H,P10/10*ONEDAY),P10/10*ONEDAY))-MAX(LLV(L,P10/10*ONEDAY),REF(LLV(L,P10/10*ONEDAY),P10/10*ONEDAY)));


# && C==HHV(C,N.value) ;
# && C==LLV(C,N.value) ;

LONG1:=C>UPPERBOUND_BREAK && C-LLV(L,MIN(N.value,50))<MAX(LLV(L,MIN(N.value,50))*25/1000,CFJ*185/100) && C>O
&& IF(HH_TODAY<MAX(REF(SETTLE,N.value)*(1+40/1000),CC_YESTODAY*(1+40/1000)),C==HHV(C,N.value),C==HHV(C,MIN(35,N.value)) && C>HH_TODAY*(1-30/1000))
&& IFDO && IF((REF(C,N.value-1)/REF(C,N.value)-1)>23/1000,N>=3 || (N>1 && C>HV(H,N.value-1)),1) && (not(REF(C/OO_TODAY,N.value)<1-23/1000) || C>MIN(OO_TODAY,CC_YESTODAY)*(1+22/1000))
&& (TEMP<0 || TEMP>P9/10 || (MIN(HHV(H,P10/10*ONEDAY),REF(HHV(H,P10/10*ONEDAY),P10/10*ONEDAY))/MAX(LLV(L,P10/10*ONEDAY),REF(LLV(L,P10/10*ONEDAY),P10/10*ONEDAY)))>1+P11/1000 || C>MIN(REF(O,N.value-1),REF(C,N.value))*(1+P12/1000))
;

SHORT1:=C<LOWERBOUND_BREAK && HHV(L,MIN(N.value,50))-C<MAX(HHV(L,MIN(N.value,50))*25/1000,CFJ*185/100) && C<O
&& IF(LL_TODAY>MIN(REF(SETTLE,N.value)*(1-40/1000),CC_YESTODAY*(1-40/1000)),C==LLV(C,N.value),C==LLV(C,MIN(35,N.value)) && C<LL_TODAY*(1+30/1000))
&& IFDO && IF((1-REF(C,N.value-1)/REF(C,N.value))>23/1000,N>=3 || (N>1 && C<LV(L,N.value-1)),1) && (not(REF(C/OO_TODAY,N.value)>1+23/1000) || C<MAX(OO_TODAY,CC_YESTODAY)*(1-22/1000))
&& (TEMP<0 || TEMP>P9/10 || (MIN(HHV(H,P10/10*ONEDAY),REF(HHV(H,P10/10*ONEDAY),P10/10*ONEDAY))/MAX(LLV(L,P10/10*ONEDAY),REF(LLV(L,P10/10*ONEDAY),P10/10*ONEDAY)))>1+P11/1000 || C<MAX(REF(O,N.value-1),REF(C,N.value))*(1-P12/1000))
;
if BKVOL==0 && SKVOL==0 && LONG1 && CONTROL_B then
    self.BK(1);
if SKVOL==0 && BKVOL==0 && SHORT1 && CONTROL_S then
    self.SK(1);
        
XN2=9;
XN4=8;
XN5=15;
XN3=38;

if BKVOL>0 && C<BKPRICE*(1000-XN2-IF(BARSBK<26,1,0))/1000 && C<O && C==LLV(MIN(C,O),2) then
    self.SP(BKVOL);
if SKVOL>0 && C>SKPRICE*(1000+XN2+IF(BARSSK<26,1,0))/1000 && C>O && C==HHV(MAX(C,O),2) then
    self.BP(SKVOL);
if BKVOL>0 && C<=BKHIGH*(1000-XN5)/1000 && C<BKPRICE*(1000+XN4)/1000 && C<O && C==LLV(MIN(C,O),2) then
    self.SP(BKVOL);
if SKVOL>0 && C>=SKLOW*(1000+XN5)/1000 && C>SKPRICE*(1000-XN4)/1000 && C>O && C==HHV(MAX(C,O),2) then
    self.BP(SKVOL);
if BKVOL>0 && C<=BKHIGH*(1000-XN3)/1000 && C>=BKPRICE*(1000+XN4)/1000 && C<O && C==LLV(MIN(C,O),2) then
    self.SP(BKVOL);
if SKVOL>0 && C>=SKLOW*(1000+XN3)/1000 && C<=SKPRICE*(1000-XN4)/1000 && C>O && C==HHV(MAX(C,O),2) then
    self.BP(SKVOL);
"""
        exec(self.edit_code(code))


if __name__ == "__main__":
    t = strategy_load(
        TestStrategy,
        mode=Mode.BACK_TESTING,
        symbol="BU8888.SHFE",
        financial_asset=FinancialAsset.FUTURES,
        basic_data=BasicData.MINUTE,
        merge_num=3,
        init_money=10000000,
        parameters={},
        start_time="20220101 2100",
        # end_time="20220719 1500",
        end_time="20220720 1500",
        size=2000,
    )

    _ = t.run()
    _["trade_log"].to_csv("trade_log.csv")
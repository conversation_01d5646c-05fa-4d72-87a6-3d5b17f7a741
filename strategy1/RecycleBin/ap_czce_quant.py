import numpy as np
import pandas as pd

pd.set_option("display.max_rows", None)

from quant.protocols.enums import BasicData, FinancialAsset, Mode
from quant.protocols.request import *
from quant.research.strategy_manager.strategy import *
from quant.research.strategy_manager.utility import *
from quant.utility import *


class TestStrategy(SingleUnderlyingStrategy):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def init(self):
        self.data.create_series("tr", TR(3))
        self.data.create_series("hhv_today", HHVToday("high"))
        self.data.create_series("llv_today", LLVToday("low"))
        self.data.create_series("llvc_today", LLVToday("close"))
        self.data.create_series("llvo_today", LLVToday("open"))
        self.data.create_series("hhvc_today", HHVToday("close"))
        self.data.create_series("hhvo_today", HHVToday("open"))
        self.data.create_series("my_settle", Settle())
        self.data.create_series("cfj1")
        self.data.create_series("exxn1")
        self.data.create_series("exxn2")
        self.data.create_series("fac1_")
        self.data.create_series("fac2_")
        self.status: int = 0
        self.bkhigh: Optional[float] = None
        self.bkhigh_loc: Optional[int] = None
        self.sklow: Optional[float] = None
        self.sklow_loc: Optional[int] = None
        self.barslast_open: Optional[int] = None

    def calc(self):
        # self.logger.logger.info("%d %09d open: %d, high: %d, low: %d, close: %d", self.data.date[-1], self.data.time[-1], self.data.open[-1],
        #                         self.data.high[-1], self.data.low[-1], self.data.close[-1])
        n = self.data.n_trade_day[-1]
        nnn = self.data.n_natural_day[-1]
        n1 = self.data.n_trade_day[int(-n - 1)]
        n2 = self.data.n_trade_day[int(-n - n1 - 1)]
        n3 = self.data.n_trade_day[int(-n - n1 - n2 - 1)]
        num_bars_oneday = max(n1, n2, n3)

        param1 = int(3.5 * num_bars_oneday)
        ex1 = (
            self.data.high[-(2 * param1 - 1) :].max() == self.data.high[-param1]
            and not self.data.low[-(2 * param1 - 1) :].min() == self.data.low[-param1]
        )
        ex2 = (
            self.data.low[-(2 * param1 - 1) :].min() == self.data.low[-param1]
            and not self.data.high[-(2 * param1 - 1) :].max() == self.data.high[-param1]
        )
        if ex1:
            self.data.exxn1.update(self.data.high[-param1])
        else:
            if self.data.exxn1.count == 0:
                self.data.exxn1.update(0)
            else:
                self.data.exxn1.update(self.data.exxn1[-1])
        if ex2:
            self.data.exxn2.update(self.data.low[-param1])
        else:
            if self.data.exxn2.count == 0:
                self.data.exxn2.update(0)
            else:
                self.data.exxn2.update(self.data.exxn2[-1])
        if self.data.exxn1[-1] > self.data.exxn2[-1]:
            self.status = 0
        else:
            if self.data.exxn1[-1] != self.data.exxn1[-2] and self.data.exxn2[-1] == self.data.exxn2[-2]:
                self.status = 1
            elif self.data.exxn1[-1] == self.data.exxn1[-2] and self.data.exxn2[-1] != self.data.exxn2[-2]:
                self.status = -1
        if self.status == 0:
            loc = (self.data.close[-1] - self.data.exxn2[-1]) / (self.data.exxn1[-1] - self.data.exxn2[-1]) * 100
        elif self.status == 1:
            loc = 150
        elif self.status == -1:
            loc = 50
        if loc > 50 - 20:
            if loc > 100:
                fac1_ = 9 / 10
            else:
                fac1_ = 10 / 10
        else:
            if loc > 0:
                fac1_ = 9 / 10
            else:
                fac1_ = 13 / 10
        if loc < 50 + 20:
            if loc < 0:
                fac2_ = 9 / 10
            else:
                fac2_ = 10 / 10
        else:
            if loc < 100:
                fac2_ = 9 / 10
            else:
                fac2_ = 13 / 10
        self.data.fac1_.update(fac1_)
        self.data.fac2_.update(fac2_)
        fac1 = self.data.fac1_[-n:].max()
        fac2 = self.data.fac2_[-n:].max()
        # self.logger.logger.info("%s loc %f fac1_ %f fac2_ %f fac1 %f fac2 %f",self.date_time.strftime("%Y-%m-%d %H:%M:%S"),loc,fac1_,fac2_,fac1,fac2)

        atr = self.data.tr[-int(num_bars_oneday) :].mean()
        hh_today = self.data.hhv_today[-1]
        hh_yestoday = self.data.hhv_today[-1 - n]
        ll_today = self.data.llv_today[-1]
        ll_yestoday = self.data.llv_today[-1 - n]
        cc_yestoday = self.data.close[-1 - n]
        oo_today = self.data.open[-n]

        cfj1 = max(hh_yestoday - cc_yestoday, cc_yestoday - ll_yestoday) * 75 / 100 if n == 1 else 0
        self.data.cfj1.update(cfj1)
        cfj = self.data.cfj1[np.argwhere(self.data.n_trade_day == 1)[-5][0] :].sum() / (4 + 1)
        # self.logger.logger.info("%s cfj1 %f cfj %f fac1 %f fac2 %f cc_yestoday %f oo_today %f hh_yestoday %f ll_yestoday %f",self.date_time.strftime("%Y-%m-%d %H:%M:%S"),cfj1,cfj,fac1,fac2,cc_yestoday,oo_today,hh_yestoday,ll_yestoday)

        upperbound = min(max(cc_yestoday + fac1 * cfj, oo_today + 65 / 100 * cfj * fac1), oo_today + 110 / 100 * cfj * fac1)
        lowerbound = max(min(cc_yestoday - fac2 * cfj, oo_today - 65 / 100 * cfj * fac2), oo_today - 110 / 100 * cfj * fac2)

        p9 = 4
        p10 = 50
        p11 = 35
        p12 = 10
        temp_len = int(p10 / 10 * num_bars_oneday)
        temp_hhv = self.data.high[-temp_len:].max()
        temp_hhv_ref = self.data.high[-2 * temp_len : -temp_len].max()
        temp_llv = self.data.low[-temp_len:].min()
        temp_llv_ref = self.data.low[-2 * temp_len : -temp_len].min()
        temp = (max(temp_hhv, temp_hhv_ref) - min(temp_hhv, temp_hhv_ref) + max(temp_llv, temp_llv_ref) - min(temp_llv, temp_llv_ref)) / (
            min(temp_hhv, temp_hhv_ref) - max(temp_llv, temp_llv_ref)
        )
        # self.logger.logger.info("%s num_bars_oneday %d temp_hhv %f temp_hhv_ref %f temp_llv %f temp_llv_ref %f temp %f",
        #                         self.date_time.strftime("%Y-%m-%d %H:%M:%S"), num_bars_oneday, temp_hhv, temp_hhv_ref, temp_llv, temp_llv_ref, temp)

        if self.account.positions[self.symbol]["pos_long"]["amount"] == 0 and self.account.positions[self.symbol]["pos_short"]["amount"] == 0:
            con1_l = self.data.close[-1] > upperbound
            con2_l = self.data.close[-1] >= max(self.data.close[-n:].max(), self.data.open[-n:].max())
            con3_l = self.data.close[-1] < self.data.my_settle[-n - 1] * (1 + 50 / 1000)
            con4_l = self.data.close[-1] > self.data.close[-n - 1] * (1 - 15 / 1000)
            con5_l = (
                temp < 0
                or temp > p9 / 10
                or min(temp_hhv, temp_hhv_ref) / max(temp_llv, temp_llv_ref) > 1 + p11 / 1000
                or self.data.close[-1] > min(self.data.close[-n - 1], self.data.open[-n]) * (1 + p12 / 1000)
            )

            con1_s = self.data.close[-1] < lowerbound
            con2_s = self.data.close[-1] <= min(self.data.close[-n:].min(), self.data.open[-n:].min())
            con3_s = self.data.close[-1] > self.data.my_settle[-n - 1] * (1 - 50 / 1000)
            con4_s = self.data.close[-1] < self.data.close[-n - 1] * (1 + 15 / 1000)
            con5_s = (
                temp < 0
                or temp > p9 / 10
                or min(temp_hhv, temp_hhv_ref) / max(temp_llv, temp_llv_ref) > 1 + p11 / 1000
                or self.data.close[-1] < max(self.data.close[-n - 1], self.data.open[-n]) * (1 - p12 / 1000)
            )
            # self.logger.logger.info(
            #     "%s con1_l=%s, con2_l=%s, con3_l=%s, con4_l=%s, con5_l=%s, con1_s=%s, con2_s=%s, con3_s=%s, con4_s=%s, con5_s=%s ",
            #     self.date_time.strftime("%Y-%m-%d %H:%M:%S"), con1_l, con2_l, con3_l, con4_l, con5_l, con1_s, con2_s, con3_s, con4_s, con5_s)
            if con1_l and con2_l and con3_l and con4_l and con5_l:
                self.buy_open(None, self.init_money * 0.3)
                self.bkhigh = -np.inf
                self.barslast_open = self.data.count
            elif con1_s and con2_s and con3_s and con4_s and con5_s:
                self.sell_open(None, self.init_money * 0.3)
                self.sklow = np.inf
                self.barslast_open = self.data.count
        elif self.account.positions[self.symbol]["pos_long"]["amount"] > 0:
            self.bkhigh = max(self.bkhigh, self.data.high[-1])
            self.bkhigh_loc = self.data.count if self.data.high[-1] == self.bkhigh else self.bkhigh_loc
            xn5 = 8 + (
                3
                if (self.data.count - self.barslast_open + 1) < abs(np.argwhere(self.data.n_trade_day == 1)[-1][0] - len(self.data.n_trade_day))
                else 0
            )
            if self.data.close[-1] < (self.account.positions[self.symbol]["pos_long"]["cost_basis"] - 1) * (1 - 9 / 1000):
                self.sell_close(self.account.positions[self.symbol]["pos_long"]["amount"], None)
            elif (
                self.data.close[-1] < self.bkhigh * (1 - xn5 / 1000)
                and self.data.close[-1] < (self.account.positions[self.symbol]["pos_long"]["cost_basis"] - 1) * (1 + 10 / 1000)
                and self.data.close[-1] == self.data.close[-(self.data.count - self.bkhigh_loc + 1) :].min()
            ):
                self.sell_close(self.account.positions[self.symbol]["pos_long"]["amount"], None)
            elif (
                self.data.close[-1] < self.bkhigh * (1 - 37 / 1000)
                and self.data.close[-1] >= (self.account.positions[self.symbol]["pos_long"]["cost_basis"] - 1) * (1 + 10 / 1000)
                and self.data.close[-1] == self.data.close[-(self.data.count - self.bkhigh_loc + 1) :].min()
            ):
                self.sell_close(self.account.positions[self.symbol]["pos_long"]["amount"], None)
        elif self.account.positions[self.symbol]["pos_short"]["amount"] > 0:
            self.sklow = min(self.sklow, self.data.low[-1])
            self.sklow_loc = self.data.count if self.data.low[-1] == self.sklow else self.sklow_loc
            xn5 = 8 + (
                3
                if (self.data.count - self.barslast_open + 1) < abs(np.argwhere(self.data.n_trade_day == 1)[-1][0] - len(self.data.n_trade_day))
                else 0
            )
            if self.data.close[-1] > (self.account.positions[self.symbol]["pos_short"]["cost_basis"] + 1) * (1 + 9 / 1000):
                self.buy_close(abs(self.account.positions[self.symbol]["pos_short"]["amount"]), None)
            elif (
                self.data.close[-1] > self.sklow * (1 + xn5 / 1000)
                and self.data.close[-1] > (self.account.positions[self.symbol]["pos_short"]["cost_basis"] + 1) * (1 - 10 / 1000)
                and self.data.close[-1] == self.data.close[-(self.data.count - self.sklow_loc + 1) :].max()
            ):
                self.buy_close(abs(self.account.positions[self.symbol]["pos_short"]["amount"]), None)
            elif (
                self.data.close[-1] > self.sklow * (1 + 37 / 1000)
                and self.data.close[-1] <= (self.account.positions[self.symbol]["pos_short"]["cost_basis"] + 1) * (1 - 10 / 1000)
                and self.data.close[-1] == self.data.close[-(self.data.count - self.sklow_loc + 1) :].max()
            ):
                self.buy_close(abs(self.account.positions[self.symbol]["pos_short"]["amount"]), None)


if __name__ == "__main__":
    t = strategy_load(
        TestStrategy,
        mode=Mode.BACKTEST,
        symbol="AP8888.CZCE",
        financial_asset=FinancialAsset.FUTURES,
        basic_data=BasicData.MINUTE,
        merge_num=2,
        init_money=********,
        parameters={},
        start_time="******** 2100",
        # start_time="******** 2100",
        end_time=None,
        size=2000,
    )
    res = t.run()
    res[0]["trade_log"].to_csv("trade_log.csv")

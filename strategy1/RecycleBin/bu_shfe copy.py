import numpy as np
import pandas as pd
from quant.protocols.enums import BasicData, FinancialAsset, Mode
from quant.protocols.request import *
from quant.research.strategies.indicator_parse import *
from quant.research.strategies.strategy import *

np.seterr(divide="ignore", invalid="ignore")
pd.set_option("display.max_rows", None)


class TestStrategy(IndicatorStrategy):
    def __init__(
        self,
        mode,
        symbol,
        financial_asset,
        basic_data,
        merge_num,
        init_money,
        parameters,
        start_time,
        end_time,
        size,
        array_manager=None,
        trade_account: int = -1,
        trade_symbol: QSymbol = QSymbol(""),
    ):
        super().__init__(
            mode,
            symbol,
            financial_asset,
            basic_data,
            merge_num,
            init_money,
            parameters,
            start_time,
            end_time,
            size,
            array_manager,
            trade_account,
            trade_symbol,
        )
        self._series.extend(
            [
                "N",
                "N_1",
                "N_2",
                "N_3",
                "HH_TODAY",
                "HH_YESTODAY",
                "LL_TODAY",
                "LL_YESTODAY",
                "CC_YESTODAY",
                "OO_TODAY",
                "CFJ1",
                "CFJ1_",
                "CFJ2",
                "CFJ",
                "UPPERBOUND_BREAK",
                "LOWERBOUND_BREAK",
                "IFDO",
                "BARSBP1",
                "BARSSP1",
                "CONTROL_B",
                "CONTROL_S",
                "TEMP",
                "LONG1",
                "SHORT1",
            ]
        )

    def calc(self):
        self.data.C.update(self.data.close[-1])
        C = self.data.C
        self.data.CLOSE.update(self.data.close[-1])
        CLOSE = self.data.CLOSE
        self.data.H.update(self.data.high[-1])
        H = self.data.H
        self.data.HIGH.update(self.data.high[-1])
        HIGH = self.data.HIGH
        self.data.L.update(self.data.low[-1])
        L = self.data.L
        self.data.LOW.update(self.data.low[-1])
        LOW = self.data.LOW
        self.data.O.update(self.data.open[-1])
        O = self.data.O
        self.data.OPEN.update(self.data.open[-1])
        OPEN = self.data.OPEN
        self.data.V.update(self.data.volume[-1])
        V = self.data.V
        self.data.VOL.update(self.data.volume[-1])
        VOL = self.data.VOL
        self.data.OPI.update(self.data.open_interest[-1])
        OPI = self.data.OPI
        BK = self.data.BK
        BKVOL = self.data.BKVOL
        BKPRICE = self.data.BKPRICE
        BARSBK = self.data.BARSBK
        BKHIGH = self.data.BKHIGH
        SK = self.data.SK
        SKVOL = self.data.SKVOL
        SKPRICE = self.data.SKPRICE
        BARSSK = self.data.BARSSK
        SKLOW = self.data.SKLOW
        BP = self.data.BP
        BARSBP = self.data.BARSBP
        SP = self.data.SP
        BARSSP = self.data.BARSSP

        BKVOL.update(self.account.positions[self.symbol]["pos_long"])
        if BKVOL[-1] > BKVOL[-2]:
            BK.modify(1)
        elif BKVOL[-1] < BKVOL[-2]:
            SP.modify(1)
        BK.update(0)
        SP.update(0)
        if BK[-2] == 1:
            BKPRICE.update(CLOSE[-2])
            BARSBK.update(1)
            BKHIGH.update(HIGH[-1])
        else:
            BKPRICE.update(BKPRICE[-1])
            BARSBK.update(BARSBK[-1] + 1)
            BKHIGH.update(max(BKHIGH[-1], HIGH[-1]))
        if SP[-2] == 1:
            BARSSP.update(1)
        else:
            BARSSP.update(BARSSP[-1] + 1)

        SKVOL.update(self.account.positions[self.symbol]["pos_short"])
        if SKVOL[-1] > SKVOL[-2]:
            SK.modify(1)
        elif SKVOL[-1] < SKVOL[-2]:
            BP.modify(1)
        SK.update(0)
        BP.update(0)
        if SK[-2] == 1:
            SKPRICE.update(CLOSE[-2])
            BARSSK.update(1)
            SKLOW.update(LOW[-1])
        else:
            SKPRICE.update(SKPRICE[-1])
            BARSSK.update(BARSSK[-1] + 1)
            SKLOW.update(min(SKLOW[-1], LOW[-1]))
        if BP[-2] == 1:
            BARSBP.update(1)
        else:
            BARSBP.update(BARSBP[-1] + 1)

        N = self.data.N
        N_1 = self.data.N_1
        N_2 = self.data.N_2
        N_3 = self.data.N_3
        HH_TODAY = self.data.HH_TODAY
        HH_YESTODAY = self.data.HH_YESTODAY
        LL_TODAY = self.data.LL_TODAY
        LL_YESTODAY = self.data.LL_YESTODAY
        CC_YESTODAY = self.data.CC_YESTODAY
        OO_TODAY = self.data.OO_TODAY
        CFJ1 = self.data.CFJ1
        CFJ1_ = self.data.CFJ1_
        CFJ2 = self.data.CFJ2
        CFJ = self.data.CFJ
        UPPERBOUND_BREAK = self.data.UPPERBOUND_BREAK
        LOWERBOUND_BREAK = self.data.LOWERBOUND_BREAK
        IFDO = self.data.IFDO
        BARSBP1 = self.data.BARSBP1
        BARSSP1 = self.data.BARSSP1
        CONTROL_B = self.data.CONTROL_B
        CONTROL_S = self.data.CONTROL_S
        TEMP = self.data.TEMP
        LONG1 = self.data.LONG1
        SHORT1 = self.data.SHORT1
        N.update(self.data.n_trade_day[-1])
        N_1.update(REF(N, N.value))
        N_2.update(REF(N_1, N.value))
        N_3.update(REF(N_2, N.value))
        # print(MAX1(N_1, N_2, N_3).data)
        ONEDAY = MAX1(N_1, N_2, N_3).value
        HH_TODAY.update(HHV(HIGH, N.value))
        HH_YESTODAY.update(REF(HH_TODAY, N.value))
        LL_TODAY.update(LLV(LOW, N.value))
        LL_YESTODAY.update(REF(LL_TODAY, N.value))
        CC_YESTODAY.update(REF(C, N.value))
        OO_TODAY.update(REF(O, N.value - 1))
        CFJ1.update(IF(N == 1, MAX(HH_YESTODAY - CC_YESTODAY, CC_YESTODAY - LL_YESTODAY) * 35 / 100, 0))
        CFJ1_.update(IF(N == 1, REF(CFJ1, SUMBARS(N == 1, 2).value - 1), 0))
        CFJ2.update(
            IF(
                N == 1,
                MIN(
                    0,
                    (
                        ABS(CC_YESTODAY - REF(O, SUMBARS(N == 1, 2).value - 1))
                        + ABS(REF(CC_YESTODAY, N.value) - REF(O, SUMBARS(N == 1, 3).value - 1))
                        - CC_YESTODAY * 25 / 1000
                    ),
                )
                * 15
                / 100,
                0,
            )
        )
        self.logger.logger.info(
            "%s OY: %f, CY:%f, HY: %f, LY: %f, CFJ1: %f, CFJ1_: %f, CFJ2: %f",
            self.date_time,
            REF(OO_TODAY, N.value).value,
            CC_YESTODAY.value,
            HH_YESTODAY.value,
            LL_YESTODAY.value,
            CFJ1.value,
            CFJ1_.value,
            CFJ2.value,
        )
        # CFJ.update(MIN(SUM(ABS(CFJ1+CFJ1+CFJ2),SUMBARS(N==1,4))/4,45/100*HHV(REF(ABS(C-OO_TODAY),N),SUMBARS(N==1,80))))
        CFJ.update(SUM(ABS(CFJ1 + CFJ1 + CFJ2), SUMBARS(N == 1, 4).value) / 4)
        if OO_TODAY >= CC_YESTODAY:
            UPPERBOUND_BREAK.update(
                MIN(MAX(CC_YESTODAY + CFJ, OO_TODAY + 50 / 100 * CFJ), MAX(CC_YESTODAY, LL_TODAY) + 90 / 100 * CFJ)
            )
            LOWERBOUND_BREAK.update(MAX(CC_YESTODAY - CFJ, OO_TODAY - 115 / 100 * CFJ))
        else:
            UPPERBOUND_BREAK.update(MIN(CC_YESTODAY + CFJ, OO_TODAY + 115 / 100 * CFJ))
            LOWERBOUND_BREAK.update(
                MAX(MIN(CC_YESTODAY - CFJ, OO_TODAY - 50 / 100 * CFJ), MIN(CC_YESTODAY, HH_TODAY) - 90 / 100 * CFJ)
            )
        IFDO.update(COUNTSIG(BK, N) + COUNTSIG(SK, N) < 2)
        BARSBP1.update(BARSLAST(SKVOL == 0 and REF(SKVOL, 1) > 0) + 1)
        BARSSP1.update(BARSLAST(BKVOL == 0 and REF(BKVOL, 1) > 0) + 1)
        CONTROL_B.update(
            IF(
                BARSBP1 < SUMBARS(N == 1, 1) and REF(C, BARSBP1.value) > SKPRICE,
                C > MIN(REF(C, N.value), REF(O, N.value - 1)) * (1 + 10 / 1000) or C > LLV(L, BARSSK.value + 1) * (1 + 20 / 1000),
                1,
            )
            and IF(BARSBK < N and REF(C, BARSSP1.value) < BKPRICE * (1 - 5 / 1000), C > BKPRICE * (1 + 12 / 1000), 1)
            and IF(
                BARSBK < SUMBARS(N == 1, 2) and BARSBK >= N and REF(C, BARSSP1.value) < BKPRICE * (1 - 4 / 1000),
                C > LLV(L, BARSBK.value + 1) * (1 + 30 / 1000)
                or C > BKPRICE * (1 + 10 / 1000)
                or C > MIN(REF(C, N.value), REF(O, N.value - 1)) * (1 + 20 / 1000),
                1,
            )
        )
        CONTROL_S.update(
            IF(
                BARSSP1 < SUMBARS(N == 1, 1) and REF(C, BARSSP1.value) < BKPRICE,
                C < MAX(REF(C, N.value), REF(O, N.value - 1)) * (1 - 10 / 1000) or C < HHV(H, BARSBK.value + 1) * (1 - 20 / 1000),
                1,
            )
            and IF(BARSSK < N and REF(C, BARSBP1.value) > SKPRICE * (1 + 5 / 1000), C < SKPRICE * (1 - 12 / 1000), 1)
            and IF(
                BARSSK < SUMBARS(N == 1, 2) and BARSSK >= N and REF(C, BARSBP1.value) > SKPRICE * (1 + 4 / 1000),
                C < HHV(H, BARSSK.value + 1) * (1 - 30 / 1000)
                or C < SKPRICE * (1 - 10 / 1000)
                or C < MAX(REF(C, N.value), REF(O, N.value - 1)) * (1 - 20 / 1000),
                1,
            )
        )
        P9 = 4
        P10 = 45
        P11 = 55
        P12 = 20
        TEMP.update(
            (
                MAX(HHV(H, P10 / 10 * ONEDAY), REF(HHV(H, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                - MIN(HHV(H, P10 / 10 * ONEDAY), REF(HHV(H, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                + MAX(LLV(L, P10 / 10 * ONEDAY), REF(LLV(L, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                - MIN(LLV(L, P10 / 10 * ONEDAY), REF(LLV(L, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
            )
            / (
                MIN(HHV(H, P10 / 10 * ONEDAY), REF(HHV(H, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                - MAX(LLV(L, P10 / 10 * ONEDAY), REF(LLV(L, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
            )
        )
        # SETTLE=SUM(VOL*C, N)/SUM(VOL, N)
        # IF(HHV(C,N)<MAX(REF(SETTLE,N)*(1+40/1000),REF(C,N)*(1+40/1000)),C==HHV(C,N),C==HHV(C,MIN(35,N))  and  C>HH_TODAY*(1-30/1000))
        # IF(LLV(C,N)>MIN(REF(SETTLE,N)*(1-40/1000),REF(C,N)*(1-40/1000)),C==LLV(C,N),C==LLV(C,MIN(35,N))  and  C<LL_TODAY*(1+30/1000))
        LONG1.update(
            C > UPPERBOUND_BREAK
            and C - LLV(L, MIN(N.value, 50)) < MAX(LLV(L, MIN(N.value, 50)) * 25 / 1000, CFJ * 185 / 100)
            and C > O
            and C == HHV(C, N.value)
            and IFDO
            and IF((REF(C, N.value - 1) / REF(C, N.value) - 1) > 23 / 1000, N >= 3 or (N > 1 and C > HV(H, N.value - 1)), 1)
            and (not (REF(C / OO_TODAY, N.value) < 1 - 23 / 1000) or C > MIN(OO_TODAY, CC_YESTODAY) * (1 + 22 / 1000))
            and (
                TEMP < 0
                or TEMP > P9 / 10
                or (
                    MIN(HHV(H, P10 / 10 * ONEDAY), REF(HHV(H, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                    / MAX(LLV(L, P10 / 10 * ONEDAY), REF(LLV(L, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                )
                > 1 + P11 / 1000
                or C > MIN(REF(O, N.value - 1), REF(C, N.value)) * (1 + P12 / 1000)
            )
        )
        SHORT1.update(
            C < LOWERBOUND_BREAK
            and HHV(L, MIN(N.value, 50)) - C < MAX(HHV(L, MIN(N.value, 50)) * 25 / 1000, CFJ * 185 / 100)
            and C < O
            and C == LLV(C, N.value)
            and IFDO
            and IF((1 - REF(C, N.value - 1) / REF(C, N.value)) > 23 / 1000, N >= 3 or (N > 1 and C < LV(L, N.value - 1)), 1)
            and (not (REF(C / OO_TODAY, N.value) > 1 + 23 / 1000) or C < MAX(OO_TODAY, CC_YESTODAY) * (1 - 22 / 1000))
            and (
                TEMP < 0
                or TEMP > P9 / 10
                or (
                    MIN(HHV(H, P10 / 10 * ONEDAY), REF(HHV(H, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                    / MAX(LLV(L, P10 / 10 * ONEDAY), REF(LLV(L, P10 / 10 * ONEDAY), P10 / 10 * ONEDAY))
                )
                > 1 + P11 / 1000
                or C < MAX(REF(O, N.value - 1), REF(C, N.value)) * (1 - P12 / 1000)
            )
        )
        self.logger.logger.info(
            "%s bkvol: %d, skvol: %d, CFJ: %f, UPPERBREAK: %f, LOWERBREAK: %f, CONTROL_B: %d, CONTROL_S: %d, TEMP: %f",
            self.date_time,
            BKVOL.value,
            SKVOL.value,
            CFJ.value,
            UPPERBOUND_BREAK.value,
            LOWERBOUND_BREAK.value,
            CONTROL_B.value,
            CONTROL_S.value,
            TEMP.value,
        )
        if BKVOL == 0 and SKVOL == 0 and LONG1 and CONTROL_B:
            self.BK(1)
        if SKVOL == 0 and BKVOL == 0 and SHORT1 and CONTROL_S:
            self.SK(1)
        XN2 = 9
        XN4 = 8
        XN5 = 15
        XN3 = 38
        if BKVOL > 0 and C < BKPRICE * (1000 - XN2 - IF(BARSBK < 26, 1, 0)) / 1000 and C < O and C == LLV(MIN(C, O), 2):
            self.SP(BKVOL)
        if SKVOL > 0 and C > SKPRICE * (1000 + XN2 + IF(BARSSK < 26, 1, 0)) / 1000 and C > O and C == HHV(MAX(C, O), 2):
            self.BP(SKVOL)
        if (
            BKVOL > 0
            and C <= BKHIGH * (1000 - XN5) / 1000
            and C < BKPRICE * (1000 + XN4) / 1000
            and C < O
            and C == LLV(MIN(C, O), 2)
        ):
            self.SP(BKVOL)
        if (
            SKVOL > 0
            and C >= SKLOW * (1000 + XN5) / 1000
            and C > SKPRICE * (1000 - XN4) / 1000
            and C > O
            and C == HHV(MAX(C, O), 2)
        ):
            self.BP(SKVOL)
        if (
            BKVOL > 0
            and C <= BKHIGH * (1000 - XN3) / 1000
            and C >= BKPRICE * (1000 + XN4) / 1000
            and C < O
            and C == LLV(MIN(C, O), 2)
        ):
            self.SP(BKVOL)
        if (
            SKVOL > 0
            and C >= SKLOW * (1000 + XN3) / 1000
            and C <= SKPRICE * (1000 - XN4) / 1000
            and C > O
            and C == HHV(MAX(C, O), 2)
        ):
            self.BP(SKVOL)


import cProfile

cProfile.run(
    """
t = strategy_load(
    TestStrategy,
    mode=Mode.BACK_TESTING,
    # mode=Mode.TRADE,
    symbol="BU2209.SHFE",
    # symbol="BU8888.SHFE",
    financial_asset=FinancialAsset.FUTURES,
    basic_data=BasicData.MINUTE,
    merge_num=3,
    init_money=********,
    parameters={
        "a1": 130,
        "a2": 11,
        "a3": 15,
        "a4": 18,
        "a5": 30,
        "a6": 5,
        "a7": 55,
        "a8": 0.8,
        "a9": 1.2,
    },
    start_time="******** 2100",
    end_time="******** 1500",
    # end_time="******** 1500",
    size=2000,
    trade_account="178081",
    trade_symbol="AP2210.CZCE",
)

_ = t.run()
_["trade_log"].to_csv("trade_log.csv")
""",
    "cProfile_res",
    sort=1,
)

{"cells": [{"cell_type": "code", "execution_count": 1, "id": "972f1cd3-fcfb-488c-a4cd-6f0d00713c4d", "metadata": {"execution": {"iopub.execute_input": "2024-06-02T13:14:32.853231Z", "iopub.status.busy": "2024-06-02T13:14:32.852888Z", "iopub.status.idle": "2024-06-02T13:14:32.864833Z", "shell.execute_reply": "2024-06-02T13:14:32.864318Z", "shell.execute_reply.started": "2024-06-02T13:14:32.853216Z"}}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "id": "2ea83151-cd8d-4160-8965-4e548d5616aa", "metadata": {"execution": {"iopub.execute_input": "2024-06-02T13:14:33.244091Z", "iopub.status.busy": "2024-06-02T13:14:33.243659Z", "iopub.status.idle": "2024-06-02T13:14:35.577668Z", "shell.execute_reply": "2024-06-02T13:14:35.577119Z", "shell.execute_reply.started": "2024-06-02T13:14:33.244073Z"}, "tags": []}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from qnt_research.data_manager.original_data import gl_original_data\n", "from qnt_research.engine import (\n", "    EventDriveEngine,\n", "    gl_event_drive_engine,\n", "    hyperparameter_tuning,\n", "    hyperparameter_tuning1,\n", ")\n", "from qnt_research.trader.trader import Trader\n", "from qnt_research.virtual_exchange import gl_virtual_exchange\n", "from qnt_utils.enums import BasicData, StrategyMode\n", "from qnt_utils.label import QSymbol\n", "from sa_czce import TestStrategy\n", "\n", "pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "7af1810c-13ec-4be2-8a3d-e64c94cfaa39", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["if __name__ == \"__main__\":\n", "    from itertools import product\n", "\n", "    # res = hyperparameter_tuning1(\n", "    #     5,\n", "    #     \"20200101 0900\",\n", "    #     \"20230531 1500\",\n", "    #     1e7,\n", "    #     TestStrategy,\n", "    #     [\n", "    #         dict(zip((\"p1\", \"p2\", \"p3\", \"p4\", \"p5\", \"p6\", \"p7\", \"p8\", \"symbol\"), i))\n", "    #         for i in list(\n", "    #             product(\n", "    #                 # range(95, 110, 5),\n", "    #                 # range(5, 8, 1),\n", "    #                 [100],\n", "    #                 [6],\n", "    #                 # range(60, 75, 5),\n", "    #                 [65],\n", "    #                 # range(135, 160, 5),\n", "    #                 [140],\n", "    #                 # range(7, 10, 1),\n", "    #                 [8],\n", "    #                 # range(25, 30, 1),\n", "    #                 [27],\n", "    #                 range(50, 60, 2),\n", "    #                 range(50, 65, 3),\n", "    #                 # [56],\n", "    #                 [\n", "    #                     \"SA8888.CZCE\",\n", "    #                     \"RB8888.SHFE\",\n", "    #                     \"FG8888.CZCE\",\n", "    #                     \"SC8888.INE\",\n", "    #                     \"BU8888.SHFE\",\n", "    #                     \"TA8888.CZCE\",\n", "    #                     \"FU8888.SHFE\",\n", "    #                     # \"AG8888.SHFE\",\n", "    #                     # \"NI8888.SHFE\",\n", "    #                     # \"ZN8888.SHFE\",\n", "    #                     # \"RU8888.SHFE\",\n", "    #                     # \"FU8888.SHFE\",\n", "    #                     # \"I8888.DCE\",\n", "    #                     # \"JD8888.DCE\",\n", "    #                     # \"PP8888.DCE\",\n", "    #                     # \"PG8888.DCE\",\n", "    #                     # \"TA8888.CZCE\",\n", "    #                     # \"MA8888.CZCE\",\n", "    #                     # \"AP8888.CZCE\",\n", "    #                     # \"EG8888.DCE\",\n", "    #                     # \"EB8888.DCE\",\n", "    #                 ],\n", "    #             )\n", "    #         )\n", "    #     ],\n", "    # )\n", "    # print(res)\n", "    # res.to_csv(\"res1.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "c763b7e2-0d18-4a07-9fce-c8850ff25380", "metadata": {"execution": {"execution_failed": "2024-06-02T16:28:08.931Z", "iopub.execute_input": "2024-06-02T16:27:49.093201Z", "iopub.status.busy": "2024-06-02T16:27:49.092892Z"}, "scrolled": true, "tags": []}, "outputs": [], "source": ["gl_event_drive_engine.set_env(StrategyMode.BACKTEST, \"20200101 0900\")\n", "strategy = TestStrategy(False, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": \"SA8888.CZCE\"})\n", "strategy.register_trader(Trader(1e7, gl_virtual_exchange))\n", "gl_event_drive_engine.register_strategy(strategy)\n", "res = gl_event_drive_engine.run()"]}, {"cell_type": "code", "execution_count": null, "id": "0c744ca1-29ea-4f1b-ad21-48222531e596", "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["for symbol in [\n", "    \"SA8888.CZCE\",\n", "    \"RB8888.SHFE\",\n", "    \"FG8888.CZCE\",\n", "    \"SC8888.INE\",\n", "    \"BU8888.SHFE\",\n", "    \"TA8888.CZCE\",\n", "    \"FU8888.SHFE\",\n", "    \"AG8888.SHFE\",\n", "    \"NI8888.SHFE\",\n", "    \"ZN8888.SHFE\",\n", "    \"RU8888.SHFE\",\n", "    \"FU8888.SHFE\",\n", "    \"I8888.DCE\",\n", "    \"JD8888.DCE\",\n", "    \"PP8888.DCE\",\n", "    \"PG8888.DCE\",\n", "    \"TA8888.CZCE\",\n", "    \"MA8888.CZCE\",\n", "    \"AP8888.CZCE\",\n", "    \"EG8888.DCE\",\n", "    \"EB8888.DCE\",\n", "]:\n", "    print(\"本次回测: {}\".format(symbol))\n", "    gl_event_drive_engine.set_env(StrategyMode.BACKTEST, \"20220101 0900\")\n", "    strategy = TestStrategy(True, **{\"p1\": 100, \"p2\": 6, \"p3\": 65, \"p4\": 140, \"p5\": 8, \"p6\": 27, \"p7\": 50, \"p8\": 56, \"symbol\": symbol})\n", "    strategy.register_trader(Trader(1e7, gl_virtual_exchange))\n", "    gl_event_drive_engine.register_strategy(strategy)\n", "    res = gl_event_drive_engine.run()"]}, {"cell_type": "code", "execution_count": null, "id": "d6f27787-6f26-4f06-ba8a-ea83ac787d78", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abe8e4b8-c49f-440e-b9c6-be59ec48f25f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}
import re
from collections.abc import Generator
from itertools import product
from typing import Dict, List, Literal, Optional, Tuple, Union

import numpy as np
import pandas as pd
import qnt_research.api as qnt
import six
from aichemy.factor_generator import FactorGen, ensure_list
from aichemy.utils import to_nstimestamp

from .utils import format_alpha_name


def generate_args_group_key(args_group):
    args_list = list(args_group.keys())
    args_list.sort(key=lambda x: int(re.search(r"\d+$", x).group()))
    return ", ".join([str(args_group[x]) for x in args_list])


class TechnicalIndicators(FactorGen):
    _ARGS_GROUP = {
        "alpha_001": {"arg_1": [5, 10, 20, 30, 60, 120, 250]},
        # "alpha_002": {
        #     "arg_1": {"start": 2, "stop": 30, "step": 2},
        #     "arg_2": {"start": 5, "stop": 105, "step": 5},
        #     "arg_3": {"start": 1, "stop": 60, "step": 3},
        # },
    }

    def __init__(self, *args, **kwds):
        super().__init__(*args, **kwds)
        self.args: Dict[str, Dict] = {}

    def alpha_001(self, code, end_date="", fq="forward", arg_1=None):
        arg_1 = arg_1 if arg_1 is not None else self.args.get("alpha_001", {})["arg_1"]

        _, close_, *_ = self._fetch_price(code, end_date, fq, count=arg_1)
        alpha = close_.iloc[-1] / close_.mean(axis=0)
        return alpha

    def alpha_002(self, code, end_date="", fq="forward", arg_1=None, arg_2=None, arg_3=None):
        arg_1 = arg_1 if arg_1 is not None else self.args.get("alpha_002", {})["arg_1"]
        arg_2 = arg_2 if arg_2 is not None else self.args.get("alpha_002", {})["arg_2"]
        arg_3 = arg_3 if arg_3 is not None else self.args.get("alpha_002", {})["arg_3"]

        _, close_, *_ = self._fetch_price(code, end_date, fq, count=max(arg_1 + arg_3, arg_2 + arg_3) + 10)
        ma1 = close_.rolling(arg_1).apply(lambda x: x.mean(axis=0))
        ma2 = close_.rolling(arg_2).apply(lambda x: x.mean(axis=0))
        alpha = (ma1 - ma2).iloc[-arg_3:].mean(axis=0) / close_.iloc[-1]
        return alpha

    def _args_gnt(self, alpha_name: str) -> Generator[Dict, None, None]:
        """生成迭代的参数组

        Args:
            alpha_name (str): 因子名称

        Yields:
            Generator[Dict, None, None]: _description_
        """        
        keys = list(self._ARGS_GROUP[alpha_name].keys())
        for i in product(*[self._ARGS_GROUP[alpha_name][k] for k in keys]):
            yield {alpha_name: dict(zip(keys, i))}

    def _alpha_gnt(self, alpha_name, symbol_list, benchmark, date, fq=Optional[Literal["forward", "backward"]]):
        for args in self._args_gnt(alpha_name):
            label = f"{alpha_name}({generate_args_group_key(args[alpha_name])})"
            data = self.calc(symbol_list, benchmark, date, fq, alpha=[alpha_name], args_group=args)[
                f"{self.__class__.__name__}_{alpha_name}"
            ]
            yield label, data

    def tuning(
        self,
        criterion,
        alpha_name,
        symbol_list,
        benchmark,
        start_dt,
        end_dt,
        fq: Optional[Literal["forward", "backward"]],
    ):
        date_list = list(map(lambda x: x.strftime("%Y-%m-%d"), qnt.get_trade_days("SHFE", start_dt, end_dt)))
        close_df = self.data_fetcher.get_price(symbol_list, date_list[-1], "1d", "backward", len(date_list))["close"]

        return criterion(self._alpha_gnt(alpha_name, symbol_list, benchmark, date_list, fq=fq), close_df)

    def calc(
        self,
        symbol_list: list,
        benchmark: str,
        date: Union[List[str], str],
        fq: Optional[Literal["forward", "backward"]] = "forward",
        alpha: Union[str, List] = "all",
        args_group: Optional[Dict] = None,
    ):
        if isinstance(alpha, six.string_types) and alpha == "all":
            alphas = ["alpha_{:03d}".format(i) for i in range(1, 3)]
        else:
            alphas = [format_alpha_name(a) for a in ensure_list(alpha)]

        alpha_need_bm = {}

        if len(symbol_list) == 0:
            return pd.DataFrame(columns=alphas, dtype=np.float64)

        args_group = {} if args_group is None else args_group
        date = [date] if isinstance(date, six.string_types) else date
        res = []
        for end_date in date:
            edt = to_nstimestamp(f"{end_date} 15:00:00")
            result = list()
            for alpha_name in alphas:
                if alpha_name in alpha_need_bm:
                    result.append(
                        getattr(self, alpha_name)(
                            code=symbol_list,
                            benchmark=benchmark,
                            end_date=end_date,
                            fq=fq,
                            **args_group.get(alpha_name, {}),
                        ).rename(alpha_name)
                    )
                else:
                    result.append(
                        getattr(self, alpha_name)(
                            code=symbol_list, end_date=end_date, fq=fq, **args_group.get(alpha_name, {})
                        ).rename(alpha_name)
                    )
            result = pd.concat(result, axis=1)  # index - symbol, columns - alpha_name
            result.index = pd.MultiIndex.from_tuples(
                result.index.map(lambda symbol: (edt, symbol))
            )  # index - (timestamp, symbol)
            res.append(result)
        res = pd.concat(res, axis=0)
        res = {f"{self.__class__.__name__}_{key}": res[key].unstack().sort_index() for key in res.columns}
        return res


class CacheTechnicalIndicators(TechnicalIndicators):
    def __init__(self, *args, **kwds):
        super().__init__(*args, **kwds)
        self.cache = {}

    def _alpha_gnt(self, alpha_name, symbol_list, benchmark, date, fq: Optional[Literal["forward", "backward"]]):
        for args in self._args_gnt(alpha_name):
            label = f"{alpha_name}({generate_args_group_key(args[alpha_name])})"
            if label in self.cache:
                yield label, self.cache[label]
            else:
                self.cache[label] = self.calc(symbol_list, benchmark, date, fq, alpha=[alpha_name], args_group=args)[
                    f"{self.__class__.__name__}_{alpha_name}"
                ]
                yield label, self.cache[label]

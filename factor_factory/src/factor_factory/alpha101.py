# -*- coding: utf-8 -*-

# *************************************************************
#  Copyright (c) JoinQuant Product Department
#
#  Droduct Manager: <PERSON><PERSON><PERSON> <<EMAIL>>
#  Developer: Danxuan Han
#  CreateTime: 2017-04
# *************************************************************

# functions which are in common use
from __future__ import division

import datetime
import warnings
from functools import wraps
from typing import List, Union

import numpy as np
import pandas as pd
import six
from aichemy.factor_generator import FactorGen, ensure_list, ignore_warning
from aichemy.utils import to_nstimestamp

from .utils import format_alpha_name

# from .jqapi import G_IN_STRATEGY, get_index_stocks, get_all_securities, valuation, query, get_fundamentals
# from .utils import ignore_warning, few_stock_warning_alpha101, ensure_list


###################### 共用函数群 ######################
def few_stock_warning_alpha101(func):
    # 对 alpha191 中含有 rank 的表达式
    # 如果输入股票数量太少，提示 warnings

    @wraps(func)
    def func_wrapper(self, *args, **kwargs):
        if len(args) >= 2:
            code = args[0]
        elif kwargs:
            code = kwargs.get("index", None)
        else:
            code = []
        if not isinstance(code, six.string_types) and len(ensure_list(code)) <= 5:
            warnings.warn("{funcname} 函数的值受股票池影响, 建议增加股票池股票数量".format(funcname=func.__name__))
        return func(self, *args, **kwargs)

    return func_wrapper


def rolling_pipe(dataframe, window, min_periods, fctn):
    return pd.concat(
        [
            (
                pd.Series(dataframe.iloc[max(i - window, 0) : i].pipe(fctn), index=dataframe.columns)
                if i >= min_periods
                else pd.Series(np.nan, index=dataframe.columns)
            )
            for i in range(1, len(dataframe) + 1)
        ],
        axis=1,
        keys=dataframe.index,
    ).T


def _abs(x):
    # x is a dataframe
    """
    Meaning:
        绝对值

    Inputs:
        x: 一个Series, index为股票代码

    Outputs:
        result: 一个Series, index为股票代码
    """
    return np.abs(x)


def _sign(x):
    # x is a series
    """
    Meaning:
        判断正负号，正数为1，负数为-1

    Inputs:
        x: 一个Series, index为股票代码，values为需要判断正负号的数值

    Outputs:
        result: 一个Series, index为股票代码，values为+1或-1
    """
    # return np.sign(x, where=~x.isnull()).where(~x.isnull(), np.nan)
    xx = ~x.isnull()
    ret = x.copy()
    ret[xx] = np.sign(x)
    ret[~xx] = np.nan
    return ret


def __log(x):
    def _protected_log(x1):
        """Closure of log for zero arguments."""
        with np.errstate(divide="ignore", invalid="ignore"):
            return pd.DataFrame(
                np.where(x1.abs().values > 1e-7, np.log(x1.abs()), np.nan), index=x1.index, columns=x1.columns
            )

    return _protected_log(x)


def _div(data1, data2):
    def _protected_division(x1, x2):
        """Closure of division (x1/x2) for zero denominator."""
        with np.errstate(divide="ignore", invalid="ignore"):
            return pd.DataFrame(
                np.where(x2.abs().values > 1e-7, np.divide(x1.values, x2.values), np.nan),
                index=x1.index,
                columns=x1.columns,
            )

    return _protected_division(data1, data2)


def __correlation(x, y, window=10):
    """
    Meaning:
       correlation value

    Inputs:
       x & y : x,y can be a DataFrame, x and y have same length

    Outputs:
       相关系数的数值

    """
    min_count = min(x.shape[0], y.shape[0])
    x = x.iloc[-min_count:]
    y = y.iloc[-min_count:]

    def _corr(X, Y):
        # display(X)
        # display(Y)
        # display("----------------------------------------------------------")
        mask = np.isfinite(X) & np.isfinite(Y)
        # display(mask)
        # display('----------------------------------------------------------')
        X = np.ma.masked_array(X, mask=~mask)
        Y = np.ma.masked_array(Y, mask=~mask)
        # display(X)
        # display(Y)
        # display('----------------------------------------------------------')
        X = (X - X.mean(axis=0)) / X.std(axis=0)
        Y = (Y - Y.mean(axis=0)) / Y.std(axis=0)

        # display(X)
        # display(Y)
        # display('----------------------------------------------------------')
        N = mask.sum(axis=0)
        # display(N)
        # display('----------------------------------------------------------')
        c = np.nansum(X * Y, axis=0) / np.where(N >= 2, N, np.nan)
        # c = np.nansum(X * Y, axis=0) / np.where(N >= 3, N, np.nan)
        # display(c)
        # display('----------------------------------------------------------')
        return np.ma.filled(c, np.nan)

    ret = x.pipe(
        rolling_pipe,
        window=int(window),
        min_periods=max(2, int(window)),
        fctn=lambda df: pd.Series(_corr(df.values, y.loc[df.index].values), index=df.columns),
    )
    # display(ret)
    return ret


def _rank(x):
    """
    Inputs:
        x: 一个需要rank的series/DataFrame

    Outputs:
        第几名-从第一名开始往后从大到小排（最大值为第一名）
        DataFrame是按时间截面进行排序
    """
    ret = x.T.rank(pct=True, method="average", na_option="keep").T
    # display(ret)
    return ret


def __max(data1, data2):
    if hasattr(data1, "shape") and hasattr(data2, "shape"):
        min_count = min(data1.shape[0], data2.shape[0])
        data1 = data1.iloc[-min_count:]
        data2 = data2.iloc[-min_count:]

    return np.maximum(data1, data2)


def __min(data1, data2):
    if hasattr(data1, "shape") and hasattr(data2, "shape"):
        min_count = min(data1.shape[0], data2.shape[0])
        data1 = data1.iloc[-min_count:]
        data2 = data2.iloc[-min_count:]

    return np.minimum(data1, data2)


def __stddev(x, window=10):
    """
    Meaning:
        moving time-series standard deviation over the past d days

    Inputs:
        dataframe: 需要求标准差的dataframe（已经把长度切割好的）,index为时间,columns为股票池

    Outputs:
        标准差series,index为股票代码
    """
    if str(pd.__version__) >= "0.18.0":
        return x.rolling(int(window), min_periods=2).std()
    else:
        return pd.rolling_std(x, int(window), min_periods=2)


def __signedpower(x, a):
    """
    Meaning:
        signedpower(x,a)=x^a
    Inputs:
        x 是一个series,或者一个int
        a 是一个series,或者一个int,当a为series时，index与x相同
    Outputs:
        x中每个元素的a次方

    """

    return np.power(x, a)


def __decay_linear(x, window=10):
    """
    Meaning:
        weighted moving average over the past d days with linearly decaying weights d,d-1,...1, ---rescaled to sum up to 1

    Inputs:
        x: series or list, which length is d

    Outputs:
        value of weighted moving average
    """
    window = int(window)
    weights = np.arange(window) + 1
    weights = weights / np.sum(weights)
    return x.pipe(
        rolling_pipe,
        window=window,
        min_periods=window,
        fctn=lambda df: np.where(
            np.isfinite(df).any(axis=0),
            np.average(np.ma.filled(np.ma.masked_invalid(df.values), 0.0), axis=0, weights=weights),
            np.nan,
        ),
    )


def __decay_exp(x, window=10):
    window = int(window)

    def get_ts_weights_ewma(window):
        weights = 0.25
        weights = np.power(weights, np.arange(window))
        weights /= weights.sum()
        return np.flipud(weights)

    weights = get_ts_weights_ewma(window)
    return x.pipe(
        rolling_pipe,
        window=window,
        min_periods=window,
        fctn=lambda df: np.where(
            np.isfinite(df).any(axis=0),
            np.average(np.ma.filled(np.ma.masked_invalid(df.values), 0.0), axis=0, weights=weights),
            np.nan,
        ),
    )


def __scale(x, k=1):
    """
    Meaning:
        rescaled x such that sum(abs(x)) = a (the default is a=1)

    Inputs:
        x: a series, can't be a list

    Outputs:
        value of x
    """
    return x.mul(k).div(np.nansum(np.abs(x), axis=1), axis=0)


def __covariance(dataframe1, dataframe2, window=10):
    """
    Meaning:
        covariance value

    Inputs:
        dataframe1 & dataframe2 : 需要进行covariance的dataframe，两个dataframe具有相同的index和相同的columns

    Outputs:
         一个series，index为股票代码，values为对应的covariance的数值
    """
    min_count = min(dataframe1.shape[0], dataframe2.shape[0])
    dataframe1 = dataframe1.iloc[-min_count:]
    dataframe2 = dataframe2.iloc[-min_count:]

    def _cov(X, Y):
        mask = np.isfinite(X) & np.isfinite(Y)
        X = np.ma.masked_array(X, mask=~mask)
        Y = np.ma.masked_array(Y, mask=~mask)

        X = X - X.mean(axis=0)
        Y = Y - Y.mean(axis=0)

        N = mask.sum(axis=0)
        c = np.nansum(X * Y, axis=0) / np.where(N >= 3, N, np.nan)
        return np.ma.filled(c, np.nan)

    return dataframe1.pipe(
        rolling_pipe,
        window=int(window),
        min_periods=max(2, int(window)),
        fctn=lambda df: _cov(df.values, dataframe2.loc[df.index].values),
    )


def __delta(dataframe, d):
    """
    Meaning:
       delta(x,d):today's value of x minus the value of x d days ago

    Inputs:
       dataframe: 要计算的x已经计算好了, 并且dataframe也已经切割好，dataframe的最后一行为被减数

    Outputs:
       一个series，index为股票代码，values为delta值

    """
    return dataframe.diff(int(d))


def _delay(dataframe, d):
    return dataframe.shift(int(d))


def __ts_rank(dataframe, window=10):
    """
    Meaning:
       ts_rank(x,d) = time-series rank in the past d days

    Inputs:
       dataframe: 要计算的x已经计算好了, 并且dataframe也已经切割好，共有d行，dataframe的最后一行为被rank的数

    Outputs:
       一个series，index为股票代码，values为delta值

    """
    try:
        from bottleneck import nanrankdata

        bn = True
    except ImportError:
        bn = False

    def _rolling_rank(x):
        if bn:
            return nanrankdata(x, axis=0)[-1] / np.isfinite(x).sum(axis=0)
        else:
            return x.rank(pct=True, method="average", na_option="keep").iloc[-1]

    return dataframe.pipe(rolling_pipe, window=int(window), min_periods=max(1, int(window)), fctn=_rolling_rank)


def _ts_sum(x, window=10):
    if str(pd.__version__) >= "0.18.0":
        return x.rolling(int(window), min_periods=1).sum()
    else:
        return pd.rolling_sum(x, int(window), min_periods=1)


def __ts_product(x, window=10):
    def _rolling_prod(a):
        return np.where(np.isfinite(a).any(axis=0), np.nanprod(a, axis=0), np.nan)

    return x.pipe(rolling_pipe, window=int(window), min_periods=max(1, int(window)), fctn=_rolling_prod)


def _ts_mean(x, window=10):
    if str(pd.__version__) >= "0.18.0":
        return x.rolling(int(window), min_periods=1).mean()
    else:
        return pd.rolling_mean(x, int(window), min_periods=1)


def __ts_argmax(dataframe, window=10):
    """
    Meaning:
        which day ts_max(x,d) occurred on

    Inputs:
       dataframe : 需要进行求值的DataFrame，columns为股票代码，index为时间

    Outputs:
       一个Series，index为股票代码，values为每只股票这几天最大的值对应的序数，（第一天为1，有d天，最后一天为d，若最大值发生在第二天，
       则这只股票对应值为2）
    """
    return dataframe.pipe(
        rolling_pipe,
        window=int(window),
        min_periods=max(1, int(window)),
        fctn=lambda df: np.ma.filled(
            np.where(np.isfinite(df.values).any(axis=0), np.nanargmax(np.ma.masked_invalid(df.values), axis=0), np.nan),
            np.nan,
        ),
    )


def __ts_argmin(dataframe, window=10):
    """
    Meaning:
        which day ts_min(x,d) occurred on

    Inputs:
       dataframe : 需要进行求值的DataFrame，columns为股票代码，index为时间

    Outputs:
       一个Series，index为股票代码，values为每只股票这几天最小的值对应的序数，（第一天为1，有d天，最后一天为d，若最大值发生在第二天，
       则这只股票对应值为2）
    """
    return dataframe.pipe(
        rolling_pipe,
        window=int(window),
        min_periods=max(1, int(window)),
        fctn=lambda df: np.ma.filled(
            np.where(np.isfinite(df.values).any(axis=0), np.nanargmin(np.ma.masked_invalid(df.values), axis=0), np.nan),
            np.nan,
        ),
    )


def __ts_min(dataframe, window=10):
    """
    Meaning:
       time-series min over the past d days

    Inputs:
       dataframe: 被切割好的DataFrame, index长度为d

    Outputs:
       一个series，index为股票代码，values为min值

    """
    if str(pd.__version__) >= "0.18.0":
        return dataframe.rolling(int(window), min_periods=1).min()
    else:
        return pd.rolling_min(dataframe, int(window), min_periods=1)


def __ts_max(dataframe, window=10):
    """
    Meaning:
       time-series max over the past d days

    Inputs:
       dataframe: 被切割好的DataFrame, index长度为d

    Outputs:
       一个series，index为股票代码，values为min值

    """
    if str(pd.__version__) >= "0.18.0":
        return dataframe.rolling(int(window), min_periods=1).max()
    else:
        return pd.rolling_max(dataframe, int(window), min_periods=1)


def __where(c, x=1, y=0):
    min_size = c.shape[0]
    if hasattr(x, "shape"):
        min_size = min(min_size, x.shape[0])
    if hasattr(y, "shape"):
        min_size = min(min_size, y.shape[0])

    if hasattr(x, "shape"):
        x = x.iloc[-min_size:]
    if hasattr(y, "shape"):
        y = y.iloc[-min_size:]

    return pd.DataFrame(
        np.where(np.asarray(c.iloc[-min_size:].values, dtype=bool), x, y), index=c.index[-min_size:], columns=c.columns
    ).astype("float64")


def _reindex_to_same_index(**kwargs):
    if len(kwargs) <= 1:
        return kwargs

    df = pd.concat(kwargs, axis=1)
    return dict([(k, df[k].sort_index()) for k in kwargs])


def _get_func_globals():
    return dict(
        div=_div,
        log=__log,
        abs=_abs,
        sign=_sign,
        rank=_rank,
        signedpower=__signedpower,
        scale=__scale,
        max=__max,
        min=__min,
        sum=_ts_sum,
        sma=_ts_mean,
        product=__ts_product,
        stddev=__stddev,
        ts_rank=__ts_rank,
        ts_max=__ts_max,
        ts_min=__ts_min,
        ts_argmax=__ts_argmax,
        ts_argmin=__ts_argmin,
        decay_linear=__decay_linear,
        decay_exp=__decay_exp,
        delta=__delta,
        delay=_delay,
        correlation=__correlation,
        covariance=__covariance,
        where=__where,
    )


###################### Alpha函数群 ######################
class Alpha101(FactorGen):
    """用于计算Alpha191因子的类

    Examples:
        >>> from aichemy.factor_gen import Alpha101
        >>> from aichemy.data_fetcher import DataFetcherOfQNT()
        >>> data_fetcher = DataFetcherOfQNT()
        >>> alpha = Alpha101(data_fetcher)
        >>> alpha.alpha_001("20231231", ["RU8888.SHFE", "RB8888.SHFE", "SA8888.CZCE", "J8888.DCE", "I8888.DCE", "NI8888.SHFE", "AG8888.SHFE", "AU8888.SHFE", "AP8888.CZCE", "JD8888.DCE"])

    """

    def get_pool(self, index, enddate=None):
        """
        Meaning:
            得到成分股的股票代码list

        Inputs:
            index: 股指代码

        Outputs:
            成分股的list
        """
        if enddate is None:
            enddate = datetime.date.today().strftime("%Y-%m-%d")
        else:
            enddate = pd.Timestamp(enddate).strftime("%Y-%m-%d")
        pool = list(index)
        if not pool:
            return pd.Series(index=pool, dtype=bool)
        return self.__get_price(pool, end_date=enddate, field="paused", count=1, fq=None).iloc[-1].astype(bool)

    def __get_price(self, pool, end_date=None, fq=None, count=1, field="close"):
        if field == "vwap":
            money = self.__get_price(pool, end_date=end_date, fq=fq, count=count, field="money")
            volume = self.__get_price(pool, end_date=end_date, fq=fq, count=count, field="volume")
            return money / volume
        elif field.startswith("adv"):
            window = int(field.replace("adv", ""))
            volume = self.__get_price(pool, end_date=end_date, fq=fq, count=count + window - 1, field="volume")
            return _ts_mean(volume, window=window)
        elif field == "returns":
            close = self.__get_price(pool, end_date=end_date, fq=fq, count=count + 1, field="close")
            return _div(close.iloc[1:], _delay(close, 1).iloc[1:]) - 1
        elif field == "market_cap":
            raise NotImplementedError
        else:
            if isinstance(pool, six.string_types):
                pool = [pool]
            if end_date is None:
                end_date = datetime.date.today().strftime("%Y-%m-%d")
            else:
                end_date = pd.Timestamp(end_date).strftime("%Y-%m-%d")
            temp = self._fetch_price(pool, end_date=end_date, fq=fq, count=count)
            return temp[
                {"open": 0, "close": 1, "high": 2, "low": 3, "volume": 4, "money": 5, "paused": 6}.get(field)
            ].copy()

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_001(self, index, enddate, fq="forward"):
        """
        公式：
            rank(Ts_ArgMax(SignedPower(((returns < 0) ? stddev(returns, 20) : close), 2.), 5)) - 0.5
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(rank(ts_argmax(signedpower(where((returns < 0), stddev(returns, 20), close), 2.), 5)) -0.5)"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=24, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=5, fq=fq)

        globals_ = _reindex_to_same_index(returns=returns, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    @ignore_warning(message="divide by zero", category=RuntimeWarning)
    def alpha_002(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * correlation(rank(delta(log(volume), 2)), rank(((close - open) / open)), 6))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * correlation(rank(delta(log(volume), 2)), rank(div((close - open), open)), 6))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        open_ = self.__get_price(valid_pool, end_date=enddate, field="open", count=6, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=6, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=8, fq=fq)

        globals_ = _reindex_to_same_index(open=open_, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_003(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * correlation(rank(open), rank(volume), 10))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * correlation(rank(open), rank(volume), 10))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        open_ = self.__get_price(valid_pool, end_date=enddate, field="open", count=10, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=10, fq=fq)

        globals_ = _reindex_to_same_index(open=open_, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_004(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * Ts_Rank(rank(low), 9))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * ts_rank(rank(low), 9))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=9, fq=fq)
        globals_ = _reindex_to_same_index(low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_005(self, index, enddate, fq="forward"):
        """
        公式：
            (rank((open - (sum(vwap, 10) / 10))) * (-1 * abs(rank((close - vwap)))))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(rank((open - (sum(vwap, 10) / 10))) * (-1 * abs(rank((close - vwap)))))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=10, fq=fq)
        open_ = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)

        globals_ = _reindex_to_same_index(open=open_, close=close, vwap=vwap)

        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_006(self, index, enddate, fq="forward"):
        """
        公式：
            -1 * correlation(open, volume, 10)
        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * correlation(open, volume, 10))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        open_ = self.__get_price(valid_pool, end_date=enddate, field="open", count=10, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=10, fq=fq)

        globals_ = _reindex_to_same_index(open=open_, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_007(self, index, enddate, fq="forward"):
        """
        公式：
            ((adv20 < volume) ? ((-1 * ts_rank(abs(delta(close, 7)), 60)) * sign(delta(close, 7))) : (-1 * 1))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "where((adv20 < volume), ((-1 * ts_rank(abs(delta(close, 7)), 60)) * sign(delta(close, 7))), (-1* 1))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=1, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=67, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=1, fq=fq)

        globals_ = _reindex_to_same_index(adv20=adv20, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_008(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * rank(((sum(open, 5) * sum(returns, 5)) - delay((sum(open, 5) * sum(returns, 5)), 10))))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * rank(((sum(open, 5) * sum(returns, 5)) - delay((sum(open, 5) * sum(returns, 5)),10))))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=15, fq=fq)
        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=15, fq=fq)

        globals_ = _reindex_to_same_index(open=open, returns=returns)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_009(self, index, enddate, fq="forward"):
        """
        公式：
        ((0 < ts_min(delta(close, 1), 5)) ? delta(close, 1) : ((ts_max(delta(close, 1), 5) < 0) ? delta(close, 1) : (-1 * delta(close, 1))))

        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "where((0 < ts_min(delta(close, 1), 5)), delta(close, 1), where((ts_max(delta(close, 1), 5) < 0), delta(close, 1), (-1 * delta(close, 1))))"
        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=6, fq=fq)

        globals_ = _reindex_to_same_index(close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_010(self, index, enddate, fq="forward"):
        """
        公式：
            rank((0 < ts_min(delta(close, 1), 4)) ? delta(close, 1) : ((ts_max(delta(close, 1), 4) < 0) ? delta(close, 1) : (-1 * delta(close, 1))))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "rank(where((0 < ts_min(delta(close, 1), 4)), delta(close, 1), where((ts_max(delta(close, 1), 4) < 0), delta(close, 1), (-1 * delta(close, 1)))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=5, fq=fq)

        globals_ = _reindex_to_same_index(close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    # @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_011(self, index, enddate, fq="forward"):
        """
        公式：
            ((rank(ts_max((vwap - close), 3)) + rank(ts_min((vwap - close), 3))) * rank(delta(volume, 3)))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "((rank(ts_max((vwap - close), 3)) + rank(ts_min((vwap - close), 3))) *rank(delta(volume, 3)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=4, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=3, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=3, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_012(self, index, enddate, fq="forward"):
        """
        公式：
            sign(delta(volume, 1)) * (-1 * delta(close, 1))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(sign(delta(volume, 1)) * (-1 * delta(close, 1)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=2, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=2, fq=fq)

        globals_ = _reindex_to_same_index(close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_013(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * rank(covariance(rank(close), rank(volume), 5)))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * rank(covariance(rank(close), rank(volume), 5)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=5, fq=fq)

        globals_ = _reindex_to_same_index(close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_014(self, index, enddate, fq="forward"):
        """
        公式：
            ((-1 * rank(delta(returns, 3))) * correlation(open, volume, 10))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "((-1 * rank(delta(returns, 3))) * correlation(open, volume, 10))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=10, fq=fq)
        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=4, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=10, fq=fq)

        globals_ = _reindex_to_same_index(open=open, returns=returns, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_015(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * sum(rank(correlation(rank(high), rank(volume), 3)), 3))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * sum(rank(correlation(rank(high), rank(volume), 3)), 3))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=5, fq=fq)

        globals_ = _reindex_to_same_index(high=high, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_016(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * rank(covariance(rank(high), rank(volume), 5)))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * rank(covariance(rank(high), rank(volume), 5)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=5, fq=fq)

        globals_ = _reindex_to_same_index(high=high, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_017(self, index, enddate, fq="forward"):
        """
        公式：
            (((-1 * rank(ts_rank(close, 10))) * rank(delta(delta(close, 1), 1))) * rank(ts_rank((volume / adv20), 5)))
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(((-1 * rank(ts_rank(close, 10))) * rank(delta(delta(close, 1), 1))) *rank(ts_rank((div(volume, adv20)), 5)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=5, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=10, fq=fq)

        globals_ = _reindex_to_same_index(adv20=adv20, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_018(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * rank(((stddev(abs((close - open)), 5) + (close - open)) + correlation(close, open, 10))))

        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(-1 * rank(((stddev(abs((close - open)), 5) + (close - open)) + correlation(close, open,10))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=10, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=10, fq=fq)

        globals_ = _reindex_to_same_index(open=open, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_019(self, index, enddate, fq="forward"):
        """
        公式：
            ((-1 * sign(((close - delay(close, 7)) + delta(close, 7)))) * (1 + rank((1 + sum(returns, 250)))))

        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "((-1 * sign(((close - delay(close, 7)) + delta(close, 7)))) * (1 + rank((1 + sum(returns,250)))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=250, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=8, fq=fq)

        globals_ = _reindex_to_same_index(returns=returns, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_020(self, index, enddate, fq="forward"):
        """
        公式：
            (((-1 * rank((open - delay(high, 1)))) * rank((open - delay(close, 1)))) * rank((open - delay(low, 1))))

        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "(((-1 * rank((open - delay(high, 1)))) * rank((open - delay(close, 1)))) * rank((open -delay(low, 1))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=2, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=2, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=2, fq=fq)

        globals_ = _reindex_to_same_index(open=open, close=close, high=high, low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_021(self, index, enddate, fq="forward"):
        """
        公式：
            ((sum(close, 8) / 8 + stddev(close, 8)) < sum(close, 2) / 2) ? -1 : (sum(close, 2) / 2 < (sum(close, 8) / 8 - stddev(close, 8)) ? 1 : ((1 <= volume / adv20) ? 1 : -1))
        Inputs:
            enddate: 查询日期
            Index: 股票池
        Outputs:
            因子的值
        """
        func = "where((((sum(close, 8) / 8) + stddev(close, 8)) < (sum(close, 2) / 2)), (-1 * 1), where(((sum(close,2) / 2) < ((sum(close, 8) / 8) - stddev(close, 8))), 1, where(((1 < div(volume, adv20)) | (div(volume, adv20) == 1)), 1, (-1 * 1))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=8, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=1, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=1, fq=fq)

        globals_ = _reindex_to_same_index(adv20=adv20, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_022(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * (delta(correlation(high, volume, 5), 5) * rank(stddev(close, 20))))
        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * (delta(correlation(high, volume, 5), 5) * rank(stddev(close, 20))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=10, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=20, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=10, fq=fq)

        globals_ = _reindex_to_same_index(high=high, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_023(self, index, enddate, fq="forward"):
        """
        公式：
            (((sum(high, 20) / 20) < high) ? (-1 * delta(high, 2)) : 0)
        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "where(((sum(high, 20) / 20) < high), (-1 * delta(high, 2)), 0)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=20, fq=fq)

        globals_ = _reindex_to_same_index(high=high)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_024(self, index, enddate, fq="forward"):
        """
        公式：
            ((((delta((sum(close, 100) / 100), 100) / delay(close, 100)) < 0.05) ||
            ((delta((sum(close, 100) / 100), 100) / delay(close, 100)) == 0.05)) ?
            (-1 * (close - ts_min(close, 100))) : (-1 * delta(close, 3)))
        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "where((((delta((sum(close, 100) / 100), 100) / delay(close, 100)) < 0.05) | ((delta((sum(close, 100) / 100), 100) / delay(close, 100)) == 0.05)), (-1 * (close - ts_min(close,100))), (-1 * delta(close, 3)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=200, fq=fq)

        globals_ = _reindex_to_same_index(close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_025(self, index, enddate, fq="forward"):
        """
        公式：
            rank(((((-1 * returns) * adv20) * vwap) * (high - close)))
        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "rank(((((-1 * returns) * adv20) * vwap) * (high - close)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=1, fq=fq)
        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=1, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=1, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=1, fq=fq)

        globals_ = _reindex_to_same_index(high=high, close=close, returns=returns, vwap=vwap, adv20=adv20)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_026(self, index, enddate, fq="forward"):
        """
        公式：
            (-1 * ts_max(correlation(ts_rank(volume, 5), ts_rank(high, 5), 5), 3))
        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * ts_max(correlation(ts_rank(volume, 5), ts_rank(high, 5), 5), 3))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=11, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=11, fq=fq)

        globals_ = _reindex_to_same_index(high=high, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_027(self, index, enddate, fq="forward"):
        """
        公式：
            ((0.5 < rank((sum(correlation(rank(volume), rank(vwap), 6), 2) / 2.0))) ? (-1 * 1) : 1)
        Inputs:
            index: 股票池
            enddate: 查询日期
        Outputs:
            因子的值
        """
        func = "where((0.5 < rank((sum(correlation(rank(volume), rank(vwap), 6), 2) / 2.0))), (-1 * 1), 1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=7, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=7, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_028(self, index, enddate, fq="forward"):
        """
        公式：
            scale(((correlation(adv20, low, 5) + ((high + low) / 2)) - close))
        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "scale(((correlation(adv20, low, 5) + ((high + low) / 2)) - close))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=5, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=1, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=5, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)

        globals_ = _reindex_to_same_index(adv20=adv20, high=high, low=low, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_029(self, index, enddate, fq="forward"):
        """
        公式：
        (min(product(rank(rank(scale(log(sum(ts_min(rank(rank((-1 * rank(delta((close - 1), 5))))), 2), 1))))), 1), 5)
        + ts_rank(delay((-1 * returns), 6), 5))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(min(product(rank(rank(scale(log(sum(ts_min(rank(rank((-1 * rank(delta((close - 1),5))))), 2), 1))))), 1), 5) + ts_rank(delay((-1 * returns), 6), 5))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=11, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=7, fq=fq)

        globals_ = _reindex_to_same_index(returns=returns, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_030(self, index, enddate, fq="forward"):
        """
        公式：
        (((1.0 - rank(((sign((close - delay(close, 1))) + sign((delay(close, 1) - delay(close, 2))))
        + sign((delay(close, 2) - delay(close, 3)))))) * sum(volume, 5)) / sum(volume, 20))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "div(((1.0 - rank(((sign((close - delay(close, 1))) + sign((delay(close, 1) - delay(close, 2)))) +sign((delay(close, 2) - delay(close, 3)))))) * sum(volume, 5)), sum(volume, 20))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=4, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=20, fq=fq)

        globals_ = _reindex_to_same_index(close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_031(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(rank(rank(decay_linear((-1 * rank(rank(delta(close, 10)))), 10)))) + rank((-1 * delta(close, 3))))
        + sign(scale(correlation(adv20, low, 12))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "((rank(rank(rank(decay_linear((-1 * rank(rank(delta(close, 10)))), 10)))) + rank((-1 *delta(close, 3)))) + sign(scale(correlation(adv20, low, 12))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=20, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=12, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=12, fq=fq)

        globals_ = _reindex_to_same_index(close=close, low=low, adv20=adv20)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_032(self, index, enddate, fq="forward"):
        """
        公式：
        (scale(((sum(close, 7) / 7) - close)) + (20 * scale(correlation(vwap, delay(close, 5), 230))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(scale(((sum(close, 7) / 7) - close)) + (20 * scale(correlation(vwap, delay(close, 5),230))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=235, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=230, fq=fq)

        globals_ = _reindex_to_same_index(close=close, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_033(self, index, enddate, fq="forward"):
        """
        公式：
        rank((-1 * ((1 - (open / close))^1)))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "rank((-1 * ((1 - (open / close))**1)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)

        globals_ = _reindex_to_same_index(open=open, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_034(self, index, enddate, fq="forward"):
        """
        公式：
        rank(((1 - rank((stddev(returns, 2) / stddev(returns, 5)))) + (1 - rank(delta(close, 1)))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "rank(((1 - rank(div(stddev(returns, 2), stddev(returns, 5)))) + (1 - rank(delta(close, 1)))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=5, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=2, fq=fq)

        globals_ = _reindex_to_same_index(returns=returns, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_035(self, index, enddate, fq="forward"):
        """
        公式：
        ((Ts_Rank(volume, 32) * (1 - Ts_Rank(((close + high) - low), 16))) * (1 - Ts_Rank(returns, 32)))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "((ts_rank(volume, 32) * (1 - ts_rank(((close + high) - low), 16))) * (1 -ts_rank(returns, 32)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=32, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=16, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=16, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=16, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=32, fq=fq)

        globals_ = _reindex_to_same_index(returns=returns, close=close, high=high, low=low, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_036(self, index, enddate, fq="forward"):
        """
        公式：
        (((((2.21 * rank(correlation((close - open), delay(volume, 1), 15))) + (0.7 * rank((open - close))))
        + (0.73 * rank(Ts_Rank(delay((-1 * returns), 6), 5)))) + rank(abs(correlation(vwap, adv20, 6))))
        + (0.6 * rank((((sum(close, 200) / 200) - open) * (close - open)))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(((((2.21 * rank(correlation((close - open), delay(volume, 1), 15))) + (0.7 * rank((open- close)))) + (0.73 * rank(ts_rank(delay((-1 * returns), 6), 5)))) + rank(abs(correlation(vwap,adv20, 6)))) + (0.6 * rank((((sum(close, 200) / 200) - open) * (close - open)))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=200, fq=fq)
        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=11, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=15, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=6, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=16, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=6, fq=fq)

        globals_ = _reindex_to_same_index(
            close=close, returns=returns, open=open, adv20=adv20, volume=volume, vwap=vwap
        )
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_037(self, index, enddate, fq="forward"):
        """
        公式：
        (rank(correlation(delay((open - close), 1), close, 200)) + rank((open - close)))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(rank(correlation(delay((open - close), 1), close, 200)) + rank((open - close)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=201, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=201, fq=fq)

        globals_ = _reindex_to_same_index(close=close, open=open)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_038(self, index, enddate, fq="forward"):
        """
        公式：
        ((-1 * rank(Ts_Rank(close, 10))) * rank((close / open)))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "((-1 * rank(ts_rank(close, 10))) * rank((close / open)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=10, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)

        globals_ = _reindex_to_same_index(close=close, open=open)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_039(self, index, enddate, fq="forward"):
        """
        公式：
        ((-1 * rank((delta(close, 7) * (1 - rank(decay_linear((volume / adv20), 9)))))) * (1 + rank(sum(returns, 250))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "((-1 * rank((delta(close, 7) * (1 - rank(decay_linear(div(volume, adv20), 9)))))) * (1 +rank(sum(returns, 250))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=250, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="returns", count=8, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="returns", count=9, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=9, fq=fq)

        globals_ = _reindex_to_same_index(returns=returns, close=close, adv20=adv20, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_040(self, index, enddate, fq="forward"):
        """
        公式：
        ((-1 * rank(stddev(high, 10))) * correlation(high, volume, 10))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "((-1 * rank(stddev(high, 10))) * correlation(high, volume, 10))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=10, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=10, fq=fq)

        globals_ = _reindex_to_same_index(volume=volume, high=high)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_041(self, index, enddate, fq="forward"):
        """
        公式：
        (((high * low)^0.5) - vwap)

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(((high * low)**0.5) - vwap)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=1, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=1, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=1, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, high=high, low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_042(self, index, enddate, fq="forward"):
        """
        公式：
        (rank((vwap - close)) / rank((vwap + close)))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "div(rank((vwap - close)), rank((vwap + close)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=1, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_043(self, index, enddate, fq="forward"):
        """
        公式：
        (ts_rank((volume / adv20), 20) * ts_rank((-1 * delta(close, 7)), 8))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(ts_rank(div(volume, adv20), 20) * ts_rank((-1 * delta(close, 7)), 8))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=20, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=15, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=20, fq=fq)

        globals_ = _reindex_to_same_index(adv20=adv20, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_044(self, index, enddate, fq="forward"):
        """
        公式：
        (-1 * correlation(high, rank(volume), 5))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * correlation(high, rank(volume), 5))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=5, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)

        globals_ = _reindex_to_same_index(high=high, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_045(self, index, enddate, fq="forward"):
        """
        公式：
        (-1 * ((rank((sum(delay(close, 5), 20) / 20)) * correlation(close, volume, 2))
        * rank(correlation(sum(close, 5), sum(close, 20), 2))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * ((rank((sum(delay(close, 5), 20) / 20)) * correlation(close, volume, 2)) *rank(correlation(sum(close, 5), sum(close, 20), 2))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=25, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=2, fq=fq)

        globals_ = _reindex_to_same_index(close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_046(self, index, enddate, fq="forward"):
        """
        公式：
        ((0.25 < (((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10)))
        ? (-1 * 1)
        : (((((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10)) < 0)
        ? 1 : ((-1 * 1) * (close - delay(close, 1)))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "where((0.25 < (((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10))), (-1 * 1), where(((((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10)) < 0), 1, ((-1 * 1) * (close - delay(close, 1)))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=21, fq=fq)

        globals_ = _reindex_to_same_index(close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_047(self, index, enddate, fq="forward"):
        """
        公式：
        ((((rank((1 / close)) * volume) / adv20) * ((high * rank((high - close))) / (sum(high, 5) / 5)))
        - rank((vwap - delay(vwap, 5))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "((div((rank((1 / close)) * volume), adv20) * ((high * rank((high - close))) / (sum(high, 5) /5))) - rank((vwap - delay(vwap, 5))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=1, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=1, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=5, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=6, fq=fq)

        globals_ = _reindex_to_same_index(close=close, adv20=adv20, volume=volume, high=high, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_049(self, index, enddate, fq="forward"):
        """
        公式：
        (((((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10)) < (-1 * 0.1)) ? 1 : ((-1 * 1) * (close - delay(close, 1))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "where(((((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10)) < (-1 *0.1)), 1, ((-1 * 1) * (close - delay(close, 1))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=21, fq=fq)

        globals_ = _reindex_to_same_index(close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_050(self, index, enddate, fq="forward"):
        """
        公式：
        (-1 * ts_max(rank(correlation(rank(volume), rank(vwap), 5)), 5))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * ts_max(rank(correlation(rank(volume), rank(vwap), 5)), 5))"
        func = "correlation(rank(volume), rank(vwap), 5)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=9, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=9, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_051(self, index, enddate, fq="forward"):
        """
        公式：
        (((((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10)) < (-1 * 0.05)) ?
        1 : ((-1 * 1) * (close - delay(close, 1))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "where(((((delay(close, 20) - delay(close, 10)) / 10) - ((delay(close, 10) - close) / 10)) < (-1 *0.05)), 1, ((-1 * 1) * (close - delay(close, 1))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=21, fq=fq)

        globals_ = _reindex_to_same_index(close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_052(self, index, enddate, fq="forward"):
        """
        公式：
        ((((-1 * ts_min(low, 5)) + delay(ts_min(low, 5), 5)) * rank(((sum(returns, 240) - sum(returns, 20)) / 220)))
        * ts_rank(volume, 5))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "((((-1 * ts_min(low, 5)) + delay(ts_min(low, 5), 5)) * rank(((sum(returns, 240) -sum(returns, 20)) / 220))) * ts_rank(volume, 5))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=240, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=10, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)

        globals_ = _reindex_to_same_index(returns=returns, low=low, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_053(self, index, enddate, fq="forward"):
        """
        公式：
        (-1 * delta((((close - low) - (high - close)) / (close - low)), 9))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * delta(div(((close - low) - (high - close)), (close - low)), 9))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=10, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=10, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=10, fq=fq)

        globals_ = _reindex_to_same_index(close=close, low=low, high=high)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_054(self, index, enddate, fq="forward"):
        """
        公式：
        ((-1 * ((low - close) * (open^5))) / ((low - high) * (close^5)))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "div((-1 * ((low - close) * (open**5))), ((low - high) * (close**5)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=1, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=1, fq=fq)

        globals_ = _reindex_to_same_index(close=close, open=open, low=low, high=high)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_055(self, index, enddate, fq="forward"):
        """
        公式：
        (-1 * correlation(rank(((close - ts_min(low, 12)) / (ts_max(high, 12) - ts_min(low, 12)))), rank(volume), 6))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值
        """
        func = "(-1 * correlation(rank(div((close - ts_min(low, 12)), (ts_max(high, 12) - ts_min(low,12)))), rank(volume), 6))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=6, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=17, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=17, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=6, fq=fq)

        globals_ = _reindex_to_same_index(volume=volume, low=low, high=high, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    # @few_stock_warning_alpha101
    # @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    # def alpha_056(self, index, enddate, fq="forward"):
    #     """
    #     公式：
    #     (0 - (1 * (rank((sum(returns, 10) / sum(sum(returns, 2), 3))) * rank((returns * cap)))))

    #     Inputs:
    #         enddate: 查询日期
    #         index: 股票池
    #     Outputs:
    #         因子的值
    #     """
    #     func = "(0 - (1 * (rank(div(sum(returns, 10), sum(sum(returns, 2), 3))) * rank((returns * market_cap)))))"

    #     pool = self.get_pool(index, enddate)

    #     valid_pool = pool[~pool].index.tolist()
    #     if not valid_pool:
    #         return pd.Series(dtype="float64", index=pool.index)

    #     market_cap = self.__get_price(valid_pool, end_date=enddate, field="market_cap", count=1, fq=fq)
    #     returns = self.__get_price(valid_pool, end_date=enddate, field="returns", count=10, fq=fq)

    #     globals_ = _reindex_to_same_index(market_cap=market_cap, returns=returns)
    #     globals_.update(_get_func_globals())

    #     return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_057(self, index, enddate, fq="forward"):
        """
        公式：
        0 - (close - vwap) / decay_linear(rank(ts_argmax(close, 30)), 2)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(0 - (1 * div((close - vwap), decay_linear(rank(ts_argmax(close, 30)), 2))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=31, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=1, fq=fq)

        globals_ = _reindex_to_same_index(close=close, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_060(self, index, enddate, fq="forward"):
        """
        公式：
        (0 - (1 * ((2 * scale(rank(((((close - low) - (high - close)) / (high - low)) * volume))))
        - scale(rank(ts_argmax(close, 10))))))

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            index成分股对应的因子的值
        """
        func = "(0 - (1 * ((2 * scale(rank((div(((close - low) - (high - close)), (high - low)) * volume)))) -scale(rank(ts_argmax(close, 10))))))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=10, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=1, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=1, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=1, fq=fq)

        globals_ = _reindex_to_same_index(close=close, high=high, low=low, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_061(self, index, enddate, fq="forward"):
        """
        公式：
        (rank((vwap - ts_min(vwap, 16.1219))) < rank(correlation(vwap, adv180, 17.9282)))


        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            符合的股票对应值为1，不符合的股票对应值为-1
        """
        func = "where(rank((vwap - ts_min(vwap, 16.1219))) < rank(correlation(vwap, adv180, 17.9282)), 1, -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv180 = self.__get_price(valid_pool, end_date=enddate, field="adv180", count=17, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=17, fq=fq)

        globals_ = _reindex_to_same_index(adv180=adv180, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_062(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(correlation(vwap, sum(adv20, 22.4101), 9.91009)) < rank(((rank(open) + rank(open))
        < (rank(((high + low) / 2)) + rank(high))))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            符合的股票对应值为-1，不符合的股票对应值为1
        """
        func = "((rank(correlation(vwap, sum(adv20, 22.4101), 9.91009)) < rank(((rank(open) +rank(open)) < (rank(((high + low) / 2)) + rank(high))))) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=9, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=30, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=1, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=1, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, adv20=adv20, open=open, high=high, low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_064(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(correlation(sum(((open * 0.178404) + (low * (1 - 0.178404))), 12.7054), sum(adv120, 12.7054), 16.6208))
        < rank(delta(((((high + low) / 2) * 0.178404) + (vwap * (1 - 0.178404))), 3.69741))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(where(rank(correlation(sum(((open * 0.178404) + (low * (1 - 0.178404))), 12.7054),sum(adv120, 12.7054), 16.6208)) < rank(delta(((((high + low) / 2) * 0.178404) + (vwap * (1 -0.178404))), 3.69741)), 1, -1) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv120 = self.__get_price(valid_pool, end_date=enddate, field="adv120", count=27, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=4, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=4, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=27, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=27, fq=fq)

        globals_ = _reindex_to_same_index(adv120=adv120, vwap=vwap, high=high, low=low, open=open)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_065(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(correlation(((open * 0.00817205) + (vwap * (1 - 0.00817205))), sum(adv60, 8.6911), 6.40374))
        < rank((open - ts_min(open, 13.635)))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(where(rank(correlation(((open * 0.00817205) + (vwap * (1 - 0.00817205))), sum(adv60,8.6911), 6.40374)) < rank((open - ts_min(open, 13.635))), 1, -1) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=6, fq=fq)
        adv60 = self.__get_price(valid_pool, end_date=enddate, field="adv60", count=13, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=13, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, adv60=adv60, open=open)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_066(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(decay_linear(delta(vwap, 3.51013), 7.23052)) + Ts_Rank(decay_linear(((((low * 0.96633)
        + (low * (1 - 0.96633))) - vwap) / (open - ((high + low) / 2))), 11.4157), 6.72611)) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "((rank(decay_linear(delta(vwap, 3.51013), 7.23052)) + ts_rank(decay_linear(div((((low* 0.96633) + (low * (1 - 0.96633))) - vwap), (open - ((high + low) / 2))), 11.4157), 6.72611)) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=16, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=16, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=16, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=16, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, high=high, low=low, open=open)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_068(self, index, enddate, fq="forward"):
        """
        公式：
        ((Ts_Rank(correlation(rank(high), rank(adv15), 8.91644), 13.9333)
        < rank(delta(((close * 0.518371) + (low * (1 - 0.518371))), 1.06157))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(where(ts_rank(correlation(rank(high), rank(adv15), 8.91644), 13.9333) <rank(delta(((close * 0.518371) + (low * (1 - 0.518371))), 1.06157)), 1, -1) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv15 = self.__get_price(valid_pool, end_date=enddate, field="adv15", count=20, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=20, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=2, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=2, fq=fq)

        globals_ = _reindex_to_same_index(adv15=adv15, high=high, low=low, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_071(self, index, enddate, fq="forward"):
        """
        公式：
        max(  Ts_Rank(decay_linear(correlation(Ts_Rank(close, 3.43976), Ts_Rank(adv180, 12.0647), 18.0175), 4.20501), 15.6948),
            Ts_Rank(decay_linear((rank(((low + open) - (vwap + vwap)))^2), 16.4662), 4.4388))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "max(ts_rank(decay_linear(correlation(ts_rank(close, 3.43976), ts_rank(adv180,12.0647), 18.0175), 4.20501), 15.6948), ts_rank(decay_linear((rank(((low + open) - (vwap +vwap)))**2), 16.4662), 4.4388))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv180 = self.__get_price(valid_pool, end_date=enddate, field="adv180", count=46, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=19, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=19, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=19, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=37, fq=fq)

        globals_ = _reindex_to_same_index(adv180=adv180, vwap=vwap, open=open, low=low, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_072(self, index, enddate, fq="forward"):
        """
        公式：
        (rank(decay_linear(correlation(((high + low) / 2), adv40, 8.93345), 10.1519)) /
        rank(decay_linear(correlation(Ts_Rank(vwap, 3.72469), Ts_Rank(volume, 18.5188), 6.86671), 2.95011)))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "div(rank(decay_linear(correlation(((high + low) / 2), adv40, 8.93345), 10.1519)), rank(decay_linear(correlation(ts_rank(vwap, 3.72469), ts_rank(volume, 18.5188), 6.86671),2.95011)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv40 = self.__get_price(valid_pool, end_date=enddate, field="adv40", count=17, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=9, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=17, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=17, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=24, fq=fq)

        globals_ = _reindex_to_same_index(adv40=adv40, vwap=vwap, low=low, high=high, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_073(self, index, enddate, fq="forward"):
        """
        公式：
        (max(rank(decay_linear(delta(vwap, 4.72775), 2.91864)),
        Ts_Rank(decay_linear(((delta(((open * 0.147155) + (low * (1 - 0.147155))), 2.03608) / ((open * 0.147155)
        + (low * (1 - 0.147155)))) * -1), 3.33829), 16.7411)) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池
        Outputs:
            因子的值

        """
        func = "(max(rank(decay_linear(delta(vwap, 4.72775), 2.91864)),ts_rank(decay_linear((div(delta(((open * 0.147155) + (low * (1 - 0.147155))), 2.03608), ((open *0.147155) + (low * (1 - 0.147155)))) * -1), 3.33829), 16.7411)) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=20, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=20, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=6, fq=fq)

        globals_ = _reindex_to_same_index(open=open, low=low, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_074(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(correlation(close, sum(adv30, 37.4843), 15.1365)) <
        rank(correlation(rank(((high * 0.0261661) + (vwap * (1 - 0.0261661)))), rank(volume), 11.4791))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(where(rank(correlation(close, sum(adv30, 37.4843), 15.1365)) <rank(correlation(rank(((high * 0.0261661) + (vwap * (1 - 0.0261661)))), rank(volume), 11.4791)), 1, -1)* -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv30 = self.__get_price(valid_pool, end_date=enddate, field="adv30", count=51, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=11, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=15, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=11, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=11, fq=fq)

        globals_ = _reindex_to_same_index(adv30=adv30, vwap=vwap, close=close, high=high, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_075(self, index, enddate, fq="forward"):
        """
        公式：
        (rank(correlation(vwap, volume, 4.24304)) < rank(correlation(rank(low), rank(adv50), 12.4413)))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            一个Series：index为成分股代码，values为1或-1，当满足条件时为1，否则为-1

        """
        func = (
            "where(rank(correlation(vwap, volume, 4.24304)) < rank(correlation(rank(low), rank(adv50),12.4413)), 1, -1)"
        )

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv50 = self.__get_price(valid_pool, end_date=enddate, field="adv50", count=12, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=12, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=4, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=4, fq=fq)

        globals_ = _reindex_to_same_index(adv50=adv50, low=low, vwap=vwap, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_077(self, index, enddate, fq="forward"):
        """
        公式：
        min(rank(decay_linear(((((high + low) / 2) + high) - (vwap + high)), 20.0451)),
        rank(decay_linear(correlation(((high + low) / 2), adv40, 3.1614), 5.64125)))

        Inputs:
            enddate: 查询日期
            index:股票池

        Outputs:
            因子的值

        """
        func = "min(rank(decay_linear(((((high + low) / 2) + high) - (vwap + high)), 20.0451)),rank(decay_linear(correlation(((high + low) / 2), adv40, 3.1614), 5.64125)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv40 = self.__get_price(valid_pool, end_date=enddate, field="adv40", count=7, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=20, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=20, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=20, fq=fq)

        globals_ = _reindex_to_same_index(adv40=adv40, low=low, high=high, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_078(self, index, enddate, fq="forward"):
        """
        公式：
        (rank(correlation(sum(((low * 0.352233) + (vwap * (1 - 0.352233))), 19.7428),
        sum(adv40, 19.7428), 6.83313))^rank(correlation(rank(vwap), rank(volume), 5.77492)))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(rank(correlation(sum(((low * 0.352233) + (vwap * (1 - 0.352233))), 19.7428),sum(adv40, 19.7428), 6.83313))**rank(correlation(rank(vwap), rank(volume), 5.77492)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv40 = self.__get_price(valid_pool, end_date=enddate, field="adv40", count=24, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=24, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=24, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)

        globals_ = _reindex_to_same_index(adv40=adv40, low=low, vwap=vwap, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_081(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(Log(product(rank((rank(correlation(vwap, sum(adv10, 49.6054),8.47743))^4)), 14.9655))) < rank(correlation(rank(vwap), rank(volume), 5.07914))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(where(rank(log(product(rank((rank(correlation(vwap, sum(adv10, 49.6054),8.47743))**4)), 14.9655))) < rank(correlation(rank(vwap), rank(volume), 5.07914)), 1, -1) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv10 = self.__get_price(valid_pool, end_date=enddate, field="adv10", count=69, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=21, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=5, fq=fq)

        globals_ = _reindex_to_same_index(adv10=adv10, vwap=vwap, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_083(self, index, enddate, fq="forward"):
        """
        公式：
        ((rank(delay(((high - low) / (sum(close, 5) / 5)), 2)) * rank(rank(volume)))
        / (((high - low) / (sum(close, 5) / 5)) / (vwap - close)))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "div((rank(delay(div((high - low), (sum(close, 5) / 5)), 2)) * rank(rank(volume))), div(((high -low) / (sum(close, 5) / 5)), (vwap - close)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=7, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=3, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=3, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=1, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=1, fq=fq)

        globals_ = _reindex_to_same_index(close=close, low=low, high=high, vwap=vwap, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_084(self, index, enddate, fq="forward"):
        """
        公式：

        SignedPower(Ts_Rank((vwap - ts_max(vwap, 15.3217)), 20.7127), delta(close, 4.96796))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "signedpower(ts_rank((vwap - ts_max(vwap, 15.3217)), 20.7127), delta(close,4.96796))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=34, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=5, fq=fq)

        globals_ = _reindex_to_same_index(vwap=vwap, close=close)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_085(self, index, enddate, fq="forward"):
        """
        公式：

        (rank(correlation(((high * 0.876703) + (close * (1 - 0.876703))), adv30, 9.61331))
        ^rank(correlation(Ts_Rank(((high + low) / 2), 3.70596), Ts_Rank(volume, 10.1595), 7.11408)))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(rank(correlation(((high * 0.876703) + (close * (1 - 0.876703))), adv30,9.61331))**rank(correlation(ts_rank(((high + low) / 2), 3.70596), ts_rank(volume, 10.1595),7.11408)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv30 = self.__get_price(valid_pool, end_date=enddate, field="adv30", count=9, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=16, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=9, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=9, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=9, fq=fq)

        globals_ = _reindex_to_same_index(adv30=adv30, volume=volume, close=close, high=high, low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_086(self, index, enddate, fq="forward"):
        """
        公式：

        ((Ts_Rank(correlation(close, sum(adv20, 14.7444), 6.00049), 20.4195) < rank(((open + close) - (vwap + open)))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(where(ts_rank(correlation(close, sum(adv20, 14.7444), 6.00049), 20.4195) < rank(((open+ close) - (vwap + open))), 1, -1) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=25, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)
        adv20 = self.__get_price(valid_pool, end_date=enddate, field="adv20", count=38, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=1, fq=fq)

        globals_ = _reindex_to_same_index(close=close, open=open, adv20=adv20, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_088(self, index, enddate, fq="forward"):
        """
        公式：

        min(rank(decay_linear(((rank(open) + rank(low)) - (rank(high) + rank(close))),
        8.06882)), Ts_Rank(decay_linear(correlation(Ts_Rank(close, 8.44728), Ts_Rank(adv60, 20.6966), 8.01266), 6.65053), 2.61957))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "min(rank(decay_linear(((rank(open) + rank(low)) - (rank(high) + rank(close))),8.06882)), ts_rank(decay_linear(correlation(ts_rank(close, 8.44728), ts_rank(adv60,20.6966), 8.01266), 6.65053), 2.61957))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv60 = self.__get_price(valid_pool, end_date=enddate, field="adv60", count=33, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=21, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=8, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=8, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=8, fq=fq)

        globals_ = _reindex_to_same_index(adv60=adv60, close=close, open=open, high=high, low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_092(self, index, enddate, fq="forward"):
        """
        公式：

        min(Ts_Rank(decay_linear(((((high + low) / 2) + close) < (low + open)), 14.7221),
        18.8683), Ts_Rank(decay_linear(correlation(rank(low), rank(adv30), 7.58555), 6.94024), 6.80584))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "min(ts_rank(decay_linear(where((((high + low) / 2) + close) < (low + open), 1, -1), 14.7221),18.8683), ts_rank(decay_linear(correlation(rank(low), rank(adv30), 7.58555), 6.94024),6.80584))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv30 = self.__get_price(valid_pool, end_date=enddate, field="adv30", count=17, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=31, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=31, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=31, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=31, fq=fq)

        globals_ = _reindex_to_same_index(adv30=adv30, close=close, open=open, high=high, low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_094(self, index, enddate, fq="forward"):
        """
        公式：

        ((rank((vwap - ts_min(vwap, 11.5783)))
        ^Ts_Rank(correlation(Ts_Rank(vwap, 19.6462), Ts_Rank(adv60, 4.02992), 18.0926), 2.70756)) * -1)

        Inputs:
            index: 股票池
            enddate: 查询日期

        Outputs:
            因子的值

        """
        func = "((rank((vwap - ts_min(vwap, 11.5783)))**ts_rank(correlation(ts_rank(vwap,19.6462), ts_rank(adv60, 4.02992), 18.0926), 2.70756)) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv60 = self.__get_price(valid_pool, end_date=enddate, field="adv60", count=22, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=37, fq=fq)

        globals_ = _reindex_to_same_index(adv60=adv60, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_095(self, index, enddate, fq="forward"):
        """
        公式：

        (rank((open - ts_min(open, 12.4105))) < Ts_Rank((rank(correlation(sum(((high + low) / 2), 19.1351), sum(adv40, 19.1351), 12.8742))^5), 11.7584))


        Inputs:
            index: 股票池
            enddate: 查询日期

        Outputs:
            因子的值

        """
        func = "where(rank((open - ts_min(open, 12.4105))) < ts_rank((rank(correlation(sum(((high + low)/ 2), 19.1351), sum(adv40, 19.1351), 12.8742))**5), 11.7584), 1, -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv40 = self.__get_price(valid_pool, end_date=enddate, field="adv40", count=40, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=12, fq=fq)
        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=40, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=40, fq=fq)

        globals_ = _reindex_to_same_index(adv40=adv40, open=open, high=high, low=low)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_096(self, index, enddate, fq="forward"):
        """
        公式：

        (max(Ts_Rank(decay_linear(correlation(rank(vwap), rank(volume), 3.83878), 4.16783), 8.38151),
        Ts_Rank(decay_linear(Ts_ArgMax(correlation(Ts_Rank(close, 7.45404), Ts_Rank(adv60, 4.13242), 3.65459), 12.6556), 14.0365), 13.4143)) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(max(ts_rank(decay_linear(correlation(rank(vwap), rank(volume), 3.83878),4.16783), 8.38151), ts_rank(decay_linear(ts_argmax(correlation(ts_rank(close, 7.45404),ts_rank(adv60, 4.13242), 3.65459), 12.6556), 14.0365), 13.4143)) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv60 = self.__get_price(valid_pool, end_date=enddate, field="adv60", count=42, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=13, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=45, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=13, fq=fq)

        globals_ = _reindex_to_same_index(adv60=adv60, vwap=vwap, close=close, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_098(self, index, enddate, fq="forward"):
        """
        公式：

        (rank(decay_linear(correlation(vwap, sum(adv5, 26.4719), 4.58418), 7.18088)) -
        rank(decay_linear(Ts_Rank(Ts_ArgMin(correlation(rank(open), rank(adv15), 20.8187), 8.62571), 6.95668), 8.07206)))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(rank(decay_linear(correlation(vwap, sum(adv5, 26.4719), 4.58418), 7.18088)) -rank(decay_linear(ts_rank(ts_argmin(correlation(rank(open), rank(adv15), 20.8187), 8.62571),6.95668), 8.07206)))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        adv15 = self.__get_price(valid_pool, end_date=enddate, field="adv15", count=39, fq=fq)
        adv5 = self.__get_price(valid_pool, end_date=enddate, field="adv5", count=35, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=39, fq=fq)
        vwap = self.__get_price(valid_pool, end_date=enddate, field="vwap", count=10, fq=fq)

        globals_ = _reindex_to_same_index(adv15=adv15, adv5=adv5, open=open, vwap=vwap)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_099(self, index, enddate, fq="forward"):
        """
        公式：

        ((rank(correlation(sum(((high + low) / 2), 19.8975), sum(adv60, 19.8975), 8.8136))
        < rank(correlation(low, volume, 6.28259))) * -1)

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "(where(rank(correlation(sum(((high + low) / 2), 19.8975), sum(adv60, 19.8975), 8.8136)) <rank(correlation(low, volume, 6.28259)), 1, -1) * -1)"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=26, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=26, fq=fq)
        adv60 = self.__get_price(valid_pool, end_date=enddate, field="adv60", count=26, fq=fq)
        volume = self.__get_price(valid_pool, end_date=enddate, field="volume", count=6, fq=fq)

        globals_ = _reindex_to_same_index(high=high, low=low, adv60=adv60, volume=volume)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="invalid value encountered", category=RuntimeWarning)
    def alpha_101(self, index, enddate, fq="forward"):
        """
        公式：
        ((close - open) / ((high - low) + .001))

        Inputs:
            enddate: 查询日期
            index: 股票池

        Outputs:
            因子的值

        """
        func = "div((close - open), ((high - low) + .001))"

        pool = self.get_pool(index, enddate)

        valid_pool = pool[~pool].index.tolist()
        if not valid_pool:
            return pd.Series(dtype="float64", index=pool.index)

        high = self.__get_price(valid_pool, end_date=enddate, field="high", count=1, fq=fq)
        close = self.__get_price(valid_pool, end_date=enddate, field="close", count=1, fq=fq)
        low = self.__get_price(valid_pool, end_date=enddate, field="low", count=1, fq=fq)
        open = self.__get_price(valid_pool, end_date=enddate, field="open", count=1, fq=fq)

        globals_ = _reindex_to_same_index(high=high, close=close, low=low, open=open)
        globals_.update(_get_func_globals())

        return eval(func, globals_).iloc[-1].reindex(pool.index).astype("float64")

    # def alpha_048(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_058(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_059(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_063(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_067(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_069(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_070(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_076(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_079(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_080(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_082(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_087(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_089(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_090(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_091(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_093(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_097(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    # def alpha_100(enddate, index, fq="forward"):
    #     return pd.Series(dtype="float64")

    @few_stock_warning_alpha101
    @ignore_warning(message="alpha_\d\d\d 函数的值受股票池影响, 建议增加股票池股票数量")
    def calc(self, code: list, date: Union[List[str], str], fq: str = "forward", alpha: Union[str, List] = "all"):
        if isinstance(alpha, six.string_types) and alpha == "all":
            alphas = ["alpha_{:03d}".format(i) for i in range(1, 102)]

        else:
            alphas = [format_alpha_name(a) for a in ensure_list(alpha)]
        alphas = [i for i in alphas if hasattr(self, i)]

        date = [date] if isinstance(date, six.string_types) else date
        res = []
        for end_date in date:
            edt = to_nstimestamp(f"{end_date} 15:00:00")
            result = list()
            for a in alphas:
                result.append(getattr(self, a)(index=code, enddate=end_date, fq=fq))
            result = pd.concat(result, axis=1, keys=alphas)
            result.index = pd.MultiIndex.from_tuples(result.index.map(lambda x: (edt, x)))
            res.append(result)
        res = pd.concat(res, axis=0)
        res = {f"{self.__class__.__name__}_{key}": res[key].unstack().sort_index() for key in res.columns}
        return res

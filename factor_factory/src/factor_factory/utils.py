import re
import six


def format_alpha_name(x):
    if isinstance(x, int) or (isinstance(x, six.string_types) and x.isdigit()):
        x = "alpha_{:03d}".format(int(x))
    elif isinstance(x, six.string_types):
        pass
    else:
        raise ValueError("invalid alpha type")

    syntex = re.compile("alpha_(\d\d\d)")
    match = re.match(syntex, x)
    if not match or not 1 <= int(match.group(1)) <= 101:
        raise ValueError("invalid alpha {}".format(str(x)))
    return x

import multiprocessing as mp

import pandas as pd
from aichemy.data_fetcher import DataFetcherOfQNT
from aichemy.database_manager import DatabaseManager
from factor_factory.alpha101 import Alpha101
from factor_factory.alpha191 import Alpha191
from qnt_research.api import get_all_mfut, get_trade_days

symbols = get_all_mfut("now")


def fnc101(dt):
    al101 = Alpha101(DataFetcherOfQNT())
    ret = al101.calc(symbols, [dt])
    print(f"{dt}计算完成")
    return ret


def fnc191(dt):
    al191 = Alpha191(DataFetcherOfQNT())
    ret = al191.calc(symbols, "", [dt])
    print(f"{dt}计算完成")
    return ret


if __name__ == "__main__":
    mp.set_start_method("spawn")

    trade_days = list(map(lambda x: x.strftime("%Y-%m-%d"), get_trade_days("SHFE", "20210101", "20231231")))
    with mp.Pool(10) as p:
        res = p.starmap(fnc191, [(i,) for i in trade_days])

    res1 = {}
    for factor_name in res[1].keys():
        res1[factor_name] = []
        for res_of_day in res:
            res1[factor_name].append(res_of_day[factor_name])

    res1 = {key: pd.concat(value, axis=0) for key, value in res1.items()}
    res1 = {key: value.sort_index() for key, value in res1.items()}

    factor = DatabaseManager()
    factor.bind_db("factor191.h5")

    for key in res1:
        factor.update(key, res1[key])
